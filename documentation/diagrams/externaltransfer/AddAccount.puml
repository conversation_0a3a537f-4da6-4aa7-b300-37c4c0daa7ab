@startuml
skinparam sequenceMessageAlign center

participant Client as client
participant "Move Money" as mm
participant "CES Proxy" as proxy

||20||
    group Add Account (Risk Token Success) && Valid Request
    ||20||
        client -> mm: POST: /externaltransfer/addAccount (valid Risk Token)
        activate client
        activate mm
        mm -> mm: Risk Token Verified
        mm -> mm: Valid Routing Number, Credit Routing Number, Account Number, and Credit Account Number
        mm -> mm: Map request to CES Add External Account By Instant Request
        mm -> mm: tokenize CES Add External Account By Instant Request
        mm -> proxy: POST: /tokenized/services/externaltransfer/addExternalAccountByInstant
        activate proxy
        proxy -> mm: CES Add External Account Real Time Response
        deactivate proxy
        mm -> mm: Map to BFF Add Account Response
        mm -> client: BFF Add Account Response
        deactivate mm
        deactivate client
    ||20||
    end group
||20||

||20||
    group Add Account (Risk Token Failure)
    ||20||
        client -> mm: POST: /externaltransfer/addAccount (invalid Risk Token)
        activate client
        activate mm
        mm -x mm: Risk Token Fails to Verify
        mm -> client: Risk Exception
        deactivate mm
        deactivate client
    ||20||
    end group
||20||

||20||
    group Add Account (Risk Token Success) && Invalid Request
    ||20||
        client -> mm: POST: /externaltransfer/addAccount (valid Risk Token)
        activate client
        activate mm
        mm -> mm: Risk Token Verified
        mm -x mm: Invalid Routing Number || Credit Routing Number || Account Number || Credit Account Number
        mm -> mm: Map to BFF Add Account Response with 400 BadRequest status and validation error message
        mm -> client: BFF Add Account Response
        deactivate mm
        deactivate client
    ||20||
    end group
||20||

@enduml