@startuml
skinparam sequenceMessageAlign center

participant Client as client
participant "Move Money" as mm
participant "CES Proxy" as proxy

||20||
    group Get Brokerages
    ||20||
        client -> mm: GET: /externaltransfer/brokerages
        activate client
        activate mm
        mm -> proxy: GET: /services/externaltransfer/brokerages
        activate proxy
        proxy -> mm: Authorized Brokerage List Response
        deactivate proxy
        mm -> mm: Map Authorized Brokerage List Response to BFF Brokerage List Response
        mm -> client: BFF Brokerage List Response
        deactivate mm
        deactivate client
    ||20||
    end group
||20||

@enduml