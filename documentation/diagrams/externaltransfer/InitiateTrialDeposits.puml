@startuml
skinparam sequenceMessageAlign center

participant Client as client
participant "Move Money" as mm
participant "CES Proxy" as proxy

    group Initiate Trial Deposits (Risk Token Success)
       ||20||
        client -> mm: POST: /externaltransfer/initiateTrialDeposits (valid Risk Token)
        activate client
        activate mm
        mm -> mm: Risk Token Verified
        mm -> mm: Map request to CES Add External Account Request
        mm -> mm: tokenize CES Add External Account Request
        mm -> proxy: POST: /tokenized/services/externaltransfer/addExternalAccountByTrialDeposit
        activate proxy
        proxy -> mm: CES Add External Account Response
        deactivate proxy
        mm -> mm: Map to BFF Add Account Response
        mm -> client: BFF Add Account Response
        deactivate mm
        deactivate client
    ||20||
    end group
||20||
    group Initiate Trial Deposits (Risk Token Failure)
        ||20||
        client -> mm: POST: /externaltransfer/initiateTrialDeposits (invalid Risk Token)
        activate client
        activate mm
        mm -x mm: Risk Token Fails to Verify
        mm -> client: Risk Exception
        deactivate mm
        deactivate client
        ||20||
    end group
||20||
    group Initiate Trial Deposits (Risk Token Success) && FeignClientException.status == 404 && CESResponse.statusCode == FIID_SEED_DATA_ERROR
        ||20||
        client -> mm: POST: /externaltransfer/initiateTrialDeposits (valid Risk Token)
        activate client
        activate mm
        mm -> mm: Risk Token Verified
        mm -> mm: Map request to CES Add External Account Request
        mm -> mm: tokenize CES Add External Account Request
        mm -> proxy: POST: /tokenized/services/externaltransfer/addExternalAccountByTrialDeposit
        activate proxy
        proxy -> mm: HTTP 404 FeignClientException && CESRepsonse.statusCode == FIID_NOT_FOUND
        deactivate proxy

        group if Brokerage Name is not null
            mm -> proxy: GET: /services/externaltransfer/fiBrokerageRoutingInfo
            activate proxy
            proxy -> mm: CES Financial Institution Info Response
            deactivate proxy
            mm -> mm: Do nothing with response, just seeds data in CES
        end group

        group else
            mm -> proxy: GET: /services/externaltransfer/fiInfo
            activate proxy
            proxy -> mm: CES Financial Institution Info Response
            deactivate proxy
            mm -> mm: Do nothing with response, just seeds data in CES
        end group

        mm -> proxy: POST: /tokenized/services/externaltransfer/addExternalAccountByTrialDeposit
                activate proxy
                proxy -> mm: CES Add External Account Response
                deactivate proxy
                mm -> mm: Map to BFF Add Account Response
                mm -> client: BFF Add Account Response
                deactivate mm
                deactivate client
        ||20||
    end group
||20||
    group Initiate Trial Deposits (Risk Token Success) && FeignClientException.status == 404 && CESResponse.statusCode != FIID_SEED_DATA_ERROR
        ||20||
        client -> mm: POST: /externaltransfer/initiateTrialDeposits (valid Risk Token)
        activate client
        activate mm
        mm -> mm: Risk Token Verified
        mm -> mm: Map request to CES Add External Account Request
        mm -> mm: tokenize CES Add External Account Request
        mm -> proxy: POST: /tokenized/services/externaltransfer/addExternalAccountByTrialDeposit
        activate proxy
        proxy -> mm: HTTP non 404 FeignClientException
        deactivate proxy
        mm -> mm: Map FeignClientException to General Exception Response
        mm -> client: General Exception Response
        deactivate mm
        deactivate client
        ||20||
    end group
||20||
    group Initiate Trial Deposits (Risk Token Success) && FeignClientException.status != 404
        ||20||
        client -> mm: POST: /externaltransfer/initiateTrialDeposits (valid Risk Token)
        activate client
        activate mm
        mm -> mm: Risk Token Verified
        mm -> mm: Map request to CES Add External Account Request
        mm -> mm: tokenize CES Add External Account Request
        mm -> proxy: POST: /tokenized/services/externaltransfer/addExternalAccountByTrialDeposit
        activate proxy
        proxy -> mm: HTTP non 404 FeignClientException
        deactivate proxy
        mm -> mm: Map FeignClientException to General Exception Response
        mm -> client: General Exception Response
        deactivate mm
        deactivate client
        ||20||
    end group
@enduml