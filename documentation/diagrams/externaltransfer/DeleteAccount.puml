@startuml
skinparam sequenceMessageAlign center

participant Client as client
participant "Move Money" as mm
participant "CES Proxy" as proxy

||20||
    group Delete Account
    ||20||
        client -> mm: DELETE: /externaltransfer/deleteAccount
        activate client
        activate mm
        mm -> proxy: DELETE: /services/externaltransfer/deleteExternalAccount
        activate proxy
        proxy -> mm: CES Base Response
        deactivate proxy
        mm -> mm: Map CES Base Response to BFF Response
        mm -> client: BFF Response
        deactivate mm
        deactivate client
    ||20||
    end group
||20||

@enduml