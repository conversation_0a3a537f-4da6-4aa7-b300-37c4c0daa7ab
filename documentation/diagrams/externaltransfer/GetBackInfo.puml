@startuml
skinparam sequenceMessageAlign center

participant Client as client
participant "Move Money" as mm
participant "CES Proxy" as proxy

||20||
    group Get Bank Info - Only Routing Number Provided (Valid)
    ||20||
        client -> mm: GET: /externaltransfer/bankInfo?routingNumber=123
        activate client
        activate mm
        mm -> mm: Routing number is provided and properly formatted, and Brokerage Name is null
        mm -> proxy: GET: /services/externaltransfer/fiInfo
        activate proxy
        proxy -> mm: CES Financial Institution Info Response
        deactivate proxy
        mm -> mm: Map CES Financial Institution Info Response to BFF Financial Institution Info
        mm -> client: BFF Financial Institution Info
        deactivate mm
        deactivate client
    ||20||
    end group
||20||

||20||
    group Get Bank Info - Only Brokerage Name Provided (Valid)
    ||20||
        client -> mm: GET: /externaltransfer/bankInfo?brokerageName=name
        activate client
        activate mm
        mm -> mm: Brokerage Name is provided, and Routing Number null
        mm -> proxy: GET: /services/externaltransfer/fiBrokerageRoutingInfo
        activate proxy
        proxy -> mm: CES Financial Institution Info Response
        deactivate proxy
        mm -> mm: Map CES Financial Institution Info Response to BFF Financial Institution Info
        mm -> client: BFF Financial Institution Info
        deactivate mm
        deactivate client
    ||20||
    end group
||20||

||20||
    group Get Bank Info - Routing Number Provided With Improper Format (Invalid)
    ||20||
        client -> mm: GET: /externaltransfer/bankInfo?routingNumber=invalidFormat
        activate client
        activate mm
        mm -x mm: Routing Number is improperly formatted
        mm -> mm: Map to BFF Financial Institution Info with Error info
        mm -> client: BFF Financial Institution Info with Error info
        deactivate mm
        deactivate client
    ||20||
    end group
||20||

||20||
    group Get Bank Info - Neither Brokerage Name, Nor Routing Number Provided (Invalid)
    ||20||
        client -> mm: GET: /externaltransfer/bankInfo
        activate client
        activate mm
        mm -x mm: Brokerage Name is null and Routing Number is null
        mm -> mm: Map to BFF Financial Institution Info with Error info
        mm -> client: BFF Financial Institution Info with Error info
        deactivate mm
        deactivate client
    ||20||
    end group
||20||

||20||
    group Get Bank Info - Both Routing Number and Brokerage Name Provided (Invalid)
    ||20||
        client -> mm: GET: /externaltransfer/bankInfo?routingNumber=123&brokerageName=name
        activate client
        activate mm
        mm -x mm: Brokerage Name is not null and Routing Number is not null
        mm -> mm: Map to BFF Financial Institution Info with Error info
        mm -> client: BFF Financial Institution Info with Error info
        deactivate mm
        deactivate client
    ||20||
    end group
||20||

@enduml