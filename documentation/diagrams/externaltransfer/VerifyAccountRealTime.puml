@startuml
skinparam sequenceMessageAlign center

participant Client as client
participant "Move Money" as mm
participant "CES Proxy" as proxy

    group Verify Trial Deposits
    ||20||
        client -> mm: POST: /externaltransfer/verifyTrialDeposits
        activate client
        activate mm
        mm -> mm: Map request to CES Verify Trial Deposit Request
        mm -> proxy: POST: /services/externaltransfer/verifyTrialDepositAmounts
        activate proxy
        proxy -> mm: CES Response
        deactivate proxy
        mm -> mm: Map to Simple BFF Response
        mm -> client: Simple BFF Response
        deactivate mm
        deactivate client
    ||20||
    end group
@enduml