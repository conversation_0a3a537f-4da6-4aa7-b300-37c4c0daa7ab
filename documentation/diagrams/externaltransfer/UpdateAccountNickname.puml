@startuml
skinparam sequenceMessageAlign center

participant Client as client
participant "Move Money" as mm
participant "CES Proxy" as proxy

||20||
    group Update Account Nickname (Valid)
    ||20||
        client -> mm: PUT: /externaltransfer/updateAccountNickname
        activate client
        activate mm
        mm -> mm: Validate Nickname and Account ID values provided
        mm -> mm: Map request to CES Update Ext Account NickName Request
        mm -> proxy: POST: /services/externaltransfer/updateNickName
        activate proxy
        proxy -> mm: CES Base Response
        deactivate proxy
        mm -> mm: Map to BFF Simple Response
        mm -> client: BFF Simple Response
        deactivate mm
        deactivate client
    ||20||
    end group
||20||

||20||
    group Add Account (Invalid)
    ||20||
        client -> mm: PUT: /externaltransfer/updateAccountNickname
        activate client
        activate mm
        mm -x mm: Validate Nickname and Account ID values provided
        mm -> mm: Map to BFF Simple Response with error info
        mm -> client: BFF Simple Response with error info
        deactivate mm
        deactivate client
    ||20||
    end group
||20||

@enduml