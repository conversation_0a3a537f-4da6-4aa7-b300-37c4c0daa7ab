@startuml
skinparam sequenceMessageAlign center

participant Client as client
participant "Move Money" as mm
participant "CES Proxy" as proxy

group Verify Account Real Time (Risk Token Success)
    ||20||
    client -> mm: POST: /externaltransfer/verifyAccountRealTime (valid Risk Token)
    activate client
    activate mm
    mm -> mm: Risk Token Verified
    mm -> mm: Map request to CES Add External Account Real Time Request
    mm -> proxy: POST: /tokenized/services/externaltransfer/addExternalAccountByRealTime
    activate proxy
    proxy -> mm: CES Add External Account Real Time Response
    deactivate proxy
    mm -> mm: Map to BFF Real Time Verification Response
    mm -> client: BFF Real Time Verification Response
    deactivate mm
    deactivate client
    ||20||
end group
||20||
group Verify Account Real Time (Risk Token Failure)
    ||20||
    client -> mm: POST: /externaltransfer/verifyAccountRealTime (invalid Risk Token)
    activate client
    activate mm
    mm -x mm: Risk Token Fails to Verify
    mm -> client: Risk Exception
    deactivate mm
    deactivate client
    ||20||
end group


@enduml