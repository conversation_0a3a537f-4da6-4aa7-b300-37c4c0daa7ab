@startuml
skinparam sequenceMessageAlign center

participant Client as client
participant "Move Money API" as api
participant "CES Proxy" as ces

group Get Account Details - Success
||20||
    client -> api: HTTP GET: /account/detail?id={id}
    activate client
    activate api
    api -> ces: HTTP GET: /services/account/detail
    activate ces
    ces -> api: CesBaseResponse
    deactivate ces
    api -> api: Map to BffResponse
    api -> client: BffResponse
    deactivate api
    deactivate client
||20||
end group

||20||

group Get Account Details - CES Exception
||20||
    client -> api: HTTP GET: /account/detail?id={id}
    activate client
    activate api
    api -> ces: HTTP GET: /services/account/detail
    activate ces
    ces -> api: CesBaseResponse w/ error
    deactivate ces
    api -> api: Map to BffResponse w/ error
    api -> client: BffResponse w/ error
    deactivate api
    deactivate client
||20||
end group

@enduml