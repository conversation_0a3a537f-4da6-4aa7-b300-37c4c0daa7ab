@startuml
skinparam sequenceMessageAlign center

participant Client as client
participant "Move Money API" as api
participant "CES Proxy" as ces

group Add Payee - Success
||20||
    client -> api: HTTP POST: /addpayee
    activate client
    activate api
    api -> api: Risk Token Verified
    api -> api: Validate Payee
    api -> ces: POST: /tokenized/services/billpay/addPayee
    activate ces
    ces -> api: CesBaseResponse
    deactivate ces
    api -> api: Map CesBaseResponse to BFFResponse
    api -> client: BFFResponse
    deactivate ces
    deactivate api
    deactivate client
||20||
end group

group Add Payee - Risk Failure
||20||
    client -> api: HTTP POST: /addpayee
    activate client
    activate api
    api -> api: Risk Token Failure
    api -> client: Risk Exception
    deactivate api
    deactivate client
||20||
end group

||20||

group Add Payee - Payee Validation Failure
||20||
    client -> api: HTTP POST: /addpayee
    activate client
    activate api
    api -> api: Risk Token Verified
    api -> api: Validate Payee - Invalid, throws BFFException
    api -> api: Map BFFException to BFFResponse w/ Error
    api -> client: BFFResponse w/ Error
    deactivate ces
    deactivate api
    deactivate client
||20||
end group

||20||

group Add Payee - CES Failure
||20||
    client -> api: HTTP POST: /addpayee
    activate client
    activate api
    api -> api: Risk Token Verified
    api -> api: Validate Payee
    api -> ces: POST: /tokenized/services/billpay/addPayee
    activate ces
    ces -> api: HTTP non 404 CesFeignException
    deactivate ces
    api -> api: Map CesFeignException to BFFResponse /w Error
    api -> client: BFFResponse /w Error
    deactivate ces
    deactivate api
    deactivate client
||20||
end group

||20||
@enduml