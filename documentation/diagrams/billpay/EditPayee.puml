@startuml
skinparam sequenceMessageAlign center

participant Client as client
participant "Move Money API" as api
participant "CES Proxy" as ces

group Edit Payee - Success
||20||
    client -> api: HTTP PUT: /editpayee
    activate client
    activate api
    api -> api: Risk Token Verified
    api -> ces: PUT: /tokenized/services/billpay/updatePayee
    activate ces
    ces -> api: CesBaseResponse
    deactivate ces
    api -> api: Map CesBaseResponse to BFFResponse
    api -> client: BFFResponse
    deactivate ces
    deactivate api
    deactivate client
||20||
end group

group Edit Payee - Risk Failure
||20||
    client -> api: HTTP PUT: /editpayee
    activate client
    activate api
    api -> api: Risk Token Failure
    api -> client: Risk Exception
    deactivate api
    deactivate client
||20||
end group

||20||

group Edit Payee - CES Failure
||20||
    client -> api: HTTP PUT: /editpayee
    activate client
    activate api
    api -> api: Risk Token Verified
    api -> api: Validate Payee
    api -> ces: PUT: /tokenized/services/billpay/updatePayee
    activate ces
    ces -> api: HTTP non 404 CesFeignException
    deactivate ces
    api -> api: Map CesFeignException to BFFResponse /w Error
    api -> client: BFFResponse /w Error
    deactivate ces
    deactivate api
    deactivate client
||20||
end group

||20||
@enduml