@startuml
skinparam sequenceMessageAlign center

participant Client as client
participant "Move Money API" as api
participant "CES Proxy" as ces

group Get Transfer Limits- Success
||20||
    client -> api: HTTP GET: /activity/transferlimits
    activate client
    activate api
    api -> ces: GET: /services/externaltransfer/limits
    activate ces
    ces -> api: CesBaseResponse
    deactivate ces
    api -> api: Map CesBaseResponse to BFFResponse
    api -> client: BFFResponse
    deactivate ces
    deactivate api
    deactivate client
||20||
end group

||20||

group Get Transfer Limits - CES Failure
||20||
    client -> api: HTTP GET: /activity/transferlimits
    activate client
    activate api
        api -> ces: GET: /services/externaltransfer/limits
        activate ces
        ces -> api: CesBaseResponse w/ error
        deactivate ces
        api -> api: Map CesBaseResponse to BFFResponse
    api -> client: BFFResponse w/ error
    deactivate ces
    deactivate api
    deactivate client
||20||
end group

||20||
@enduml