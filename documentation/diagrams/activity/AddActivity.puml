@startuml
skinparam sequenceMessageAlign center

participant Client as client
participant "Move Money API" as api
participant "Transfer and Pay Orc" as tpo
participant "CES Proxy" as ces

group Add Activity - Success
||20||
    client -> api: HTTP POST: /activity
    activate client
    activate api
    api -> api: Risk Token Verified
    api -> api: Validate Due Date
    api -> api: Call FeatureFlag Service
    group if TPO Feature Flag Enabled
        api -> tpo: POST: /activity
        activate tpo
        tpo -> api: TPOResponse
        deactivate tpo
        api -> api: Map TPOResponse to BFFResponse
    end group
    group else
        api -> ces: POST: /services/transferandpay/activity
        activate ces
        ces -> api: CesBaseResponse
        deactivate ces
        api -> api: Map CesBaseResponse to BFFResponse
    end group
    api -> client: BFFResponse
    deactivate ces
    deactivate api
    deactivate client
||20||
end group

||20||

group Add Activity - Risk Token Failure
||20||
    client -> api: HTTP POST: /activity
    activate client
    activate api
    api -> api: Risk Token Fails to Verify
    api -> client: Risk Exception
    deactivate ces
    deactivate api
    deactivate client
||20||
end group

||20||


group Add Activity - Bad Due Date
||20||
    client -> api: HTTP POST: /activity
    activate client
    activate api
    api -> api: Risk Token Verified
    api -> api: Validate Due Date - returns InvalidDueDateException
    api -> api: Map to BffResponse /w error
    api -> client: BffResponse /w error
    deactivate api
    deactivate client
||20||
end group

||20||

group Add Activity - FeatureFlag Service Failure
||20||
    client -> api: HTTP POST: /activity
    activate client
    activate api
    api -> api: Risk Token Verified
    api -> api: Validate Due Date
    api -> api: Call FeatureFlag Service returns BffResponse /w error
    api -> client: BffResponse /w error
    deactivate api
    deactivate client
||20||
end group

||20||

group Add Activity - CES Failure
||20||
    client -> api: HTTP POST: /activity
    activate client
    activate api
    api -> api: Risk Token Verified
    api -> api: Validate Due Date
    api -> api: Call FeatureFlag Service - TPO Not Enabled
        api -> ces: POST: /services/transferandpay/activity
        activate ces
        ces -> api: CesBaseResponse w/ error
        deactivate ces
        api -> api: Map CesBaseResponse to BFFResponse
    api -> client: BFFResponse w/ error
    deactivate ces
    deactivate api
    deactivate client
||20||
end group

||20||

group Add Activity - TPO Error
||20||
    client -> api: HTTP POST: /activity
    activate client
    activate api
    api -> api: Risk Token Verified
    api -> api: Validate Due Date
    api -> api: Call FeatureFlag Service - TPO Enabled
    api -> tpo: POST: /activity
    activate tpo
    tpo -> api: TPOResponse w/ error
    deactivate tpo
    api -> api: Map TPOResponse to BFFResponse
    api -> client: BFFResponse w/ error
    deactivate tpo
    deactivate api
    deactivate client
||20||
end group

||20||
@enduml