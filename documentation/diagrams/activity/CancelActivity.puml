@startuml
skinparam sequenceMessageAlign center

participant Client as client
participant "Move Money API" as api
participant "Transfer and Pay Orc" as tpo
participant "CES Proxy" as ces

group Cancel Activity - Success
||20||
    client -> api: HTTP DELETE: /activity/{activityId}
    activate client
    activate api
    api -> api: Call FeatureFlag Service
    group if TPO Feature Flag Enabled
        api -> tpo: DELETE: /activity?activityId={activityId}
        activate tpo
        tpo -> api: TPOResponse
        deactivate tpo
        api -> api: Map TPOResponse to BFFResponse
    end group
    group else
        api -> ces: DELETE: /services/transferandpay/activity?activityId={activityId}
        activate ces
        ces -> api: CesBaseResponse
        deactivate ces
        api -> api: Map CesBaseResponse to BFFResponse
    end group
    api -> client: BFFResponse
    deactivate ces
    deactivate api
    deactivate client
||20||
end group

||20||

group Cancel Activity - FeatureFlag Service Failure
||20||
    client -> api: HTTP DELETE: /activity
    activate client
    activate api
    api -> api: Call FeatureFlag Service throws InternalServerError
    api -> client: InternalServerError
    deactivate api
    deactivate client
||20||
end group

||20||

group Cancel Activity - CES Failure
||20||
    client -> api: HTTP DELETE: /activity
    activate client
    activate api
    api -> api: Call FeatureFlag Service - TPO Not Enabled
        api -> ces: DELETE: /services/transferandpay/activity
        activate ces
        ces -> api: CesBaseResponse w/ error
        deactivate ces
        api -> api: Map CesBaseResponse to BFFResponse
    api -> client: BFFResponse w/ error
    deactivate ces
    deactivate api
    deactivate client
||20||
end group

||20||

group Cancel Activity - TPO Error
||20||
    client -> api: HTTP DELETE: /activity
    activate client
    activate api
    api -> api: Call FeatureFlag Service - TPO Enabled
    api -> tpo: DELETE: /activity
    activate tpo
    tpo -> api: TPOResponse w/ error
    deactivate tpo
    api -> api: Map TPOResponse to BFFResponse
    api -> client: BFFResponse w/ error
    deactivate tpo
    deactivate api
    deactivate client
||20||
end group

||20||
@enduml