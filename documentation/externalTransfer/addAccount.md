# Add External Account

Move Money BFF interacts with CES Cash Edge Account Service through CES Proxy. 

We support two paths through the add external transfer account flow. The dominant path is for banking products (checking,
savings, credit card) and a flow for a select number of brokerages. The user determines the type of account that will be
added before entering the add account flow.

CES manages the interaction with FiServ to determine how to add an account. Most of the error codes returned are directly 
from FiServe and are of little use to the end user or mobile client.

## Basic Flow

Once the user has determined the type of account they want to add they can enter the details. Checking, savings, money 
market checking, and money market savings all require a routing number and account number. Brokerage accounts may require 
a routing nubmer, account number, checking routing number and checking account number.

1. User provides routing number(s) and account number(s). 
2. Validate the routing number by getting the bank information.
3. Attempt to verify bank account information using:
* Instant verification.
* Real time verification.
* Trial deposit.
4. If by trial deposit, user will have to await confirmation deposits and send ammounts in a separate request. 

## Get a List of Brokerages

```shell
GET /move-money-bff/externaltransfers/brokerages
```

Returns a list of bokerages describing their name, bank routing number, credit card routing number and information about 
prompting for brokerage and checking account routing numbers.

```json
        {
            "name": "Ameriprise Financial Brokerage",
            "routingNumber": "*********",
            "creditRoutingNumber": "*********",
            "brokerageAccount": {
                "optional": false,
                "toolTip": "Select the checking account number from the check. The checking account number is the right most 10 digits from the check MICR line.",
                "message": null
            },
            "checkingAccount": {
                "optional": false,
                "toolTip": null,
                "message": null
            }
        }
```

## Get Bank Information

Bank information must be called in order to initialize the CES/FiServe data structures. 
```shell
GET /move-money-bff/externaltransfer/bankInfo?routingNumber=*********
```

Returns a payload containing verification methods supported by the institution.

```json
{
    "status": "SUCCESS",
    "statusCode": null,
    "statusReason": null,
    "retrievalErrors": [],
    "institutionName": "CashEdge Bank (Test 2FA)",
    "trialDepositVerification": true,
    "realTimeVerification": true,
    "finInsLoginInfoList": [
        {
            "paramId": "10796",
            "paramCaption": "User ID",
            "paramMaxLength": 30,
            "paramType": null
        },
        {
            "paramId": "10797",
            "paramCaption": "Password",
            "paramMaxLength": 30,
            "paramType": null
        }
    ],
    "instantVerificationEnabledAcctTypes": null,
    "hostInstitution": false
}
```

In this case, trial deposit and real time (user name and password) are supported. 


## Instant Verification

```shell
POST /move-money-bff/externaltransfer/addAccount
```
Sample payload:

```json
{
    "accountNickName": "Postman Account",
    "accountNumber": "**********",
    "accountTypeCode": "CHECKING",
    "routingNumber": "*********"
}
```

Sample response:

```json
{
    "status": "SUCCESS",
    "statusCode": null,
    "statusReason": null,
    "retrievalErrors": [],
    "externalAccountVerificationStatus": null
}
```

## Real Time Verification

```shell
POST /move-money-bff/externaltransfer/verifyAccountRealTime
```

Sample payload:
```json
{
    "accountId": "4028DC39-F304-FB35-BD94-67D06F10AB5E",
    "accountParameters":  [{"name": "alice", "value": "bob"}]
}
```

Frequently leads to a request for more information from the user.

```json
{
    "status": "SUCCESS",
    "statusCode": null,
    "statusReason": null,
    "retrievalErrors": [],
    "verificationStatus": {
        "statusCode": "5092",
        "statusDesc": "Two FA Online Account Verification",
        "accountNumber": "X0000",
        "verificationMode": "online",
        "verificationStatus": "Requires Approval",
        "failureReasonCode": "303",
        "failureReasonDesc": "303"
    },
    "parameters": [
        {
            "promptId": "answer",
            "promptText": "What is your favorite color?"
        }
    ]
}
```

## Trial Deposit Request

```shell
POST /move-money-bff/externaltransfer/initiateTrialDeposits
```

Sample payload:
```json
{
    "accountId": "4028DC39-F304-FB35-BD94-67D06F10AB5E"
}
```

Returns the response:
```json
{
    "status": "SUCCESS",
    "statusCode": null,
    "statusReason": null,
    "retrievalErrors": [],
    "externalAccountVerificationStatus": {
        "statusCode": "0",
        "statusDesc": "Success",
        "accountNumber": "X0001",
        "account": null,
        "verificationMode": "trial-deposit",
        "verificationStatus": "Requires Approval"
    }
}
```
## Trial Deposit Verify


## CES Dependencies

Consumer CES - *********************:fitb-digital-monoliths/consumer-ces.git

Service definition at:
olb/ces/src/com/fifththird/cb/ces/externaltransfers/service/cashedge/account/CesCashEdgeManagementService.java

Account management implementation at: 
olb/ces/src/com/fifththird/cb/ces/externaltransfers/service/cashedge/account/CashEdgeAccountServiceAdapterImpl.java

