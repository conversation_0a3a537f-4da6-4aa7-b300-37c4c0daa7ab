# Get Recent and Upcoming Activity

## Request
```shell
GET /transferandpay/activity?dateRange=-30_396
```
## Response
```json
{
  "status": "SUCCESS",
  "retrievalErrors": [],
  "activities": [
    {
      "id": "********",
      "amount": 0.01,
      "dueDate": "2021-06-15",
      "displayId": "********",
      "fromAccountId": "3E4ADE0D-40EB-73FE-8640-FDBF51EBA5F2",
      "fromAccountNumber": "X3643",
      "fromAccountName": "MASTERCARD ACCOUNT",
      "toAccountId": "C5BC40EC-58E7-001C-AB21-79ACBE6D0D5F",
      "toAccountNumber": "X4492",
      "toAccountName": "5/3 ESSENTIAL CHECKING",
      "expressDelivery": false,
      "status": "3",
      "displayStatus": "Processed",
      "createTimestamp": "2021-06-15T20:12:32.000Z",
      "editable": false,
      "cancelable": false,
      "type": "INTERNAL_TRANSFER"
    },
    {
      "id": "********",
      "recurringId": "35824",
      "amount": 0.02,
      "dueDate": "2021-06-15",
      "displayId": "********",
      "fromAccountId": "3E4ADE0D-40EB-73FE-8640-FDBF51EBA5F2",
      "fromAccountNumber": "X3643",
      "fromAccountName": "MASTERCARD ACCOUNT",
      "toAccountId": "C5BC40EC-58E7-001C-AB21-79ACBE6D0D5F",
      "toAccountNumber": "X4492",
      "toAccountName": "5/3 ESSENTIAL CHECKING",
      "expressDelivery": false,
      "status": "3",
      "displayStatus": "Processed",
      "createTimestamp": "2021-06-15T20:13:29.000Z",
      "editable": false,
      "cancelable": false,
      "type": "INTERNAL_TRANSFER"
    },
    {
      "id": "********",
      "recurringId": "35825",
      "amount": 0.02,
      "dueDate": "2021-06-15",
      "displayId": "********",
      "fromAccountId": "3E4ADE0D-40EB-73FE-8640-FDBF51EBA5F2",
      "fromAccountNumber": "X3643",
      "fromAccountName": "MASTERCARD ACCOUNT",
      "toAccountId": "C5BC40EC-58E7-001C-AB21-79ACBE6D0D5F",
      "toAccountNumber": "X4492",
      "toAccountName": "5/3 ESSENTIAL CHECKING",
      "expressDelivery": false,
      "status": "3",
      "displayStatus": "Processed",
      "createTimestamp": "2021-06-15T20:13:55.000Z",
      "editable": false,
      "cancelable": false,
      "type": "INTERNAL_TRANSFER"
    },
    {
      "id": "********",
      "recurringId": "35826",
      "amount": 0.03,
      "dueDate": "2021-06-16",
      "displayId": "********",
      "fromAccountId": "3E4ADE0D-40EB-73FE-8640-FDBF51EBA5F2",
      "fromAccountNumber": "X3643",
      "fromAccountName": "MASTERCARD ACCOUNT",
      "toAccountId": "C5BC40EC-58E7-001C-AB21-79ACBE6D0D5F",
      "toAccountNumber": "X4492",
      "toAccountName": "5/3 ESSENTIAL CHECKING",
      "expressDelivery": false,
      "frequency": "ONCE_IN_2_WEEKS",
      "numberOfActivities": 3,
      "numberOfRemainingActivities": 2,
      "status": "5",
      "displayStatus": "In Process",
      "createTimestamp": "2021-06-15T20:16:32.000Z",
      "editable": false,
      "cancelable": false,
      "type": "INTERNAL_TRANSFER"
    },
    {
      "id": "********",
      "recurringId": "35824",
      "amount": 0.02,
      "dueDate": "2021-06-22",
      "displayId": "********",
      "fromAccountId": "3E4ADE0D-40EB-73FE-8640-FDBF51EBA5F2",
      "fromAccountNumber": "X3643",
      "fromAccountName": "MASTERCARD ACCOUNT",
      "toAccountId": "C5BC40EC-58E7-001C-AB21-79ACBE6D0D5F",
      "toAccountNumber": "X4492",
      "toAccountName": "5/3 ESSENTIAL CHECKING",
      "expressDelivery": false,
      "frequency": "ONCE_A_WEEK",
      "numberOfActivities": 0,
      "status": "5",
      "displayStatus": "In Process",
      "createTimestamp": "2021-06-15T20:35:00.000Z",
      "editable": false,
      "cancelable": false,
      "type": "INTERNAL_TRANSFER"
    },
    {
      "id": "********",
      "recurringId": "35825",
      "amount": 0.02,
      "dueDate": "2021-06-22",
      "displayId": "********",
      "fromAccountId": "3E4ADE0D-40EB-73FE-8640-FDBF51EBA5F2",
      "fromAccountNumber": "X3643",
      "fromAccountName": "MASTERCARD ACCOUNT",
      "toAccountId": "C5BC40EC-58E7-001C-AB21-79ACBE6D0D5F",
      "toAccountNumber": "X4492",
      "toAccountName": "5/3 ESSENTIAL CHECKING",
      "expressDelivery": false,
      "frequency": "ONCE_A_WEEK",
      "numberOfActivities": 0,
      "status": "5",
      "displayStatus": "In Process",
      "createTimestamp": "2021-06-15T20:35:00.000Z",
      "editable": false,
      "cancelable": false,
      "type": "INTERNAL_TRANSFER"
    },
    {
      "id": "********",
      "amount": 500,
      "dueDate": "2021-06-22",
      "displayId": "********",
      "fromAccountId": "3E4ADE0D-40EB-73FE-8640-FDBF51EBA5F2",
      "fromAccountNumber": "X3643",
      "fromAccountName": "MASTERCARD ACCOUNT",
      "toAccountId": "C5BC40EC-58E7-001C-AB21-79ACBE6D0D5F",
      "toAccountNumber": "X4492",
      "toAccountName": "5/3 ESSENTIAL CHECKING",
      "expressDelivery": false,
      "status": "3",
      "displayStatus": "Processed",
      "createTimestamp": "2021-06-22T20:30:46.000Z",
      "editable": false,
      "cancelable": false,
      "type": "INTERNAL_TRANSFER"
    },
    {
      "id": "********",
      "amount": 1,
      "dueDate": "2021-06-23",
      "displayId": "********",
      "fromAccountId": "3E4ADE0D-40EB-73FE-8640-FDBF51EBA5F2",
      "fromAccountNumber": "X3643",
      "fromAccountName": "MASTERCARD ACCOUNT",
      "toAccountId": "C5BC40EC-58E7-001C-AB21-79ACBE6D0D5F",
      "toAccountNumber": "X4492",
      "toAccountName": "5/3 ESSENTIAL CHECKING",
      "expressDelivery": false,
      "status": "3",
      "displayStatus": "Processed",
      "createTimestamp": "2021-06-23T20:41:55.000Z",
      "editable": false,
      "cancelable": false,
      "type": "INTERNAL_TRANSFER"
    },
    {
      "id": "DA54BF65-AA72-588F-420E-C0171F610A98",
      "amount": 0.11,
      "dueDate": "2021-06-29",
      "displayId": "4BGBVZN4",
      "fromAccountId": "C5BC40EC-58E7-001C-AB21-79ACBE6D0D5F",
      "fromAccountNumber": "X4492",
      "fromAccountName": "5/3 ESSENTIAL CHECKING",
      "toAccountId": "A6D02B09-5255-5656-C36D-97FDBFB7C699",
      "toAccountName": "Bob's Daycare",
      "expressDelivery": false,
      "status": "CANCELED",
      "displayStatus": "CANCELED",
      "editable": false,
      "cancelable": false,
      "type": "BILLPAY",
      "checkNumber": "9000"
    },
    {
      "id": "********",
      "recurringId": "35825",
      "amount": 0.02,
      "dueDate": "2021-06-29",
      "displayId": "********",
      "fromAccountId": "3E4ADE0D-40EB-73FE-8640-FDBF51EBA5F2",
      "fromAccountNumber": "X3643",
      "fromAccountName": "MASTERCARD ACCOUNT",
      "toAccountId": "C5BC40EC-58E7-001C-AB21-79ACBE6D0D5F",
      "toAccountNumber": "X4492",
      "toAccountName": "5/3 ESSENTIAL CHECKING",
      "expressDelivery": false,
      "status": "4",
      "displayStatus": "Scheduled",
      "createTimestamp": "2021-06-22T04:05:22.000Z",
      "editable": true,
      "cancelable": true,
      "type": "INTERNAL_TRANSFER"
    },
    {
      "id": "********",
      "recurringId": "35824",
      "amount": 0.02,
      "dueDate": "2021-06-29",
      "displayId": "********",
      "fromAccountId": "3E4ADE0D-40EB-73FE-8640-FDBF51EBA5F2",
      "fromAccountNumber": "X3643",
      "fromAccountName": "MASTERCARD ACCOUNT",
      "toAccountId": "C5BC40EC-58E7-001C-AB21-79ACBE6D0D5F",
      "toAccountNumber": "X4492",
      "toAccountName": "5/3 ESSENTIAL CHECKING",
      "expressDelivery": false,
      "status": "4",
      "displayStatus": "Scheduled",
      "createTimestamp": "2021-06-22T04:05:22.000Z",
      "editable": true,
      "cancelable": true,
      "type": "INTERNAL_TRANSFER"
    },
    {
      "id": "********",
      "recurringId": "35826",
      "amount": 0.03,
      "dueDate": "2021-06-30",
      "displayId": "********",
      "fromAccountId": "3E4ADE0D-40EB-73FE-8640-FDBF51EBA5F2",
      "fromAccountNumber": "X3643",
      "fromAccountName": "MASTERCARD ACCOUNT",
      "toAccountId": "C5BC40EC-58E7-001C-AB21-79ACBE6D0D5F",
      "toAccountNumber": "X4492",
      "toAccountName": "5/3 ESSENTIAL CHECKING",
      "expressDelivery": false,
      "status": "4",
      "displayStatus": "Scheduled",
      "createTimestamp": "2021-06-16T04:05:23.000Z",
      "editable": true,
      "cancelable": true,
      "type": "INTERNAL_TRANSFER"
    },
    {
      "id": "********",
      "recurringId": "35827",
      "amount": 0.01,
      "dueDate": "2021-08-11",
      "displayId": "********",
      "fromAccountId": "C5BC40EC-58E7-001C-AB21-79ACBE6D0D5F",
      "fromAccountNumber": "X4492",
      "fromAccountName": "5/3 ESSENTIAL CHECKING",
      "toAccountId": "3E4ADE0D-40EB-73FE-8640-FDBF51EBA5F2",
      "toAccountNumber": "X3643",
      "toAccountName": "MASTERCARD ACCOUNT",
      "expressDelivery": false,
      "frequency": "ONCE_IN_2_WEEKS",
      "numberOfActivities": 0,
      "status": "4",
      "displayStatus": "Scheduled",
      "createTimestamp": "2021-06-15T20:28:02.000Z",
      "editable": true,
      "cancelable": true,
      "type": "INTERNAL_TRANSFER"
    }
  ],
  "recurringActivities": [
    {
      "id": "********",
      "recurringId": "35826",
      "amount": 0.03,
      "dueDate": "2021-06-16",
      "displayId": "35826",
      "fromAccountId": "3E4ADE0D-40EB-73FE-8640-FDBF51EBA5F2",
      "fromAccountNumber": "X3643",
      "fromAccountName": "MASTERCARD ACCOUNT",
      "toAccountId": "C5BC40EC-58E7-001C-AB21-79ACBE6D0D5F",
      "toAccountNumber": "X4492",
      "toAccountName": "5/3 ESSENTIAL CHECKING",
      "expressDelivery": false,
      "frequency": "ONCE_IN_2_WEEKS",
      "numberOfActivities": 3,
      "numberOfRemainingActivities": 2,
      "status": "4",
      "displayStatus": "Scheduled",
      "createTimestamp": "2021-06-15T20:16:32.000Z",
      "editable": false,
      "cancelable": true,
      "type": "INTERNAL_TRANSFER"
    },
    {
      "id": "********",
      "recurringId": "35825",
      "amount": 0.02,
      "dueDate": "2021-06-22",
      "displayId": "35825",
      "fromAccountId": "3E4ADE0D-40EB-73FE-8640-FDBF51EBA5F2",
      "fromAccountNumber": "X3643",
      "fromAccountName": "MASTERCARD ACCOUNT",
      "toAccountId": "C5BC40EC-58E7-001C-AB21-79ACBE6D0D5F",
      "toAccountNumber": "X4492",
      "toAccountName": "5/3 ESSENTIAL CHECKING",
      "expressDelivery": false,
      "frequency": "ONCE_A_WEEK",
      "numberOfActivities": 0,
      "status": "4",
      "displayStatus": "Scheduled",
      "createTimestamp": "2021-06-15T20:13:55.000Z",
      "editable": false,
      "cancelable": true,
      "type": "INTERNAL_TRANSFER"
    },
    {
      "id": "********",
      "recurringId": "35824",
      "amount": 0.02,
      "dueDate": "2021-06-22",
      "displayId": "35824",
      "fromAccountId": "3E4ADE0D-40EB-73FE-8640-FDBF51EBA5F2",
      "fromAccountNumber": "X3643",
      "fromAccountName": "MASTERCARD ACCOUNT",
      "toAccountId": "C5BC40EC-58E7-001C-AB21-79ACBE6D0D5F",
      "toAccountNumber": "X4492",
      "toAccountName": "5/3 ESSENTIAL CHECKING",
      "expressDelivery": false,
      "frequency": "ONCE_A_WEEK",
      "numberOfActivities": 0,
      "status": "4",
      "displayStatus": "Scheduled",
      "createTimestamp": "2021-06-15T20:13:29.000Z",
      "editable": false,
      "cancelable": true,
      "type": "INTERNAL_TRANSFER"
    },
    {
      "id": "********",
      "recurringId": "35827",
      "amount": 0.01,
      "dueDate": "2021-08-11",
      "displayId": "35827",
      "fromAccountId": "C5BC40EC-58E7-001C-AB21-79ACBE6D0D5F",
      "fromAccountNumber": "X4492",
      "fromAccountName": "5/3 ESSENTIAL CHECKING",
      "toAccountId": "3E4ADE0D-40EB-73FE-8640-FDBF51EBA5F2",
      "toAccountNumber": "X3643",
      "toAccountName": "MASTERCARD ACCOUNT",
      "expressDelivery": false,
      "frequency": "ONCE_IN_2_WEEKS",
      "numberOfActivities": 0,
      "status": "4",
      "displayStatus": "Scheduled",
      "createTimestamp": "2021-06-15T20:28:02.000Z",
      "editable": true,
      "cancelable": true,
      "type": "INTERNAL_TRANSFER"
    }
  ],
  "shouldPromptForIncome": false
}
```