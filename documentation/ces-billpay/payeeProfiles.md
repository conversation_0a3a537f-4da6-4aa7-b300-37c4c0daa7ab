# Payee Profiles

CES does not seem to consolidate profile information from internal, external, and web payment accounts. 
```shell
GET /billpay/profile
```

```json
{
  "status": "SUCCESS",
  "payees": [
    {
      "id": "164405E1-D416-037B-444D-1CE0F4D8BA0C",
      "nickname": "Grants Lawn Service",
      "paymentCutoffTime": "14:00:00.000",
      "nearestNewPaymentDate": "2021-06-22",
      "categoryTypeId": "1001",
      "type": "PERSONAL",
      "electronicBillingEnabled": false,
      "eBillsStatus": "NOT_AVAILABLE",
      "accountNumber": "X1321",
      "name": "Grants Lawn Service",
      "cesAddress": {
        "@type": "CesAddress",
        "streetLine1": "38 FOUNTAIN SQUARE PLZ",
        "streetLine2": "FL 5167",
        "city": "CINCINNATI",
        "stateOrProvince": "OH",
        "postalCode": "45202",
        "badAddressIndicator": false,
        "country": "US"
      },
      "lastPaymentAmount": 50,
      "lastPaymentDate": "2021-03-05"
    },
    {
      "id": "BF382FBC-31C6-D4DE-A3E3-15B5E939480E",
      "nickname": "Cincy Bell",
      "paymentCutoffTime": "14:00:00.000",
      "nearestNewPaymentDate": "2021-06-17",
      "categoryTypeId": "1001",
      "type": "BUSINESS",
      "electronicBillingEnabled": true,
      "eBillsStatus": "SUPPORTED",
      "accountNumber": "X2309",
      "name": "Cincinnati Bell",
      "phoneNumber": "**********",
      "cesAddress": {
        "@type": "CesAddress",
        "streetLine1": "PO BOX 748003",
        "city": "CINCINNATI",
        "stateOrProvince": "OH",
        "postalCode": "*********",
        "badAddressIndicator": false,
        "country": "US"
      }
    }
  ],
  "fundingAccounts": [
    {
      "accountId": "0E2DE8AA-20E2-BD77-773E-A5A27D308428",
      "displayName": "5/3 ESSENTIAL CHECKING",
      "displayAccountNumber": "X3426",
      "primaryFundingAccount": true,
      "shortName": "CK1",
      "accountDisplayName": "5/3 ESSENTIAL CHECKING",
      "accountNumber": "X3426"
    }
  ]
}
```

Request a list of external accounts available as funding sources.

```shell
GET /externaltransfer/userAccounts
```

```json
{
  "status": "SUCCESS",
  "accountDataList": [
    {
      "id": "F806423B-41EA-F538-22AF-ECDDCF3B3D2C",
      "institutionName": "Fifth Third Bank",
      "accountStatus": "ACCOUNT_APPROVED",
      "accountType": "CHECKING",
      "availableBalance": "751.13",
      "isHostAccount": "1",
      "accountGroup": "Cash",
      "realTimeVerificationStatus": "NOT ATTEMPTED",
      "realTimeRemainingAttempts": 2,
      "trialDepositRemainingAttempts": 2,
      "routingNumber": "*********",
      "displayAccountNumber": "X3426"
    },
    {
      "id": "B446B8B8-63C2-EAAC-E479-EDB6C687A8CB",
      "institutionName": "Capital One",
      "accountStatus": "ACCOUNT_APPROVED",
      "accountType": "CHECKING",
      "availableBalance": "0.00",
      "accountNickName": "Cap 1 checking",
      "isHostAccount": "0",
      "accountGroup": "Cash",
      "realTimeVerificationStatus": "NOT ATTEMPTED",
      "realTimeRemainingAttempts": 2,
      "trialDepositRemainingAttempts": 2,
      "trialDepositStartDate": "2021-05-12",
      "routingNumber": "*********",
      "displayAccountNumber": "X4321"
    },
    {
      "id": "F744CCC7-C077-2000-4B19-D3F0498057DC",
      "institutionName": "Harris Bank",
      "accountStatus": "ACCOUNT_APPROVED",
      "accountType": "CHECKING",
      "availableBalance": "0.00",
      "accountNickName": "BMO Checking",
      "isHostAccount": "0",
      "accountGroup": "Cash",
      "realTimeVerificationStatus": "NOT ATTEMPTED",
      "realTimeRemainingAttempts": 2,
      "trialDepositRemainingAttempts": 2,
      "trialDepositStartDate": "2021-05-12",
      "routingNumber": "*********",
      "displayAccountNumber": "X4321"
    }
  ]
}
```

Locate accounts registered with the web payment service.

```shell
GET /webpayment/profile
```

```json
{
  "status": "SUCCESS",
  "accounts": [
    {
      "id": "8BC6DDDE-E9D7-0B52-46B1-874833036AE8",
      "accountType": "DDA",
      "accountNumber": "X4321",
      "name": "CAPITAL ONE BANK (USA), N.A. CHECKING",
      "bankName": "CAPITAL ONE BANK (USA), N.A.",
      "preferredRoutingNumber": "*********",
      "accountSetupDate": "2021-05-11T04:00:00.000Z"
    }
  ]
}
```