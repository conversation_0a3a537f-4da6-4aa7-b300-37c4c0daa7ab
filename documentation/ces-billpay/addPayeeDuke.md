# Add new payee (Duke Energy)

## Request
```shell
POST /billpay/addPayee
```
```json
{
  "cesPayee": {
    "name": "Duke Energy - Ohio",
    "cesPayeeType": "TWOFACTOR",
    "accountNumber": "********",
    "nickname": "<PERSON> Duke"
  }
}
```
## Response
CODE: 400
```json
{
  "status": "BILL_PAY_ERROR",
  "statusCode": "MER018",
  "statusReason": "Error while searching Global Payee ID Need more info: Duke Energy - Ohio ********"
}
```

Prompts me for zip code, then this.

## Request
```shell
POST /billpay/addPayee
```
```json
{
  "cesPayee": {
    "name": "Duke Energy - Ohio",
    "cesPayeeType": "THREEFACTOR",
    "accountNumber": "********",
    "nickname": "Dirty Duke",
    "cesAddress": {
      "postalCode": "45202"
    }
  }
}
```
## Response
CODE: 400
```json
{
  "status": "BILL_PAY_ERROR",
  "statusCode": "MER017",
  "statusReason": "Error while searching Global Payee ID Need more info: Duke Energy - Ohio ********"
}
```

Prompted for an address.
## Request
```shell
POST /billpay/addPayee
```
```json
{
  "cesPayee": {
    "name": "Duke Energy - Ohio",
    "cesPayeeType": "SIXFACTOR",
    "accountNumber": "********",
    "nickname": "Dirty Duke",
    "cesAddress": {
      "postalCode": "45202",
      "streetLine1": "300 E 4th Street",
      "city": "Cincinnati",
      "stateOrProvince": "OH"
    }
  }
}
```
## Response
CODE: 422
```json
{
  "status": "VALIDATION_ERROR",
  "statusCode": "ADDRESS_CHANGED",
  "cesPayee": {
    "cesAddress": {
      "@type": "CesAddress",
      "streetLine1": "300 E 4TH ST",
      "streetLine2": " ",
      "city": "CINCINNATI",
      "stateOrProvince": "OH",
      "postalCode": "45202",
      "badAddressIndicator": false
    }
  }
}
```

Confirm the address change.

## Request
```shell
POST /billpay/addPayee
```
```json
{
  "cesPayee": {
    "name": "Duke Energy - Ohio",
    "cesPayeeType": "SIXFACTOR",
    "accountNumber": "********",
    "nickname": "Dirty Duke",
    "cesAddress": {
      "postalCode": "45202",
      "streetLine1": "300 E 4TH ST",
      "city": "CINCINNATI",
      "stateOrProvince": "OH",
      "@type": "CesAddress",
      "streetLine2": " ",
      "badAddressIndicator": false
    }
  }
}
```
## Response
CODE: 200
```json
{
  "status": "SUCCESS",
  "cesPayee": {
    "id": "D9EAF05F-87EC-CE23-CE17-6176C223FD08",
    "nickname": "Dirty Duke",
    "paymentCutoffTime": "14:00:00.000",
    "nearestNewPaymentDate": "2021-06-28",
    "categoryTypeId": "1001",
    "type": "PERSONAL",
    "electronicBillingEnabled": false,
    "eBillsStatus": "NOT_AVAILABLE",
    "accountNumber": "X0666",
    "name": "Duke Energy - Ohio",
    "cesAddress": {
      "@type": "CesAddress",
      "streetLine1": "300 E 4TH ST",
      "streetLine2": " ",
      "city": "CINCINNATI",
      "stateOrProvince": "OH",
      "postalCode": "45202",
      "badAddressIndicator": false,
      "country": "US"
    }
  }
}
```

Followed by:

    * GET /transferandpay/profile
    * GET /transferandpay/groups
