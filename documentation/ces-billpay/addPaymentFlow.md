# Add Payment Flow
The process from OLB:

Request /transferandpay/profile. This provides the list of available payees.

A payee is selected, then an account to fund the payment.

Get account details from payee. For example: 
```shell
GET /account/detail?id=ECC6C7E1-A31D-AC71-31C1-8B7012BBFC81
```

Select amount and review. Confirm.

## Request
```shell

POST activity to /transferandpay/activity
```
```json
{
  "requestGuid": "bc96f1f0-4bf5-423e-8f82-a9dc1d243a15",
  "id": null,
  "recurringId": null,
  "fromAccountId": "EF1005F9-059E-3F67-0D06-907078E66E03",
  "toAccountId": "ECC6C7E1-A31D-AC71-31C1-8B7012BBFC81",
  "amount": 1,
  "dueDate": "2021-06-21",
  "frequency": "ONE_TIME",
  "numberOfTransactions": null,
  "memo": null,
  "additionalPrincipleAmount": null,
  "displayId": "3BDBMZN4"
}
```
## Response
```json
{
  "status": "SUCCESS",
  "retrievalErrors": [],
  "activities": [
    {
      "id": "********",
      "amount": 1,
      "displayId": "********",
      "fromAccountNumber": "X3426",
      "fromAccountName": "5/3 ESSENTIAL CHECKING",
      "toAccountNumber": "X8655",
      "toAccountName": "EQUITY LINE",
      "expressDelivery": false,
      "status": "3",
      "displayStatus": "Processed",
      "createTimestamp": "2021-06-21T18:23:26.739Z",
      "editable": false,
      "cancelable": false,
      "type": "INTERNAL_TRANSFER"
    }
  ],
  "shouldPromptForIncome": false
}
```