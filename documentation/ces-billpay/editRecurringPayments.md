# CES - Editing a Recurring Transfer or Payment

Once a recurring activity has been created the use may want to edit either the next occurance or the recurring aspects of
the activity. Both are supported by CES.

## Get Activity
Given the (edited) activity response below, we have two forms that we can edit. The first three payements to <PERSON>'s Daycare 
have a status of COMPLETED, they cannot be edited and the editable and cancelable fields are false.

There is one pending transaction that can be edited/canceled. Notice the flags in the response.

```shell
GET https://onlinebanking-qa.cb.info53.com/mobile/v1/services/transferandpay/activity
```

```json
{
  "status": "SUCCESS",
  "retrievalErrors": [],
  "activities": [
    {
      "id": "B2603B9B-21B4-5E8A-5F35-22CE8EB52EF8",
      "recurringId": "C911DBA1-2C9F-D8D2-1B86-733060BDBB16",
      "amount": 1.25,
      "dueDate": "2021-10-28",
      "displayId": "1B9BGZO4",
      "fromAccountId": "a1dc14ae-84d8-452d-baff-e6ee193eb83c",
      "fromAccountNumber": "X4492",
      "fromAccountName": "5/3 ESSENTIAL CHECKING",
      "toAccountId": "D62C04F5-8467-B8A6-F5C8-97BD9D0F8C5B",
      "toAccountName": "Bob's Daycare",
      "expressDelivery": false,
      "memo": "do it",
      "status": "COMPLETED",
      "displayStatus": "COMPLETED",
      "editable": false,
      "cancelable": false,
      "type": "BILLPAY",
      "checkNumber": "9010"
    },
    {
      "id": "7B649D78-297D-D65B-57F4-74715F68572B",
      "recurringId": "C911DBA1-2C9F-D8D2-1B86-733060BDBB16",
      "amount": 1.25,
      "dueDate": "2021-11-04",
      "displayId": "RB9BGZO4",
      "fromAccountId": "a1dc14ae-84d8-452d-baff-e6ee193eb83c",
      "fromAccountNumber": "X4492",
      "fromAccountName": "5/3 ESSENTIAL CHECKING",
      "toAccountId": "D62C04F5-8467-B8A6-F5C8-97BD9D0F8C5B",
      "toAccountName": "Bob's Daycare",
      "expressDelivery": false,
      "memo": "do it",
      "status": "COMPLETED",
      "displayStatus": "COMPLETED",
      "editable": false,
      "cancelable": false,
      "type": "BILLPAY",
      "checkNumber": "9011"
    },
    {
      "id": "F7191513-46FA-49D9-D585-3FF016FFCB65",
      "recurringId": "C911DBA1-2C9F-D8D2-1B86-733060BDBB16",
      "amount": 1.25,
      "dueDate": "2021-11-10",
      "displayId": "2B9BGZO4",
      "fromAccountId": "a1dc14ae-84d8-452d-baff-e6ee193eb83c",
      "fromAccountNumber": "X4492",
      "fromAccountName": "5/3 ESSENTIAL CHECKING",
      "toAccountId": "D62C04F5-8467-B8A6-F5C8-97BD9D0F8C5B",
      "toAccountName": "Bob's Daycare",
      "expressDelivery": false,
      "memo": "do it",
      "status": "COMPLETED",
      "displayStatus": "COMPLETED",
      "editable": false,
      "cancelable": false,
      "type": "BILLPAY",
      "checkNumber": "9012"
    },
    {
      "id": "8E1A2EFF-E854-6131-0814-A499551B4134",
      "recurringId": "C911DBA1-2C9F-D8D2-1B86-733060BDBB16",
      "amount": 1.25,
      "dueDate": "2021-12-09",
      "displayId": "MBXBKZO4",
      "fromAccountId": "a1dc14ae-84d8-452d-baff-e6ee193eb83c",
      "fromAccountNumber": "X4492",
      "fromAccountName": "5/3 ESSENTIAL CHECKING",
      "toAccountId": "D62C04F5-8467-B8A6-F5C8-97BD9D0F8C5B",
      "toAccountName": "Bob's Daycare",
      "expressDelivery": false,
      "memo": "do it",
      "frequency": "ONCE_A_WEEK",
      "status": "SCHEDULED",
      "displayStatus": "SCHEDULED",
      "editable": true,
      "cancelable": true,
      "type": "BILLPAY",
      "checkNumber": "9019"
    }
  ],
  "recurringActivities": [
    {
      "recurringId": "C911DBA1-2C9F-D8D2-1B86-733060BDBB16",
      "amount": 1.25,
      "dueDate": "2021-10-28",
      "fromAccountId": "a1dc14ae-84d8-452d-baff-e6ee193eb83c",
      "fromAccountNumber": "X4492",
      "fromAccountName": "5/3 ESSENTIAL CHECKING",
      "toAccountId": "D62C04F5-8467-B8A6-F5C8-97BD9D0F8C5B",
      "toAccountName": "Bob's Daycare",
      "expressDelivery": false,
      "memo": "do it",
      "frequency": "ONCE_A_WEEK",
      "status": "SCHEDULED",
      "displayStatus": "SCHEDULED",
      "editable": true,
      "cancelable": true,
      "type": "BILLPAY"
    }
  ],
  "shouldPromptForIncome": false
}
```

## Edit Scheduled Payment

The first example is updating a transaction that is scheduled for the near term.

If you want to change the amount of the upcoming payment from 1.29 to 1.30 a request is built with:
- requestGuid - unique identifier for the reqeust.
- id - The identifier for the scheduled activity found in the get activity response.
- recurringId - The recurring identifier for the activity found in the get activity response.
- fromAccountId - Source account for payment. Must be the same as activity response.
- toAccountId - Destination account for payment. Must be the same as activity response.
- amount - A new amount field. 
- dueDate - A new due date if needed or copy previous value.
- memo - An updated test field if needed, otherwise copy previous value.
- numberOfTransactions - Do not set for single instance.
- displayId - Do not set.
- checkNumber - Copy if it exists in get activity.

Update the instance using the PUT verb.

```shell
PUT https://onlinebanking-qa.cb.info53.com/mobile/v1/services/transferandpay/activity
```

```json
{
  "requestGuid": "f0415c3d-ed9e-405b-a85a-72434d9c97b0",
  "id": "8E1A2EFF-E854-6131-0814-A499551B4134",
  "recurringId": "C911DBA1-2C9F-D8D2-1B86-733060BDBB16",
  "fromAccountId": "a1dc14ae-84d8-452d-baff-e6ee193eb83c",
  "toAccountId": "D62C04F5-8467-B8A6-F5C8-97BD9D0F8C5B",
  "amount": 1.3,
  "dueDate": "2021-12-09",
  "frequency": "ONE_TIME",
  "numberOfTransactions": null,
  "memo": "do it",
  "displayId": "MBXBKZO4",
  "checkNumber": "9019"
}
```

Returns a 200 response code and the following payload.

```json
{
  "status": "SUCCESS",
  "retrievalErrors": [],
  "activities": [
    {
      "id": "CD1F75C8-5994-99C8-88C0-B07C6C4AD025",
      "recurringId": "608893D0-82A6-5937-43EA-B5954081660C",
      "amount": 1.30,
      "dueDate": "2021-12-09",
      "displayId": "MBXBKZO4",
      "toAccountId": "D62C04F5-8467-B8A6-F5C8-97BD9D0F8C5B",
      "toAccountName": "Bob's Daycare",
      "expressDelivery": false,
      "memo": "do it",
      "status": "SCHEDULED",
      "displayStatus": "SCHEDULED",
      "editable": false,
      "cancelable": false,
      "type": "BILLPAY",
      "checkNumber": "9019"
    }
  ],
  "shouldPromptForIncome": false
}
```

Of note are the editable and cancelable fields, they are now false. 

## Edit the Recurring Series
The series template may also be edited if the editable field is true.
- requestGuid - An unique identifier for this transaction.
- id - The recurring identifier of the transaction series.
- recurringId - The same as id.
- fromAccountId - Source account for payment. Must be the same as activity response.
- toAccountId - Destination account for payment. Must be the same as activity response.
- amount - A new amount field.
- dueDate - A new due date if needed or copy previous value.
- frequency - How often the recurring transaction should occur.
- memo - An updated test field if needed, otherwise copy previous value.
- numberOfTransactions - The upper limit of transactions, if desired.
- displayId - Copy from activity.
- checkNumber - Copy if it exists in get activity.

```shell
PUT https://onlinebanking-qa.cb.info53.com/mobile/v1/services/transferandpay/activity
```

```json
{
  "requestGuid": "916d04f3-8336-46cb-b451-e7b7c65200f4",
  "id": "9131E0D5-ED17-BDA2-2DC1-347AB08FE3C0",
  "recurringId": "9131E0D5-ED17-BDA2-2DC1-347AB08FE3C0",
  "fromAccountId": "a1dc14ae-84d8-452d-baff-e6ee193eb83c",
  "toAccountId": "D62C04F5-8467-B8A6-F5C8-97BD9D0F8C5B",
  "amount": 1.29,
  "dueDate": "2021-10-28",
  "frequency": "ONCE_A_WEEK",
  "numberOfTransactions": null,
  "memo": "do it",
  "displayId": "MBXBKZO4",
  "checkNumber": "9019"
}
```

The response code should be 200 with a payload:

```json
{
  "status": "SUCCESS",
  "retrievalErrors": [],
  "activities": [
    {
      "id": "0050F8E0-D968-BA86-0B97-F279D6B49F10",
      "recurringId": "F6F2C708-785E-FEF1-F753-8D19E1A5E9BA",
      "amount": 1.29,
      "dueDate": "2021-12-09",
      "displayId": "MBXBKZO4",
      "fromAccountId": "a1dc14ae-84d8-452d-baff-e6ee193eb83c",
      "fromAccountNumber": "X4492",
      "fromAccountName": "5/3 ESSENTIAL CHECKING",
      "toAccountId": "D62C04F5-8467-B8A6-F5C8-97BD9D0F8C5B",
      "toAccountName": "Bob's Daycare",
      "expressDelivery": false,
      "memo": "do it",
      "frequency": "ONCE_A_WEEK",
      "status": "SCHEDULED",
      "displayStatus": "SCHEDULED",
      "editable": true,
      "cancelable": true,
      "type": "BILLPAY",
      "checkNumber": "9019"
    }
  ],
  "shouldPromptForIncome": false
}
```

Notice that this response contains a new recurringId. The old identifier is no longer valid.