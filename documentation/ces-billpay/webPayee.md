# Web Payment Usage

# Bank Lookup
In order to add a web payee the user must first enter a routing number.

## Request
```shell
GET /webpayment/bank?routingNumber=*********
```
## Response
```json
{
  "status": "SUCCESS",
  "preferredRoutingNumber": "*********",
  "bankName": "METABANK"
}
```

# Add Account
Add a new external account to the list of payment sources.
## Request
```shell
POST /webpayment/account
```
```json
{
  "accountType": "DDA",
  "routingNumber": "*********",
  "accountNumber": "*********",
  "toAccountId": "1EFF85F5-817C-CEE2-EEEC-8BE2C805A039"
}
```
## Response
```json
{
  "status": "SUCCESS",
  "accounts": [
    {
      "id": "8E804425-429E-0BC9-5BE5-9F2266C8DEE0",
      "accountType": "DDA",
      "accountNumber": "X3456",
      "name": "METABANK CHECKING",
      "bankName": "METABANK",
      "preferredRoutingNumber": "*********",
      "accountSetupDate": "2021-06-24T04:00:00.000Z"
    }
  ]
}
```