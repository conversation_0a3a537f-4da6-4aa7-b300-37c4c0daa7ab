# Update Bill Payment Payee
The nickname, account number and address can be changed.

## Request
```shell
PUT /billpay/updatePayee
```
```json
{
  "cesPayee": {
    "id": "568231CA-4998-7A51-7A67-92C08B0FDD84",
    "nickname": "Dirty Duke Cincy",
    "paymentCutoffTime": "14:00:00.000",
    "nearestNewPaymentDate": "2021-06-29",
    "categoryTypeId": "1001",
    "type": "PERSONAL",
    "electronicBillingEnabled": false,
    "eBillsStatus": "NOT_AVAILABLE",
    "accountNumber": "********",
    "name": "Duke Energy - Ohio",
    "cesAddress": {
      "@type": "CesAddress",
      "streetLine1": "300 E 4TH ST",
      "streetLine2": " ",
      "city": "CINCINNATI",
      "stateOrProvince": "OH",
      "postalCode": "45202",
      "badAddressIndicator": false,
      "country": "US"
    }
  }
}
```
## Response
CODE: 200
```json
{
  "status": "SUCCESS",
  "cesPayee": {
    "id": "568231CA-4998-7A51-7A67-92C08B0FDD84",
    "nickname": "Dirty Duke Cincy",
    "paymentCutoffTime": "14:00:00.000",
    "nearestNewPaymentDate": "2021-06-29",
    "categoryTypeId": "1001",
    "type": "PERSONAL",
    "electronicBillingEnabled": false,
    "eBillsStatus": "NOT_AVAILABLE",
    "accountNumber": "X0666",
    "name": "Duke Energy - Ohio",
    "cesAddress": {
      "@type": "CesAddress",
      "streetLine1": "300 E 4TH ST",
      "city": "CINCINNATI",
      "stateOrProvince": "OH",
      "postalCode": "45202",
      "badAddressIndicator": false,
      "country": "US"
    }
  }
}
```