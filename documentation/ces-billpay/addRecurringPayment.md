```shell
POST https://onlinebanking-qa.cb.info53.com/mobile/v1/services/transferandpay/activity
```

```json
{
  "requestGuid": "200a52c0-2b71-432e-b4c4-6a49e0930e59",
  "id": null,
  "recurringId": null,
  "fromAccountId": "a1dc14ae-84d8-452d-baff-e6ee193eb83c",
  "toAccountId": "e4fed84c-9fce-44e8-8830-e8e612b09fa6",
  "amount": 0.42,
  "dueDate": "2021-10-22",
  "frequency": "ONCE_A_WEEK",
  "numberOfTransactions": null,
  "memo": null,
  "additionalPrincipleAmount": null,
  "displayId": "36131"
}
```
```json
{
  "status": "SUCCESS",
  "retrievalErrors": [],
  "activities": [
    {
      "recurringId": "36133",
      "amount": 0.42,
      "dueDate": "2021-10-29",
      "displayId": "36133",
      "fromAccountNumber": "X4492",
      "fromAccountName": "5/3 ESSENTIAL CHECKING",
      "toAccountNumber": "X2734",
      "toAccountName": "MASTERCARD ACCOUNT",
      "expressDelivery": false,
      "frequency": "ONCE_A_WEEK",
      "numberOfActivities": 0,
      "status": "4",
      "displayStatus": "Scheduled",
      "createTimestamp": "2021-10-22T14:36:23.000Z",
      "editable": true,
      "cancelable": true,
      "type": "INTERNAL_TRANSFER"
    }
  ],
  "shouldPromptForIncome": false
}
```