# Edit Single Payment
You need to manage an account from the Transfers and Payments screen in Internet Banking.
## Request
```shell
PUT /transferandpay/activity
```
```json
{
  "requestGuid": "ecc22fa9-4685-4891-a014-714534e9dfd1",
  "id": "7B83AF75-D123-64DA-E45C-FC76E549F268",
  "fromAccountId": "3AFE325A-FDB0-B499-7758-71CE41F240D2",
  "toAccountId": "8F452A8A-6361-F5E4-660F-F3792D33EB89",
  "amount": 0.11,
  "dueDate": "2021-06-29",
  "frequency": "ONE_TIME",
  "numberOfTransactions": null,
  "displayId": "4BGBVZN4",
  "checkNumber": "9000"
}
```
## Response
```json
{
  "status": "SUCCESS",
  "retrievalErrors": [],
  "activities": [
    {
      "id": "4DE8F628-4E7F-F4BD-C56D-59C0DE3EEE46",
      "amount": 0.11,
      "dueDate": "2021-06-29",
      "displayId": "4BGBVZN4",
      "toAccountId": "8F452A8A-6361-F5E4-660F-F3792D33EB89",
      "toAccountName": "Bob's Daycare",
      "expressDelivery": false,
      "status": "SCHEDULED",
      "displayStatus": "SCHEDULED",
      "editable": false,
      "cancelable": false,
      "type": "BILLPAY",
      "checkNumber": "9000"
    }
  ],
  "shouldPromptForIncome": false
}
```