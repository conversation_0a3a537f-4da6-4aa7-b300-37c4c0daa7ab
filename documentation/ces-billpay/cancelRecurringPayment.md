```shell
GET https://onlinebanking-qa.cb.info53.com/mobile/v1/services/transferandpay/activity?dateRange=-30_396
```

```json
{
  "status": "SUCCESS",
  "retrievalErrors": [],
  "activities": [
    {
      "id": "1715E4CC-D7BC-D2C8-F086-BCEFD1662DBD",
      "amount": 1.23,
      "dueDate": "2021-08-30",
      "displayId": "PBYBIZV4",
      "fromAccountId": "a1dc14ae-84d8-452d-baff-e6ee193eb83c",
      "fromAccountNumber": "X4492",
      "fromAccountName": "5/3 ESSENTIAL CHECKING",
      "toAccountId": "A2D7CA0E-4303-957C-46F1-44A4FA45EF84",
      "toAccountName": "Bob's Daycare",
      "expressDelivery": false,
      "memo": "test",
      "status": "COMPLETED",
      "displayStatus": "COMPLETED",
      "editable": false,
      "cancelable": false,
      "type": "BILLPAY",
      "checkNumber": "9003"
    },
    {
      "id": "********",
      "recurringId": "36131",
      "amount": 2297.48,
      "dueDate": "2021-10-21",
      "displayId": "********",
      "fromAccountId": "a1dc14ae-84d8-452d-baff-e6ee193eb83c",
      "fromAccountNumber": "X4492",
      "fromAccountName": "5/3 ESSENTIAL CHECKING",
      "createTimestamp": "2021-10-21T18:46:07.000Z",
      "editable": false,
      "cancelable": false,
      "type": "INTERNAL_TRANSFER"
    },
    {
      "id": "********",
      "recurringId": "36133",
      "amount": 0.42,
      "dueDate": "2021-10-22",
      "displayId": "********",
      "fromAccountId": "a1dc14ae-84d8-452d-baff-e6ee193eb83c",
      "fromAccountNumber": "X4492",
      "fromAccountName": "5/3 ESSENTIAL CHECKING",
      "toAccountId": "e4fed84c-9fce-44e8-8830-e8e612b09fa6",
      "toAccountNumber": "X2734",
      "toAccountName": "MASTERCARD ACCOUNT",
      "expressDelivery": false,
      "status": "6",
      "displayStatus": "Unsuccessful",
      "createTimestamp": "2021-10-22T14:36:23.000Z",
      "editable": false,
      "cancelable": false,
      "type": "INTERNAL_TRANSFER"
    },
    {
      "recurringId": "36133",
      "amount": 0.42,
      "dueDate": "2021-10-29",
      "displayId": "36133",
      "fromAccountId": "a1dc14ae-84d8-452d-baff-e6ee193eb83c",
      "fromAccountNumber": "X4492",
      "fromAccountName": "5/3 ESSENTIAL CHECKING",
      "toAccountId": "e4fed84c-9fce-44e8-8830-e8e612b09fa6",
      "toAccountNumber": "X2734",
      "toAccountName": "MASTERCARD ACCOUNT",
      "expressDelivery": false,
      "frequency": "ONCE_A_WEEK",
      "numberOfActivities": 0,
      "status": "4",
      "displayStatus": "Scheduled",
      "createTimestamp": "2021-10-22T14:36:23.000Z",
      "editable": false,
      "cancelable": false,
      "type": "INTERNAL_TRANSFER"
    },
    {
      "id": "EA513AD8-2BF8-3A4D-3655-C92C199FD4E2",
      "amount": 4.45,
      "dueDate": "2021-11-02",
      "displayId": "3B9BGZO4",
      "fromAccountId": "a1dc14ae-84d8-452d-baff-e6ee193eb83c",
      "fromAccountNumber": "X4492",
      "fromAccountName": "5/3 ESSENTIAL CHECKING",
      "toAccountId": "A2D7CA0E-4303-957C-46F1-44A4FA45EF84",
      "toAccountName": "Bob's Daycare",
      "expressDelivery": false,
      "status": "SCHEDULED",
      "displayStatus": "SCHEDULED",
      "editable": true,
      "cancelable": true,
      "type": "BILLPAY",
      "checkNumber": "9014"
    },
    {
      "id": "********",
      "recurringId": "35867",
      "amount": 25,
      "dueDate": "2021-11-21",
      "displayId": "********",
      "fromAccountId": "a1dc14ae-84d8-452d-baff-e6ee193eb83c",
      "fromAccountNumber": "X4492",
      "fromAccountName": "5/3 ESSENTIAL CHECKING",
      "toAccountId": "a3139c53-db84-49f0-baab-27369f19b79c",
      "toAccountNumber": "X3643",
      "toAccountName": "MASTERCARD ACCOUNT",
      "expressDelivery": false,
      "status": "4",
      "displayStatus": "Scheduled",
      "createTimestamp": "2021-10-21T04:05:40.000Z",
      "editable": false,
      "cancelable": true,
      "type": "INTERNAL_TRANSFER"
    },
    {
      "id": "********",
      "recurringId": "36131",
      "amount": 2297.48,
      "dueDate": "2021-11-21",
      "displayId": "********",
      "fromAccountId": "a1dc14ae-84d8-452d-baff-e6ee193eb83c",
      "fromAccountNumber": "X4492",
      "fromAccountName": "5/3 ESSENTIAL CHECKING",
      "toAccountId": "e4fed84c-9fce-44e8-8830-e8e612b09fa6",
      "toAccountNumber": "X2734",
      "toAccountName": "MASTERCARD ACCOUNT",
      "expressDelivery": false,
      "frequency": "ONCE_A_MONTH",
      "numberOfActivities": 0,
      "status": "4",
      "displayStatus": "Scheduled",
      "createTimestamp": "2021-10-21T19:05:00.000Z",
      "editable": true,
      "cancelable": true,
      "type": "INTERNAL_TRANSFER"
    },
    {
      "id": "********",
      "recurringId": "36109",
      "amount": 1.23,
      "dueDate": "2021-11-23",
      "displayId": "********",
      "fromAccountId": "a1dc14ae-84d8-452d-baff-e6ee193eb83c",
      "fromAccountNumber": "X4492",
      "fromAccountName": "5/3 ESSENTIAL CHECKING",
      "toAccountId": "a3139c53-db84-49f0-baab-27369f19b79c",
      "toAccountNumber": "X3643",
      "toAccountName": "MASTERCARD ACCOUNT",
      "expressDelivery": false,
      "status": "4",
      "displayStatus": "Scheduled",
      "createTimestamp": "2021-10-22T04:05:46.000Z",
      "editable": false,
      "cancelable": true,
      "type": "INTERNAL_TRANSFER"
    }
  ],
  "recurringActivities": [
    {
      "id": "********",
      "recurringId": "36109",
      "amount": 1.23,
      "dueDate": "2021-09-23",
      "displayId": "36109",
      "fromAccountId": "a1dc14ae-84d8-452d-baff-e6ee193eb83c",
      "fromAccountNumber": "X4492",
      "fromAccountName": "5/3 ESSENTIAL CHECKING",
      "toAccountId": "a3139c53-db84-49f0-baab-27369f19b79c",
      "toAccountNumber": "X3643",
      "toAccountName": "MASTERCARD ACCOUNT",
      "expressDelivery": false,
      "frequency": "ONCE_A_MONTH",
      "numberOfActivities": 6,
      "numberOfRemainingActivities": 4,
      "status": "4",
      "displayStatus": "Scheduled",
      "createTimestamp": "2021-09-16T12:56:36.000Z",
      "editable": false,
      "cancelable": true,
      "type": "INTERNAL_TRANSFER"
    },
    {
      "recurringId": "36133",
      "amount": 0.42,
      "dueDate": "2021-10-29",
      "displayId": "36133",
      "fromAccountId": "a1dc14ae-84d8-452d-baff-e6ee193eb83c",
      "fromAccountNumber": "X4492",
      "fromAccountName": "5/3 ESSENTIAL CHECKING",
      "toAccountId": "e4fed84c-9fce-44e8-8830-e8e612b09fa6",
      "toAccountNumber": "X2734",
      "toAccountName": "MASTERCARD ACCOUNT",
      "expressDelivery": false,
      "frequency": "ONCE_A_WEEK",
      "numberOfActivities": 0,
      "status": "4",
      "displayStatus": "Scheduled",
      "createTimestamp": "2021-10-22T14:36:23.000Z",
      "editable": false,
      "cancelable": true,
      "type": "INTERNAL_TRANSFER"
    },
    {
      "id": "********",
      "recurringId": "36131",
      "amount": 2297.48,
      "dueDate": "2021-11-21",
      "displayId": "36131",
      "fromAccountId": "a1dc14ae-84d8-452d-baff-e6ee193eb83c",
      "fromAccountNumber": "X4492",
      "fromAccountName": "5/3 ESSENTIAL CHECKING",
      "toAccountId": "e4fed84c-9fce-44e8-8830-e8e612b09fa6",
      "toAccountNumber": "X2734",
      "toAccountName": "MASTERCARD ACCOUNT",
      "expressDelivery": false,
      "frequency": "ONCE_A_MONTH",
      "numberOfActivities": 0,
      "status": "4",
      "displayStatus": "Scheduled",
      "createTimestamp": "2021-10-21T18:46:07.000Z",
      "editable": true,
      "cancelable": true,
      "type": "INTERNAL_TRANSFER"
    }
  ],
  "shouldPromptForIncome": false
}
```

```shell
DELETE https://onlinebanking-qa.cb.info53.com/mobile/v1/services/transferandpay/activity?activityId=36133
```

