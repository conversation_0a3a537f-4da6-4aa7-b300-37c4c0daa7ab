# Request
```shell
POST /mobile/v1/services/billpay/addPersonalPayee
```
# Body
```json
{
  "status": "SUCCESS",
  "accountDetail": {
    "@type": "Card",
    "accountDescription": "Fifth Third Private Bank Card",
    "displayAccountNumber": "X3643",
    "accountType": "MMC",
    "affiliate": "NPAL",
    "availableBalance": 9811.52,
    "ledgerBalance": 2121,
    "openDate": "2019-09-03",
    "customerServiceDescription": "Fifth Third Private Bank Card",
    "productName": "Fifth Third Private Bank Card",
    "accountOwners": [
      {
        "relationshipCode": "P",
        "relationshipText": "PRIMARY_OWNER",
        "relationshipType": "P",
        "primary": true,
        "secondary": false,
        "coOwner": false,
        "authUser": false
      }
    ],
    "interestSinceLastStatement": 0,
    "interestOnLastStatement": 15.5,
    "interestYtd": 0,
    "interestLastYear": 0,
    "yearForInterestYtd": 2021,
    "yearForInterestLastYear": 2020,
    "isHardshipAllowed": true,
    "isSmartSavingsEnabled": false,
    "isScheduledSavingsEnabled": false,
    "cardNumber": "X3643",
    "cardLimitAmount": 12000,
    "maximumCardLimitAmount": 12000,
    "cashLimitAmount": 6000,
    "maximumCashLimitAmount": 6000,
    "expirationDate": "2023-09-28",
    "lastTransactionDate": "2021-05-17",
    "enhancedCard": false,
    "flexCard": false,
    "isBlocked": false,
    "creditCard": true,
    "cashAdvanceRate": 24.99,
    "purchaseRate": 9.24,
    "creditLimit": 12000,
    "minimumPaymentAmount": 132,
    "lastStatementBalance": 2121,
    "lastStatementDate": "2021-06-17",
    "lastActivityDate": "2021-05-17",
    "creditCardName": "Fifth Third Private Bank Card",
    "annualFee": true,
    "worldCard": false,
    "lastPaymentAmount": 0,
    "atmPinExists": false,
    "tempAtmPinExists": false,
    "tempAtmPinExpired": false,
    "availableForOnlinePayment": true,
    "isDisplayCardAgreementLink": true,
    "isDisplayBalanceTransferLink": false,
    "productCode": "MWFTM",
    "isBankEmployeeAccount": true,
    "reissued": false,
    "familyCardAccount": false
  }
}
```

# Response

