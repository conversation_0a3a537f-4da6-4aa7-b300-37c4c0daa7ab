## Request

```shell
POST /billpay/addPayee
```

```json
{
    "cesPayee": {
        "name": "Cincinnati Bell",
        "cesPayeeType": "TWOFACTOR",
        "accountNumber": "*************",
        "nickname": "<PERSON><PERSON><PERSON>"
    }
}
```

## Response

```json
{
    "status": "SUCCESS",
    "cesPayee": {
        "id": "FD133687-E12E-4B3E-E289-8381BF79887D",
        "nickname": "Cincy Bell",
        "paymentCutoffTime": "14:00:00.000",
        "nearestNewPaymentDate": "2021-06-17",
        "categoryTypeId": "1001",
        "type": "BUSINESS",
        "electronicBillingEnabled": true,
        "eBillsStatus": "SUPPORTED",
        "accountNumber": "X2309",
        "name": "Cincinnati Bell",
        "phoneNumber": "**********",
        "cesAddress": {
            "@type": "CesAddress",
            "streetLine1": "PO BOX 748003",
            "city": "CINCINNATI",
            "stateOrProvince": "OH",
            "postalCode": "*********",
            "badAddressIndicator": false,
            "country": "US"
        }
    }
}
```

If more information is needed we get HTTP Status 400 and a CesResponse payload.

```json
{
  "status": "BILL_PAY_ERROR",
  "statusCode": "MER018",
  "statusReason": "Error while searching Global Payee ID Need more info: Duke Energy - Ohio ********"
}
```

If an address cannot be validated we get HTTP status 422 and CesResponse payload.

```json
{
  "status": "VALIDATION_ERROR",
  "statusCode": "ADDRESS_NOT_FOUND"
}
```