# Bill Payment Implementation
Most operations related to bill payment are implemented using the CES transfer interface. Adding Payees and obtaining 
bill payment information requires use of the bill pay end points.

Bill payments are different from transfers in that they can also use bill payment, web payments and external transfer end
points in CES to locate funding sources and payment accounts. The actual payment appears to always be submitted using the 
transfer an dpay add activity method.

External transfers profile returns account that have been set up as targets for transfers (maybe payees) using the FiServ 
interface. Web payment profile returns accounts that have been set up via the web payments flow, I assume this uses ACH
behind the scenes. Bill pay profile returns accounts that are configured as internal payment accounts.

# Details

[Add Payee Example - Two Factor](addPayee.md)

[Add Payee Example - Three Factor](addPayeeDuke.md)

[Edit Payee](updatePayee.md)

[Delete Payee](deletePayee.md)

[Web Payees](webPayee.md)

# Online Banking Bill Payment Calls

If you follow the Make Payment flow OLB will attempt to retrieve the groups of accounts.
See [Payee Groups](payeeGroups.md)

If you choose to Manage Payees several calls are made to determine the available accounts, payees and funding accounts.
There are three sources of payee profile data. Internal bill payment, external bill payment and web payments.
See [Payee Types](payeeProfiles.md)

[Add Payment Annotated Flow](addPaymentFlow.md)

[Get Recent Activity](getRecentActivity.md)

[Edit Recurring Payment](editRecurringPayements.md)

[Cancel Recurring Payment](cancelRecurringPayment.md)

