# CES Proxy Local Mock
We use WireMock to simulate CES Proxy locally. WireMock provides a way to specify 
canned responses to requests from the BFF. These canned responses are saved responses 
from the SIT environment.

# Usage
We usually run WireMock as a docker images.

```bash
$ docker-compose -f ./src/test/newman/wiremock/docker-compose.yml up
```

The mappings are defined in src/test/resources/stubs/mappings/mmbff, and responses
are in src/test/resources/stubs/__files/mmbff. The files beneath ces.services 
directories are for the general use cases. Files beneath users directory are tailored 
for specific users (determined by the sub field in the JWT).

# Special Users
Use [jwt.io](https://jwt.io/) to create JWT tokens for your users. The five users 
currently supported are:

bshuckman - has a variety of account types and activity data.
eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.GyR3_G7SWb6p9ScdjViZeqHiEO7fxm-vqImjcgc8xJw

mmmm_cconly
eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************.Gy1g1Yrkk6GRJoLqXMXEmHu9fWPfwOLMX2L_kYOskuk

mmmm_ddaonly
eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************.FHw9lhkjMzTqIeFgXUP1RbvUTieKb0Rf22J50NPFZEk

mmmm_loanonly
eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************.UVvhb9ngsHuAjxU5frhyZgHy-w0iABl4uwr3s7UEF5U

mmmm_savonly
eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************.lElnt9RBwVfbgn5mux-DWgX9inUvrpNVJLmApRYA6l0