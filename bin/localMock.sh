#!/bin/bash
parent_path=$( cd "$(dirname "${BASH_SOURCE[0]}")" ; pwd -P )
cd "$parent_path"
# Assumes that you have a lib directory off of the project root, this directory contains the current versions of
# wiremock standalone and wiremodk jwt extension.
java \
  -cp ../lib/wiremock-jre8-standalone-2.33.2.jar:../lib/wiremock-jwt-extension.jar \
        com.github.tomakehurst.wiremock.standalone.WireMockServerRunner \
  --port 3000 --verbose \
  --extensions="com.github.masonm.JwtMatcherExtension,com.github.masonm.JwtStubMappingTransformer" \
  --root-dir ../src/test/resources/stubs
