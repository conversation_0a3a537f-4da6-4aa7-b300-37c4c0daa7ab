variable cluster_name {}
variable service_name {}
variable base_environment {}
variable name_tag {}

data "aws_region" "current" {}
//data "aws_lambda_function" "log_to_elasticsearch" {
//  function_name = "LogsToElasticsearch_artemis"
//}

data "aws_ssm_parameter" "kinesis_stream" {
  name = "/config/application/app_log_kinesis_arn"
}

data "aws_ssm_parameter" "cwl_firehose_arn" {
  name = "/config/application/cloudwatch_firehose_role_arn"
}

resource "aws_cloudwatch_log_group" "ECSCloudWatchGroup" {
  name              = "/ecs/${var.cluster_name}/${var.base_environment}/${var.service_name}"#TODO may need a task_name someday
  retention_in_days = 7

  # ... potentially other configuration ...

  tags = {
    Name = var.name_tag
  }
}

resource "aws_cloudwatch_log_subscription_filter" "cloudwatch_log_filter" {
  name            = "Digital-${var.cluster_name}-${var.service_name}-${var.base_environment}-CWLFilter"
  role_arn        = data.aws_ssm_parameter.cwl_firehose_arn.value
  destination_arn = data.aws_ssm_parameter.kinesis_stream.value
  log_group_name  = "/ecs/${var.cluster_name}/${var.base_environment}/${var.service_name}"
  filter_pattern  = ""
  depends_on = [aws_cloudwatch_log_group.ECSCloudWatchGroup]
}

//resource "aws_lambda_permission" "allow_cloudwatch" {
//  statement_id  = "allow-cloudwatch-${var.service_name}"
//  action        = "lambda:InvokeFunction"
//  function_name = data.aws_lambda_function.log_to_elasticsearch.function_name
//  principal     = "logs.${data.aws_region.current.name}.amazonaws.com"
//  source_arn    = aws_cloudwatch_log_group.ECSCloudWatchGroup.arn
//}
//
//resource "aws_cloudwatch_log_subscription_filter" "cloudwatch_lambda_subscription" {
//  depends_on      = ["aws_lambda_permission.allow_cloudwatch"]
//  name            = "cloudwatch_lambda_subscription"
//  log_group_name  = aws_cloudwatch_log_group.ECSCloudWatchGroup.name
//  filter_pattern  = ""
//  destination_arn = data.aws_lambda_function.log_to_elasticsearch.arn
//}