##################################################################################
# PROVIDERS
##################################################################################
terraform {
  required_providers {
    aws = {
      source = "hashicorp/aws"
      version = "~>4.22"
    }
    dockerless = {
      source  = "nullstone-io/dockerless"
      version = "0.1.1"
    }
  }
  required_version = ">= 0.15"
}
data "aws_ssm_parameter" "hashi_vault_dev_uri" {
  count = var.base_environment == "DEV" ? 1 : 0
  name  = "/config/application/apigw_public_url/vault"
}

data "aws_ssm_parameter" "hashi_vault_uri_p" {
  count = var.base_environment != "DEV" ? 1 : 0
  name  = "/config/application/hashi_vault_uri"
}

data "aws_ssm_parameter" "hashi_vault_provider_role" {
  name = "/config/application/hashi_vault_role"
}

data "aws_secretsmanager_secret" "hashi_vault_provider_secret" {
  name = var.base_environment != "DEV" ? "/secret/hashi_vault_secret" : "/secret/vaultRootToken"
}
data "aws_secretsmanager_secret_version" "hashi_vault_provider_secret" {
  secret_id = data.aws_secretsmanager_secret.hashi_vault_provider_secret.id
}

provider "vault" {
  address = var.base_environment != "DEV" ? data.aws_ssm_parameter.hashi_vault_uri_p[0].value : data.aws_ssm_parameter.hashi_vault_dev_uri[0].value
  token   = var.base_environment != "DEV" ? null : data.aws_secretsmanager_secret_version.hashi_vault_provider_secret.secret_string

  dynamic "auth_login" {
    for_each = var.base_environment != "DEV" ? [1] : []
    content {
      path = "auth/approle/login"

      parameters = {
        role_id   = data.aws_ssm_parameter.hashi_vault_provider_role.value
        secret_id = data.aws_secretsmanager_secret_version.hashi_vault_provider_secret.secret_string
      }
    }
  }
}

#configuration for dockerless image pusher
data "aws_ecr_authorization_token" "temporary" {
  registry_id = data.aws_caller_identity.current.account_id
}

provider "dockerless" {
  registry_auth = {
    "${data.aws_caller_identity.current.account_id}.dkr.ecr.${data.aws_region.current.name}.amazonaws.com" = {
      username = data.aws_ecr_authorization_token.temporary.user_name
      password = data.aws_ecr_authorization_token.temporary.password
    }
  }
}