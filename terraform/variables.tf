##################################################################################
# VARIABLES
##################################################################################
variable "cluster_name" {
  default     = "consumer-mobile"
  description = "CLUSTER_NAME where this service should be assigned"
}

variable "docker_image_tag" {
  description = "The versioned docker image tag"
}

variable "service_key" {
  description = "Unique service name in the cluster - should contain branch name for feature branches"
}

variable "cloud_config_profile" {
  description = "The optional profile suffix to append to the app_name for parameters stored in SSM or Secrets Manager"
  type        = string
  default     = ""
}

variable "default_tags" {
  type    = map
  default = {}
}

variable "secrets" {
  description = "Any secrets that need to be placed into secrets manager."
  type    = map
  default = {}
}

variable "base_environment" {
  description = "The base environment from which to source the remote state from."
}

variable "environment_variables" {
  description = "A list of maps that represent the environment variables to pass to the container. [{name=\"foo\", value=\"bar\"}]"
  type        = list
  default     = []
}

variable "app_prefix" {
  description = "The terraform prefix value"
  default     = "xqz-"
}

variable "use_legacy_ecs_task_execution_role_name" {
  default = false
  description = "Set to true if using ECS Cluster module versions before v2.x.x. If set to true, the legacy ECS the task execution role naming convention will be assumed: \"$${var.cluster_name}$${data.aws_region.current.name}_ECS_role\". If false, the default pattern defined by the current ECS Service module will be used."
  type = bool
}