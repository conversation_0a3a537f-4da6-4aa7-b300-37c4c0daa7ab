###################################################################################
# WORKSPACE
###################################################################################

data "aws_vpc" "defaultVPC" {}
data aws_region "current" {}
data aws_caller_identity "current" {}

data "aws_ssm_parameter" "api_authorizer" {
  name= "/config/application/cross_account_lambda_authorizer_id"
}

data "aws_ssm_parameter" "hashi_vault_uri" {
  name = "/config/application/hashi_vault_uri"
}

data "aws_ssm_parameter" "hashi_vault_role" {
  name = "/config/application/hashi_vault_role"
}

data "aws_ssm_parameter" "alpine_ecr_image" {
  name = "/config/application/alpine_image_uri"
}

data "aws_secretsmanager_secret" "hashi_vault_secret" {
  name = var.base_environment != "DEV" ? "/secret/hashi_vault_secret" : "/secret/vaultRootToken"
}
data "aws_secretsmanager_secret_version" "hashi_vault_secret" {
  secret_id = data.aws_secretsmanager_secret.hashi_vault_secret.id
}

//--------------------------------------------------------------------
// Locals
//--------------------------------------------------------------------
locals {
  environment_variables = concat(var.environment_variables, [{
    name  = "SPRING_PROFILES_ACTIVE",
    value = "aws,${var.base_environment},${var.service_key}"
  }, {
    name  = "SERVICE_KEY",
    value = var.service_key
  }, {
    name = "hashi_vault_uri",
    value = data.aws_ssm_parameter.hashi_vault_uri.value
  }, {
    name = "hashi_vault_role",
    value = data.aws_ssm_parameter.hashi_vault_role.value
  }, {
       name  = "ENV",
       value = lower(var.base_environment)
   }, {
    //property for dynatrace auto tagging: <CMDB>:<ServiceName>:<ApplicationType>:<Environment>
    name = "DT_CUSTOM_PROP",
    value = "DT_PROCESS_GROUP=${local.localTags["ftb:app:cmdb"]}:${var.service_key}:domain:${var.base_environment}"
  }],
  //NOTE this variable is optional based on environment.
  var.base_environment != "DEV" ? [{
    name  = "hashi_vault_secret",
    value = data.aws_secretsmanager_secret_version.hashi_vault_secret.secret_string
  }] : [])
  ecs_task_execution_role_name = var.use_legacy_ecs_task_execution_role_name ? "${var.cluster_name}_ECS_role" : null
  secrets = merge(var.secrets, {})
  localTags = merge(var.default_tags, {
    "ftb:app:name"    = "move-money-bff"        // make this more specific
    "ftb:app:contact" = "<EMAIL>" // this is the person who gets called when it breaks
    "ftb:app:owner"   = "<EMAIL>" //this is the person who gets fired when it breaks something else
    #"ftb:provisioner" = "consumer-mobile"        //No rules about what you put in here, just don't use "pubcloud"
    "ftb:provisioner" = "consumermobile"        //No rules about what you put in here, just don't use "pubcloud"
    "ftb:app:cmdb"    = "mo3607"
  })
  enable_execute_command  = lower(var.base_environment) != "prod"
  prod_desired_count = 6
  palo_routing = false
  target_max_capacity = lookup({
    dev  = 2
    sit  = 5
    prod = 27
  }, lower(var.base_environment), 1)
  target_min_capacity = lookup({
    dev  = 1
    sit  = 2
    prod = 6
  }, lower(var.base_environment), 1)
}

data "vault_generic_secret" "dynatrace_paas_token" {
  count = lower(var.base_environment) != "dev" ? 1 : 0
  path = "kv/${local.localTags["ftb:app:cmdb"]}/${local.localTags["ftb:provisioner"]}"
}

//--------------------------------------------------------------------
// Resources
//--------------------------------------------------------------------
resource aws_iam_role "service_task_role" {
  name               = replace(substr("${var.service_key}_ECS_role", 0, 64), "/-+$/", "")
  description        = "Role assigned to the tasks to allow access to parameters in SSM"
  permissions_boundary = "arn:aws:iam::${data.aws_caller_identity.current.account_id}:policy/pubcloud/AppTeamIAMBoundary"
  assume_role_policy = <<POLICY
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "",
      "Effect": "Allow",
      "Principal": {
        "Service": "ecs-tasks.amazonaws.com"
      },
      "Action": "sts:AssumeRole"
    }
  ]
}
POLICY
}

# This policy allows Spring Cloud AWS to access parameters in AWS. Access is allow only to the following parameters:
#   /config/application/*           -- parameters shared across applications
#   /config/application_{PROFILE}/* -- parameters shared across applications with the specified profile active
#   /config/{APP_NAME}/*            -- parameters specific to the specified application
#   /config/{APP_NAME}_{PROFILE}/*  -- parameters specific to the specified application with the specified profile active
resource aws_iam_role_policy "service_task_policy" {
  role   = aws_iam_role.service_task_role.id
  policy = <<POLICY
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
          "ssm:Get*",
          "secretsmanager:Get*",
          "secretsmanager:DescribeSecret"
      ],
      "Resource": [
        "arn:aws:ssm:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:parameter/config/application*",
        "arn:aws:ssm:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:parameter/config/${var.service_key}*",
        "arn:aws:ssm:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:parameter/config/${var.app_prefix}${var.service_key}*",
        "arn:aws:secretsmanager:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:secret:/secret/application*",
        "arn:aws:secretsmanager:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:secret:/secret/${var.service_key}*"
      ]
    }
  ]
}
POLICY
}

//resource "aws_secretsmanager_secret" "secret" {
//  name = "/secret/${var.service_key}"
//}

//resource "aws_secretsmanager_secret_version" "version" {
//  secret_id = aws_secretsmanager_secret.secret.id
//  secret_string = jsonencode(merge(local.secrets,
//  module.xqz-rds-aurora.secret_map))
//}

//resource "aws_ssm_parameter" "spring_boot_admin_service_base_url" {
//  name        = "/config/${var.service_key}/spring_boot_admin_service_base_url"
//  description = "The APIGateway url to the spring boot service"
//  value       = module.xqz_service_apigateway.endpoint
//  type        = "String"
//}

//--------------------------------------------------------------------
// Modules
//--------------------------------------------------------------------

module "xqz_ecr" {
  source = "git::https://github.info53.com/Fifth-Third/terraform-aws-xqz_ecr?ref=v1.1.1"

  cluster_name      = var.cluster_name
  service_name      = var.service_key
  default_tags      = local.localTags
  base_environment  = var.base_environment
}

module "xqz_service_loadbalancer" {
  source = "git::https://github.info53.com/Fifth-Third/terraform-aws-xqz_service_loadbalancer?ref=v3.0.3"

  default_tags      = local.localTags
  loadbalancer_name = replace(substr(var.service_key, 0, 32), "/-+$/", "") // truncate the name within the AWS naming limits and remove trailing hyphens (if any)
  vpc_id            = data.aws_vpc.defaultVPC.id
  application_port  = 8443
  base_environment  = replace(var.base_environment, "_", "-")
  palo_routing      = false
}

module "xqz_service_apigateway" {
  source = "git::https://github.info53.com/Fifth-Third/terraform-aws-xqz_service_apigateway?ref=v2.0.5"

  api_name = "api"
  aws_lb_arn  = module.xqz_service_loadbalancer.lb_arn
  lb_dns      = module.xqz_service_loadbalancer.lb_dns
  link_name   = var.service_key
  authorizer_id = data.aws_ssm_parameter.api_authorizer.value
  description   = "The service that provides ${var.service_key} content"
  path          = lower(var.service_key)
  //base_environment = var.base_environment
  default_tags = local.localTags
  #bypass_request_gateway = true
}

//TODO module auto scaler for the service (elastic scaling)
module "xqz_ecs_service" {
  source = "git::https://github.info53.com/Fifth-Third/terraform-aws-xqz_ecs_service?ref=v3.0.7"

  ecs_task_execution_role_name = local.ecs_task_execution_role_name
  cluster_name                = var.cluster_name
  default_tags                = local.localTags
  ecs_task_role_arn           = aws_iam_role.service_task_role.arn
  environment_variables       = local.environment_variables
  image_url                   = lower("${module.xqz_ecr.repository_url}:${var.docker_image_tag}")
  artifactory_image_name      = "move-money-bff-scf-${var.service_key}:${var.docker_image_tag}" #must pre-pend github repo
  lb_target_group_arn         = module.xqz_service_loadbalancer.lb_target_group_arn
  service_name                = var.service_key
  base_environment            = var.base_environment
  dynatrace_paas_token        = lower(var.base_environment) != "dev" ? data.vault_generic_secret.dynatrace_paas_token[0].data["dynatrace_paas_token"] : null
  enable_execute_command      = local.enable_execute_command
  log_group_name              = "/ecs/${var.cluster_name}/${var.base_environment}/${var.service_key}"
  ist_desired_count           = 2
  prod_desired_count          = local.prod_desired_count
  cpu                         = 1024
  memory                      = 4096
  healthcheck_command         =  ["CMD-SHELL", "wget --no-check-certificate -t 1 -nv -O - https://localhost:8443/actuator/health || exit 1"]
  container_port              = 8443
}

module "xqz_ecs_autoscaling" {
  source = "git::https://github.info53.com/fitb-mobile-tribe/terraform-aws-xqz-ecs-autoscaling.git?ref=v1.0.1"

  depends_on = [module.xqz_ecs_service]
  ecs_service_name = var.service_key
  ecs_cluster_name = var.cluster_name
  scale_target_max_capacity = local.target_max_capacity
  scale_target_min_capacity = local.target_min_capacity
}

#TODO update with external module for Firelinse and Kinesis
module "logging" {
  source = "./modules/logging"

  service_name = var.service_key
  cluster_name = var.cluster_name
  base_environment = var.base_environment
  name_tag     =  "Digital-${var.cluster_name}-${var.base_environment}-${var.service_key}"
}
