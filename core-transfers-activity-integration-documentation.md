# Core Transfers Activity Integration Documentation

## Overview

This document describes the integration of Core Transfers as a new source for immediate transfer activities in the Move Money BFF. The implementation enables parallel retrieval of activities from both the existing CES (Customer Experience Service) and the new Core Transfers service, providing a unified activity response while maintaining backward compatibility.

## Architecture Overview

The integration introduces Core Transfers as an additional data source alongside CES for retrieving transfer activities. When the `CORE_TRANSFERS_TDS_ENABLED` feature flag is enabled, the system makes parallel asynchronous calls to:

1. **CES** - Existing Customer Experience Service for traditional activities
2. **Core Transfers** - New TDS (Transfer Deposit Service) for immediate transfers
3. **Account Service** - For account details needed by Core Transfers mapping

## Entry Point

The main entry point for activity retrieval is:

```java
// MmActivityController.java
@GetMapping(value = "/v2/activity")
public BffGetActivityResponse getActivityV2(
    @RequestParam(value = "recentLimit", required = false) Integer recentLimit,
    @RequestParam(value = "upcomingLimit", required = false) Integer upcomingLimit) {
    
    return activityServiceV1.getActivity(recentLimit, upcomingLimit);
}
```

This delegates to `ActivityServiceV1.getActivity(recentLimit, upcomingLimit)` which orchestrates the parallel service calls.

## Feature Flag Control

The integration is controlled by the feature flag:
- **Flag Name**: `Digital-Move-Money-CoreTransfers-TDS-Enabled`
- **Constant**: `CORE_TRANSFERS_TDS_ENABLED`
- **Default**: `false` (disabled)

When enabled, the system makes parallel calls to both CES and Core Transfers. When disabled, only CES is called.

## Parallel Asynchronous Execution

### Three Parallel Service Calls

When Core Transfers is enabled, the system initiates three parallel asynchronous calls:

```java
// ActivityServiceV1.java - Parallel execution setup
CompletableFuture<ClientActivityResponse> cesActivityFuture = null;
CompletableFuture<TDSCoreTransferActivityResponse> coreTransfersActivityFuture = null;
CompletableFuture<ListResponse> accountListFuture = null;

// 1. Start CES call
cesActivityFuture = activityServiceAsync.getActivityAsync();

// 2. Start account service call (if Core Transfers enabled)
if (coreTransfersEnabled) {
    accountListFuture = accountServiceAsync.getAccountListAsync(true);
}

// 3. Start Core Transfers call (if Core Transfers enabled)
if (coreTransfersEnabled) {
    coreTransfersActivityFuture = coreTransfersActivityService.getCoreTransfersActivitiesAsync();
}
```

### Execution Flow

1. **Initiation Phase**: All three service calls are started simultaneously using `CompletableFuture`
2. **Safe Startup**: Each call is wrapped in try-catch to ensure one failing to start doesn't prevent others
3. **Parallel Execution**: All calls execute independently and asynchronously
4. **Result Retrieval**: Results are retrieved using `.get()` with proper exception handling
5. **Merging**: Successful responses are merged into a unified result

### Service Implementations

#### 1. CES Activity Service
```java
@Service
public class ActivityServiceAsync extends CesClientService {
    @Async
    public CompletableFuture<ClientActivityResponse> getActivityAsync() {
        return CompletableFuture.completedFuture(
            super.callClient(() -> cesClient.getTransferAndPayActivity()));
    }
}
```

#### 2. Core Transfers Activity Service
```java
@Service
public class CoreTransfersActivityServiceAsync {
    @Async
    public CompletableFuture<TDSCoreTransferActivityResponse> getCoreTransferActivityAsync() {
        try {
            TDSCoreTransferActivityResponse response = coreTransfersClient.getTransferActivity();
            return CompletableFuture.completedFuture(
                response != null ? response : new TDSCoreTransferActivityResponse());
        } catch (Exception e) {
            log.error("Error calling Core Transfers activity service", e);
            return CompletableFuture.completedFuture(new TDSCoreTransferActivityResponse());
        }
    }
}
```

#### 3. Account Service
- Uses existing `AccountServiceAsync.getAccountListAsync(true)` to retrieve account details
- Required for mapping Core Transfers activities to include account information

## Core Transfers to BFF Activity Mapping

### Source Model: TDSCoreTransferActivity

```java
public class TDSCoreTransferActivity {
    private String referenceId;
    private String fromAccountId;
    private String fromAccountType;
    private String toAccountId;
    private String toAccountType;
    private BigDecimal amount;
    private String transferStatus;
    private LocalDate createdDate;
    private LocalDate expectedPostingDate;
}
```

### Target Model: BffActivity

The mapping transforms Core Transfers data into the standard BFF activity format:

```java
// CoreTransfersActivityMapper.java
private BffActivity mapSingleActivity(TDSCoreTransferActivity coreActivity, List<InternalAccount> accountList) {
    BffActivity bffActivity = new BffActivity();
    
    // Basic field mapping
    bffActivity.setId(coreActivity.getReferenceId());
    bffActivity.setDisplayId(coreActivity.getReferenceId());
    bffActivity.setFromAccountId(coreActivity.getFromAccountId());
    bffActivity.setToAccountId(coreActivity.getToAccountId());
    bffActivity.setToAccountType(coreActivity.getToAccountType());
    bffActivity.setAmount(coreActivity.getAmount().doubleValue());
    
    // Date mapping
    bffActivity.setCreationDate(coreActivity.getCreatedDate());
    bffActivity.setDueDate(coreActivity.getExpectedPostingDate());
    bffActivity.setCreateTimestamp(coreActivity.getCreatedDate().atStartOfDay());
    
    // Activity type - all Core Transfers are internal transfers
    bffActivity.setActivityType(ActivityBase.INTERNAL_TRANSFER);
    bffActivity.setType(ActivityBase.INTERNAL_TRANSFER);
    
    // Status mapping (see below)
    mapStatus(coreActivity, bffActivity);
    
    return bffActivity;
}
```

### Status Mapping Logic

Core Transfers status values are mapped to BFF-specific status codes to maintain consistency with CES behavior:

```java
// CoreTransfersActivityMapper.java - Status mapping
private void mapStatus(TDSCoreTransferActivity coreActivity, BffActivity bffActivity) {
    String transferStatus = coreActivity.getTransferStatus();

    if (CT_SUCCESS_STATUS.equals(transferStatus) || CT_PENDING_STATUS.equals(transferStatus)) {
        // SUCCESS or PENDING → "Completed" with status code "3"
        // PENDING is treated as completed to maintain parity with CES behavior
        bffActivity.setDisplayStatus(ActivityBase.COMPLETED_STATUS); // "Completed"
        bffActivity.setStatus(BFF_PROCESSED_STATUS_CODE); // "3"
    } else {
        // All other statuses → "Unsuccessful" with status code "1"
        bffActivity.setDisplayStatus(ActivityBase.UNSUCCESSFUL_STATUS); // "Unsuccessful"
        bffActivity.setStatus(BFF_UNSUCCESSFUL_STATUS_CODE); // "1"
    }
}
```

### Status Code Mapping Table

| Core Transfers Status | BFF Display Status | BFF Status Code | Description |
|----------------------|-------------------|-----------------|-------------|
| `SUCCESS` | `Completed` | `3` | Transfer completed successfully |
| `PENDING` | `Completed` | `3` | Transfer pending batch settlement (treated as completed for UX consistency) |
| Any other value | `Unsuccessful` | `1` | Transfer failed or in error state |

**Important Note**: `PENDING` status is mapped to `Completed` to maintain parity with CES behavior. This means that transfers awaiting batch settlement on UDS (Unified Data Service) are displayed as completed to the user.

### Activity Categorization

All Core Transfers activities are categorized as:
- **Activity Type**: `INTERNAL_TRANSFER`
- **Category**: Recent Activities (immediate transfers)
- **Placement**: Always added to `recentActivities` list regardless of status

## Error Handling and Success Scenarios

The system implements robust error handling with three distinct success scenarios:

### Success Scenarios

#### 1. Full Success
- **Condition**: Both CES and Core Transfers succeed
- **Response Status**: `SUCCESS`
- **Behavior**: Activities from both sources are merged
- **Retrieval Errors**: Empty list

#### 2. Partial Success - CES Fails, Core Transfers Succeeds
- **Condition**: CES call fails but Core Transfers succeeds
- **Response Status**: `PARTIAL_SUCCESS`
- **Behavior**: Only Core Transfers activities returned
- **Retrieval Errors**: `["UNABLE_TO_GET_ACTIVITY"]`

#### 3. Partial Success - Core Transfers Fails, CES Succeeds
- **Condition**: Core Transfers call fails but CES succeeds
- **Response Status**: `PARTIAL_SUCCESS`
- **Behavior**: Only CES activities returned
- **Retrieval Errors**: `["RETRIEVAL_ERROR_CORE_TRANSFERS"]`

### Error Scenarios

#### 1. Both Services Fail (Core Transfers Enabled)
- **Condition**: Both CES and Core Transfers fail when Core Transfers is enabled
- **Response**: `BffException.serviceUnavailable("Unable to retrieve activities from any service")`
- **HTTP Status**: 503 Service Unavailable

#### 2. CES Fails (Core Transfers Disabled)
- **Condition**: CES fails when Core Transfers feature flag is disabled
- **Response**: `BffException.serviceUnavailable("Unable to retrieve activities from any service")`
- **HTTP Status**: 503 Service Unavailable
- **Behavior**: Exception thrown since no fallback service is available

### Error Handling Implementation

```java
// ActivityServiceV1.java - Error handling logic
// Check if both services failed OR if CES failed and Core Transfers is disabled
if ((cesCallFailed && coreTransfersCallFailed) || (cesCallFailed && !coreTransfersEnabled)) {
    log.error("Unable to retrieve activities from any available service. CES failed: {}, Core Transfers enabled: {}, Core Transfers failed: {}",
              cesCallFailed, coreTransfersEnabled, coreTransfersCallFailed);
    throw BffException.serviceUnavailable("Unable to retrieve activities from any service");
}

// Handle CES failure but Core Transfers success
if (cesCallFailed && coreTransfersAvailable) {
    return handleCoreTransfersOnly(coreTransfersActivities, recentLimit, upcomingLimit);
}

// Handle Core Transfers failure (CES succeeded)
if (coreTransfersEnabled && coreTransfersCallFailed) {
    bffGetActivityResponse.getRetrievalErrors().add(RetrievalErrors.RETRIEVAL_ERROR_CORE_TRANSFERS);
    bffGetActivityResponse.setStatus(PARTIAL_SUCCESS);
}
```

### Thread Interruption Handling

Each async call properly handles thread interruption:

```java
try {
    clientActivity = cesActivityFuture.get();
} catch (InterruptedException ie) {
    cesCallFailed = true;
    Thread.currentThread().interrupt(); // Restore interrupted status
    log.warn("Thread interrupted while retrieving CES activities", ie);
} catch (Exception ex) {
    cesCallFailed = true;
    log.error("Error retrieving CES activities", ex);
}
```

## Activity Merging Strategy

### Merging Logic

1. **CES Activities**: Processed first and categorized into recent, upcoming, and recurring
2. **Core Transfers Activities**: Always added to recent activities regardless of status
3. **Merge Operation**: Core Transfers activities are appended to the recent activities list

```java
// Merge Core Transfers activities if available
if (coreTransfersAvailable) {
    List<BffActivity> recentActivities = new ArrayList<>(bffGetActivityResponse.getRecentActivities());
    // All core transfers are immediate transfers and go to recentActivities regardless of status
    recentActivities.addAll(coreTransfersActivities);
    bffGetActivityResponse.setRecentActivities(getRecentList(recentActivities));
}
```

### Activity Limits

Activity limits (`recentLimit`, `upcomingLimit`) are applied after merging:
- Limits are enforced on the final merged list
- Truncation flags are set if limits are exceeded
- Core Transfers activities are included in limit calculations

## Testing Considerations

### Test Coverage Requirements

Based on the implementation, comprehensive test coverage should include:

1. **Feature Flag Scenarios**:
   - Core Transfers enabled vs disabled
   - All combinations of service success/failure

2. **Parallel Execution Tests**:
   - All three services succeed
   - Various failure combinations
   - Thread interruption handling

3. **Mapping Tests**:
   - All Core Transfers status values
   - Account details integration
   - Edge cases (null responses, empty lists)

4. **Error Handling Tests**:
   - Service unavailable scenarios
   - Partial success scenarios
   - Exception propagation

### Key Test Patterns

```java
@Test
void getActivity_WhenBothServicesFailAndCoreTransfersEnabled_ShouldThrowException() {
    // Given
    when(featureFlagService.isFeatureEnabled(CORE_TRANSFERS_TDS_ENABLED)).thenReturn(true);
    when(activityServiceAsync.getActivityAsync())
        .thenThrow(new RuntimeException("CES service unavailable"));
    when(coreTransfersActivityService.getCoreTransfersActivitiesAsync())
        .thenReturn(mockFailingCoreTransfersActivitiesAsync());

    // When & Then
    BffException exception = assertThrows(BffException.class,
        () -> activityServiceV1.getActivity(null, null));
    assertEquals("Unable to retrieve activities from any service", exception.getStatusReason());
}
```

## Configuration

### Feature Flag Configuration

```json
// ld-flags.json
{
  "flagValues": {
    "Digital-Move-Money-CoreTransfers-TDS-Enabled": false
  }
}
```

### Service Configuration

```yaml
# application.yml
feign:
  client:
    config:
      core-transfer-tds:
        url: http://localhost:3000
```

## Recent Changes and Evolution

### PR #31 - Initial Core Transfers Activity Integration
- Added Core Transfers activity retrieval functionality
- Introduced parallel async execution
- Implemented activity mapping from Core Transfers to BFF format

### PR #32 - Improvements and Refinements
- Enhanced status mapping logic (PENDING → Completed)
- Improved error handling for partial success scenarios
- Refined parallel execution with better exception handling
- Added comprehensive test coverage

### Recent Fix - Corrected Error Handling Logic
- **Issue**: When CES failed and Core Transfers was disabled, the system returned an empty response instead of throwing an exception
- **Fix**: Updated error handling logic to throw `BffException.serviceUnavailable()` when CES fails and Core Transfers is disabled, since no fallback service is available
- **Impact**: Ensures consistent error behavior - exceptions are thrown when no services are available to retrieve activities

## Error Handling Matrix

| CES Status | Core Transfers Enabled | Core Transfers Status | Result |
|------------|----------------------|---------------------|---------|
| ✅ Success | ✅ Enabled | ✅ Success | **Full Success** |
| ✅ Success | ✅ Enabled | ❌ Failed | **Partial Success** (CES only) |
| ❌ Failed | ✅ Enabled | ✅ Success | **Partial Success** (Core Transfers only) |
| ❌ Failed | ✅ Enabled | ❌ Failed | **Exception Thrown** |
| ✅ Success | ❌ Disabled | N/A | **Full Success** (CES only) |
| ❌ Failed | ❌ Disabled | N/A | **Exception Thrown** ← **Fixed** |

### Matrix Key Points:

- **Full Success**: Both services succeed or only available service succeeds
- **Partial Success**: One service succeeds, other fails (returns `PARTIAL_SUCCESS` status with retrieval errors)
- **Exception Thrown**: No services available to retrieve activities (throws `BffException.serviceUnavailable()`)
- **Fixed Row**: The last row was corrected to throw an exception instead of returning empty response when CES fails and Core Transfers is disabled

## Summary

The Core Transfers integration provides a robust, scalable solution for retrieving immediate transfer activities from multiple sources. Key benefits include:

- **Parallel Execution**: Improved performance through concurrent service calls
- **Graceful Degradation**: System continues to function if one service fails
- **Backward Compatibility**: Existing CES functionality remains unchanged
- **Feature Flag Control**: Safe rollout and rollback capabilities
- **Comprehensive Error Handling**: Clear distinction between full success, partial success, and failure scenarios

The implementation follows established patterns in the codebase and maintains consistency with existing error handling and response structures.
