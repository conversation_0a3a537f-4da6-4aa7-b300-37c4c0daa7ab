{"version": 1, "collections": [{"id": "3d1cd8ec-f6a2-4869-9e93-5be8a3d7641e", "uid": "0-3d1cd8ec-f6a2-4869-9e93-5be8a3d7641e", "name": "Authentication Collection", "description": null, "auth": null, "events": null, "variables": [{"key": "auth_flows", "value": "", "disabled": false}, {"key": "auth_code", "value": "", "disabled": false}, {"key": "flow_id", "value": "", "disabled": false}, {"key": "code_challenge", "value": "", "disabled": false}, {"key": "auth_token", "value": "", "disabled": false}, {"key": "loginFlowId", "value": "", "disabled": false}, {"key": "login_jwt", "value": "", "disabled": false}, {"key": "risk_token", "value": "", "disabled": false}], "order": ["fdff9e96-017c-4ecb-83d2-4678aaf03e70", "27dfa64b-68ae-4372-9768-7484ac9e38f8", "e25ae5fb-d05e-40be-a5e9-e246b9e81cd3", "644e4640-9b0d-469b-8696-3eb582eb945a", "ecb7ba5d-6813-40e4-94a5-e60996d2d23f", "54036fe2-5a97-403a-bcef-f18a928521c6", "e92a53ce-e003-410f-a834-155d30dff92a", "b032faff-1503-4d9b-8b95-b2db50603394"], "folders_order": [], "protocolProfileBehavior": {}, "createdAt": "2023-01-13T18:55:34.233Z", "folders": [], "requests": [{"id": "27dfa64b-68ae-4372-9768-7484ac9e38f8", "uid": "0-27dfa64b-68ae-4372-9768-7484ac9e38f8", "name": "SIT Logout", "url": "https://np.sso.53.com/idp/startSLO.ping", "description": null, "data": null, "dataOptions": null, "dataMode": null, "headerData": [{"key": "X-XSRF-Header", "value": "test", "type": "text"}, {"key": "Content-Type", "value": "application/x-www-form-urlencoded", "type": "text"}, {"key": "Authorization", "value": "Basic TW9iaWxlX0FuZHJvaWQ6", "type": "default"}], "method": "GET", "pathVariableData": [], "queryParams": [], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "events": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "id": "a27c2b56-d6d1-4b00-9171-7ef3a74e519f"}}, {"listen": "test", "script": {"exec": [""], "type": "text/javascript", "id": "00ecdd46-b772-4743-ba65-d85b833530c3"}}], "folder": null, "responses_order": [], "protocolProfileBehavior": {"disabledSystemHeaders": {"content-type": true}}, "preRequestScript": null, "tests": null, "currentHelper": null, "helperAttributes": null, "collectionId": "3d1cd8ec-f6a2-4869-9e93-5be8a3d7641e", "headers": "X-XSRF-Header: test\nContent-Type: application/x-www-form-urlencoded\nAuthorization: Basic TW9iaWxlX0FuZHJvaWQ6\n", "pathVariables": {}}, {"id": "644e4640-9b0d-469b-8696-3eb582eb945a", "uid": "0-644e4640-9b0d-469b-8696-3eb582eb945a", "name": "Prod Logout", "url": "https://sso.53.com/idp/startSLO.ping", "description": null, "data": null, "dataOptions": null, "dataMode": null, "headerData": [{"key": "X-XSRF-Header", "value": "test", "type": "text"}, {"key": "Content-Type", "value": "application/x-www-form-urlencoded", "type": "text"}, {"key": "Authorization", "value": "Basic TW9iaWxlX0FuZHJvaWQ6", "type": "default"}], "method": "GET", "pathVariableData": [], "queryParams": [], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "events": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "id": "636f6764-4704-470d-b9ac-cf59dba29aa3"}}, {"listen": "test", "script": {"exec": [""], "type": "text/javascript", "id": "8c1e4ac2-eb3d-4e11-bbcc-ad640a781d16"}}], "folder": null, "responses_order": [], "protocolProfileBehavior": {"disabledSystemHeaders": {"content-type": true}}, "preRequestScript": null, "tests": null, "currentHelper": null, "helperAttributes": null, "collectionId": "3d1cd8ec-f6a2-4869-9e93-5be8a3d7641e", "headers": "X-XSRF-Header: test\nContent-Type: application/x-www-form-urlencoded\nAuthorization: Basic TW9iaWxlX0FuZHJvaWQ6\n", "pathVariables": {}}, {"id": "e25ae5fb-d05e-40be-a5e9-e246b9e81cd3", "uid": "0-e25ae5fb-d05e-40be-a5e9-e246b9e81cd3", "name": "PROD Ping <PERSON>", "url": "https://sso.53.com/as/token.oauth2", "description": null, "data": [{"key": "code", "value": "{{auth_code}}", "type": "text"}, {"key": "grant_type", "value": "authorization_code", "type": "text"}, {"key": "code_verifier", "value": "{{code_challenge}}", "type": "text"}], "dataOptions": {"raw": {"language": "text"}}, "dataMode": "u<PERSON><PERSON><PERSON>", "headerData": [{"key": "X-XSRF-Header", "value": "test", "type": "text"}, {"key": "Content-Type", "value": "application/x-www-form-urlencoded", "type": "text"}, {"key": "Authorization", "value": "{{client_credentials}}", "type": "text"}, {"key": "X-Device-UID", "value": "{{device_uuid}}", "type": "text"}, {"key": "X-Device-Fingerprint", "value": "version=1", "type": "text"}], "method": "POST", "pathVariableData": [], "queryParams": [], "auth": null, "events": [{"listen": "prerequest", "script": {"exec": ["var uuid = require('uuid');", "var myUUID = uuid.v4();", "pm.environment.set(\"device_uuid\",myUUID)", "", "let username = \"\"", "let password = \"\"", "", "", "let client_id = 'Mobile_Android'; //pm.variables.get('client_id')", "let code_challenge = 'yMtNV4HnHMcdSe5PHr22-9uEJ4CJmeRThMzHWeUAeps'", "pm.collectionVariables.set('code_challenge', code_challenge);", "", "", "let requestUrl = `https://sso.53.com/as/authorization.oauth2?client_id=${client_id}&code_challenge=${code_challenge}&scope=openid&redirect_uri=http://localhost:8080?response_type=code&response_mode=pi.flow&code_challenge=S256&response_type=code`", "", "", "pm.sendRequest({", "    method: 'GET',", "    url: requestUrl,", "    header: {", "        'X-XSRF-Header': 'test',", "        'X-Device-UID': myUUID,", "        'X-Device-Fingerprint': 'version=1'", "    }", "}, function (err, res) {", "", "    if (res.json().preconditions != undefined){", "        pm.collectionVariables.set(\"login_jwt\", res.json().preconditions[0].access_token.tokenValue);", "        console.log(\"WARNING USING OLD SESSION FOR AUTH\")", "        riskAnalyize();", "    }else{", "        ", "        authFlow = res.json()._links.checkUsernamePassword.href", "        pm.collectionVariables.set('loginFlowId', res.json().id);", "", "        pm.sendRequest({", "            method:'POST',", "            url: authFlow+\"?Content-Type=application/vnd.pingidentity.checkUsernamePassword+json\",", "            header: {", "                'Content-Type': 'application/vnd.pingidentity.checkUsernamePassword+json',", "                'X-XSRF-Header': 'test',", "                'X-Device-UID': myUUID,", "                'X-Device-Fingerprint': 'version=1'", "", "            },", "            body: {", "                mode: 'raw',", "                raw: JSON.stringify({", "                    \"password\":password,", "                    \"rememberMyUsername\":false,", "                    \"thisIsMyDevice\":false,", "                    \"username\":username", "                })", "            }", "        }, function(err, res) { ", "            pm.collectionVariables.set(\"login_jwt\", res.json().preconditions[0].access_token.tokenValue);", "            riskAnalyize();", "        })", "    }", "})", "", "function riskAnalyize(){", "    console.log(\"risk analyse\");", "    riskAnalyzeUrl=\"https://api.consumer-mobile.nube.53.com/risk-bff/risk/limited/analyze\"", "    let jwt=pm.collectionVariables.get(\"login_jwt\");", "    let [jwtHeader,jwtPayload,jwtSignature]=jwt.split('.');", "    let jwtPayloadJsonString=atob(jwtPayload);", "    let jwtPayloadJson=JSON.parse(jwtPayloadJsonString);", "", "    let encodedBody =btoa(", "    JSON.stringify({\"directoryId\":jwtPayloadJson.directoryId,\"timestamp\":jwtPayloadJson.exp}));", "", "    pm.sendRequest(analyzeRiskCall(riskAnalyzeUrl,encodedBody,jwt), function(err, res) { ", "        riskToken = res.json().riskToken;", "        pm.collectionVariables.set(\"risk_token\", res.json().riskToken);", "", "        sendRiskUrl = \"https://sso.53.com/as/authorization.oauth2?scope=openid&response_mode=pi.flow&response_type=code&code_challenge=\"+code_challenge+\"&client_id=\"+client_id+\"&redirect_uri=fitb://callback\"", "", "        pm.sendRequest(sendRiskCall(sendRiskUrl,myUUID,riskToken), function (err, res) {", "            pm.collectionVariables.set(\"auth_code\", res.json().authorizeResponse.code);", "        })", "    })", "}", "", "", "function sendRiskCall(sendRiskUrl,myUUID,riskToken){", "    return {", "    method: 'GET',", "    url: sendRiskUrl,", "    header: {", "        'X-XSRF-Header': 'test',", "        'X-Device-UID': myUUID,", "        'X-Device-Fingerprint': 'version=1',", "        'X-Risk-Authorization': riskToken", "    }", "    };", "}", "function analyzeRiskCall(riskAnalyzeUrl,encodedBody, jwt){", "    return {", "        method:'POST',", "        url: riskAnalyzeUrl,", "        header: {", "            'Content-Type': 'application/json',", "            'X-XSRF-Header': 'test',", "            'X-Device-UID': myUUID,", "            'X-Device-Fingerprint': 'version=1',", "            'Authorization': 'Bearer '+jwt", "", "        },", "        ", "        body: {", "            mode: 'raw',", "            raw: JSON.stringify({", "                \"path\":\"/pf-ws/authn/flows/\"+pm.collectionVariables.get('loginFlowId'), ", "                \"method\":\"POST\", ", "                \"body\":<PERSON><PERSON><PERSON>, ", "                \"orgname\":\"53com\"", "            })", "        }", "    }", "}", ""], "type": "text/javascript", "id": "80a6f0ae-4188-4d62-a998-f5cb8faadcb6"}}, {"listen": "test", "script": {"exec": [" pm.test(\"access_token received\", function () {", "    pm.expect(pm.response.json()).ownProperty('access_token');", " })", "", " pm.globals.set(\"auth_token\", pm.response.json().access_token);"], "type": "text/javascript", "id": "7075ab9e-5c90-4e6c-aae2-d5bbdc104125"}}], "folder": null, "responses_order": [], "protocolProfileBehavior": {"disabledSystemHeaders": {"content-type": true}}, "preRequestScript": "var uuid = require('uuid');\nvar myUUID = uuid.v4();\npm.environment.set(\"device_uuid\",myUUID)\n\nlet username = \"\"\nlet password = \"\"\n\n\nlet client_id = 'Mobile_Android'; //pm.variables.get('client_id')\nlet code_challenge = 'yMtNV4HnHMcdSe5PHr22-9uEJ4CJmeRThMzHWeUAeps'\npm.collectionVariables.set('code_challenge', code_challenge);\n\n\nlet requestUrl = `https://sso.53.com/as/authorization.oauth2?client_id=${client_id}&code_challenge=${code_challenge}&scope=openid&redirect_uri=http://localhost:8080?response_type=code&response_mode=pi.flow&code_challenge=S256&response_type=code`\n\n\npm.sendRequest({\n    method: 'GET',\n    url: requestUrl,\n    header: {\n        'X-XSRF-Header': 'test',\n        'X-Device-UID': myUUID,\n        'X-Device-Fingerprint': 'version=1'\n    }\n}, function (err, res) {\n\n    if (res.json().preconditions != undefined){\n        pm.collectionVariables.set(\"login_jwt\", res.json().preconditions[0].access_token.tokenValue);\n        console.log(\"WARNING USING OLD SESSION FOR AUTH\")\n        riskAnalyize();\n    }else{\n        \n        authFlow = res.json()._links.checkUsernamePassword.href\n        pm.collectionVariables.set('loginFlowId', res.json().id);\n\n        pm.sendRequest({\n            method:'POST',\n            url: authFlow+\"?Content-Type=application/vnd.pingidentity.checkUsernamePassword+json\",\n            header: {\n                'Content-Type': 'application/vnd.pingidentity.checkUsernamePassword+json',\n                'X-XSRF-Header': 'test',\n                'X-Device-UID': myUUID,\n                'X-Device-Fingerprint': 'version=1'\n\n            },\n            body: {\n                mode: 'raw',\n                raw: JSON.stringify({\n                    \"password\":password,\n                    \"rememberMyUsername\":false,\n                    \"thisIsMyDevice\":false,\n                    \"username\":username\n                })\n            }\n        }, function(err, res) { \n            pm.collectionVariables.set(\"login_jwt\", res.json().preconditions[0].access_token.tokenValue);\n            riskAnalyize();\n        })\n    }\n})\n\nfunction riskAnalyize(){\n    console.log(\"risk analyse\");\n    riskAnalyzeUrl=\"https://api.consumer-mobile.nube.53.com/risk-bff/risk/limited/analyze\"\n    let jwt=pm.collectionVariables.get(\"login_jwt\");\n    let [jwtHeader,jwtPayload,jwtSignature]=jwt.split('.');\n    let jwtPayloadJsonString=atob(jwtPayload);\n    let jwtPayloadJson=JSON.parse(jwtPayloadJsonString);\n\n    let encodedBody =btoa(\n    JSON.stringify({\"directoryId\":jwtPayloadJson.directoryId,\"timestamp\":jwtPayloadJson.exp}));\n\n    pm.sendRequest(analyzeRiskCall(riskAnalyzeUrl,encodedBody,jwt), function(err, res) { \n        riskToken = res.json().riskToken;\n        pm.collectionVariables.set(\"risk_token\", res.json().riskToken);\n\n        sendRiskUrl = \"https://sso.53.com/as/authorization.oauth2?scope=openid&response_mode=pi.flow&response_type=code&code_challenge=\"+code_challenge+\"&client_id=\"+client_id+\"&redirect_uri=fitb://callback\"\n\n        pm.sendRequest(sendRiskCall(sendRiskUrl,myUUID,riskToken), function (err, res) {\n            pm.collectionVariables.set(\"auth_code\", res.json().authorizeResponse.code);\n        })\n    })\n}\n\n\nfunction sendRiskCall(sendRiskUrl,myUUID,riskToken){\n    return {\n    method: 'GET',\n    url: sendRiskUrl,\n    header: {\n        'X-XSRF-Header': 'test',\n        'X-Device-UID': myUUID,\n        'X-Device-Fingerprint': 'version=1',\n        'X-Risk-Authorization': riskToken\n    }\n    };\n}\nfunction analyzeRiskCall(riskAnalyzeUrl,encodedBody, jwt){\n    return {\n        method:'POST',\n        url: riskAnalyzeUrl,\n        header: {\n            'Content-Type': 'application/json',\n            'X-XSRF-Header': 'test',\n            'X-Device-UID': myUUID,\n            'X-Device-Fingerprint': 'version=1',\n            'Authorization': 'Bearer '+jwt\n\n        },\n        \n        body: {\n            mode: 'raw',\n            raw: JSON.stringify({\n                \"path\":\"/pf-ws/authn/flows/\"+pm.collectionVariables.get('loginFlowId'), \n                \"method\":\"POST\", \n                \"body\":encodedBody, \n                \"orgname\":\"53com\"\n            })\n        }\n    }\n}\n", "tests": " pm.test(\"access_token received\", function () {\n    pm.expect(pm.response.json()).ownProperty('access_token');\n })\n\n pm.globals.set(\"auth_token\", pm.response.json().access_token);", "currentHelper": null, "helperAttributes": null, "collectionId": "3d1cd8ec-f6a2-4869-9e93-5be8a3d7641e", "headers": "X-XSRF-Header: test\nContent-Type: application/x-www-form-urlencoded\nAuthorization: {{client_credentials}}\nX-Device-UID: {{device_uuid}}\nX-Device-Fingerprint: version=1\n", "pathVariables": {}}, {"id": "e92a53ce-e003-410f-a834-155d30dff92a", "uid": "0-e92a53ce-e003-410f-a834-155d30dff92a", "name": "DEV", "url": "", "description": null, "data": null, "dataOptions": null, "dataMode": null, "headerData": [], "method": "GET", "pathVariableData": [], "queryParams": [], "auth": {"type": "oauth2", "oauth2": [{"key": "useBrowser", "value": true, "type": "boolean"}, {"key": "authUrl", "value": "https://consumer-mobile-dev.auth.us-east-2.amazoncognito.com/oauth2/authorize", "type": "string"}, {"key": "redirect_uri", "value": "fitb://callback", "type": "string"}, {"key": "grant_type", "value": "authorization_code_with_pkce", "type": "string"}, {"key": "clientSecret", "value": "bajldfvpgomtka73c3lagg4g2u2tr0ootua3hdee2dfpptqah42", "type": "string"}, {"key": "clientId", "value": "**************************", "type": "string"}, {"key": "accessTokenUrl", "value": "https://consumer-mobile-dev.auth.us-east-2.amazoncognito.com/oauth2/token", "type": "string"}, {"key": "scope", "value": "openid", "type": "string"}, {"key": "tokenName", "value": "access_token", "type": "string"}, {"key": "addTokenTo", "value": "header", "type": "string"}]}, "events": null, "folder": null, "responses_order": [], "currentHelper": "oAuth2", "helperAttributes": {"id": "oAuth2", "addTokenTo": "header", "authUrl": "https://consumer-mobile-dev.auth.us-east-2.amazoncognito.com/oauth2/authorize", "accessTokenUrl": "https://consumer-mobile-dev.auth.us-east-2.amazoncognito.com/oauth2/token", "clientId": "**************************", "clientSecret": "bajldfvpgomtka73c3lagg4g2u2tr0ootua3hdee2dfpptqah42", "scope": "openid"}, "collectionId": "3d1cd8ec-f6a2-4869-9e93-5be8a3d7641e", "headers": "", "pathVariables": {}}, {"id": "ecb7ba5d-6813-40e4-94a5-e60996d2d23f", "uid": "0-ecb7ba5d-6813-40e4-94a5-e60996d2d23f", "name": "DEV Token ( Cognito )", "url": "https://api.consumer-mobile-dev.nube.53.com/mock-idp/api/as/token.oauth2", "description": null, "data": [], "dataOptions": {"raw": {"language": "json"}}, "dataMode": "raw", "headerData": [], "method": "POST", "pathVariableData": [], "queryParams": [], "auth": null, "events": [{"listen": "prerequest", "script": {"exec": ["pm.collectionVariables.set('flow_id', 'postmanflow')", "pm.sendRequest({", "    method: 'POST',", "    url: `https://api.consumer-mobile-dev.nube.53.com/mock-idp/api/pf-ws/authn/flows/${pm.collectionVariables.get('flow_id')}`,", "    body: {", "        mode: 'raw',", "        raw: JSON.stringify({username: 'homer.simpson', password: 'Springfield1!'})", "    }", "}, function (err, res) {", "    if (err) {", "        console.log(err);", "    }", "    pm.collectionVariables.set('auth_code', res.json().authorizeResponse.code);", "})"], "type": "text/javascript", "id": "7de6e5b0-626e-4086-99ce-f5cfd96a893b"}}, {"listen": "test", "script": {"exec": ["pm.globals.set('auth_token', pm.response.json().access_token);"], "type": "text/javascript", "id": "25a923e9-95d1-4a5a-972e-01e346301e2b"}}], "folder": null, "responses_order": [], "preRequestScript": "pm.collectionVariables.set('flow_id', 'postmanflow')\npm.sendRequest({\n    method: 'POST',\n    url: `https://api.consumer-mobile-dev.nube.53.com/mock-idp/api/pf-ws/authn/flows/${pm.collectionVariables.get('flow_id')}`,\n    body: {\n        mode: 'raw',\n        raw: JSON.stringify({username: 'homer.simpson', password: 'Springfield1!'})\n    }\n}, function (err, res) {\n    if (err) {\n        console.log(err);\n    }\n    pm.collectionVariables.set('auth_code', res.json().authorizeResponse.code);\n})", "tests": "pm.globals.set('auth_token', pm.response.json().access_token);", "currentHelper": null, "helperAttributes": null, "collectionId": "3d1cd8ec-f6a2-4869-9e93-5be8a3d7641e", "rawModeData": "{\n    \"code\": \"{{auth_code}}\"\n}", "headers": "", "pathVariables": {}}, {"id": "fdff9e96-017c-4ecb-83d2-4678aaf03e70", "uid": "0-fdff9e96-017c-4ecb-83d2-4678aaf03e70", "name": "SIT Ping Token", "url": "https://np.sso.53.com/as/token.oauth2", "description": null, "data": [{"key": "code", "value": "{{auth_code}}", "type": "default"}, {"key": "grant_type", "value": "authorization_code", "type": "default"}, {"key": "code_verifier", "value": "{{code_challenge}}", "type": "default"}, {"key": "client_id", "value": "Mobile_Android", "type": "default"}, {"key": "redirect_uri", "value": "fitb://callback", "type": "default"}], "dataOptions": {"raw": {"language": "text"}}, "dataMode": "u<PERSON><PERSON><PERSON>", "headerData": [{"key": "X-XSRF-Header", "value": "test", "type": "text"}, {"key": "Content-Type", "value": "application/x-www-form-urlencoded", "type": "text"}, {"key": "Authorization", "value": "Basic TW9iaWxlX0FuZHJvaWQ6", "type": "text"}, {"key": "X-Device-UID", "value": "{{device_uuid}}", "type": "text"}, {"key": "X-Device-Fingerprint", "value": "version=1", "type": "text"}, {"key": "", "value": "", "type": "default", "enabled": false}], "method": "POST", "pathVariableData": [], "queryParams": [], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "events": [{"listen": "prerequest", "script": {"exec": ["var uuid = require('uuid');", "var myUUID = uuid.v4();", "pm.environment.set(\"device_uuid\",myUUID)", "", "let username = \"dava<PERSON><PERSON><PERSON>\"", "let password = \"Test1234\"", "", "", "let client_id = 'Mobile_Android'; //pm.variables.get('client_id')", "let code_challenge = 'yMtNV4HnHMcdSe5PHr22-9uEJ4CJmeRThMzHWeUAeps'", "pm.collectionVariables.set('code_challenge', code_challenge);", "", "", "let requestUrl = `https://np.sso.53.com/as/authorization.oauth2?client_id=${client_id}&code_challenge=${code_challenge}&scope=openid&redirect_uri=http://localhost:8080?response_type=code&response_mode=pi.flow&code_challenge=S256&response_type=code`", "", "", "pm.sendRequest({", "    method: 'GET',", "    url: requestUrl,", "    header: {", "        'X-XSRF-Header': 'test',", "        'X-Device-UID': myUUID,", "        'X-Device-Fingerprint': 'version=1'", "    }", "}, function (err, res) {", "", "    if (res.json().preconditions != undefined){", "        pm.collectionVariables.set(\"login_jwt\", res.json().preconditions[0].access_token.tokenValue);", "        console.log(\"WARNING USING OLD SESSION FOR AUTH\")", "        riskAnalyize();", "    }else{", "        ", "        authFlow = res.json()._links.checkUsernamePassword.href", "        pm.collectionVariables.set('loginFlowId', res.json().id);", "", "        pm.sendRequest({", "            method:'POST',", "            url: authFlow+\"?Content-Type=application/vnd.pingidentity.checkUsernamePassword+json\",", "            header: {", "                'Content-Type': 'application/vnd.pingidentity.checkUsernamePassword+json',", "                'X-XSRF-Header': 'test',", "                'X-Device-UID': myUUID,", "                'X-Device-Fingerprint': 'version=1'", "", "            },", "            body: {", "                mode: 'raw',", "                raw: JSON.stringify({", "                    \"password\":password,", "                    \"rememberMyUsername\":false,", "                    \"thisIsMyDevice\":false,", "                    \"username\":username", "                })", "            }", "        }, function(err, res) { ", "            pm.collectionVariables.set(\"login_jwt\", res.json().preconditions[0].access_token.tokenValue);", "            riskAnalyize();", "        })", "    }", "})", "", "function riskAnalyize(){", "    console.log(\"risk analyse\");", "    riskAnalyzeUrl=\"https://api.consumermobile-sit.nube.53.com/risk-bff/risk/limited/analyze\"", "    let jwt=pm.collectionVariables.get(\"login_jwt\");", "    let [jwtHeader,jwtPayload,jwtSignature]=jwt.split('.');", "    let jwtPayloadJsonString=atob(jwtPayload);", "    let jwtPayloadJson=JSON.parse(jwtPayloadJsonString);", "", "    let encodedBody =btoa(", "    JSON.stringify({\"directoryId\":jwtPayloadJson.directoryId,\"timestamp\":jwtPayloadJson.exp}));", "", "    pm.sendRequest(analyzeRiskCall(riskAnalyzeUrl,encodedBody,jwt), function(err, res) { ", "        riskToken = res.json().riskToken;", "        pm.collectionVariables.set(\"risk_token\", res.json().riskToken);", "", "        sendRiskUrl = \"https://np.sso.53.com/as/authorization.oauth2?scope=openid&response_mode=pi.flow&response_type=code&code_challenge=\"+code_challenge+\"&client_id=\"+client_id+\"&redirect_uri=fitb://callback\"", "", "        pm.sendRequest(sendRiskCall(sendRiskUrl,myUUID,riskToken), function (err, res) {", "            pm.collectionVariables.set(\"auth_code\", res.json().authorizeResponse.code);", "        })", "    })", "}", "", "", "function sendRiskCall(sendRiskUrl,myUUID,riskToken){", "    return {", "    method: 'GET',", "    url: sendRiskUrl,", "    header: {", "        'X-XSRF-Header': 'test',", "        'X-Device-UID': myUUID,", "        'X-Device-Fingerprint': 'version=1',", "        'X-Risk-Authorization': riskToken", "    }", "    };", "}", "function analyzeRiskCall(riskAnalyzeUrl,encodedBody, jwt){", "    return {", "        method:'POST',", "        url: riskAnalyzeUrl,", "        header: {", "            'Content-Type': 'application/json',", "            'X-XSRF-Header': 'test',", "            'X-Device-UID': myUUID,", "            'X-Device-Fingerprint': 'version=1',", "            'Authorization': 'Bearer '+jwt", "", "        },", "        ", "        body: {", "            mode: 'raw',", "            raw: JSON.stringify({", "                \"path\":\"/pf-ws/authn/flows/\"+pm.collectionVariables.get('loginFlowId'), ", "                \"method\":\"POST\", ", "                \"body\":<PERSON><PERSON><PERSON>, ", "                \"orgname\":\"53com\"", "            })", "        }", "    }", "}", ""], "type": "text/javascript", "id": "34387de3-5ed4-4ed4-b93a-8f225b17cdfd"}}, {"listen": "test", "script": {"exec": [" pm.test(\"access_token received\", function () {", "    pm.expect(pm.response.json()).ownProperty('access_token');", " })", " ", "pm.globals.set(\"auth_token\", pm.response.json().access_token);"], "type": "text/javascript", "id": "4026248a-9a11-4462-b46a-5b48c4fba74b"}}], "folder": null, "responses_order": [], "protocolProfileBehavior": {"disabledSystemHeaders": {"content-type": true}}, "preRequestScript": "var uuid = require('uuid');\nvar myUUID = uuid.v4();\npm.environment.set(\"device_uuid\",myUUID)\n\nlet username = \"davajclay\"\nlet password = \"Test1234\"\n\n\nlet client_id = 'Mobile_Android'; //pm.variables.get('client_id')\nlet code_challenge = 'yMtNV4HnHMcdSe5PHr22-9uEJ4CJmeRThMzHWeUAeps'\npm.collectionVariables.set('code_challenge', code_challenge);\n\n\nlet requestUrl = `https://np.sso.53.com/as/authorization.oauth2?client_id=${client_id}&code_challenge=${code_challenge}&scope=openid&redirect_uri=http://localhost:8080?response_type=code&response_mode=pi.flow&code_challenge=S256&response_type=code`\n\n\npm.sendRequest({\n    method: 'GET',\n    url: requestUrl,\n    header: {\n        'X-XSRF-Header': 'test',\n        'X-Device-UID': myUUID,\n        'X-Device-Fingerprint': 'version=1'\n    }\n}, function (err, res) {\n\n    if (res.json().preconditions != undefined){\n        pm.collectionVariables.set(\"login_jwt\", res.json().preconditions[0].access_token.tokenValue);\n        console.log(\"WARNING USING OLD SESSION FOR AUTH\")\n        riskAnalyize();\n    }else{\n        \n        authFlow = res.json()._links.checkUsernamePassword.href\n        pm.collectionVariables.set('loginFlowId', res.json().id);\n\n        pm.sendRequest({\n            method:'POST',\n            url: authFlow+\"?Content-Type=application/vnd.pingidentity.checkUsernamePassword+json\",\n            header: {\n                'Content-Type': 'application/vnd.pingidentity.checkUsernamePassword+json',\n                'X-XSRF-Header': 'test',\n                'X-Device-UID': myUUID,\n                'X-Device-Fingerprint': 'version=1'\n\n            },\n            body: {\n                mode: 'raw',\n                raw: JSON.stringify({\n                    \"password\":password,\n                    \"rememberMyUsername\":false,\n                    \"thisIsMyDevice\":false,\n                    \"username\":username\n                })\n            }\n        }, function(err, res) { \n            pm.collectionVariables.set(\"login_jwt\", res.json().preconditions[0].access_token.tokenValue);\n            riskAnalyize();\n        })\n    }\n})\n\nfunction riskAnalyize(){\n    console.log(\"risk analyse\");\n    riskAnalyzeUrl=\"https://api.consumermobile-sit.nube.53.com/risk-bff/risk/limited/analyze\"\n    let jwt=pm.collectionVariables.get(\"login_jwt\");\n    let [jwtHeader,jwtPayload,jwtSignature]=jwt.split('.');\n    let jwtPayloadJsonString=atob(jwtPayload);\n    let jwtPayloadJson=JSON.parse(jwtPayloadJsonString);\n\n    let encodedBody =btoa(\n    JSON.stringify({\"directoryId\":jwtPayloadJson.directoryId,\"timestamp\":jwtPayloadJson.exp}));\n\n    pm.sendRequest(analyzeRiskCall(riskAnalyzeUrl,encodedBody,jwt), function(err, res) { \n        riskToken = res.json().riskToken;\n        pm.collectionVariables.set(\"risk_token\", res.json().riskToken);\n\n        sendRiskUrl = \"https://np.sso.53.com/as/authorization.oauth2?scope=openid&response_mode=pi.flow&response_type=code&code_challenge=\"+code_challenge+\"&client_id=\"+client_id+\"&redirect_uri=fitb://callback\"\n\n        pm.sendRequest(sendRiskCall(sendRiskUrl,myUUID,riskToken), function (err, res) {\n            pm.collectionVariables.set(\"auth_code\", res.json().authorizeResponse.code);\n        })\n    })\n}\n\n\nfunction sendRiskCall(sendRiskUrl,myUUID,riskToken){\n    return {\n    method: 'GET',\n    url: sendRiskUrl,\n    header: {\n        'X-XSRF-Header': 'test',\n        'X-Device-UID': myUUID,\n        'X-Device-Fingerprint': 'version=1',\n        'X-Risk-Authorization': riskToken\n    }\n    };\n}\nfunction analyzeRiskCall(riskAnalyzeUrl,encodedBody, jwt){\n    return {\n        method:'POST',\n        url: riskAnalyzeUrl,\n        header: {\n            'Content-Type': 'application/json',\n            'X-XSRF-Header': 'test',\n            'X-Device-UID': myUUID,\n            'X-Device-Fingerprint': 'version=1',\n            'Authorization': 'Bearer '+jwt\n\n        },\n        \n        body: {\n            mode: 'raw',\n            raw: JSON.stringify({\n                \"path\":\"/pf-ws/authn/flows/\"+pm.collectionVariables.get('loginFlowId'), \n                \"method\":\"POST\", \n                \"body\":encodedBody, \n                \"orgname\":\"53com\"\n            })\n        }\n    }\n}\n", "tests": " pm.test(\"access_token received\", function () {\n    pm.expect(pm.response.json()).ownProperty('access_token');\n })\n \npm.globals.set(\"auth_token\", pm.response.json().access_token);", "currentHelper": null, "helperAttributes": null, "collectionId": "3d1cd8ec-f6a2-4869-9e93-5be8a3d7641e", "headers": "X-XSRF-Header: test\nContent-Type: application/x-www-form-urlencoded\nAuthorization: Basic TW9iaWxlX0FuZHJvaWQ6\nX-Device-UID: {{device_uuid}}\nX-Device-Fingerprint: version=1\n", "pathVariables": {}}]}, {"id": "3dbeb74f-646a-4800-8523-6f9febf32a4a", "uid": "0-3dbeb74f-646a-4800-8523-6f9febf32a4a", "name": "Risk Diomain", "description": null, "auth": null, "events": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""], "id": "c37adac7-c064-4b6b-af35-3ca113b99cc6"}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""], "id": "f5bff56b-2438-4cf4-b137-62fcd5a9da69"}}], "variables": [{"key": "flow_id", "value": "", "disabled": false}, {"key": "auth_code", "value": "", "disabled": false}], "order": ["51d6745c-1554-497b-96d4-ca17c98f1845", "12835cfa-422e-462f-8f31-a13d3d1a8553", "1e8da5fb-7b79-4f96-a634-d412281313d4"], "folders_order": [], "protocolProfileBehavior": {}, "createdAt": "2023-01-13T18:55:34.656Z", "folders": [], "requests": [{"id": "12835cfa-422e-462f-8f31-a13d3d1a8553", "uid": "0-12835cfa-422e-462f-8f31-a13d3d1a8553", "name": "risk-jwks-sit", "url": "https://edc2kagkrc-vpce-04e3fad85e181e1ea.execute-api.us-east-2.amazonaws.com/api/risk-microservice/JWKS", "description": null, "data": null, "dataOptions": null, "dataMode": null, "headerData": [{"key": "scope", "value": "openid", "type": "text"}], "method": "GET", "pathVariableData": [], "queryParams": [], "auth": {"type": "oauth2", "oauth2": [{"key": "grant_type", "value": "client_credentials", "type": "string"}, {"key": "clientSecret", "value": "16e0bipa1lm3noa2v0ep3u780lj2j0p8nj1rosisu35pdnf5rs2k", "type": "string"}, {"key": "clientId", "value": "5sskqqc5uo12rnenmlb6b7fufo", "type": "string"}, {"key": "scope", "value": "UUIDForPII/read", "type": "string"}, {"key": "useBrowser", "value": true, "type": "boolean"}, {"key": "client_authentication", "value": "header", "type": "string"}, {"key": "tokenName", "value": "TokenExchange", "type": "string"}, {"key": "accessTokenUrl", "value": "https://consumer-mobile-dev.auth.us-east-2.amazoncognito.com/oauth2/token", "type": "string"}, {"key": "authUrl", "value": "https://consumer-mobile-dev.auth.us-east-2.amazoncognito.com/oauth2/authorize", "type": "string"}, {"key": "redirect_uri", "value": "http://localhost:8080", "type": "string"}, {"key": "addTokenTo", "value": "header", "type": "string"}]}, "events": null, "folder": null, "responses_order": [], "currentHelper": "oAuth2", "helperAttributes": {"id": "oAuth2", "addTokenTo": "header", "authUrl": "https://consumer-mobile-dev.auth.us-east-2.amazoncognito.com/oauth2/authorize", "accessTokenUrl": "https://consumer-mobile-dev.auth.us-east-2.amazoncognito.com/oauth2/token", "clientId": "5sskqqc5uo12rnenmlb6b7fufo", "clientSecret": "16e0bipa1lm3noa2v0ep3u780lj2j0p8nj1rosisu35pdnf5rs2k", "scope": "UUIDForPII/read"}, "collectionId": "3dbeb74f-646a-4800-8523-6f9febf32a4a", "headers": "scope: openid\n", "pathVariables": {}}, {"id": "1e8da5fb-7b79-4f96-a634-d412281313d4", "uid": "0-1e8da5fb-7b79-4f96-a634-d412281313d4", "name": "analyze risk-sit", "url": "https://edc2kagkrc-vpce-04e3fad85e181e1ea.execute-api.us-east-2.amazonaws.com/api/risk-microservice/risk/analyze", "description": null, "data": [], "dataOptions": {"raw": {"language": "json"}}, "dataMode": "raw", "headerData": [{"key": "scope", "value": "openid", "type": "text"}, {"key": "true-client-ip", "value": "**************", "type": "text"}, {"key": "deviceTokenCookie", "value": "BBAB1471-00E5-420B-9362-D1D5F932FC03", "type": "text"}, {"key": "httpReferrer", "value": " https://onlinebanking.53.com/ib/?device_id=bbab1471-00e5-420b-9362-d1d5f932fc03", "type": "text"}, {"key": "httpAcceptLanguage", "value": "en-US,en;q=0.", "type": "text"}, {"key": "X-Device-Fingerprint", "value": "version=3.5.1_4&pm_fpua=mozilla/5.0 (linux; android 8.1.0; lm-x220pm) applewebkit/537.36 (khtml, like gecko) chrome/89.0.4389.105 mobile safari/537.36|5.0 (Linux; Android 8.1.0; LM-X220PM) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.105 Mobile Safari/537.36|Linux armv7l&pm_fpsc=24|320|569|569&pm_fpsw=&pm_fptz=-5&pm_fpln=lang=en-US|syslang=|userlang=&pm_fpjv=0&pm_fpco=1&pm_fpasw=&pm_fpan=Netscape&pm_fpacn=Mozilla&pm_fpol=true&pm_fposp=&pm_fpup=&pm_fpsaw=320&pm_fpspd=24&pm_fpsbd=&pm_fpsdx=&pm_fpsdy=&pm_fpslx=&pm_fpsly=&pm_fpsfse=&pm_fpsui=&pm_os=Android&pm_brmjv=89&pm_br=Chrome&pm_inpt=&pm_expt=", "type": "text"}, {"key": "X-Device-UID", "value": "BBAB1471-00E5-420B-9362-D1D5F932FC03", "type": "default"}], "method": "POST", "pathVariableData": [], "queryParams": [], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "events": [{"listen": "test", "script": {"id": "d6a94b8e-75fc-4487-9058-fdaa69499603", "exec": [" pm.test(\"riskToken received\", function () {", "    pm.expect(pm.response.json()).ownProperty('riskToken');", " })", " ", "pm.globals.set(\"risk_token\", pm.response.json().riskToken);"], "type": "text/javascript"}}], "folder": null, "responses_order": [], "preRequestScript": null, "tests": " pm.test(\"riskToken received\", function () {\n    pm.expect(pm.response.json()).ownProperty('riskToken');\n })\n \npm.globals.set(\"risk_token\", pm.response.json().riskToken);", "currentHelper": "<PERSON><PERSON><PERSON>", "helperAttributes": {"id": "bearer", "token": "{{auth_token}}"}, "collectionId": "3dbeb74f-646a-4800-8523-6f9febf32a4a", "rawModeData": "{\n\"method\": \"POST\",\n\"path\": \"/move-money-bff/activity\",\n\"orgName\": \"53com\",\n\"body\": \"ewogICAgImFtb3VudCI6IDIuMDAsCiAgICAiZHVlRGF0ZSI6ICIyMDIzLTAxLTE3IiwKICAgICJmcmVxdWVuY3kiOiAiT05FX1RJTUUiLAogICAgImZyb21BY2NvdW50SWQiOiAiOTZkMTMxZDctZmE0NC00ZjM3LWJiMGItNDFlNjVlMDVjMzY3IiwKICAgICJ0b0FjY291bnRJZCI6ICJhMzlmYTU1OS0yYzUzLTQ1NGMtODQzZi0zNDUxZDNhYmE0NTUiLAogICAgImludm9pY2VkUmVjdXJyaW5nUGF5bWVudCI6IGZhbHNlLAogICAgIm1lbW8iOiAiIiwKICAgICJyZXF1ZXN0R3VpZCI6ICIyNjY1MTJiMS1jNjRlLTQyMTktYmEzNi0xYmU5YTVhMjgzOTUiCgp9\"\n}", "headers": "scope: openid\ntrue-client-ip: **************\ndeviceTokenCookie: BBAB1471-00E5-420B-9362-D1D5F932FC03\nhttpReferrer:  https://onlinebanking.53.com/ib/?device_id=bbab1471-00e5-420b-9362-d1d5f932fc03\nhttpAcceptLanguage: en-US,en;q=0.\nX-Device-Fingerprint: version=3.5.1_4&pm_fpua=mozilla/5.0 (linux; android 8.1.0; lm-x220pm) applewebkit/537.36 (khtml, like gecko) chrome/89.0.4389.105 mobile safari/537.36|5.0 (Linux; Android 8.1.0; LM-X220PM) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.105 Mobile Safari/537.36|Linux armv7l&pm_fpsc=24|320|569|569&pm_fpsw=&pm_fptz=-5&pm_fpln=lang=en-US|syslang=|userlang=&pm_fpjv=0&pm_fpco=1&pm_fpasw=&pm_fpan=Netscape&pm_fpacn=Mozilla&pm_fpol=true&pm_fposp=&pm_fpup=&pm_fpsaw=320&pm_fpspd=24&pm_fpsbd=&pm_fpsdx=&pm_fpsdy=&pm_fpslx=&pm_fpsly=&pm_fpsfse=&pm_fpsui=&pm_os=Android&pm_brmjv=89&pm_br=Chrome&pm_inpt=&pm_expt=\nX-Device-UID: BBAB1471-00E5-420B-9362-D1D5F932FC03\n", "pathVariables": {}}, {"id": "51d6745c-1554-497b-96d4-ca17c98f1845", "uid": "0-51d6745c-1554-497b-96d4-ca17c98f1845", "name": "Requires-risk-sit", "url": "https://edc2kagkrc-vpce-04e3fad85e181e1ea.execute-api.us-east-2.amazonaws.com/api/risk-microservice/requiresRisk", "description": null, "data": null, "dataOptions": null, "dataMode": null, "headerData": [{"key": "scope", "value": "openid", "type": "text"}], "method": "GET", "pathVariableData": [], "queryParams": [], "auth": {"type": "oauth2", "oauth2": [{"key": "grant_type", "value": "client_credentials", "type": "string"}, {"key": "clientSecret", "value": "16e0bipa1lm3noa2v0ep3u780lj2j0p8nj1rosisu35pdnf5rs2k", "type": "string"}, {"key": "clientId", "value": "5sskqqc5uo12rnenmlb6b7fufo", "type": "string"}, {"key": "scope", "value": "UUIDForPII/read", "type": "string"}, {"key": "useBrowser", "value": true, "type": "boolean"}, {"key": "client_authentication", "value": "header", "type": "string"}, {"key": "tokenName", "value": "TokenExchange", "type": "string"}, {"key": "accessTokenUrl", "value": "https://consumer-mobile-dev.auth.us-east-2.amazoncognito.com/oauth2/token", "type": "string"}, {"key": "authUrl", "value": "https://consumer-mobile-dev.auth.us-east-2.amazoncognito.com/oauth2/authorize", "type": "string"}, {"key": "redirect_uri", "value": "http://localhost:8080", "type": "string"}, {"key": "addTokenTo", "value": "header", "type": "string"}]}, "events": null, "folder": null, "responses_order": [], "currentHelper": "oAuth2", "helperAttributes": {"id": "oAuth2", "addTokenTo": "header", "authUrl": "https://consumer-mobile-dev.auth.us-east-2.amazoncognito.com/oauth2/authorize", "accessTokenUrl": "https://consumer-mobile-dev.auth.us-east-2.amazoncognito.com/oauth2/token", "clientId": "5sskqqc5uo12rnenmlb6b7fufo", "clientSecret": "16e0bipa1lm3noa2v0ep3u780lj2j0p8nj1rosisu35pdnf5rs2k", "scope": "UUIDForPII/read"}, "collectionId": "3dbeb74f-646a-4800-8523-6f9febf32a4a", "headers": "scope: openid\n", "pathVariables": {}}]}, {"id": "6f72a114-c577-4d6c-aa4f-c8f7a47fed5d", "uid": "0-6f72a114-c577-4d6c-aa4f-c8f7a47fed5d", "name": "MoveMoney-Service", "description": null, "auth": {"type": "oauth2", "oauth2": [{"key": "scope", "value": "openid", "type": "string"}, {"key": "clientSecret", "value": "bajldfvpgomtka73c3lagg4g2u2tr0ootua3hdee2dfpptqah42", "type": "string"}, {"key": "clientId", "value": "**************************", "type": "string"}, {"key": "accessTokenUrl", "value": "https://consumer-mobile-dev.auth.us-east-2.amazoncognito.com/oauth2/token", "type": "string"}, {"key": "authUrl", "value": "https://consumer-mobile-dev.auth.us-east-2.amazoncognito.com/oauth2/authorize", "type": "string"}, {"key": "tokenName", "value": "access_token", "type": "string"}, {"key": "useBrowser", "value": true, "type": "boolean"}, {"key": "addTokenTo", "value": "header", "type": "string"}]}, "events": [{"listen": "prerequest", "script": {"id": "17c3fca6-b62f-40f6-9da1-3b6061f249c0", "type": "text/javascript", "exec": ["Object.prototype.analyzeRiskScoreIst = function(pm, path)", "{", "    data = pm.request.body;", "    body = data[data.mode];", "    base64Body = CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(body));", "        ", "    result = pm.sendRequest( ", "        {", "            method: 'POST',", "            url: \"https://edc2kagkrc-vpce-04e3fad85e181e1ea.execute-api.us-east-2.amazonaws.com/api/risk-microservice/risk/analyze\",", "            header: {", "                'Content-Type': 'application/json',", "                    'X-XSRF-Header': 'test',", "                    'X-Device-UID': \"BBAB1471-00E5-420B-9362-D1D5F932FC03\",", "                    'X-Device-Fingerprint': 'version=1',", "                    'Authorization': 'Bearer '+pm.variables.get(\"auth_token\")", "            },", "            body: {", "", "                        mode: 'raw',", "", "                        raw: JSON.stringify({", "                            \"method\": pm.request.method,", "                            \"path\": path,", "                            \"orgName\": \"53com\",", "                            \"body\": base64Body", "                        })", "            }", "        }", "        , (error, response) => { ", "            if(error){ console.log(\"Error getting risk_token: \" + error); }", "            else if(response.status === \"Unauthorized\"  || typeof(response.json().riskToken)===\"undefined\"){ console.log(\"Risk Token not set. Is your auth token current?\")}", "            else{", "                //console.log(response.status)", "                pm.variables.set(\"risk_token\", response.json().riskToken);", "                console.log(\"risk_token set\");", "            }", "    });", "}", "", "Object.prototype.getTransferInfo = function(pm){", "    result = pm.sendRequest( ", "        {", "            method: 'GET',", "            url: \"https://api.consumermobile-sit.nube.53.com:443/move-money-bff/transfer/info\",", "            header: {", "                'Content-Type': 'application/json',", "                    'X-XSRF-Header': 'test',", "                    'X-Device-UID': \"BBAB1471-00E5-420B-9362-D1D5F932FC03\",", "                    'X-Device-Fingerprint': 'version=1',", "                    'Authorization': 'Bearer '+pm.variables.get(\"auth_token\")", "            }", "        }", "        , (error, response) => { ", "            if(error){ console.log(\"Error getting transfer info: \" + error); }", "            else if(response.status === \"Unauthorized\"  || typeof(response.json().riskToken)===\"undefined\"){ console.log(\"Risk Token not set. Is your auth token current?\")}", "            else{", "                console.log(\"transfer info received\");", "            }", "    });", "}", ""]}}, {"listen": "test", "script": {"id": "ae745cd0-b580-4b43-ac99-0ed3ab567572", "type": "text/javascript", "exec": [""]}}], "variables": [], "order": ["aa490a63-691d-466d-82e9-0fe57cfefcee", "77f381f8-aa16-47ba-9ab6-1e49194289a0", "0879a1b3-884f-4424-925b-b0906a06b670", "3ac2e4bc-568c-4df7-ab9d-30903e92e835", "241e88e4-5eea-4ff4-ae79-4698c1a450b6", "6eea816e-969c-45e7-9312-4b78b59e81ae", "eb27c583-451c-4a39-a1ac-292d58aa7254", "11442b1c-d432-4846-a008-9c65423fefad", "7f321560-fd2f-4eff-afe0-16ced4fbdb02", "ac43ca70-eebb-4a95-9fc3-4cdeacee4891", "f6d21b50-f493-47ee-bce4-25c07afdcdc6", "789c6750-4a83-4b03-8e54-43ce88692961", "63e63933-aa8d-4341-8b4f-d771a60b3c75", "8b315c10-63c4-4fbb-9e59-2fbfcf97cc5f", "31a33018-08f8-47a1-9fc7-fea63f0b124d", "3746242c-738b-480c-9933-539b0a050c98", "5800245b-**************-6f974feb70f2", "bb912d9b-2cdd-4d5d-80a8-31127be43560", "b8a7fe9d-fbf5-4e2d-b233-7b3657cef80d", "ec169a83-d6f4-4405-8396-f41236c2f995", "143631cc-fc75-43f4-8117-e8e862e785d1", "327aa5b5-17d8-4515-9b41-e94e9036827d", "31679f94-458f-462b-801d-6855c7c1aa13", "e37be14e-ce61-4a03-b793-33b5771a778f", "6b20cb39-dfea-4822-a7e7-dcb64cd021cf", "cc6e4d38-1cf2-4d21-b467-9ab0c89ff59e", "2a271d2f-3376-464f-aa27-99abfc1af016", "09fe6967-8ef4-4679-9ae3-13a5a6e18554", "8ed32633-de0c-4afb-ab18-e8da7ae2e747"], "folders_order": [], "protocolProfileBehavior": {}, "createdAt": "2023-01-13T18:55:34.544Z", "folders": [], "requests": [{"id": "0879a1b3-884f-4424-925b-b0906a06b670", "uid": "0-0879a1b3-884f-4424-925b-b0906a06b670", "name": "myadvance", "url": "{{protocol}}://{{host}}:{{port}}{{servletcontext}}{{context}}/myadvance", "description": "Demonstrates the mock-oauth2 flow for local development.  This allows us to easily define security expectations in integration tests for what scopes the requestor has without using a full-fledged oauth2 provider.  This is controlled from the client via headers.\n\nPlease note the 'scopes' and 'userId' headers", "data": [], "dataOptions": {"raw": {"language": "json"}}, "dataMode": "raw", "headerData": [{"key": "scope", "value": "openid", "type": "text"}, {"key": "sub", "value": "{{mockUserID}}", "type": "text"}, {"key": "X-Risk-Authorization", "value": "{{risk_token}}", "type": "text"}, {"key": "x-device-uid", "value": "asdfasdfasdf", "type": "text"}, {"key": "x-client-version", "value": "4.0", "type": "text"}, {"key": "x-platform-version", "value": "9.3.1", "type": "text"}, {"key": "x-platform", "value": "Apple", "type": "text"}, {"key": "x-api-key", "value": "postman", "type": "text"}], "method": "POST", "pathVariableData": [], "queryParams": [], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "events": [{"listen": "test", "script": {"id": "9bf6b823-7520-4ad9-92d6-a9370d84f12c", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"id": "27641810-96b8-43cf-a04c-984a64a22b45", "exec": ["path = `/move-money-bff/myadvance`;", "_.analyzeRiskScoreIst(pm, path);"], "type": "text/javascript"}}], "folder": null, "responses_order": [], "preRequestScript": "path = `/move-money-bff/myadvance`;\n_.analyzeRiskScoreIst(pm, path);", "tests": "pm.test(\"Status code is 200\", function () {\n    pm.response.to.have.status(200);\n});", "currentHelper": "<PERSON><PERSON><PERSON>", "helperAttributes": {"id": "bearer", "token": "{{auth_token}}"}, "collectionId": "6f72a114-c577-4d6c-aa4f-c8f7a47fed5d", "rawModeData": "{\n    \"fromAccountId\": \"e625a8df-7afd-4701-8135-aa238ff13f00\",\n    \"toAccountId\": \"96d131d7-fa44-4f37-bb0b-41e65e05c367\",\n    \"amount\": 178.00\n}", "headers": "scope: openid\nsub: {{mockUserID}}\nX-Risk-Authorization: {{risk_token}}\nx-device-uid: asdfasdfasdf\nx-client-version: 4.0\nx-platform-version: 9.3.1\nx-platform: Apple\nx-api-key: postman\n", "pathVariables": {}}, {"id": "09fe6967-8ef4-4679-9ae3-13a5a6e18554", "uid": "0-09fe6967-8ef4-4679-9ae3-13a5a6e18554", "name": "external/accounts", "url": "{{protocol}}://{{host}}:{{port}}{{servletcontext}}{{context}}/external/accounts", "description": "Demonstrates the mock-oauth2 flow for local development.  This allows us to easily define security expectations in integration tests for what scopes the requestor has without using a full-fledged oauth2 provider.  This is controlled from the client via headers.\n\nPlease note the 'scopes' and 'userId' headers", "data": null, "dataOptions": null, "dataMode": null, "headerData": [{"key": "scope", "type": "text", "value": "openid"}, {"key": "sub", "type": "text", "value": "{{mockUserID}}"}, {"key": "x-client-version", "value": "4.0", "type": "text"}, {"key": "x-device-uid", "value": "asdfasdfasdf", "type": "text"}, {"key": "X-Risk-Authorization", "value": "eyJrdHkiOiJSU0EiLCJraWQiOiIxN2E0ZGJhOS0zMjczLTRhNjYtODFjYi1iMDg3YWFiM2M4NDgiLCJhbGciOiJSUzUxMiJ9.************************************************************************************************************************************************************************************************************************************************************************************.enFMyC_6MMkLoPEotn0Te9onlVYWwXXoU8PiuYd-LMkBQLpDQ8PWZEt5ao4xgHz5GA74OVKgc7HOr4p8lpVGJFh32ORnlf4ODBPDW3saM7nR2bYs-s2UMxJWuUwWm3iqs5F0mRujLcKbmQUKxAxqLtrAPSVIfVztM-vtis4C3ZrIzYHNOqzqC4xY6hC_v7FCALvKvEI3o8ZNqwsmMkS0VCxyiIDFI93_DivsehEg-Veu3SC5vH9KHD4DrmUidpqLDyX1BUdRMNbNW5hpst9WtFpBtHZkdjwMB6lxxNi0ofjJAwdsytWTKpzo9bH7qyQCYE6NjAJhEoJ2NkZrU_NINsbyWCykyZdaNygLnxBtR02VtEeC4BPKtxJAHLZMg7G7I0iIOXZnqZKm6gz4R4KqCMQCv9i_yE2xM0Elv01BA58sK2t2eM--2501eG6fbzkHGeoyEsO66gARHik4Q88D9aKU4zH-mNJj2jhhFyTbDoRmWvgzu2iQhKoCmQdjMZQGddOwJK9VxGC7IPv6XrPT-c2E1eqswPCkkW5A2OfzaBU0E0cAND3lUTZSsOoJG-dYOA-sMs4CM-YdMTEifbHgoMKARAp98eZohGOKAmnB07eVIDm2FszOVOdEvCQ15nTQEPHLxOzL0zsK9J1Z8RLooKMxuhKIzd3xxmujQUpn87E", "type": "text"}, {"key": "x-platform-version", "value": "9.3.1", "type": "text"}, {"key": "x-platform", "value": "Apple", "type": "text"}, {"key": "x-api-key", "value": "postman", "type": "text"}], "method": "GET", "pathVariableData": [], "queryParams": [], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "events": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript", "id": "5a59827c-5bd0-4ff2-aab9-d33d5ce73c38"}}, {"listen": "prerequest", "script": {"exec": ["if(! pm.variables.get(\"mockUserID\")){", "    // generate a new, pseudo-random UUID", "    var uuid  = require('uuid');", "    pm.variables.set(\"mockUserID\", uuid.v4()); ", "    console.log(\"mockUserID set to: \"+pm.variables.get(\"mockUserID\"))", "}"], "type": "text/javascript", "id": "6bcc7029-9d05-421b-af99-c92293b2e9ba"}}], "folder": null, "responses_order": [], "preRequestScript": "if(! pm.variables.get(\"mockUserID\")){\n    // generate a new, pseudo-random UUID\n    var uuid  = require('uuid');\n    pm.variables.set(\"mockUserID\", uuid.v4()); \n    console.log(\"mockUserID set to: \"+pm.variables.get(\"mockUserID\"))\n}", "tests": "pm.test(\"Status code is 200\", function () {\n    pm.response.to.have.status(200);\n});", "currentHelper": "<PERSON><PERSON><PERSON>", "helperAttributes": {"id": "bearer", "token": "{{auth_token}}"}, "collectionId": "6f72a114-c577-4d6c-aa4f-c8f7a47fed5d", "headers": "scope: openid\nsub: {{mockUserID}}\nx-client-version: 4.0\nx-device-uid: asdfasdfasdf\nX-Risk-Authorization: eyJrdHkiOiJSU0EiLCJraWQiOiIxN2E0ZGJhOS0zMjczLTRhNjYtODFjYi1iMDg3YWFiM2M4NDgiLCJhbGciOiJSUzUxMiJ9.************************************************************************************************************************************************************************************************************************************************************************************.enFMyC_6MMkLoPEotn0Te9onlVYWwXXoU8PiuYd-LMkBQLpDQ8PWZEt5ao4xgHz5GA74OVKgc7HOr4p8lpVGJFh32ORnlf4ODBPDW3saM7nR2bYs-s2UMxJWuUwWm3iqs5F0mRujLcKbmQUKxAxqLtrAPSVIfVztM-vtis4C3ZrIzYHNOqzqC4xY6hC_v7FCALvKvEI3o8ZNqwsmMkS0VCxyiIDFI93_DivsehEg-Veu3SC5vH9KHD4DrmUidpqLDyX1BUdRMNbNW5hpst9WtFpBtHZkdjwMB6lxxNi0ofjJAwdsytWTKpzo9bH7qyQCYE6NjAJhEoJ2NkZrU_NINsbyWCykyZdaNygLnxBtR02VtEeC4BPKtxJAHLZMg7G7I0iIOXZnqZKm6gz4R4KqCMQCv9i_yE2xM0Elv01BA58sK2t2eM--2501eG6fbzkHGeoyEsO66gARHik4Q88D9aKU4zH-mNJj2jhhFyTbDoRmWvgzu2iQhKoCmQdjMZQGddOwJK9VxGC7IPv6XrPT-c2E1eqswPCkkW5A2OfzaBU0E0cAND3lUTZSsOoJG-dYOA-sMs4CM-YdMTEifbHgoMKARAp98eZohGOKAmnB07eVIDm2FszOVOdEvCQ15nTQEPHLxOzL0zsK9J1Z8RLooKMxuhKIzd3xxmujQUpn87E\nx-platform-version: 9.3.1\nx-platform: Apple\nx-api-key: postman\n", "pathVariables": {}}, {"id": "11442b1c-d432-4846-a008-9c65423fefad", "uid": "0-11442b1c-d432-4846-a008-9c65423fefad", "name": "activity", "url": "{{protocol}}://{{host}}:{{port}}{{servletcontext}}{{context}}/activity", "description": "Demonstrates the mock-oauth2 flow for local development.  This allows us to easily define security expectations in integration tests for what scopes the requestor has without using a full-fledged oauth2 provider.  This is controlled from the client via headers.\n\nPlease note the 'scopes' and 'userId' headers", "data": null, "dataOptions": null, "dataMode": null, "headerData": [{"key": "scope", "type": "text", "value": "openid"}, {"key": "sub", "type": "text", "value": "{{mockUserID}}", "enabled": false}, {"key": "X-Risk-Authorization", "value": "eyJrdHkiOiJSU0EiLCJraWQiOiIxN2E0ZGJhOS0zMjczLTRhNjYtODFjYi1iMDg3YWFiM2M4NDgiLCJhbGciOiJSUzUxMiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.hux2LBpGYspnuc-zuDcCFtPr4p8h5zrVlHEpCIIb8ykWWafS2RAxLnetTSlIfzW6qvHag2zgppoYiWRxxl5-dwGxijrfbP0tj9oYHhP8vkvZrpgmbHuIbVWWtWsmbUwn9RJmF4HHTq9Pk1KYOFwjCqCKM_zl-5S6h9GSZuZ_H5n2cGnX5K0QplGGgrcXpLwRYpmfy8mLXCp0DEaRHrho49LyHVjHM3pdfZQzKWJnCwnD5vKg_jBe6K110yUrWYWa-ZJQ7qgOZCPdRnpCP3MjtXBGw5qShpf6rigxTwBbh_R-lmpkSXyUjWX9STMCTeA_bjKJelZ2Ul9fZ0GwEk8_mmnrVM80aimJw39J3tVkj-RANklMI0-ooisl9bsdsyXdp7wbQqYhe02BdxntVvyTmcOCJXTiLofhIvqLpHi9c8hbGHdapuUSLhGQc_NNjQAZaJoBWDUICxv4qxHg1jziEgocY2QnFDGEK67XVhGJcY8id_xpjay2as1AvQrJ_s8YJcDJQmLHUUfX9M0rK8DTLWkEGyk7JeSuuCWmpYQA_0-uFg_ETexiBgYicAXWzkm8BJzGYH7g7LZxjM7d3n4nvLtuZi2RGRr6q4LOq61azkPk0aeRaLM_ygGW1i1nHdjyTc8V9UX8uIc5A9RLcD3KUEgOqkERqhNlbCLTpvgLiv", "type": "text", "enabled": false}, {"key": "x-device-uid", "value": "asdfasdfasdf", "type": "text"}, {"key": "x-forwarded-for", "value": "127.0.0.1", "type": "text"}, {"key": "x-api-key", "value": "postman", "type": "text", "enabled": false}, {"key": "x-client-version", "value": "4.0", "type": "text"}, {"key": "x-platform-version", "value": "9.3.1", "type": "text"}, {"key": "x-platform", "value": "Apple", "type": "text"}], "method": "GET", "pathVariableData": [], "queryParams": [], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "events": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript", "id": "ee408033-2229-491c-bb89-071b611a0c50"}}, {"listen": "prerequest", "script": {"exec": ["if(! pm.variables.get(\"mockUserID\")){", "    // generate a new, pseudo-random UUID", "    var uuid  = require('uuid');", "    pm.variables.set(\"mockUserID\", uuid.v4()); ", "    console.log(\"mockUserID set to: \"+pm.variables.get(\"mockUserID\"))", "}"], "type": "text/javascript", "id": "0e1599ac-3252-44a1-a7c8-80eeac41d869"}}], "folder": null, "responses_order": [], "preRequestScript": "if(! pm.variables.get(\"mockUserID\")){\n    // generate a new, pseudo-random UUID\n    var uuid  = require('uuid');\n    pm.variables.set(\"mockUserID\", uuid.v4()); \n    console.log(\"mockUserID set to: \"+pm.variables.get(\"mockUserID\"))\n}", "tests": "pm.test(\"Status code is 200\", function () {\n    pm.response.to.have.status(200);\n});", "currentHelper": "<PERSON><PERSON><PERSON>", "helperAttributes": {"id": "bearer", "token": "{{auth_token}}"}, "collectionId": "6f72a114-c577-4d6c-aa4f-c8f7a47fed5d", "headers": "scope: openid\n//sub: {{mockUserID}}\n//X-Risk-Authorization: eyJrdHkiOiJSU0EiLCJraWQiOiIxN2E0ZGJhOS0zMjczLTRhNjYtODFjYi1iMDg3YWFiM2M4NDgiLCJhbGciOiJSUzUxMiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.hux2LBpGYspnuc-zuDcCFtPr4p8h5zrVlHEpCIIb8ykWWafS2RAxLnetTSlIfzW6qvHag2zgppoYiWRxxl5-dwGxijrfbP0tj9oYHhP8vkvZrpgmbHuIbVWWtWsmbUwn9RJmF4HHTq9Pk1KYOFwjCqCKM_zl-5S6h9GSZuZ_H5n2cGnX5K0QplGGgrcXpLwRYpmfy8mLXCp0DEaRHrho49LyHVjHM3pdfZQzKWJnCwnD5vKg_jBe6K110yUrWYWa-ZJQ7qgOZCPdRnpCP3MjtXBGw5qShpf6rigxTwBbh_R-lmpkSXyUjWX9STMCTeA_bjKJelZ2Ul9fZ0GwEk8_mmnrVM80aimJw39J3tVkj-RANklMI0-ooisl9bsdsyXdp7wbQqYhe02BdxntVvyTmcOCJXTiLofhIvqLpHi9c8hbGHdapuUSLhGQc_NNjQAZaJoBWDUICxv4qxHg1jziEgocY2QnFDGEK67XVhGJcY8id_xpjay2as1AvQrJ_s8YJcDJQmLHUUfX9M0rK8DTLWkEGyk7JeSuuCWmpYQA_0-uFg_ETexiBgYicAXWzkm8BJzGYH7g7LZxjM7d3n4nvLtuZi2RGRr6q4LOq61azkPk0aeRaLM_ygGW1i1nHdjyTc8V9UX8uIc5A9RLcD3KUEgOqkERqhNlbCLTpvgLiv\nx-device-uid: asdfasdfasdf\nx-forwarded-for: 127.0.0.1\n//x-api-key: postman\nx-client-version: 4.0\nx-platform-version: 9.3.1\nx-platform: Apple\n", "pathVariables": {}}, {"id": "143631cc-fc75-43f4-8117-e8e862e785d1", "uid": "0-143631cc-fc75-43f4-8117-e8e862e785d1", "name": "activity-details", "url": "{{protocol}}://{{host}}:{{port}}{{servletcontext}}{{context}}/activity/details?activityId=********&accountId=B2D66490-72A9-8D91-E26F-70817805EC22", "description": "Demonstrates the mock-oauth2 flow for local development.  This allows us to easily define security expectations in integration tests for what scopes the requestor has without using a full-fledged oauth2 provider.  This is controlled from the client via headers.\n\nPlease note the 'scopes' and 'userId' headers", "data": null, "dataOptions": null, "dataMode": null, "headerData": [{"key": "scope", "type": "text", "value": "openid"}, {"key": "sub", "type": "text", "value": "{{mockUserID}}"}], "method": "GET", "pathVariableData": [], "queryParams": [{"key": "activityId", "value": "********"}, {"key": "accountId", "value": "B2D66490-72A9-8D91-E26F-70817805EC22"}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJSUzI1NiIsImtpZCI6IjUwTmxJUkxtLVY4NVFQQ1VZMjQwWF9rUnliRSIsInBpLmF0bSI6Im51eWoifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.cx34BXvyZwepsTgoL_G7R94oV8ZSUGxM4Za7qZN1Qf-59ehtX-4YoSM3Ur9oxtIf7C71Od889g9JkRK-V7I79YzPEHsBYvr7EbnqhejhR9o_Z-l-WdPfVMTWZA71Lc5PXn2SxXqu2kmPHCHMkstvMmO8fRzahQqGv0jmMv4ksSa8P6DA2FZZNueEhQ-HCr9haXPlQJXCcJa-dMz6a5v8UzpI0iABT7GiJeFyM5i5fxOJWhsrTqTeY_V_bymi2zEORaTKlzOGIuBHUxJuC08t0L9ZxmCnUOcojgXAf4Uu1iYCJxVPuFApDuU1dGrvXwZkNH_u1z2IhPbkGCirmCoS-w", "type": "string"}]}, "events": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript", "id": "37b851dd-0629-40e3-bab8-885f897b3b2a"}}, {"listen": "prerequest", "script": {"exec": ["if(! pm.variables.get(\"mockUserID\")){", "    // generate a new, pseudo-random UUID", "    var uuid  = require('uuid');", "    pm.variables.set(\"mockUserID\", uuid.v4()); ", "    console.log(\"mockUserID set to: \"+pm.variables.get(\"mockUserID\"))", "}"], "type": "text/javascript", "id": "b346305c-2424-441d-ad44-f89bd5c24d1c"}}], "folder": null, "responses_order": [], "preRequestScript": "if(! pm.variables.get(\"mockUserID\")){\n    // generate a new, pseudo-random UUID\n    var uuid  = require('uuid');\n    pm.variables.set(\"mockUserID\", uuid.v4()); \n    console.log(\"mockUserID set to: \"+pm.variables.get(\"mockUserID\"))\n}", "tests": "pm.test(\"Status code is 200\", function () {\n    pm.response.to.have.status(200);\n});", "currentHelper": "<PERSON><PERSON><PERSON>", "helperAttributes": {"id": "bearer", "token": "eyJhbGciOiJSUzI1NiIsImtpZCI6IjUwTmxJUkxtLVY4NVFQQ1VZMjQwWF9rUnliRSIsInBpLmF0bSI6Im51eWoifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.cx34BXvyZwepsTgoL_G7R94oV8ZSUGxM4Za7qZN1Qf-59ehtX-4YoSM3Ur9oxtIf7C71Od889g9JkRK-V7I79YzPEHsBYvr7EbnqhejhR9o_Z-l-WdPfVMTWZA71Lc5PXn2SxXqu2kmPHCHMkstvMmO8fRzahQqGv0jmMv4ksSa8P6DA2FZZNueEhQ-HCr9haXPlQJXCcJa-dMz6a5v8UzpI0iABT7GiJeFyM5i5fxOJWhsrTqTeY_V_bymi2zEORaTKlzOGIuBHUxJuC08t0L9ZxmCnUOcojgXAf4Uu1iYCJxVPuFApDuU1dGrvXwZkNH_u1z2IhPbkGCirmCoS-w"}, "collectionId": "6f72a114-c577-4d6c-aa4f-c8f7a47fed5d", "headers": "scope: openid\nsub: {{mockUserID}}\n", "pathVariables": {}}, {"id": "241e88e4-5eea-4ff4-ae79-4698c1a450b6", "uid": "0-241e88e4-5eea-4ff4-ae79-4698c1a450b6", "name": "externaltransfer/initiateTrialDeposits Copy", "url": "{{protocol}}://{{host}}:{{port}}{{servletcontext}}{{context}}/externaltransfer/initiateTrialDeposits", "description": "Demonstrates the mock-oauth2 flow for local development.  This allows us to easily define security expectations in integration tests for what scopes the requestor has without using a full-fledged oauth2 provider.  This is controlled from the client via headers.\n\nPlease note the 'scopes' and 'userId' headers", "data": [], "dataOptions": {"raw": {"language": "json"}}, "dataMode": "raw", "headerData": [{"key": "scope", "type": "text", "value": "openid"}, {"key": "sub", "type": "text", "value": "{{mockUserID}}"}, {"key": "x-device-uid", "value": "asdfasdfasdf", "type": "text"}, {"key": "x-client-version", "value": "3.0", "type": "text"}, {"key": "x-risk-authorization", "value": "{{risk_token}}", "type": "text"}], "method": "POST", "pathVariableData": [], "queryParams": [], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "events": [{"listen": "test", "script": {"id": "ff9b1abe-a1e3-4ed4-8cf0-34576dd90d70", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"id": "0f7c8449-0114-43cb-82f2-e5702081c202", "exec": ["path=`/move-money-bff/externaltransfer/initiateTrialDeposits`", "_.analyzeRiskScoreIst(pm, path);"], "type": "text/javascript"}}], "folder": null, "responses_order": [], "preRequestScript": "path=`/move-money-bff/externaltransfer/initiateTrialDeposits`\n_.analyzeRiskScoreIst(pm, path);", "tests": "pm.test(\"Status code is 200\", function () {\n    pm.response.to.have.status(200);\n});", "currentHelper": "<PERSON><PERSON><PERSON>", "helperAttributes": {"id": "bearer", "token": "{{auth_token}}"}, "collectionId": "6f72a114-c577-4d6c-aa4f-c8f7a47fed5d", "rawModeData": "{\n    \"accountId\": \"972F469D-A143-CB8E-3D65-DB516F2FA3FD\",\n    \"brokerageName\": \"Ameriprise Financial Brokerage\"\n}", "headers": "scope: openid\nsub: {{mockUserID}}\nx-device-uid: asdfasdfasdf\nx-client-version: 3.0\nx-risk-authorization: {{risk_token}}\n", "pathVariables": {}}, {"id": "2a271d2f-3376-464f-aa27-99abfc1af016", "uid": "0-2a271d2f-3376-464f-aa27-99abfc1af016", "name": "externaltransfer/bankInfo-brokerage", "url": "{{protocol}}://{{host}}:{{port}}{{servletcontext}}{{context}}/externaltransfer/bankInfo?brokerageName=Ameriprise%20Financial%20Brokerage", "description": "Demonstrates the mock-oauth2 flow for local development.  This allows us to easily define security expectations in integration tests for what scopes the requestor has without using a full-fledged oauth2 provider.  This is controlled from the client via headers.\n\nPlease note the 'scopes' and 'userId' headers", "data": null, "dataOptions": null, "dataMode": null, "headerData": [{"key": "scope", "type": "text", "value": "openid"}, {"key": "sub", "type": "text", "value": "{{mockUserID}}"}, {"key": "x-device-uid", "value": "asdfasdfasdf", "type": "text"}, {"key": "x-client-version", "value": "3.0", "type": "text"}], "method": "GET", "pathVariableData": [], "queryParams": [{"key": "brokerageName", "value": "Ameriprise%20Financial%20Brokerage"}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJSUzI1NiIsImtpZCI6IlFac0xydXlkWEdPQ01qaGk4OXdldGlKa25aVSIsInBpLmF0bSI6Im51eWoifQ.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.kkfAXlwjeH3Hvyrw8DPqKAkjdwbeG_I2wAxY1EH8JhkoXDV185QxGQLavHcMTNn1PzFcC-uxurGyWJ9nNO0CmDpdIvzJvPHiMQj4rAEzRdEz0-s1tqBaNtskHLYp3dL5mdByvDXzr_00-klTLcQe571CIUBPZ7ZB3_Hy6B7oHj-da5TPCMNmcxvgg2s6Ws-OSewDxC3qRUIuy580sTNc_px4i_8KiufkljdyW5_YhzlppD0sPNkiJxRE_WvlIZHrFHPenjaaPfHGhccz8US648ZsCkPA4AAIDnaajLZS0OHApLXGcqfoZS_czrh7NHE9c_unde-jie8Xu4EVwumj-Q", "type": "string"}]}, "events": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript", "id": "aae00eb9-c6ef-42a0-b602-6873623031b8"}}, {"listen": "prerequest", "script": {"exec": ["if(! pm.variables.get(\"mockUserID\")){", "    // generate a new, pseudo-random UUID", "    var uuid  = require('uuid');", "    pm.variables.set(\"mockUserID\", uuid.v4()); ", "    console.log(\"mockUserID set to: \"+pm.variables.get(\"mockUserID\"))", "}"], "type": "text/javascript", "id": "ab4fe912-760e-46ec-98c5-1c4cbf50dc5d"}}], "folder": null, "responses_order": [], "preRequestScript": "if(! pm.variables.get(\"mockUserID\")){\n    // generate a new, pseudo-random UUID\n    var uuid  = require('uuid');\n    pm.variables.set(\"mockUserID\", uuid.v4()); \n    console.log(\"mockUserID set to: \"+pm.variables.get(\"mockUserID\"))\n}", "tests": "pm.test(\"Status code is 200\", function () {\n    pm.response.to.have.status(200);\n});", "currentHelper": "<PERSON><PERSON><PERSON>", "helperAttributes": {"id": "bearer", "token": "eyJhbGciOiJSUzI1NiIsImtpZCI6IlFac0xydXlkWEdPQ01qaGk4OXdldGlKa25aVSIsInBpLmF0bSI6Im51eWoifQ.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.kkfAXlwjeH3Hvyrw8DPqKAkjdwbeG_I2wAxY1EH8JhkoXDV185QxGQLavHcMTNn1PzFcC-uxurGyWJ9nNO0CmDpdIvzJvPHiMQj4rAEzRdEz0-s1tqBaNtskHLYp3dL5mdByvDXzr_00-klTLcQe571CIUBPZ7ZB3_Hy6B7oHj-da5TPCMNmcxvgg2s6Ws-OSewDxC3qRUIuy580sTNc_px4i_8KiufkljdyW5_YhzlppD0sPNkiJxRE_WvlIZHrFHPenjaaPfHGhccz8US648ZsCkPA4AAIDnaajLZS0OHApLXGcqfoZS_czrh7NHE9c_unde-jie8Xu4EVwumj-Q"}, "collectionId": "6f72a114-c577-4d6c-aa4f-c8f7a47fed5d", "headers": "scope: openid\nsub: {{mockUserID}}\nx-device-uid: asdfasdfasdf\nx-client-version: 3.0\n", "pathVariables": {}}, {"id": "31679f94-458f-462b-801d-6855c7c1aa13", "uid": "0-31679f94-458f-462b-801d-6855c7c1aa13", "name": "logout", "url": "https://api.consumermobile-sit.nube.53.com/sso-bff/auth/logout", "description": "Demonstrates the mock-oauth2 flow for local development.  This allows us to easily define security expectations in integration tests for what scopes the requestor has without using a full-fledged oauth2 provider.  This is controlled from the client via headers.\n\nPlease note the 'scopes' and 'userId' headers", "data": null, "dataOptions": null, "dataMode": null, "headerData": [{"key": "scope", "type": "text", "value": "openid"}, {"key": "sub", "type": "text", "value": "{{mockUserID}}"}, {"key": "Authorization", "value": "Bearer ey<PERSON>hbGciOiJSUzI1NiIsImtpZCI6IjM5STRoOVl5anNRWTlpa0hhcWdOREQ3WHY4byIsInBpLmF0bSI6Im51eWoifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NLQnQV9wbLOC2lZnKBsc7p5KmC6s8cLEQst917RY4gdC0pmewyjKPA3ANBgSvgb36gFcBhdWsIP-useTEwJn28Q_kxdpdgW9j07OwFZZn8dWJ6yYjvY7sNHu9_Jhq0Wf8d9gEajAIvMsBGmkOk8b1ByCNH4l6xx5KmoDenXeKqW6ioQ3yBqk7Kx4RRJZlmqqim_GIPo6lZKe4V9St4i_X6ZHiuv2kjL_pX2XJT0XZ4kPjsJLPhgknW5S3GdNyofxyKOSYP3UIV3WVs6TQa0FQ8n6iY8gfLTvVmZ4rhIdafOemoCKQOlnbc8uhXlhBcjI9ePmMFMa5AVSQ7zULO9o1A", "type": "text", "enabled": false}, {"key": "x-device-uid", "value": "asdfasdfasdf", "type": "text"}, {"key": "x-api-key", "value": "postman", "type": "text", "enabled": false}], "method": "DELETE", "pathVariableData": [], "queryParams": [], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "events": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "id": "80da36e8-1e36-40ce-8a9c-08aec50cd5a3"}}, {"listen": "prerequest", "script": {"exec": ["// if(! pm.variables.get(\"mockUserID\")){", "//     // generate a new, pseudo-random UUID", "//     var uuid  = require('uuid');", "//     pm.variables.set(\"mockUserID\", uuid.v4()); ", "//     console.log(\"mockUserID set to: \"+pm.variables.get(\"mockUserID\"))", "// }"], "type": "text/javascript", "id": "9dabef32-d205-4ae0-a09c-a2ceb8068411"}}], "folder": null, "responses_order": [], "preRequestScript": "// if(! pm.variables.get(\"mockUserID\")){\n//     // generate a new, pseudo-random UUID\n//     var uuid  = require('uuid');\n//     pm.variables.set(\"mockUserID\", uuid.v4()); \n//     console.log(\"mockUserID set to: \"+pm.variables.get(\"mockUserID\"))\n// }", "tests": null, "currentHelper": "<PERSON><PERSON><PERSON>", "helperAttributes": {"id": "bearer", "token": "{{auth_token}}"}, "collectionId": "6f72a114-c577-4d6c-aa4f-c8f7a47fed5d", "headers": "scope: openid\nsub: {{mockUserID}}\n//Authorization: Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6IjM5STRoOVl5anNRWTlpa0hhcWdOREQ3WHY4byIsInBpLmF0bSI6Im51eWoifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NLQnQV9wbLOC2lZnKBsc7p5KmC6s8cLEQst917RY4gdC0pmewyjKPA3ANBgSvgb36gFcBhdWsIP-useTEwJn28Q_kxdpdgW9j07OwFZZn8dWJ6yYjvY7sNHu9_Jhq0Wf8d9gEajAIvMsBGmkOk8b1ByCNH4l6xx5KmoDenXeKqW6ioQ3yBqk7Kx4RRJZlmqqim_GIPo6lZKe4V9St4i_X6ZHiuv2kjL_pX2XJT0XZ4kPjsJLPhgknW5S3GdNyofxyKOSYP3UIV3WVs6TQa0FQ8n6iY8gfLTvVmZ4rhIdafOemoCKQOlnbc8uhXlhBcjI9ePmMFMa5AVSQ7zULO9o1A\nx-device-uid: asdfasdfasdf\n//x-api-key: postman\n", "pathVariables": {}}, {"id": "31a33018-08f8-47a1-9fc7-fea63f0b124d", "uid": "0-31a33018-08f8-47a1-9fc7-fea63f0b124d", "name": "billpay/payee/account", "url": "{{protocol}}://{{host}}:{{port}}{{servletcontext}}{{context}}/billpay/payee/account?payeeId=38A23BBA-D3A5-1C1A-6BF1-7A04CD09274E", "description": "Demonstrates the mock-oauth2 flow for local development.  This allows us to easily define security expectations in integration tests for what scopes the requestor has without using a full-fledged oauth2 provider.  This is controlled from the client via headers.\n\nPlease note the 'scopes' and 'userId' headers", "data": null, "dataOptions": null, "dataMode": null, "headerData": [{"key": "scope", "type": "text", "value": "openid"}, {"key": "sub", "type": "text", "value": "{{mockUserID}}"}, {"key": "x-device-uid", "value": "asdfasdfasdf", "type": "text"}, {"key": "X-Risk-Authorization", "value": "eyJrdHkiOiJSU0EiLCJraWQiOiIxN2E0ZGJhOS0zMjczLTRhNjYtODFjYi1iMDg3YWFiM2M4NDgiLCJhbGciOiJSUzUxMiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.AzWd0ELNvFavUj8-K9jrkc5SH_T9j5_83siq7ObfrBnkyj56u9une49Z3GOzngWinQSnN_Wut1CU0U5GNmWx9QNwHAYXX9n50ct4x7wKfyFopcTSFPMBKotVKEjzcNXYPYYxNfAknbGEV57CfSsDgHV6X4FbXRe7hx4lOQG1efJi-l_o8ajLxHCjkfpWeMSfwVBKHbA8oD_PKTkPYgfHVXRakjw9gjZen38goEz5mmpi4cfFmqjaKF3XVHLBo285c7AkCeVcHZXJhPolBiZo3gQA8d172pk9Ouwr7laJow9V6eLkQFBYJ8jfV1OnSgXkf1hgUWiobf8rixQk8KpRUB9I96L2LlK8lML7QaTX_QPhwiKYCd3g8AwUGRiN_Whdc5Qh-u1nzpOAHMXydD8F4ScYD2UYBpWotWxHarl5QB97X_Zw7VEzj_ONy4JSh2dQ8d43qCcCxpqEUhyQpFX9dvXkO7rJIEIUGVWXqzzqAS6QiloDom0KTsUb7dy2g04EHzJI2oSDMkpWYZlsAvDXx0epdzb0nCVcUE0mmg5Q9az4qP-_MLFZPJsCHH4I6d3AZflhHfvCYxiQGc-DYd1KZUxizG1Ky-evmktWFFRhdWxB6Yz6S2VNRek-GV6sNl9QSBSENYfK4hTbfndo6h9uR4RJMeC4y2IugLR1FxF75nQ", "type": "text"}], "method": "GET", "pathVariableData": [], "queryParams": [{"key": "payeeId", "value": "38A23BBA-D3A5-1C1A-6BF1-7A04CD09274E"}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************TubUXgfn2FNz-ZDKGidvE-Xk5VtnTuQTx27Gw2QlHWfYKPwcipYb7vekGN7mP4Zlc5fIA6_27_yCLpggZ7hliJY3031XN9X-z704D11Im8VSrDDjuz1f6_Azn4zfdqa5kx6LKeLvEUxLNaahn-p0UDXpaEvxIrm2eYrSOIZ3gvcKUsMl9ulAvSx7XTVY6IljPw1poP9BuHZVyrNUbGW4qha-O31xoHihSHGSyvA8qYzKDE4-sfdzGsxUmTixs_LkcT9xPSsDn_XAOTnCk1lig2A0SXuCSkvAHErJhvkfSB6dSC8uUVsGdCw", "type": "string"}]}, "events": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript", "id": "264a783f-df28-489e-96a6-7f91238c9741"}}, {"listen": "prerequest", "script": {"exec": ["if(! pm.variables.get(\"mockUserID\")){", "    // generate a new, pseudo-random UUID", "    var uuid  = require('uuid');", "    pm.variables.set(\"mockUserID\", uuid.v4()); ", "    console.log(\"mockUserID set to: \"+pm.variables.get(\"mockUserID\"))", "}"], "type": "text/javascript", "id": "f4cf09ae-7066-4bc3-ac65-595e3700c79a"}}], "folder": null, "responses_order": [], "preRequestScript": "if(! pm.variables.get(\"mockUserID\")){\n    // generate a new, pseudo-random UUID\n    var uuid  = require('uuid');\n    pm.variables.set(\"mockUserID\", uuid.v4()); \n    console.log(\"mockUserID set to: \"+pm.variables.get(\"mockUserID\"))\n}", "tests": "pm.test(\"Status code is 200\", function () {\n    pm.response.to.have.status(200);\n});", "currentHelper": "<PERSON><PERSON><PERSON>", "helperAttributes": {"id": "bearer", "token": "******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************TubUXgfn2FNz-ZDKGidvE-Xk5VtnTuQTx27Gw2QlHWfYKPwcipYb7vekGN7mP4Zlc5fIA6_27_yCLpggZ7hliJY3031XN9X-z704D11Im8VSrDDjuz1f6_Azn4zfdqa5kx6LKeLvEUxLNaahn-p0UDXpaEvxIrm2eYrSOIZ3gvcKUsMl9ulAvSx7XTVY6IljPw1poP9BuHZVyrNUbGW4qha-O31xoHihSHGSyvA8qYzKDE4-sfdzGsxUmTixs_LkcT9xPSsDn_XAOTnCk1lig2A0SXuCSkvAHErJhvkfSB6dSC8uUVsGdCw"}, "collectionId": "6f72a114-c577-4d6c-aa4f-c8f7a47fed5d", "headers": "scope: openid\nsub: {{mockUserID}}\nx-device-uid: asdfasdfasdf\nX-Risk-Authorization: eyJrdHkiOiJSU0EiLCJraWQiOiIxN2E0ZGJhOS0zMjczLTRhNjYtODFjYi1iMDg3YWFiM2M4NDgiLCJhbGciOiJSUzUxMiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.AzWd0ELNvFavUj8-K9jrkc5SH_T9j5_83siq7ObfrBnkyj56u9une49Z3GOzngWinQSnN_Wut1CU0U5GNmWx9QNwHAYXX9n50ct4x7wKfyFopcTSFPMBKotVKEjzcNXYPYYxNfAknbGEV57CfSsDgHV6X4FbXRe7hx4lOQG1efJi-l_o8ajLxHCjkfpWeMSfwVBKHbA8oD_PKTkPYgfHVXRakjw9gjZen38goEz5mmpi4cfFmqjaKF3XVHLBo285c7AkCeVcHZXJhPolBiZo3gQA8d172pk9Ouwr7laJow9V6eLkQFBYJ8jfV1OnSgXkf1hgUWiobf8rixQk8KpRUB9I96L2LlK8lML7QaTX_QPhwiKYCd3g8AwUGRiN_Whdc5Qh-u1nzpOAHMXydD8F4ScYD2UYBpWotWxHarl5QB97X_Zw7VEzj_ONy4JSh2dQ8d43qCcCxpqEUhyQpFX9dvXkO7rJIEIUGVWXqzzqAS6QiloDom0KTsUb7dy2g04EHzJI2oSDMkpWYZlsAvDXx0epdzb0nCVcUE0mmg5Q9az4qP-_MLFZPJsCHH4I6d3AZflhHfvCYxiQGc-DYd1KZUxizG1Ky-evmktWFFRhdWxB6Yz6S2VNRek-GV6sNl9QSBSENYfK4hTbfndo6h9uR4RJMeC4y2IugLR1FxF75nQ\n", "pathVariables": {}}, {"id": "327aa5b5-17d8-4515-9b41-e94e9036827d", "uid": "0-327aa5b5-17d8-4515-9b41-e94e9036827d", "name": "activity", "url": "{{protocol}}://{{host}}:{{port}}{{servletcontext}}{{context}}/activity?activityId=35921", "description": "Demonstrates the mock-oauth2 flow for local development.  This allows us to easily define security expectations in integration tests for what scopes the requestor has without using a full-fledged oauth2 provider.  This is controlled from the client via headers.\n\nPlease note the 'scopes' and 'userId' headers", "data": null, "dataOptions": null, "dataMode": null, "headerData": [{"key": "scope", "type": "text", "value": "openid"}, {"key": "sub", "type": "text", "value": "{{mockUserID}}"}], "method": "DELETE", "pathVariableData": [], "queryParams": [{"key": "activityId", "value": "35921"}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJSUzI1NiIsImtpZCI6ImlhNi1EX1pqSUpkak1RNXVfeVBHMnNyR016byIsInBpLmF0bSI6Im51eWoifQ.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.D8Fd3vkuc90nvn_evTbCWymyWcqjqkavyhRnaTvxL_y_wB6bpajiMx7Ucrvb2DEJo66LeFUjISWo1zzwxmTB7bz60267gt-mOooxA-1X0aV0mdS31OC1ZQ5W64t4RGnnISXIHbqjj8VZzVb8m00VrZrwxxjKZknoSDBukCfpv1FvSgZB9BIhkBxwcUTuEOlFF_tvUWd8IRPbwidwIYwup2FY9ihR09iYHb0Ula3OsAW0WpwR8zRFWeEjU0HChmnXai1qyXbRpVH-ZD3BKPBZ15Xb1ouHnzsKxmogWT9mO9zHVlop_Kx_tkCK5yKkNYrMu3FZkfLxC1Fe70DDSiyJXw", "type": "string"}]}, "events": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript", "id": "86c08fe3-821b-42fe-b2e4-e9280826b012"}}, {"listen": "prerequest", "script": {"exec": ["if(! pm.variables.get(\"mockUserID\")){", "    // generate a new, pseudo-random UUID", "    var uuid  = require('uuid');", "    pm.variables.set(\"mockUserID\", uuid.v4()); ", "    console.log(\"mockUserID set to: \"+pm.variables.get(\"mockUserID\"))", "}"], "type": "text/javascript", "id": "570ce26f-4b48-4e0a-af27-adf0a8dfd7ee"}}], "folder": null, "responses_order": [], "preRequestScript": "if(! pm.variables.get(\"mockUserID\")){\n    // generate a new, pseudo-random UUID\n    var uuid  = require('uuid');\n    pm.variables.set(\"mockUserID\", uuid.v4()); \n    console.log(\"mockUserID set to: \"+pm.variables.get(\"mockUserID\"))\n}", "tests": "pm.test(\"Status code is 200\", function () {\n    pm.response.to.have.status(200);\n});", "currentHelper": "<PERSON><PERSON><PERSON>", "helperAttributes": {"id": "bearer", "token": "eyJhbGciOiJSUzI1NiIsImtpZCI6ImlhNi1EX1pqSUpkak1RNXVfeVBHMnNyR016byIsInBpLmF0bSI6Im51eWoifQ.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.D8Fd3vkuc90nvn_evTbCWymyWcqjqkavyhRnaTvxL_y_wB6bpajiMx7Ucrvb2DEJo66LeFUjISWo1zzwxmTB7bz60267gt-mOooxA-1X0aV0mdS31OC1ZQ5W64t4RGnnISXIHbqjj8VZzVb8m00VrZrwxxjKZknoSDBukCfpv1FvSgZB9BIhkBxwcUTuEOlFF_tvUWd8IRPbwidwIYwup2FY9ihR09iYHb0Ula3OsAW0WpwR8zRFWeEjU0HChmnXai1qyXbRpVH-ZD3BKPBZ15Xb1ouHnzsKxmogWT9mO9zHVlop_Kx_tkCK5yKkNYrMu3FZkfLxC1Fe70DDSiyJXw"}, "collectionId": "6f72a114-c577-4d6c-aa4f-c8f7a47fed5d", "headers": "scope: openid\nsub: {{mockUserID}}\n", "pathVariables": {}}, {"id": "3746242c-738b-480c-9933-539b0a050c98", "uid": "0-3746242c-738b-480c-9933-539b0a050c98", "name": "billpay/profile", "url": "{{protocol}}://{{host}}:{{port}}{{servletcontext}}{{context}}/billpay/profile", "description": "Demonstrates the mock-oauth2 flow for local development.  This allows us to easily define security expectations in integration tests for what scopes the requestor has without using a full-fledged oauth2 provider.  This is controlled from the client via headers.\n\nPlease note the 'scopes' and 'userId' headers", "data": null, "dataOptions": null, "dataMode": null, "headerData": [{"key": "scope", "value": "openid", "type": "text"}, {"key": "sub", "value": "{{mockUserID}}", "type": "text"}, {"key": "x-device-uid", "value": "asdfasdfasdf", "type": "text"}, {"key": "x-client-version", "value": "4.0", "type": "text"}, {"key": "x-platform-version", "value": "9.3.1", "type": "text"}, {"key": "x-platform", "value": "Apple", "type": "text"}, {"key": "x-api-key", "value": "postman", "type": "text"}, {"key": "x-risk-authorization", "value": "eyJrdHkiOiJSU0EiLCJraWQiOiIxN2E0ZGJhOS0zMjczLTRhNjYtODFjYi1iMDg3YWFiM2M4NDgiLCJhbGciOiJSUzUxMiJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Ohk7qeUQN8uSsEUR2nv15IbAIbjog7QleGccbVApIclwp-gJ_5VtkUvF4hS-hnWMCOehaEbm3sYC0dE6s9yLTErmA5WaV6u8Pxb0cp0VcadZv_khuzlw4rUrBOsBd-uWI4N_dHIvkhcJZ0e_A1UYdSeGwFccmmr5Dm4eGZ94cWTYywDHVznXV6-9WvM7OITVDx6Q9X80LFR6XbBwNSZ37tpmuStMgKo4YaxYSsya94DvlJyzycS0GU1Jd-SK09vGMDl_sN-z5e_UXFyp0V3NGHoWQika0kkCEyoqH6QP2nMA56wXEvkWFITtP1Hqc7-jbR4pKF8H3umpODjRw76ayTW6-g1fDX1EoFnGDXPCFOLvyRpRVLoDu63IIu-YAqkzycaJDRBJpHqQD14gJaAhvS2fgSOLud1des3xMygumPBVxTdZ8ZvNfmDZ7q67rWHK4bBS1UXs1x2JPn1iQ-INRLLtah4fNcIOo6BHy7hIhjbCPNtHL9CjRVmRaQOkSyAvgCqUHtSM9EKp4QxGlcXxqOnlgj3K26IOW0zadD3Kax4aCdPe-ZZ7mditmQke7XvgtmRqupptFo0EL6s_SjPVDwd-hkCgZM3kQB_BgHa9Ous9TRqTE2iJZFTPAPVsJQeumcozUjfUGVIM4W-rr7hG4MdeQk2cWsMcjKARfqXUMgk", "type": "text", "enabled": false}], "method": "GET", "pathVariableData": [], "queryParams": [], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "events": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript", "id": "b31b7dca-c3c3-48e7-9c81-8f6ffb5df955"}}, {"listen": "prerequest", "script": {"exec": ["if(! pm.variables.get(\"mockUserID\")){", "    // generate a new, pseudo-random UUID", "    var uuid  = require('uuid');", "    pm.variables.set(\"mockUserID\", uuid.v4()); ", "    console.log(\"mockUserID set to: \"+pm.variables.get(\"mockUserID\"))", "}"], "type": "text/javascript", "id": "3f4a3332-88d1-47dc-839b-cbdca85fed0c"}}], "folder": null, "responses_order": [], "preRequestScript": "if(! pm.variables.get(\"mockUserID\")){\n    // generate a new, pseudo-random UUID\n    var uuid  = require('uuid');\n    pm.variables.set(\"mockUserID\", uuid.v4()); \n    console.log(\"mockUserID set to: \"+pm.variables.get(\"mockUserID\"))\n}", "tests": "pm.test(\"Status code is 200\", function () {\n    pm.response.to.have.status(200);\n});", "currentHelper": "<PERSON><PERSON><PERSON>", "helperAttributes": {"id": "bearer", "token": "{{auth_token}}"}, "collectionId": "6f72a114-c577-4d6c-aa4f-c8f7a47fed5d", "headers": "scope: openid\nsub: {{mockUserID}}\nx-device-uid: asdfasdfasdf\nx-client-version: 4.0\nx-platform-version: 9.3.1\nx-platform: Apple\nx-api-key: postman\n//x-risk-authorization: eyJrdHkiOiJSU0EiLCJraWQiOiIxN2E0ZGJhOS0zMjczLTRhNjYtODFjYi1iMDg3YWFiM2M4NDgiLCJhbGciOiJSUzUxMiJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Ohk7qeUQN8uSsEUR2nv15IbAIbjog7QleGccbVApIclwp-gJ_5VtkUvF4hS-hnWMCOehaEbm3sYC0dE6s9yLTErmA5WaV6u8Pxb0cp0VcadZv_khuzlw4rUrBOsBd-uWI4N_dHIvkhcJZ0e_A1UYdSeGwFccmmr5Dm4eGZ94cWTYywDHVznXV6-9WvM7OITVDx6Q9X80LFR6XbBwNSZ37tpmuStMgKo4YaxYSsya94DvlJyzycS0GU1Jd-SK09vGMDl_sN-z5e_UXFyp0V3NGHoWQika0kkCEyoqH6QP2nMA56wXEvkWFITtP1Hqc7-jbR4pKF8H3umpODjRw76ayTW6-g1fDX1EoFnGDXPCFOLvyRpRVLoDu63IIu-YAqkzycaJDRBJpHqQD14gJaAhvS2fgSOLud1des3xMygumPBVxTdZ8ZvNfmDZ7q67rWHK4bBS1UXs1x2JPn1iQ-INRLLtah4fNcIOo6BHy7hIhjbCPNtHL9CjRVmRaQOkSyAvgCqUHtSM9EKp4QxGlcXxqOnlgj3K26IOW0zadD3Kax4aCdPe-ZZ7mditmQke7XvgtmRqupptFo0EL6s_SjPVDwd-hkCgZM3kQB_BgHa9Ous9TRqTE2iJZFTPAPVsJQeumcozUjfUGVIM4W-rr7hG4MdeQk2cWsMcjKARfqXUMgk\n", "pathVariables": {}}, {"id": "3ac2e4bc-568c-4df7-ab9d-30903e92e835", "uid": "0-3ac2e4bc-568c-4df7-ab9d-30903e92e835", "name": "externaltransfer/verifyTrialDeposits Copy", "url": "{{protocol}}://{{host}}:{{port}}{{servletcontext}}{{context}}/externaltransfer/verifyTrialDeposits", "description": "Demonstrates the mock-oauth2 flow for local development.  This allows us to easily define security expectations in integration tests for what scopes the requestor has without using a full-fledged oauth2 provider.  This is controlled from the client via headers.\n\nPlease note the 'scopes' and 'userId' headers", "data": [], "dataOptions": {"raw": {"language": "json"}}, "dataMode": "raw", "headerData": [{"key": "scope", "type": "text", "value": "openid"}, {"key": "sub", "type": "text", "value": "{{mockUserID}}"}, {"key": "x-client-version", "value": "3.0", "type": "text"}, {"key": "x-device-uid", "value": "asdfasdfasdf", "type": "text"}, {"key": "x-risk-authorization", "value": "{{risk_token}}", "description": "", "type": "default", "enabled": true}], "method": "POST", "pathVariableData": [], "queryParams": [], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}"}]}, "events": [{"listen": "test", "script": {"id": "f3147fb2-a427-48c4-bca3-07a395972723", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"id": "a16198e6-f92e-4eaf-bfb5-c218c3b583c4", "exec": ["path=`/move-money-bff/externaltransfer/verifyTrialDeposits`", "_.analyzeRiskScoreIst(pm, path);"], "type": "text/javascript"}}], "folder": null, "responses_order": [], "preRequestScript": "path=`/move-money-bff/externaltransfer/verifyTrialDeposits`\n_.analyzeRiskScoreIst(pm, path);", "tests": "pm.test(\"Status code is 200\", function () {\n    pm.response.to.have.status(200);\n});", "currentHelper": "<PERSON><PERSON><PERSON>", "helperAttributes": {"id": "bearer", "token": "{{auth_token}}"}, "collectionId": "6f72a114-c577-4d6c-aa4f-c8f7a47fed5d", "rawModeData": "{\n    \"accountId\": \"F93336ED-EEAF-C269-7FA2-2B27EF4394CC\",\n    \"amountOne\": 0.1,\n    \"amountTwo\": 0.1\n}", "headers": "scope: openid\nsub: {{mockUserID}}\nx-client-version: 3.0\nx-device-uid: asdfasdfasdf\nx-risk-authorization: {{risk_token}}\n", "pathVariables": {}}, {"id": "5800245b-**************-6f974feb70f2", "uid": "0-5800245b-**************-6f974feb70f2", "name": "externaltransfer/bankInfo", "url": "{{protocol}}://{{host}}:{{port}}{{servletcontext}}{{context}}/externaltransfer/bankInfo?routingNumber=*********", "description": "Demonstrates the mock-oauth2 flow for local development.  This allows us to easily define security expectations in integration tests for what scopes the requestor has without using a full-fledged oauth2 provider.  This is controlled from the client via headers.\n\nPlease note the 'scopes' and 'userId' headers", "data": null, "dataOptions": null, "dataMode": null, "headerData": [{"key": "scope", "type": "text", "value": "openid"}, {"key": "sub", "type": "text", "value": "{{mockUserID}}"}, {"key": "x-client-version", "value": "3.0", "type": "text"}, {"key": "x-device-uid", "value": "asdfasdfasdf", "type": "text"}], "method": "GET", "pathVariableData": [], "queryParams": [{"key": "routingNumber", "value": "*********"}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJSUzI1NiIsImtpZCI6IlFac0xydXlkWEdPQ01qaGk4OXdldGlKa25aVSIsInBpLmF0bSI6Im51eWoifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.TfUzmdgxs2hRevAWu5ce8Q_sG3rxlQcD9_AlUmyh6Q7xY3exqCLP7Zxpw1g24bAFOup-jAYj46vSRCJd0sQh2ef1mburnjAd3uhBWcPbkf1BAgS2HXVWiHSD1f9qmAdnKI_hUjAGJiJ05gBGMuXjVz01IHv20p9nti3WXQHW64c-HBpcDH9PZOQqS4iMbsfi3K8oPIG2vJrG0qmbxyT_5n1D8o2yyjY7UYsevmHriM2imAGMn883W7TfIe7c0Ywt38OIUvDCmhShuyeMNVR-_eWrOh9bFhRF-KruHVPpYbKOvIKvjpvaRtTKvh681GsVtk5WYobGT7SZ0GeWi4fmjg", "type": "string"}]}, "events": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript", "id": "6cacd503-74a6-4a39-986a-385db64f0fb5"}}, {"listen": "prerequest", "script": {"exec": ["if(! pm.variables.get(\"mockUserID\")){", "    // generate a new, pseudo-random UUID", "    var uuid  = require('uuid');", "    pm.variables.set(\"mockUserID\", uuid.v4()); ", "    console.log(\"mockUserID set to: \"+pm.variables.get(\"mockUserID\"))", "}"], "type": "text/javascript", "id": "ec3dd01d-b8af-481e-94f0-a9ed69ebc0a9"}}], "folder": null, "responses_order": [], "preRequestScript": "if(! pm.variables.get(\"mockUserID\")){\n    // generate a new, pseudo-random UUID\n    var uuid  = require('uuid');\n    pm.variables.set(\"mockUserID\", uuid.v4()); \n    console.log(\"mockUserID set to: \"+pm.variables.get(\"mockUserID\"))\n}", "tests": "pm.test(\"Status code is 200\", function () {\n    pm.response.to.have.status(200);\n});", "currentHelper": "<PERSON><PERSON><PERSON>", "helperAttributes": {"id": "bearer", "token": "eyJhbGciOiJSUzI1NiIsImtpZCI6IlFac0xydXlkWEdPQ01qaGk4OXdldGlKa25aVSIsInBpLmF0bSI6Im51eWoifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.TfUzmdgxs2hRevAWu5ce8Q_sG3rxlQcD9_AlUmyh6Q7xY3exqCLP7Zxpw1g24bAFOup-jAYj46vSRCJd0sQh2ef1mburnjAd3uhBWcPbkf1BAgS2HXVWiHSD1f9qmAdnKI_hUjAGJiJ05gBGMuXjVz01IHv20p9nti3WXQHW64c-HBpcDH9PZOQqS4iMbsfi3K8oPIG2vJrG0qmbxyT_5n1D8o2yyjY7UYsevmHriM2imAGMn883W7TfIe7c0Ywt38OIUvDCmhShuyeMNVR-_eWrOh9bFhRF-KruHVPpYbKOvIKvjpvaRtTKvh681GsVtk5WYobGT7SZ0GeWi4fmjg"}, "collectionId": "6f72a114-c577-4d6c-aa4f-c8f7a47fed5d", "headers": "scope: openid\nsub: {{mockUserID}}\nx-client-version: 3.0\nx-device-uid: asdfasdfasdf\n", "pathVariables": {}}, {"id": "63e63933-aa8d-4341-8b4f-d771a60b3c75", "uid": "0-63e63933-aa8d-4341-8b4f-d771a60b3c75", "name": "billpay/globalpayees", "url": "{{protocol}}://{{host}}:{{port}}{{servletcontext}}{{context}}/billpay/globalpayees", "description": "Demonstrates the mock-oauth2 flow for local development.  This allows us to easily define security expectations in integration tests for what scopes the requestor has without using a full-fledged oauth2 provider.  This is controlled from the client via headers.\n\nPlease note the 'scopes' and 'userId' headers", "data": null, "dataOptions": null, "dataMode": null, "headerData": [{"key": "scope", "type": "text", "value": "openid"}, {"key": "sub", "type": "text", "value": "{{mockUserID}}"}, {"key": "x-device-uid", "value": "asdfasdfasdf", "type": "text"}], "method": "GET", "pathVariableData": [], "queryParams": [], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJSUzI1NiIsImtpZCI6IlFac0xydXlkWEdPQ01qaGk4OXdldGlKa25aVSIsInBpLmF0bSI6Im51eWoifQ.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.D_Jx_JIKSRPxMblme8c9gYel3ictgNpD9Pe7IwfoFAs25GZeiGGyUbiaFYNSDxu4KDxVMgpMJgxSjDR8NLrY9OekMtDUO1nJz2SoBtpVbT9ie0vr1yYexM4VcLBt8E9vOFEHsY3l3l4Uq_MJUUwuEEax-M4O0MuFKgylnw-y87MD-h-8_4nwqR55g4-Y7MTm_cwVOXirQ4fKZMPqtnpJD-BfmVPySLPSY53G5CFXiii7OHwnODU1a3OKB4dXdpELZvooFHpad5PpGcRFPZtSUe3mqvu41r_rNnuL7r1ZRcYeJu6w2LjoRd6UQCWiGp7mOJllIggOzX7Ky_ON067d8g", "type": "string"}]}, "events": [{"listen": "test", "script": {"id": "164b77b8-b9be-46b7-b653-520bae96c36a", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"id": "1d60aba6-7125-464d-806d-169e954cf68a", "exec": ["if(! pm.variables.get(\"mockUserID\")){", "    // generate a new, pseudo-random UUID", "    var uuid  = require('uuid');", "    pm.variables.set(\"mockUserID\", uuid.v4()); ", "    console.log(\"mockUserID set to: \"+pm.variables.get(\"mockUserID\"))", "}"], "type": "text/javascript"}}], "folder": null, "responses_order": [], "preRequestScript": "if(! pm.variables.get(\"mockUserID\")){\n    // generate a new, pseudo-random UUID\n    var uuid  = require('uuid');\n    pm.variables.set(\"mockUserID\", uuid.v4()); \n    console.log(\"mockUserID set to: \"+pm.variables.get(\"mockUserID\"))\n}", "tests": "pm.test(\"Status code is 200\", function () {\n    pm.response.to.have.status(200);\n});", "currentHelper": "<PERSON><PERSON><PERSON>", "helperAttributes": {"id": "bearer", "token": "eyJhbGciOiJSUzI1NiIsImtpZCI6IlFac0xydXlkWEdPQ01qaGk4OXdldGlKa25aVSIsInBpLmF0bSI6Im51eWoifQ.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.D_Jx_JIKSRPxMblme8c9gYel3ictgNpD9Pe7IwfoFAs25GZeiGGyUbiaFYNSDxu4KDxVMgpMJgxSjDR8NLrY9OekMtDUO1nJz2SoBtpVbT9ie0vr1yYexM4VcLBt8E9vOFEHsY3l3l4Uq_MJUUwuEEax-M4O0MuFKgylnw-y87MD-h-8_4nwqR55g4-Y7MTm_cwVOXirQ4fKZMPqtnpJD-BfmVPySLPSY53G5CFXiii7OHwnODU1a3OKB4dXdpELZvooFHpad5PpGcRFPZtSUe3mqvu41r_rNnuL7r1ZRcYeJu6w2LjoRd6UQCWiGp7mOJllIggOzX7Ky_ON067d8g"}, "collectionId": "6f72a114-c577-4d6c-aa4f-c8f7a47fed5d", "headers": "scope: openid\nsub: {{mockUserID}}\nx-device-uid: asdfasdfasdf\n", "pathVariables": {}}, {"id": "6b20cb39-dfea-4822-a7e7-dcb64cd021cf", "uid": "0-6b20cb39-dfea-4822-a7e7-dcb64cd021cf", "name": "externaltransfer/deleteAccount", "url": "{{protocol}}://{{host}}:{{port}}{{servletcontext}}{{context}}/externaltransfer/deleteAccount?accountId=4A311F08-558B-6F89-EA57-874BA7C3DEF8", "description": "Demonstrates the mock-oauth2 flow for local development.  This allows us to easily define security expectations in integration tests for what scopes the requestor has without using a full-fledged oauth2 provider.  This is controlled from the client via headers.\n\nPlease note the 'scopes' and 'userId' headers", "data": null, "dataOptions": null, "dataMode": null, "headerData": [{"key": "scope", "type": "text", "value": "openid"}, {"key": "sub", "type": "text", "value": "{{mockUserID}}"}, {"key": "x-client-version", "value": "3.0", "type": "text"}, {"key": "x-device-uid", "value": "asdfasdfasdf", "type": "text"}], "method": "DELETE", "pathVariableData": [], "queryParams": [{"key": "accountId", "value": "4A311F08-558B-6F89-EA57-874BA7C3DEF8"}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJSUzI1NiIsImtpZCI6IlFac0xydXlkWEdPQ01qaGk4OXdldGlKa25aVSIsInBpLmF0bSI6Im51eWoifQ.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ARRGYmQMK69H5p4IcNau1zj-e2lr6pFyaALkaacoupaN7dPaw7YW6E10IR-4jQIDJb8iEDUbnvikr0qwQEqFNgHhP1Uh-mZJemjQocCKBBOGEpCAROdKGHk6IawarJoQxRXx056UUr6ma12ka3YlKHpKBv_872PiW1G5E0BOJhxfrAMd3V7QIBfpijey8uB0_hI-oLGKx0ddH12bD0LvvyrNhdbEgiDpBODNr-ex13-dwiu4HeATp2jsVQpW_mmipXPMGwtQ5IJOushX-qgfKsQIL4ELcDRpNP_rhuxG9WLnHJK0nv7niN7iQbrUHSMsGsYCM7il8YUAxB_i221_Bw", "type": "string"}]}, "events": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript", "id": "6fd8e4eb-9e82-48f0-8c2d-afd0d999a098"}}, {"listen": "prerequest", "script": {"exec": ["if(! pm.variables.get(\"mockUserID\")){", "    // generate a new, pseudo-random UUID", "    var uuid  = require('uuid');", "    pm.variables.set(\"mockUserID\", uuid.v4()); ", "    console.log(\"mockUserID set to: \"+pm.variables.get(\"mockUserID\"))", "}"], "type": "text/javascript", "id": "030e5755-6ea9-4d37-ae14-ed18d3c67d8d"}}], "folder": null, "responses_order": [], "preRequestScript": "if(! pm.variables.get(\"mockUserID\")){\n    // generate a new, pseudo-random UUID\n    var uuid  = require('uuid');\n    pm.variables.set(\"mockUserID\", uuid.v4()); \n    console.log(\"mockUserID set to: \"+pm.variables.get(\"mockUserID\"))\n}", "tests": "pm.test(\"Status code is 200\", function () {\n    pm.response.to.have.status(200);\n});", "currentHelper": "<PERSON><PERSON><PERSON>", "helperAttributes": {"id": "bearer", "token": "eyJhbGciOiJSUzI1NiIsImtpZCI6IlFac0xydXlkWEdPQ01qaGk4OXdldGlKa25aVSIsInBpLmF0bSI6Im51eWoifQ.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ARRGYmQMK69H5p4IcNau1zj-e2lr6pFyaALkaacoupaN7dPaw7YW6E10IR-4jQIDJb8iEDUbnvikr0qwQEqFNgHhP1Uh-mZJemjQocCKBBOGEpCAROdKGHk6IawarJoQxRXx056UUr6ma12ka3YlKHpKBv_872PiW1G5E0BOJhxfrAMd3V7QIBfpijey8uB0_hI-oLGKx0ddH12bD0LvvyrNhdbEgiDpBODNr-ex13-dwiu4HeATp2jsVQpW_mmipXPMGwtQ5IJOushX-qgfKsQIL4ELcDRpNP_rhuxG9WLnHJK0nv7niN7iQbrUHSMsGsYCM7il8YUAxB_i221_Bw"}, "collectionId": "6f72a114-c577-4d6c-aa4f-c8f7a47fed5d", "headers": "scope: openid\nsub: {{mockUserID}}\nx-client-version: 3.0\nx-device-uid: asdfasdfasdf\n", "pathVariables": {}}, {"id": "6eea816e-969c-45e7-9312-4b78b59e81ae", "uid": "0-6eea816e-969c-45e7-9312-4b78b59e81ae", "name": "test request", "url": "{{protocol}}://{{host}}:{{port}}{{servletcontext}}{{context}}/hello", "description": "Demonstrates the mock-oauth2 flow for local development.  This allows us to easily define security expectations in integration tests for what scopes the requestor has without using a full-fledged oauth2 provider.  This is controlled from the client via headers.\n\nPlease note the 'scopes' and 'userId' headers", "data": null, "dataOptions": null, "dataMode": null, "headerData": [{"key": "scope", "type": "text", "value": "openid"}, {"key": "sub", "type": "text", "value": "{{mockUserID}}"}, {"key": "Authorization", "value": "Bearer ey<PERSON>hbGciOiJSUzI1NiIsImtpZCI6IjM5STRoOVl5anNRWTlpa0hhcWdOREQ3WHY4byIsInBpLmF0bSI6Im51eWoifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NLQnQV9wbLOC2lZnKBsc7p5KmC6s8cLEQst917RY4gdC0pmewyjKPA3ANBgSvgb36gFcBhdWsIP-useTEwJn28Q_kxdpdgW9j07OwFZZn8dWJ6yYjvY7sNHu9_Jhq0Wf8d9gEajAIvMsBGmkOk8b1ByCNH4l6xx5KmoDenXeKqW6ioQ3yBqk7Kx4RRJZlmqqim_GIPo6lZKe4V9St4i_X6ZHiuv2kjL_pX2XJT0XZ4kPjsJLPhgknW5S3GdNyofxyKOSYP3UIV3WVs6TQa0FQ8n6iY8gfLTvVmZ4rhIdafOemoCKQOlnbc8uhXlhBcjI9ePmMFMa5AVSQ7zULO9o1A", "type": "text", "enabled": false}, {"key": "x-device-uid", "value": "asdfasdfasdf", "type": "text"}, {"key": "x-forwarded-for", "value": "127.0.0.1", "type": "text"}, {"key": "x-api-key", "value": "postman", "type": "text", "enabled": false}, {"key": "x-client-version", "value": "4.0", "type": "text"}, {"key": "x-platform-version", "value": "9.3.1", "type": "text"}, {"key": "x-platform", "value": "Apple", "type": "text"}], "method": "GET", "pathVariableData": [], "queryParams": [], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "events": [{"listen": "prerequest", "script": {"id": "bb4de7b1-4be2-413f-be0a-b669993b49d6", "exec": ["if(! pm.variables.get(\"mockUserID\")){", "    // generate a new, pseudo-random UUID", "    var uuid  = require('uuid');", "    pm.variables.set(\"mockUserID\", uuid.v4()); ", "    console.log(\"mockUserID set to: \"+pm.variables.get(\"mockUserID\"))", "}", "", "result = pm.sendRequest( ", "    {", "        method: 'GET',", "", "        url: \"https://api.consumermobile-sit.nube.53.com:443/move-money-bff/hello\",", "", "        header: {", "            'Content-Type': 'application/json',", "                'X-XSRF-Header': 'test',", "                'X-Device-UID': \"BBAB1471-00E5-420B-9362-D1D5F932FC03\",", "                'X-Device-Fingerprint': 'version=1',", "                'Authorization': 'Bearer '+pm.variables.get(\"auth_token\")", "", "        },", "        body: {", "", "                    mode: 'raw',", "", "                    raw: JSON.stringify({", "                        \"method\": \"GET\",", "                        \"path\": \"/move-money-bff/hello\",", "                        \"orgName\": \"53com\"", "                    })", "        }", "    }", ", (error, response) => {", "  console.log(error ? \"Error: \"+error : response.json());", "});"], "type": "text/javascript"}}, {"listen": "test", "script": {"id": "bef84341-b25f-4f6c-b645-0f125cddd259", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript"}}], "folder": null, "responses_order": [], "preRequestScript": "if(! pm.variables.get(\"mockUserID\")){\n    // generate a new, pseudo-random UUID\n    var uuid  = require('uuid');\n    pm.variables.set(\"mockUserID\", uuid.v4()); \n    console.log(\"mockUserID set to: \"+pm.variables.get(\"mockUserID\"))\n}\n\nresult = pm.sendRequest( \n    {\n        method: 'GET',\n\n        url: \"https://api.consumermobile-sit.nube.53.com:443/move-money-bff/hello\",\n\n        header: {\n            'Content-Type': 'application/json',\n                'X-XSRF-Header': 'test',\n                'X-Device-UID': \"BBAB1471-00E5-420B-9362-D1D5F932FC03\",\n                'X-Device-Fingerprint': 'version=1',\n                'Authorization': 'Bearer '+pm.variables.get(\"auth_token\")\n\n        },\n        body: {\n\n                    mode: 'raw',\n\n                    raw: JSON.stringify({\n                        \"method\": \"GET\",\n                        \"path\": \"/move-money-bff/hello\",\n                        \"orgName\": \"53com\"\n                    })\n        }\n    }\n, (error, response) => {\n  console.log(error ? \"Error: \"+error : response.json());\n});", "tests": "pm.test(\"Status code is 200\", function () {\n    pm.response.to.have.status(200);\n});", "currentHelper": "<PERSON><PERSON><PERSON>", "helperAttributes": {"id": "bearer", "token": "{{auth_token}}"}, "collectionId": "6f72a114-c577-4d6c-aa4f-c8f7a47fed5d", "headers": "scope: openid\nsub: {{mockUserID}}\n//Authorization: Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6IjM5STRoOVl5anNRWTlpa0hhcWdOREQ3WHY4byIsInBpLmF0bSI6Im51eWoifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NLQnQV9wbLOC2lZnKBsc7p5KmC6s8cLEQst917RY4gdC0pmewyjKPA3ANBgSvgb36gFcBhdWsIP-useTEwJn28Q_kxdpdgW9j07OwFZZn8dWJ6yYjvY7sNHu9_Jhq0Wf8d9gEajAIvMsBGmkOk8b1ByCNH4l6xx5KmoDenXeKqW6ioQ3yBqk7Kx4RRJZlmqqim_GIPo6lZKe4V9St4i_X6ZHiuv2kjL_pX2XJT0XZ4kPjsJLPhgknW5S3GdNyofxyKOSYP3UIV3WVs6TQa0FQ8n6iY8gfLTvVmZ4rhIdafOemoCKQOlnbc8uhXlhBcjI9ePmMFMa5AVSQ7zULO9o1A\nx-device-uid: asdfasdfasdf\nx-forwarded-for: 127.0.0.1\n//x-api-key: postman\nx-client-version: 4.0\nx-platform-version: 9.3.1\nx-platform: Apple\n", "pathVariables": {}}, {"id": "77f381f8-aa16-47ba-9ab6-1e49194289a0", "uid": "0-77f381f8-aa16-47ba-9ab6-1e49194289a0", "name": "externaltransfer/addAccount", "url": "{{protocol}}://{{host}}:{{port}}{{servletcontext}}{{context}}/externaltransfer/addAccount", "description": "Demonstrates the mock-oauth2 flow for local development.  This allows us to easily define security expectations in integration tests for what scopes the requestor has without using a full-fledged oauth2 provider.  This is controlled from the client via headers.\n\nPlease note the 'scopes' and 'userId' headers", "data": [], "dataOptions": {"raw": {"language": "json"}}, "dataMode": "raw", "headerData": [{"key": "scope", "type": "text", "value": "openid"}, {"key": "sub", "type": "text", "value": "{{mockUserID}}"}, {"key": "x-client-version", "value": "3.0", "type": "text"}, {"key": "x-device-uid", "value": "asdfasdfasdf", "type": "text"}, {"key": "X-Risk-Authorization", "value": "{{risk_token}}", "description": "", "type": "default", "enabled": true}], "method": "POST", "pathVariableData": [], "queryParams": [], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}"}]}, "events": [{"listen": "test", "script": {"id": "ad19b0a4-**************-3bf40e57c996", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"id": "dca33673-8c60-4afa-9548-ce2de8f56717", "exec": ["path=`/move-money-bff/externaltransfer/addAccount`", "_.analyzeRiskScoreIst(pm, path);"], "type": "text/javascript"}}], "folder": null, "responses_order": [], "preRequestScript": "path=`/move-money-bff/externaltransfer/addAccount`\n_.analyzeRiskScoreIst(pm, path);", "tests": "pm.test(\"Status code is 200\", function () {\n    pm.response.to.have.status(200);\n});", "currentHelper": "<PERSON><PERSON><PERSON>", "helperAttributes": {"id": "bearer", "token": "{{auth_token}}"}, "collectionId": "6f72a114-c577-4d6c-aa4f-c8f7a47fed5d", "rawModeData": "{\n    \"accountNickName\": \"ame2\",\n    \"accountNumber\": \"********\",\n    \"accountTypeCode\": \"CASH\",\n    \"creditAccountNumber\": \"*********\",\n    \"creditRoutingNumber\": \"*********\",\n    \"routingNumber\": \"*********\"\n}", "headers": "scope: openid\nsub: {{mockUserID}}\nx-client-version: 3.0\nx-device-uid: asdfasdfasdf\nX-Risk-Authorization: {{risk_token}}\n", "pathVariables": {}}, {"id": "789c6750-4a83-4b03-8e54-43ce88692961", "uid": "0-789c6750-4a83-4b03-8e54-43ce88692961", "name": "actuator/health", "url": "{{protocol}}://{{host}}:{{port}}{{servletcontext}}{{context}}/hello", "description": "Demonstrates the mock-oauth2 flow for local development.  This allows us to easily define security expectations in integration tests for what scopes the requestor has without using a full-fledged oauth2 provider.  This is controlled from the client via headers.\n\nPlease note the 'scopes' and 'userId' headers", "data": null, "dataOptions": null, "dataMode": null, "headerData": [{"key": "scope", "type": "text", "value": "openid"}, {"key": "sub", "type": "text", "value": "{{mockUserID}}"}, {"key": "Authorization", "value": "Bearer ey<PERSON>hbGciOiJSUzI1NiIsImtpZCI6IjM5STRoOVl5anNRWTlpa0hhcWdOREQ3WHY4byIsInBpLmF0bSI6Im51eWoifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NLQnQV9wbLOC2lZnKBsc7p5KmC6s8cLEQst917RY4gdC0pmewyjKPA3ANBgSvgb36gFcBhdWsIP-useTEwJn28Q_kxdpdgW9j07OwFZZn8dWJ6yYjvY7sNHu9_Jhq0Wf8d9gEajAIvMsBGmkOk8b1ByCNH4l6xx5KmoDenXeKqW6ioQ3yBqk7Kx4RRJZlmqqim_GIPo6lZKe4V9St4i_X6ZHiuv2kjL_pX2XJT0XZ4kPjsJLPhgknW5S3GdNyofxyKOSYP3UIV3WVs6TQa0FQ8n6iY8gfLTvVmZ4rhIdafOemoCKQOlnbc8uhXlhBcjI9ePmMFMa5AVSQ7zULO9o1A", "type": "text", "enabled": false}], "method": "GET", "pathVariableData": [], "queryParams": [], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "events": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript", "id": "1aa37495-5320-4eb4-bc2c-ebfaf12a8312"}}, {"listen": "prerequest", "script": {"exec": ["if(! pm.variables.get(\"mockUserID\")){", "    // generate a new, pseudo-random UUID", "    var uuid  = require('uuid');", "    pm.variables.set(\"mockUserID\", uuid.v4()); ", "    console.log(\"mockUserID set to: \"+pm.variables.get(\"mockUserID\"))", "}"], "type": "text/javascript", "id": "1d9a956f-06a9-4cc4-b67b-7bc20e111dac"}}], "folder": null, "responses_order": [], "preRequestScript": "if(! pm.variables.get(\"mockUserID\")){\n    // generate a new, pseudo-random UUID\n    var uuid  = require('uuid');\n    pm.variables.set(\"mockUserID\", uuid.v4()); \n    console.log(\"mockUserID set to: \"+pm.variables.get(\"mockUserID\"))\n}", "tests": "pm.test(\"Status code is 200\", function () {\n    pm.response.to.have.status(200);\n});", "currentHelper": "<PERSON><PERSON><PERSON>", "helperAttributes": {"id": "bearer", "token": ""}, "collectionId": "6f72a114-c577-4d6c-aa4f-c8f7a47fed5d", "headers": "scope: openid\nsub: {{mockUserID}}\n//Authorization: Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6IjM5STRoOVl5anNRWTlpa0hhcWdOREQ3WHY4byIsInBpLmF0bSI6Im51eWoifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NLQnQV9wbLOC2lZnKBsc7p5KmC6s8cLEQst917RY4gdC0pmewyjKPA3ANBgSvgb36gFcBhdWsIP-useTEwJn28Q_kxdpdgW9j07OwFZZn8dWJ6yYjvY7sNHu9_Jhq0Wf8d9gEajAIvMsBGmkOk8b1ByCNH4l6xx5KmoDenXeKqW6ioQ3yBqk7Kx4RRJZlmqqim_GIPo6lZKe4V9St4i_X6ZHiuv2kjL_pX2XJT0XZ4kPjsJLPhgknW5S3GdNyofxyKOSYP3UIV3WVs6TQa0FQ8n6iY8gfLTvVmZ4rhIdafOemoCKQOlnbc8uhXlhBcjI9ePmMFMa5AVSQ7zULO9o1A\n", "pathVariables": {}}, {"id": "7f321560-fd2f-4eff-afe0-16ced4fbdb02", "uid": "0-7f321560-fd2f-4eff-afe0-16ced4fbdb02", "name": "activity", "url": "{{protocol}}://{{host}}:{{port}}{{servletcontext}}{{context}}/activity", "description": "Demonstrates the mock-oauth2 flow for local development.  This allows us to easily define security expectations in integration tests for what scopes the requestor has without using a full-fledged oauth2 provider.  This is controlled from the client via headers.\n\nPlease note the 'scopes' and 'userId' headers", "data": [], "dataOptions": {"raw": {"language": "json"}}, "dataMode": "raw", "headerData": [{"key": "scope", "type": "text", "value": "openid"}, {"key": "sub", "type": "text", "value": "{{mockUserID}}"}, {"key": "X-Risk-Authorization", "value": "{{risk_token}}", "type": "text"}, {"key": "x-device-uid", "value": "asdfasdfasdf", "type": "text"}, {"key": "x-client-version", "value": "4.0", "type": "text"}, {"key": "x-platform-version", "value": "9.3.1", "type": "text"}, {"key": "x-platform", "value": "Apple", "type": "text"}, {"key": "x-api-key", "value": "postman", "type": "text"}], "method": "POST", "pathVariableData": [], "queryParams": [], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "events": [{"listen": "prerequest", "script": {"id": "3c132b25-814c-448b-9b8d-6ed19dbbb2ff", "exec": ["//_.getTransferInfo(pm);", "path = `/move-money-bff/activity`;", "_.analyzeRiskScoreIst(pm, path);"], "type": "text/javascript"}}, {"listen": "test", "script": {"id": "4fe58c25-f4ee-48e1-9e49-ccbe6135678c", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript"}}], "folder": null, "responses_order": [], "preRequestScript": "//_.getTransferInfo(pm);\npath = `/move-money-bff/activity`;\n_.analyzeRiskScoreIst(pm, path);", "tests": "pm.test(\"Status code is 200\", function () {\n    pm.response.to.have.status(200);\n});", "currentHelper": "<PERSON><PERSON><PERSON>", "helperAttributes": {"id": "bearer", "token": "{{auth_token}}"}, "collectionId": "6f72a114-c577-4d6c-aa4f-c8f7a47fed5d", "rawModeData": "{\n    \"amount\": 2.17,\n    \"dueDate\": \"2023-01-25\",\n    \"frequency\": \"ONE_TIME\",\n    \"fromAccountId\": \"96d131d7-fa44-4f37-bb0b-41e65e05c367\",\n    \"toAccountId\": \"a39fa559-2c53-454c-843f-3451d3aba455\",\n    \"invoicedRecurringPayment\": false,\n    \"memo\": \"\",\n    \"requestGuid\": \"266512b1-c64e-4219-ba36-1be9a5a28395\"\n\n}", "headers": "scope: openid\nsub: {{mockUserID}}\nX-Risk-Authorization: {{risk_token}}\nx-device-uid: asdfasdfasdf\nx-client-version: 4.0\nx-platform-version: 9.3.1\nx-platform: Apple\nx-api-key: postman\n", "pathVariables": {}}, {"id": "8b315c10-63c4-4fbb-9e59-2fbfcf97cc5f", "uid": "0-8b315c10-63c4-4fbb-9e59-2fbfcf97cc5f", "name": "activity", "url": "{{protocol}}://{{host}}:{{port}}{{servletcontext}}{{context}}/activity", "description": "Demonstrates the mock-oauth2 flow for local development.  This allows us to easily define security expectations in integration tests for what scopes the requestor has without using a full-fledged oauth2 provider.  This is controlled from the client via headers.\n\nPlease note the 'scopes' and 'userId' headers", "data": [], "dataOptions": {"raw": {"language": "json"}}, "dataMode": "raw", "headerData": [{"key": "scope", "type": "text", "value": "openid"}, {"key": "sub", "type": "text", "value": "{{mockUserID}}"}, {"key": "x-device-uid", "value": "asdfasdfasdf", "type": "text"}, {"key": "x-api-key", "value": "postman", "type": "text", "enabled": false}, {"key": "x-client-version", "value": "4.0", "type": "text"}, {"key": "x-platform-version", "value": "9.3.1", "type": "text"}, {"key": "x-platform", "value": "Apple", "type": "text"}, {"key": "x-risk-authorization", "value": "{{risk_token}}", "type": "text"}], "method": "PUT", "pathVariableData": [], "queryParams": [], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "events": [{"listen": "test", "script": {"id": "6a6262ec-ef09-4422-9dfd-f85e0986a67a", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"id": "821dc4ed-6113-4193-b437-18e7c8fa5c00", "exec": ["path=`/move-money-bff/activity`;", "_.analyzeRiskScoreIst(pm, path);"], "type": "text/javascript"}}], "folder": null, "responses_order": [], "preRequestScript": "path=`/move-money-bff/activity`;\n_.analyzeRiskScoreIst(pm, path);", "tests": "pm.test(\"Status code is 200\", function () {\n    pm.response.to.have.status(200);\n});", "currentHelper": "<PERSON><PERSON><PERSON>", "helperAttributes": {"id": "bearer", "token": "{{auth_token}}"}, "collectionId": "6f72a114-c577-4d6c-aa4f-c8f7a47fed5d", "rawModeData": " {\n    \"requestGuid\": \"d45ede87-fcaa-4225-9237-e038c8a19abcd\",\n    \"id\": \"3390525\",\n    \"fromAccountId\": \"d45ede87-fcaa-4225-9237-e038c8a19bd4\",\n    \"toAccountId\": \"2e4a20c0-33c1-4208-a6e0-f0dc3b7b1e24\",\n    \"amount\": 20.02,\n    \"dueDate\": \"2022-09-08\",\n    \"frequency\": \"ONCE_A_MONTH\",\n    \"numberOfTransactions\": 100\n}", "headers": "scope: openid\nsub: {{mockUserID}}\nx-device-uid: asdfasdfasdf\n//x-api-key: postman\nx-client-version: 4.0\nx-platform-version: 9.3.1\nx-platform: Apple\nx-risk-authorization: {{risk_token}}\n", "pathVariables": {}}, {"id": "8ed32633-de0c-4afb-ab18-e8da7ae2e747", "uid": "0-8ed32633-de0c-4afb-ab18-e8da7ae2e747", "name": "externaltransfer/updateAccountNickname", "url": "{{protocol}}://{{host}}:{{port}}{{servletcontext}}{{context}}/externaltransfer/updateAccountNickname", "description": "Demonstrates the mock-oauth2 flow for local development.  This allows us to easily define security expectations in integration tests for what scopes the requestor has without using a full-fledged oauth2 provider.  This is controlled from the client via headers.\n\nPlease note the 'scopes' and 'userId' headers", "data": [], "dataOptions": {"raw": {"language": "json"}}, "dataMode": "raw", "headerData": [{"key": "scope", "type": "text", "value": "openid"}, {"key": "sub", "type": "text", "value": "{{mockUserID}}"}, {"key": "x-device-uid", "value": "asdfasdfasdf", "type": "text"}, {"key": "x-client-version", "value": "3.0", "type": "text"}], "method": "PUT", "pathVariableData": [], "queryParams": [], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJSUzI1NiIsImtpZCI6IlFac0xydXlkWEdPQ01qaGk4OXdldGlKa25aVSIsInBpLmF0bSI6Im51eWoifQ.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.eQLC4xBm_ih6SB_A0KxctamLR03ZSOJtXk3uo51ehMaepMqU40ARrJ6wy7ZYKQakggqR0RW1RCm0Ijgxb0iMovwNJDya_9pg7cILSaWcscbHW-2ETgHh0EZffvpMbaAkPgnSitlyrqpG78lqqKM-KO19GAHiSHuWyvb4Vn0Ygj3MpnrnWwVMDy90KsjJ2rwEzCIX60XazGUCYm8O-rK9AU0iMNJpXgc-PAo266s-Zr8kk0czlb_T1VNZtie37KECmngsEPIkXCvTYae8dV37V7PXdpxwSNBevThNsNjf6uIlGuXCmHquVbYfOxU4A_mv-qEIj83VxM-SC3uPo9QCgw", "type": "string"}]}, "events": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript", "id": "4c7d995b-b5c9-4cb0-bcde-763dbdc72033"}}, {"listen": "prerequest", "script": {"exec": ["if(! pm.variables.get(\"mockUserID\")){", "    // generate a new, pseudo-random UUID", "    var uuid  = require('uuid');", "    pm.variables.set(\"mockUserID\", uuid.v4()); ", "    console.log(\"mockUserID set to: \"+pm.variables.get(\"mockUserID\"))", "}"], "type": "text/javascript", "id": "d014bd47-64fa-4aff-a91d-7adbe75ea16a"}}], "folder": null, "responses_order": [], "preRequestScript": "if(! pm.variables.get(\"mockUserID\")){\n    // generate a new, pseudo-random UUID\n    var uuid  = require('uuid');\n    pm.variables.set(\"mockUserID\", uuid.v4()); \n    console.log(\"mockUserID set to: \"+pm.variables.get(\"mockUserID\"))\n}", "tests": "pm.test(\"Status code is 200\", function () {\n    pm.response.to.have.status(200);\n});", "currentHelper": "<PERSON><PERSON><PERSON>", "helperAttributes": {"id": "bearer", "token": "eyJhbGciOiJSUzI1NiIsImtpZCI6IlFac0xydXlkWEdPQ01qaGk4OXdldGlKa25aVSIsInBpLmF0bSI6Im51eWoifQ.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.eQLC4xBm_ih6SB_A0KxctamLR03ZSOJtXk3uo51ehMaepMqU40ARrJ6wy7ZYKQakggqR0RW1RCm0Ijgxb0iMovwNJDya_9pg7cILSaWcscbHW-2ETgHh0EZffvpMbaAkPgnSitlyrqpG78lqqKM-KO19GAHiSHuWyvb4Vn0Ygj3MpnrnWwVMDy90KsjJ2rwEzCIX60XazGUCYm8O-rK9AU0iMNJpXgc-PAo266s-Zr8kk0czlb_T1VNZtie37KECmngsEPIkXCvTYae8dV37V7PXdpxwSNBevThNsNjf6uIlGuXCmHquVbYfOxU4A_mv-qEIj83VxM-SC3uPo9QCgw"}, "collectionId": "6f72a114-c577-4d6c-aa4f-c8f7a47fed5d", "rawModeData": "{\n    \"accountId\": \"19F29285-99D2-5C8F-1794-7E9C57F71731\",\n    \"accountNickname\": \"ame3\"\n}", "headers": "scope: openid\nsub: {{mockUserID}}\nx-device-uid: asdfasdfasdf\nx-client-version: 3.0\n", "pathVariables": {}}, {"id": "aa490a63-691d-466d-82e9-0fe57cfefcee", "uid": "0-aa490a63-691d-466d-82e9-0fe57cfefcee", "name": "add payee", "url": "{{protocol}}://{{host}}:{{port}}{{servletcontext}}{{context}}/billpay/addpayee", "description": "Demonstrates the mock-oauth2 flow for local development.  This allows us to easily define security expectations in integration tests for what scopes the requestor has without using a full-fledged oauth2 provider.  This is controlled from the client via headers.\n\nPlease note the 'scopes' and 'userId' headers", "data": [], "dataOptions": {"raw": {"language": "json"}}, "dataMode": "raw", "headerData": [{"key": "scope", "value": "openid", "type": "text"}, {"key": "sub", "value": "{{mockUserID}}", "type": "text"}, {"key": "x-device-uid", "value": "asdfasdfasdf", "type": "text"}, {"key": "x-client-version", "value": "4.0", "type": "text"}, {"key": "x-platform-version", "value": "9.3.1", "type": "text"}, {"key": "x-platform", "value": "Apple", "type": "text"}, {"key": "x-api-key", "value": "postman", "type": "text"}, {"key": "x-risk-authorization", "value": "{{risk_token}}", "type": "text"}], "method": "POST", "pathVariableData": [], "queryParams": [], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "events": [{"listen": "test", "script": {"id": "d5c68ba3-950b-4916-9867-7f1e037f5119", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"id": "69d4ba3c-9139-49c2-90cb-95236a1d19ec", "exec": ["path = `/move-money-bff/billpay/addpayee`;", "_.analyzeRiskScoreIst(pm, path);"], "type": "text/javascript"}}], "folder": null, "responses_order": [], "preRequestScript": "path = `/move-money-bff/billpay/addpayee`;\n_.analyzeRiskScoreIst(pm, path);", "tests": "pm.test(\"Status code is 200\", function () {\n    pm.response.to.have.status(200);\n});", "currentHelper": "<PERSON><PERSON><PERSON>", "helperAttributes": {"id": "bearer", "token": "{{auth_token}}"}, "collectionId": "6f72a114-c577-4d6c-aa4f-c8f7a47fed5d", "rawModeData": "{\n    \"name\": \"no acct number\",\n    \"accountNumber\": null,\n    \"nickname\": \"no acct number\",\n    \"address\": {\n        \"streetLine1\": \"3267 BRAMPTON ST\",\n        \"streetLine2\": \" \",\n        \"city\": \"DUBLIN\",\n        \"stateOrProvince\": \"OH\",\n        \"postalCode\": \"43017\",\n        \"badAddressIndicator\": false,\n        \"country\": null\n    },\n    \"personalPayee\": true\n}", "headers": "scope: openid\nsub: {{mockUserID}}\nx-device-uid: asdfasdfasdf\nx-client-version: 4.0\nx-platform-version: 9.3.1\nx-platform: Apple\nx-api-key: postman\nx-risk-authorization: {{risk_token}}\n", "pathVariables": {}}, {"id": "ac43ca70-eebb-4a95-9fc3-4cdeacee4891", "uid": "0-ac43ca70-eebb-4a95-9fc3-4cdeacee4891", "name": "account/detail", "url": "{{protocol}}://{{host}}:{{port}}{{servletcontext}}{{context}}/account/detail?id=b5604ba3-a90f-43f2-98ab-693b61acb7f8", "description": "Demonstrates the mock-oauth2 flow for local development.  This allows us to easily define security expectations in integration tests for what scopes the requestor has without using a full-fledged oauth2 provider.  This is controlled from the client via headers.\n\nPlease note the 'scopes' and 'userId' headers", "data": null, "dataOptions": null, "dataMode": null, "headerData": [{"key": "scope", "type": "text", "value": "openid"}, {"key": "sub", "type": "text", "value": "{{mockUserID}}"}, {"key": "x-device-uid", "value": "asdfasdfasdf", "type": "text"}, {"key": "x-client-version", "value": "4.0", "type": "text"}, {"key": "x-platform-version", "value": "9.3.1", "type": "text"}, {"key": "x-platform", "value": "Apple", "type": "text"}, {"key": "x-api-key", "value": "postman", "type": "text"}], "method": "GET", "pathVariableData": [], "queryParams": [{"key": "id", "value": "b5604ba3-a90f-43f2-98ab-693b61acb7f8"}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "events": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript", "id": "fb1d95f1-c8a1-4e98-affc-0a36d602ad7d"}}, {"listen": "prerequest", "script": {"exec": ["if(! pm.variables.get(\"mockUserID\")){", "    // generate a new, pseudo-random UUID", "    var uuid  = require('uuid');", "    pm.variables.set(\"mockUserID\", uuid.v4()); ", "    console.log(\"mockUserID set to: \"+pm.variables.get(\"mockUserID\"))", "}"], "type": "text/javascript", "id": "131d6908-f7e9-4790-8802-70114e57f0b0"}}], "folder": null, "responses_order": [], "preRequestScript": "if(! pm.variables.get(\"mockUserID\")){\n    // generate a new, pseudo-random UUID\n    var uuid  = require('uuid');\n    pm.variables.set(\"mockUserID\", uuid.v4()); \n    console.log(\"mockUserID set to: \"+pm.variables.get(\"mockUserID\"))\n}", "tests": "pm.test(\"Status code is 200\", function () {\n    pm.response.to.have.status(200);\n});", "currentHelper": "<PERSON><PERSON><PERSON>", "helperAttributes": {"id": "bearer", "token": "{{auth_token}}"}, "collectionId": "6f72a114-c577-4d6c-aa4f-c8f7a47fed5d", "headers": "scope: openid\nsub: {{mockUserID}}\nx-device-uid: asdfasdfasdf\nx-client-version: 4.0\nx-platform-version: 9.3.1\nx-platform: Apple\nx-api-key: postman\n", "pathVariables": {}}, {"id": "b8a7fe9d-fbf5-4e2d-b233-7b3657cef80d", "uid": "0-b8a7fe9d-fbf5-4e2d-b233-7b3657cef80d", "name": "transfer/info", "url": "{{protocol}}://{{host}}:{{port}}{{servletcontext}}{{context}}/transfer/info?useAccountCache=true&useProfileCache=true", "description": "Demonstrates the mock-oauth2 flow for local development.  This allows us to easily define security expectations in integration tests for what scopes the requestor has without using a full-fledged oauth2 provider.  This is controlled from the client via headers.\n\nPlease note the 'scopes' and 'userId' headers", "data": null, "dataOptions": null, "dataMode": null, "headerData": [{"key": "scope", "value": "openid", "type": "text"}, {"key": "sub", "value": "{{mockUserID}}", "type": "text"}, {"key": "x-device-uid", "value": "asdfasdfasdf", "type": "text"}, {"key": "x-client-version", "value": "4.0", "type": "text"}, {"key": "x-platform-version", "value": "9.3.1", "type": "text"}, {"key": "x-platform", "value": "Apple", "type": "text"}, {"key": "x-api-key", "value": "postman", "type": "text"}], "method": "GET", "pathVariableData": [], "queryParams": [{"key": "useAccountCache", "value": "true"}, {"key": "useProfileCache", "value": "true"}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "events": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript", "id": "eef57ab7-8086-4871-b4b7-eb707e8873b9"}}, {"listen": "prerequest", "script": {"exec": ["if(! pm.variables.get(\"mockUserID\")){", "    // generate a new, pseudo-random UUID", "    var uuid  = require('uuid');", "    pm.variables.set(\"mockUserID\", uuid.v4()); ", "    console.log(\"mockUserID set to: \"+pm.variables.get(\"mockUserID\"))", "}"], "type": "text/javascript", "id": "a9a6d9dc-5951-444f-b92d-2de91511c9ee"}}], "folder": null, "responses_order": [], "preRequestScript": "if(! pm.variables.get(\"mockUserID\")){\n    // generate a new, pseudo-random UUID\n    var uuid  = require('uuid');\n    pm.variables.set(\"mockUserID\", uuid.v4()); \n    console.log(\"mockUserID set to: \"+pm.variables.get(\"mockUserID\"))\n}", "tests": "pm.test(\"Status code is 200\", function () {\n    pm.response.to.have.status(200);\n});", "currentHelper": "<PERSON><PERSON><PERSON>", "helperAttributes": {"id": "bearer", "token": "{{auth_token}}"}, "collectionId": "6f72a114-c577-4d6c-aa4f-c8f7a47fed5d", "headers": "scope: openid\nsub: {{mockUserID}}\nx-device-uid: asdfasdfasdf\nx-client-version: 4.0\nx-platform-version: 9.3.1\nx-platform: Apple\nx-api-key: postman\n", "pathVariables": {}}, {"id": "bb912d9b-2cdd-4d5d-80a8-31127be43560", "uid": "0-bb912d9b-2cdd-4d5d-80a8-31127be43560", "name": "externaltransfer/brokerages", "url": "{{protocol}}://{{host}}:{{port}}{{servletcontext}}{{context}}/externaltransfer/brokerages", "description": "Demonstrates the mock-oauth2 flow for local development.  This allows us to easily define security expectations in integration tests for what scopes the requestor has without using a full-fledged oauth2 provider.  This is controlled from the client via headers.\n\nPlease note the 'scopes' and 'userId' headers", "data": null, "dataOptions": null, "dataMode": null, "headerData": [{"key": "scope", "type": "text", "value": "openid"}, {"key": "sub", "type": "text", "value": "{{mockUserID}}"}, {"key": "x-device-uid", "value": "asdfasdfasdf", "type": "text"}, {"key": "x-client-version", "value": "3.0", "type": "text"}], "method": "GET", "pathVariableData": [], "queryParams": [], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJSUzI1NiIsImtpZCI6IlFac0xydXlkWEdPQ01qaGk4OXdldGlKa25aVSIsInBpLmF0bSI6Im51eWoifQ.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.kkfAXlwjeH3Hvyrw8DPqKAkjdwbeG_I2wAxY1EH8JhkoXDV185QxGQLavHcMTNn1PzFcC-uxurGyWJ9nNO0CmDpdIvzJvPHiMQj4rAEzRdEz0-s1tqBaNtskHLYp3dL5mdByvDXzr_00-klTLcQe571CIUBPZ7ZB3_Hy6B7oHj-da5TPCMNmcxvgg2s6Ws-OSewDxC3qRUIuy580sTNc_px4i_8KiufkljdyW5_YhzlppD0sPNkiJxRE_WvlIZHrFHPenjaaPfHGhccz8US648ZsCkPA4AAIDnaajLZS0OHApLXGcqfoZS_czrh7NHE9c_unde-jie8Xu4EVwumj-Q", "type": "string"}]}, "events": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript", "id": "c19950fd-f061-4fcd-8df4-2a9ceee7ea75"}}, {"listen": "prerequest", "script": {"exec": ["if(! pm.variables.get(\"mockUserID\")){", "    // generate a new, pseudo-random UUID", "    var uuid  = require('uuid');", "    pm.variables.set(\"mockUserID\", uuid.v4()); ", "    console.log(\"mockUserID set to: \"+pm.variables.get(\"mockUserID\"))", "}"], "type": "text/javascript", "id": "bb746949-0ba5-4eda-90b3-1ec4a91315f0"}}], "folder": null, "responses_order": [], "preRequestScript": "if(! pm.variables.get(\"mockUserID\")){\n    // generate a new, pseudo-random UUID\n    var uuid  = require('uuid');\n    pm.variables.set(\"mockUserID\", uuid.v4()); \n    console.log(\"mockUserID set to: \"+pm.variables.get(\"mockUserID\"))\n}", "tests": "pm.test(\"Status code is 200\", function () {\n    pm.response.to.have.status(200);\n});", "currentHelper": "<PERSON><PERSON><PERSON>", "helperAttributes": {"id": "bearer", "token": "eyJhbGciOiJSUzI1NiIsImtpZCI6IlFac0xydXlkWEdPQ01qaGk4OXdldGlKa25aVSIsInBpLmF0bSI6Im51eWoifQ.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.kkfAXlwjeH3Hvyrw8DPqKAkjdwbeG_I2wAxY1EH8JhkoXDV185QxGQLavHcMTNn1PzFcC-uxurGyWJ9nNO0CmDpdIvzJvPHiMQj4rAEzRdEz0-s1tqBaNtskHLYp3dL5mdByvDXzr_00-klTLcQe571CIUBPZ7ZB3_Hy6B7oHj-da5TPCMNmcxvgg2s6Ws-OSewDxC3qRUIuy580sTNc_px4i_8KiufkljdyW5_YhzlppD0sPNkiJxRE_WvlIZHrFHPenjaaPfHGhccz8US648ZsCkPA4AAIDnaajLZS0OHApLXGcqfoZS_czrh7NHE9c_unde-jie8Xu4EVwumj-Q"}, "collectionId": "6f72a114-c577-4d6c-aa4f-c8f7a47fed5d", "headers": "scope: openid\nsub: {{mockUserID}}\nx-device-uid: asdfasdfasdf\nx-client-version: 3.0\n", "pathVariables": {}}, {"id": "cc6e4d38-1cf2-4d21-b467-9ab0c89ff59e", "uid": "0-cc6e4d38-1cf2-4d21-b467-9ab0c89ff59e", "name": "externaltransfer/accountVerificationInfo", "url": "{{protocol}}://{{host}}:{{port}}{{servletcontext}}{{context}}/externaltransfer/accountVerificationInfo?accountId=6558831A-874F-57DC-EE52-CA17F6076C09", "description": "Demonstrates the mock-oauth2 flow for local development.  This allows us to easily define security expectations in integration tests for what scopes the requestor has without using a full-fledged oauth2 provider.  This is controlled from the client via headers.\n\nPlease note the 'scopes' and 'userId' headers", "data": null, "dataOptions": null, "dataMode": null, "headerData": [{"key": "scope", "type": "text", "value": "openid"}, {"key": "sub", "type": "text", "value": "{{mockUserID}}"}, {"key": "x-device-uid", "value": "asdfasdfasdf", "type": "text"}, {"key": "x-client-version", "value": "3.0", "type": "text"}], "method": "GET", "pathVariableData": [], "queryParams": [{"key": "accountId", "value": "6558831A-874F-57DC-EE52-CA17F6076C09"}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJSUzI1NiIsImtpZCI6IlFac0xydXlkWEdPQ01qaGk4OXdldGlKa25aVSIsInBpLmF0bSI6Im51eWoifQ.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.IZvi7olTdiYj3jx_9HRpn8iN1ns8akjHGVh2I9v460ecMlB4ggIzSd-eXe5yvZom5NI_qdWfKc4taQUGNA90TWwLFzUaDk-4Rpac3a4e00-r2wWOxqeqUILq22UkXFVoc2GwIzLI6Rit6txjHfPFDZKAL5bmWufJ3fewvdfjJZG17wj4LkKfX12H41qE26B4DD-w2T0uh57hyHGhrNkpvFBwwfFHAODAgmev47pzf6H03IkxYvsNawiqpiQfMIVQYHNf9RRl2DN26qnjuo8UZbJHIJ5k3FP_gAmLnJITY4_XumSpXj-CHZ9in4n_ttVX1tU9L1lLVzmLP4EYe7HrpA", "type": "string"}]}, "events": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript", "id": "e355a185-9aa5-4c88-b29e-241544b32400"}}, {"listen": "prerequest", "script": {"exec": ["if(! pm.variables.get(\"mockUserID\")){", "    // generate a new, pseudo-random UUID", "    var uuid  = require('uuid');", "    pm.variables.set(\"mockUserID\", uuid.v4()); ", "    console.log(\"mockUserID set to: \"+pm.variables.get(\"mockUserID\"))", "}"], "type": "text/javascript", "id": "668554cd-6660-4779-a4e2-e40b53119b80"}}], "folder": null, "responses_order": [], "preRequestScript": "if(! pm.variables.get(\"mockUserID\")){\n    // generate a new, pseudo-random UUID\n    var uuid  = require('uuid');\n    pm.variables.set(\"mockUserID\", uuid.v4()); \n    console.log(\"mockUserID set to: \"+pm.variables.get(\"mockUserID\"))\n}", "tests": "pm.test(\"Status code is 200\", function () {\n    pm.response.to.have.status(200);\n});", "currentHelper": "<PERSON><PERSON><PERSON>", "helperAttributes": {"id": "bearer", "token": "eyJhbGciOiJSUzI1NiIsImtpZCI6IlFac0xydXlkWEdPQ01qaGk4OXdldGlKa25aVSIsInBpLmF0bSI6Im51eWoifQ.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.IZvi7olTdiYj3jx_9HRpn8iN1ns8akjHGVh2I9v460ecMlB4ggIzSd-eXe5yvZom5NI_qdWfKc4taQUGNA90TWwLFzUaDk-4Rpac3a4e00-r2wWOxqeqUILq22UkXFVoc2GwIzLI6Rit6txjHfPFDZKAL5bmWufJ3fewvdfjJZG17wj4LkKfX12H41qE26B4DD-w2T0uh57hyHGhrNkpvFBwwfFHAODAgmev47pzf6H03IkxYvsNawiqpiQfMIVQYHNf9RRl2DN26qnjuo8UZbJHIJ5k3FP_gAmLnJITY4_XumSpXj-CHZ9in4n_ttVX1tU9L1lLVzmLP4EYe7HrpA"}, "collectionId": "6f72a114-c577-4d6c-aa4f-c8f7a47fed5d", "headers": "scope: openid\nsub: {{mockUserID}}\nx-device-uid: asdfasdfasdf\nx-client-version: 3.0\n", "pathVariables": {}}, {"id": "e37be14e-ce61-4a03-b793-33b5771a778f", "uid": "0-e37be14e-ce61-4a03-b793-33b5771a778f", "name": "delete payee", "url": "{{protocol}}://{{host}}:{{port}}{{servletcontext}}{{context}}/billpay/deletepayee?payeeId=82F0DD1F-1588-BE33-0B1C-78FAE947A066", "description": "Demonstrates the mock-oauth2 flow for local development.  This allows us to easily define security expectations in integration tests for what scopes the requestor has without using a full-fledged oauth2 provider.  This is controlled from the client via headers.\n\nPlease note the 'scopes' and 'userId' headers", "data": [], "dataOptions": {"raw": {"language": "json"}}, "dataMode": "raw", "headerData": [{"key": "scope", "type": "text", "value": "openid"}, {"key": "sub", "type": "text", "value": "{{mockUserID}}"}], "method": "DELETE", "pathVariableData": [], "queryParams": [{"key": "payeeId", "value": "82F0DD1F-1588-BE33-0B1C-78FAE947A066"}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJSUzI1NiIsImtpZCI6IlFac0xydXlkWEdPQ01qaGk4OXdldGlKa25aVSIsInBpLmF0bSI6Im51eWoifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.J56W0kCBOeZ3Je__8W_LKN2G66Wd5jwmURp6VqOxX_9nFQMfBI1zt5pIizUw2XmNrfhGXuMJ0p_wCGq7FeSwqaOMkGjdU_BwuKOz0BLfBHLVdtaz1WHZrqwJ2spZkj8lyqjfiUXE6w_IFeWV5GPj7EK3wi-i2dlxGuti6TmuBwMb3oetUUzNAvcYgb2EX3JkhjJ1ieuSjPfQdI12bigIPg5DNlKrhOEfx5Wp4kucJD0qPtsvVi0AXXolne0x8YlCwv4aYAZJf8ZslxRLLimnv52kziWDBY1qFS51nGJJsmzh109l_6B3-WW6Eeh6LB5hM4kfcavf3t6hdKN15_GqrQ", "type": "string"}]}, "events": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript", "id": "08df6891-adfd-499b-bdb3-fb878ee0e14f"}}, {"listen": "prerequest", "script": {"exec": ["if(! pm.variables.get(\"mockUserID\")){", "    // generate a new, pseudo-random UUID", "    var uuid  = require('uuid');", "    pm.variables.set(\"mockUserID\", uuid.v4()); ", "    console.log(\"mockUserID set to: \"+pm.variables.get(\"mockUserID\"))", "}"], "type": "text/javascript", "id": "4aa28775-de76-47a7-be70-5d37b2a7d01d"}}], "folder": null, "responses_order": [], "preRequestScript": "if(! pm.variables.get(\"mockUserID\")){\n    // generate a new, pseudo-random UUID\n    var uuid  = require('uuid');\n    pm.variables.set(\"mockUserID\", uuid.v4()); \n    console.log(\"mockUserID set to: \"+pm.variables.get(\"mockUserID\"))\n}", "tests": "pm.test(\"Status code is 200\", function () {\n    pm.response.to.have.status(200);\n});", "currentHelper": "<PERSON><PERSON><PERSON>", "helperAttributes": {"id": "bearer", "token": "eyJhbGciOiJSUzI1NiIsImtpZCI6IlFac0xydXlkWEdPQ01qaGk4OXdldGlKa25aVSIsInBpLmF0bSI6Im51eWoifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.J56W0kCBOeZ3Je__8W_LKN2G66Wd5jwmURp6VqOxX_9nFQMfBI1zt5pIizUw2XmNrfhGXuMJ0p_wCGq7FeSwqaOMkGjdU_BwuKOz0BLfBHLVdtaz1WHZrqwJ2spZkj8lyqjfiUXE6w_IFeWV5GPj7EK3wi-i2dlxGuti6TmuBwMb3oetUUzNAvcYgb2EX3JkhjJ1ieuSjPfQdI12bigIPg5DNlKrhOEfx5Wp4kucJD0qPtsvVi0AXXolne0x8YlCwv4aYAZJf8ZslxRLLimnv52kziWDBY1qFS51nGJJsmzh109l_6B3-WW6Eeh6LB5hM4kfcavf3t6hdKN15_GqrQ"}, "collectionId": "6f72a114-c577-4d6c-aa4f-c8f7a47fed5d", "rawModeData": "{\n    \"name\": \"Time Warner Cable - NYC\",\n    \"accountNumber\": \"********\",\n    \"nickname\": \"time warner cable\"\n}", "headers": "scope: openid\nsub: {{mockUserID}}\n", "pathVariables": {}}, {"id": "eb27c583-451c-4a39-a1ac-292d58aa7254", "uid": "0-eb27c583-451c-4a39-a1ac-292d58aa7254", "name": "externaltransfer/verifyAccountRealTime Copy", "url": "{{protocol}}://{{host}}:{{port}}{{servletcontext}}{{context}}/externaltransfer/verifyAccountRealTime", "description": "Demonstrates the mock-oauth2 flow for local development.  This allows us to easily define security expectations in integration tests for what scopes the requestor has without using a full-fledged oauth2 provider.  This is controlled from the client via headers.\n\nPlease note the 'scopes' and 'userId' headers", "data": [], "dataOptions": {"raw": {"language": "json"}}, "dataMode": "raw", "headerData": [{"key": "scope", "type": "text", "value": "openid"}, {"key": "sub", "type": "text", "value": "{{mockUserID}}"}, {"key": "x-risk-authorization", "value": "{{risk_token}}", "description": "", "type": "default", "enabled": true}], "method": "POST", "pathVariableData": [], "queryParams": [], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}"}]}, "events": [{"listen": "test", "script": {"id": "61b7a4ea-d632-45c3-8ea8-6c586c83065d", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"id": "aa8907f5-395a-4f66-86e5-fc41ea00f7cb", "exec": ["path=`/move-money-bff/externaltransfer/verifyAccountRealTime`;", "_.analyzeRiskScoreIst(pm, path);"], "type": "text/javascript"}}], "folder": null, "responses_order": [], "preRequestScript": "path=`/move-money-bff/externaltransfer/verifyAccountRealTime`;\n_.analyzeRiskScoreIst(pm, path);", "tests": "pm.test(\"Status code is 200\", function () {\n    pm.response.to.have.status(200);\n});", "currentHelper": "<PERSON><PERSON><PERSON>", "helperAttributes": {"id": "bearer", "token": "{{auth_token}}"}, "collectionId": "6f72a114-c577-4d6c-aa4f-c8f7a47fed5d", "rawModeData": "{\n    \"accountId\": \"asdfasdf-mma-checking\",\n    \"verificationParameters\": [\n        {\n            \"name\": \"some name\",\n            \"value\": \"some value\"\n        },\n        {\n            \"name\": \"some name\",\n            \"value\": \"some value\"\n        }\n    ]\n}", "headers": "scope: openid\nsub: {{mockUserID}}\nx-risk-authorization: {{risk_token}}\n", "pathVariables": {}}, {"id": "ec169a83-d6f4-4405-8396-f41236c2f995", "uid": "0-ec169a83-d6f4-4405-8396-f41236c2f995", "name": "random-full-flow-activity", "url": "{{protocol}}://{{host}}:{{port}}{{servletcontext}}{{context}}/activity", "description": "Demonstrates the mock-oauth2 flow for local development.  This allows us to easily define security expectations in integration tests for what scopes the requestor has without using a full-fledged oauth2 provider.  This is controlled from the client via headers.\n\nPlease note the 'scopes' and 'userId' headers", "data": [], "dataOptions": {"raw": {"language": "json"}}, "dataMode": "raw", "headerData": [{"key": "scope", "type": "text", "value": "openid"}, {"key": "sub", "type": "text", "value": "{{mockUserID}}"}, {"key": "X-Risk-Authorization", "value": "{{risk_token}}", "type": "text"}, {"key": "x-device-uid", "value": "asdfasdfasdf", "type": "text"}, {"key": "x-client-version", "value": "4.0", "type": "text"}, {"key": "x-platform-version", "value": "9.3.1", "type": "text"}, {"key": "x-platform", "value": "Apple", "type": "text"}, {"key": "x-api-key", "value": "postman", "type": "text"}], "method": "POST", "pathVariableData": [], "queryParams": [], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "events": [{"listen": "prerequest", "script": {"id": "98077264-b3fe-4afe-8650-19cb49d97b17", "exec": ["if(! pm.variables.get(\"mockUserID\")){", "    // generate a new, pseudo-random UUID", "    var uuid  = require('uuid');", "    pm.variables.set(\"mockUserID\", uuid.v4()); ", "    console.log(\"mockUserID set to: \"+pm.variables.get(\"mockUserID\"))", "}", "", "data = pm.request.body;", "body = data[data.mode];", "base64Body = CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(body));", "//console.log(\"base64 body: \"+base64Body)", "//console.log(\"Bearer \"+pm.variables.get(\"auth_token\"))", "result = pm.sendRequest( ", "    {", "        method: 'POST',", "        url: \"https://edc2kagkrc-vpce-04e3fad85e181e1ea.execute-api.us-east-2.amazonaws.com/api/risk-microservice/risk/analyze\",", "        header: {", "            'Content-Type': 'application/json',", "                'X-XSRF-Header': 'test',", "                'X-Device-UID': \"BBAB1471-00E5-420B-9362-D1D5F932FC03\",", "                'X-Device-Fingerprint': 'version=1',", "                'Authorization': 'Bearer '+pm.variables.get(\"auth_token\")", "        },", "        body: {", "", "                    mode: 'raw',", "", "                    raw: JSON.stringify({", "                        \"method\": \"POST\",", "                        \"path\": \"/move-money-bff/activity\",", "                        \"orgName\": \"53com\",", "                        \"body\": base64Body", "                    })", "        }", "    }", "    , (error, response) => { ", "        if(error){ console.log(\"Error getting risk_token: \" + error); }", "        else if(response.status === \"Unauthorized\"  || typeof(response.json().riskToken)===\"undefined\"){ console.log(\"Risk Token not set. Is your auth token current?\")}", "        else{", "            //console.log(response.status)", "            pm.variables.set(\"risk_token\", response.json().riskToken);", "            console.log(\"risk_token set\");", "         }", "        //console.log(response.json().riskToken);", "         ", "        //console.log(error ? \"Error: \"+error : \"risk_token set\");", "});", "", ""], "type": "text/javascript"}}, {"listen": "test", "script": {"id": "b77513f8-1e77-4ca8-bcc2-9fbea2b74c23", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript"}}], "folder": null, "responses_order": [], "preRequestScript": "if(! pm.variables.get(\"mockUserID\")){\n    // generate a new, pseudo-random UUID\n    var uuid  = require('uuid');\n    pm.variables.set(\"mockUserID\", uuid.v4()); \n    console.log(\"mockUserID set to: \"+pm.variables.get(\"mockUserID\"))\n}\n\ndata = pm.request.body;\nbody = data[data.mode];\nbase64Body = CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(body));\n//console.log(\"base64 body: \"+base64Body)\n//console.log(\"Bearer \"+pm.variables.get(\"auth_token\"))\nresult = pm.sendRequest( \n    {\n        method: 'POST',\n        url: \"https://edc2kagkrc-vpce-04e3fad85e181e1ea.execute-api.us-east-2.amazonaws.com/api/risk-microservice/risk/analyze\",\n        header: {\n            'Content-Type': 'application/json',\n                'X-XSRF-Header': 'test',\n                'X-Device-UID': \"BBAB1471-00E5-420B-9362-D1D5F932FC03\",\n                'X-Device-Fingerprint': 'version=1',\n                'Authorization': 'Bearer '+pm.variables.get(\"auth_token\")\n        },\n        body: {\n\n                    mode: 'raw',\n\n                    raw: JSON.stringify({\n                        \"method\": \"POST\",\n                        \"path\": \"/move-money-bff/activity\",\n                        \"orgName\": \"53com\",\n                        \"body\": base64Body\n                    })\n        }\n    }\n    , (error, response) => { \n        if(error){ console.log(\"Error getting risk_token: \" + error); }\n        else if(response.status === \"Unauthorized\"  || typeof(response.json().riskToken)===\"undefined\"){ console.log(\"Risk Token not set. Is your auth token current?\")}\n        else{\n            //console.log(response.status)\n            pm.variables.set(\"risk_token\", response.json().riskToken);\n            console.log(\"risk_token set\");\n         }\n        //console.log(response.json().riskToken);\n         \n        //console.log(error ? \"Error: \"+error : \"risk_token set\");\n});\n\n", "tests": "pm.test(\"Status code is 200\", function () {\n    pm.response.to.have.status(200);\n});", "currentHelper": "<PERSON><PERSON><PERSON>", "helperAttributes": {"id": "bearer", "token": "{{auth_token}}"}, "collectionId": "6f72a114-c577-4d6c-aa4f-c8f7a47fed5d", "rawModeData": "{\n    \"amount\": 2.03,\n    \"dueDate\": \"2023-01-18\",\n    \"frequency\": \"ONE_TIME\",\n    \"fromAccountId\": \"96d131d7-fa44-4f37-bb0b-41e65e05c367\",\n    \"toAccountId\": \"a39fa559-2c53-454c-843f-3451d3aba455\",\n    \"invoicedRecurringPayment\": false,\n    \"memo\": \"\",\n    \"requestGuid\": \"266512b1-c64e-4219-ba36-1be9a5a28395\"\n\n}", "headers": "scope: openid\nsub: {{mockUserID}}\nX-Risk-Authorization: {{risk_token}}\nx-device-uid: asdfasdfasdf\nx-client-version: 4.0\nx-platform-version: 9.3.1\nx-platform: Apple\nx-api-key: postman\n", "pathVariables": {}}, {"id": "f6d21b50-f493-47ee-bce4-25c07afdcdc6", "uid": "0-f6d21b50-f493-47ee-bce4-25c07afdcdc6", "name": "activity/transferlimits", "url": "{{protocol}}://{{host}}:{{port}}{{servletcontext}}{{context}}/activity/transferlimits", "description": "Demonstrates the mock-oauth2 flow for local development.  This allows us to easily define security expectations in integration tests for what scopes the requestor has without using a full-fledged oauth2 provider.  This is controlled from the client via headers.\n\nPlease note the 'scopes' and 'userId' headers", "data": null, "dataOptions": null, "dataMode": null, "headerData": [{"key": "scope", "value": "openid", "type": "text"}, {"key": "sub", "value": "{{mockUserID}}", "type": "text"}], "method": "GET", "pathVariableData": [], "queryParams": [], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJSUzI1NiIsImtpZCI6IlFac0xydXlkWEdPQ01qaGk4OXdldGlKa25aVSIsInBpLmF0bSI6Im51eWoifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.EW-ay8GIxdqnfdsHSyGs6zmEkgkXJa5K8HrBa7YjFmorXTYOavtp61oYlzltXJJRiiRy8pe6oKKtf9zVwpfgO-mMVLd-tDKyMxdCpvexpj1fKemkfyCSttkxeF9VERuho_lV5Astabp0HkbNorV2_ZhmKTMkRwSaddksPKDygbu-sgGsdtvt_dJjdHzHz4hsHVBKrrImuxqUsmW7jovUF2YHRR2cXzDUT6MZ__qZ2GITyIgmbkAwJzCsvV0Sd2ZqWJwY6q4xFtLdrbLrjizaec59MbpsOtzbehRrH116ZRlKJdjjSnWKsAvg0e25jTQFqOv1KiVIGt6Qgmh04zV2fw", "type": "string"}]}, "events": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript", "id": "3001312e-6b68-4b37-814c-ddc56be7b342"}}, {"listen": "prerequest", "script": {"exec": ["if(! pm.variables.get(\"mockUserID\")){", "    // generate a new, pseudo-random UUID", "    var uuid  = require('uuid');", "    pm.variables.set(\"mockUserID\", uuid.v4()); ", "    console.log(\"mockUserID set to: \"+pm.variables.get(\"mockUserID\"))", "}"], "type": "text/javascript", "id": "469617d9-268a-40f9-948d-53b621e8a952"}}], "folder": null, "responses_order": [], "preRequestScript": "if(! pm.variables.get(\"mockUserID\")){\n    // generate a new, pseudo-random UUID\n    var uuid  = require('uuid');\n    pm.variables.set(\"mockUserID\", uuid.v4()); \n    console.log(\"mockUserID set to: \"+pm.variables.get(\"mockUserID\"))\n}", "tests": "pm.test(\"Status code is 200\", function () {\n    pm.response.to.have.status(200);\n});", "currentHelper": "<PERSON><PERSON><PERSON>", "helperAttributes": {"id": "bearer", "token": "eyJhbGciOiJSUzI1NiIsImtpZCI6IlFac0xydXlkWEdPQ01qaGk4OXdldGlKa25aVSIsInBpLmF0bSI6Im51eWoifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.EW-ay8GIxdqnfdsHSyGs6zmEkgkXJa5K8HrBa7YjFmorXTYOavtp61oYlzltXJJRiiRy8pe6oKKtf9zVwpfgO-mMVLd-tDKyMxdCpvexpj1fKemkfyCSttkxeF9VERuho_lV5Astabp0HkbNorV2_ZhmKTMkRwSaddksPKDygbu-sgGsdtvt_dJjdHzHz4hsHVBKrrImuxqUsmW7jovUF2YHRR2cXzDUT6MZ__qZ2GITyIgmbkAwJzCsvV0Sd2ZqWJwY6q4xFtLdrbLrjizaec59MbpsOtzbehRrH116ZRlKJdjjSnWKsAvg0e25jTQFqOv1KiVIGt6Qgmh04zV2fw"}, "collectionId": "6f72a114-c577-4d6c-aa4f-c8f7a47fed5d", "headers": "scope: openid\nsub: {{mockUserID}}\n", "pathVariables": {}}]}, {"id": "84c6489f-ec63-4711-ab47-dec35208f664", "uid": "0-84c6489f-ec63-4711-ab47-dec35208f664", "name": "CES-Proxy", "description": null, "auth": {"type": "oauth2", "oauth2": [{"key": "clientSecret", "value": "bajldfvpgomtka73c3lagg4g2u2tr0ootua3hdee2dfpptqah42", "type": "string"}, {"key": "clientId", "value": "**************************", "type": "string"}, {"key": "accessTokenUrl", "value": "https://consumer-mobile-dev.auth.us-east-2.amazoncognito.com/oauth2/token", "type": "string"}, {"key": "authUrl", "value": "https://consumer-mobile-dev.auth.us-east-2.amazoncognito.com/oauth2/authorize", "type": "string"}, {"key": "useBrowser", "value": true, "type": "boolean"}, {"key": "grant_type", "value": "authorization_code", "type": "string"}, {"key": "redirect_uri", "value": "fitb://callback", "type": "string"}, {"key": "scope", "value": "openid", "type": "string"}, {"key": "tokenName", "value": "access_token", "type": "string"}, {"key": "addTokenTo", "value": "header", "type": "string"}]}, "events": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""], "id": "dcde0c57-9505-4037-901f-202c8a93d447"}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""], "id": "0e05818e-3412-4ac8-9fed-5aebc66edce0"}}], "variables": [], "order": ["a6be53c2-54fc-4978-ab07-094954484e4c", "cde27074-117f-47fc-b80e-688b3ed086e2", "8c31ce1e-b3ca-401a-b82a-0eab89105839", "0f700b52-4633-4187-8ab7-6dad3bbd2b37", "a937d480-7229-4bcf-9a5d-ce8d4ab3af37", "47e1ecd1-7345-4bc0-85d8-488c410035de", "1886a08d-df63-4c38-ba8a-c57a319e29af", "a5561f24-f0d7-463b-80b2-68d261ea4cf3", "1c098339-ee9a-48e0-9654-189b6ccf0671", "e44b2f6b-3c06-475d-8e47-efef895305b8", "9ee65888-0059-4912-98d0-b0cb26c16e03", "5de37a1d-f381-4daa-8c7c-12c7c69f5549", "5fff9c6d-2bd4-427e-8969-04859fe90463", "73ce403b-db54-4bad-9db4-efd508ae760a", "527ac280-265b-42cf-94f0-844ed2f7a381", "7c313d0f-b3c0-42a4-8da7-859daf06228a", "9a884b4e-301e-4883-b5db-b544cb4cae80", "66d1f06d-**************-a5bd5419be98", "a055d0b6-9ea4-4311-bc7f-20a164caafa0", "27438ec6-6e7d-4353-a9c3-85ae7d702e67", "58e81f6c-c51c-4e3b-b6eb-33f08244d9c2", "c9380520-74a5-4e22-8271-c32234a1a05a", "fad2e619-e147-4e39-b476-76889cc7a3fa"], "folders_order": [], "protocolProfileBehavior": {}, "createdAt": "2023-01-13T18:55:34.288Z", "folders": [], "requests": [{"id": "0f700b52-4633-4187-8ab7-6dad3bbd2b37", "uid": "0-0f700b52-4633-4187-8ab7-6dad3bbd2b37", "name": "externaltransfer/userAccounts", "url": "{{protocol}}://{{host}}:{{port}}{{servletcontext}}{{context}}/services/externaltransfer/userAccounts", "description": null, "data": [], "dataOptions": {"raw": {"language": "json"}}, "dataMode": "raw", "headerData": [{"warning": "This is a duplicate header and will be overridden by the Authorization header generated by Postman.", "key": "Authorization", "type": "text", "value": "Bearer ey<PERSON>hbGciOiJSUzI1NiIsImtpZCI6IjM5STRoOVl5anNRWTlpa0hhcWdOREQ3WHY4byIsInBpLmF0bSI6Im51eWoifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NLQnQV9wbLOC2lZnKBsc7p5KmC6s8cLEQst917RY4gdC0pmewyjKPA3ANBgSvgb36gFcBhdWsIP-useTEwJn28Q_kxdpdgW9j07OwFZZn8dWJ6yYjvY7sNHu9_Jhq0Wf8d9gEajAIvMsBGmkOk8b1ByCNH4l6xx5KmoDenXeKqW6ioQ3yBqk7Kx4RRJZlmqqim_GIPo6lZKe4V9St4i_X6ZHiuv2kjL_pX2XJT0XZ4kPjsJLPhgknW5S3GdNyofxyKOSYP3UIV3WVs6TQa0FQ8n6iY8gfLTvVmZ4rhIdafOemoCKQOlnbc8uhXlhBcjI9ePmMFMa5AVSQ7zULO9o1A"}, {"key": "x-device-uid", "type": "text", "value": "asdfasdfasdf"}, {"key": "x-client-version", "value": "4.0", "type": "text"}, {"key": "x-platform-version", "value": "9.3.1", "type": "text"}, {"key": "x-platform", "value": "Apple", "type": "text"}, {"key": "x-api-key", "value": "postman", "type": "text"}], "method": "GET", "pathVariableData": [], "queryParams": [], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "events": null, "folder": null, "responses_order": [], "protocolProfileBehavior": {"disableBodyPruning": true}, "currentHelper": "<PERSON><PERSON><PERSON>", "helperAttributes": {"id": "bearer", "token": "{{auth_token}}"}, "collectionId": "84c6489f-ec63-4711-ab47-dec35208f664", "rawModeData": "{\n    \"username\": \"dillon\"\n}", "headers": "Authorization: Bear<PERSON> eyJhbGciOiJSUzI1NiIsImtpZCI6IjM5STRoOVl5anNRWTlpa0hhcWdOREQ3WHY4byIsInBpLmF0bSI6Im51eWoifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NLQnQV9wbLOC2lZnKBsc7p5KmC6s8cLEQst917RY4gdC0pmewyjKPA3ANBgSvgb36gFcBhdWsIP-useTEwJn28Q_kxdpdgW9j07OwFZZn8dWJ6yYjvY7sNHu9_Jhq0Wf8d9gEajAIvMsBGmkOk8b1ByCNH4l6xx5KmoDenXeKqW6ioQ3yBqk7Kx4RRJZlmqqim_GIPo6lZKe4V9St4i_X6ZHiuv2kjL_pX2XJT0XZ4kPjsJLPhgknW5S3GdNyofxyKOSYP3UIV3WVs6TQa0FQ8n6iY8gfLTvVmZ4rhIdafOemoCKQOlnbc8uhXlhBcjI9ePmMFMa5AVSQ7zULO9o1A\nx-device-uid: asdfasdfasdf\nx-client-version: 4.0\nx-platform-version: 9.3.1\nx-platform: Apple\nx-api-key: postman\n", "pathVariables": {}}, {"id": "1886a08d-df63-4c38-ba8a-c57a319e29af", "uid": "0-1886a08d-df63-4c38-ba8a-c57a319e29af", "name": "transferandpay/profile", "url": "{{protocol}}://{{host}}:{{port}}{{servletcontext}}{{context}}/services/transferandpay/profile", "description": null, "data": [], "dataOptions": {"raw": {"language": "json"}}, "dataMode": "raw", "headerData": [{"key": "x-device-uid", "value": "asdfasdfasdf, asdfasdfasdf", "type": "text"}, {"key": "x-api-key", "value": "postman", "type": "text"}, {"key": "x-client-version", "value": "4.0", "type": "text"}, {"key": "x-platform-version", "value": "9.3.1", "type": "text"}, {"key": "x-platform", "value": "Apple", "type": "text"}], "method": "GET", "pathVariableData": [], "queryParams": [], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "events": null, "folder": null, "responses_order": [], "protocolProfileBehavior": {"disableBodyPruning": true}, "currentHelper": "<PERSON><PERSON><PERSON>", "helperAttributes": {"id": "bearer", "token": "{{auth_token}}"}, "collectionId": "84c6489f-ec63-4711-ab47-dec35208f664", "rawModeData": "{\n    \"username\": \"dillon\"\n}", "headers": "x-device-uid: asdfasdfasdf, asdfasdfasdf\nx-api-key: postman\nx-client-version: 4.0\nx-platform-version: 9.3.1\nx-platform: Apple\n", "pathVariables": {}}, {"id": "1c098339-ee9a-48e0-9654-189b6ccf0671", "uid": "0-1c098339-ee9a-48e0-9654-189b6ccf0671", "name": "transferandpay/activity", "url": "{{protocol}}://{{host}}:{{port}}{{servletcontext}}{{context}}/services/transferandpay/activity", "description": null, "data": [], "dataOptions": {"raw": {"language": "json"}}, "dataMode": "raw", "headerData": [{"key": "Authorization", "type": "text", "value": "Bearer ey<PERSON>hbGciOiJSUzI1NiIsImtpZCI6IjM5STRoOVl5anNRWTlpa0hhcWdOREQ3WHY4byIsInBpLmF0bSI6Im51eWoifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NLQnQV9wbLOC2lZnKBsc7p5KmC6s8cLEQst917RY4gdC0pmewyjKPA3ANBgSvgb36gFcBhdWsIP-useTEwJn28Q_kxdpdgW9j07OwFZZn8dWJ6yYjvY7sNHu9_Jhq0Wf8d9gEajAIvMsBGmkOk8b1ByCNH4l6xx5KmoDenXeKqW6ioQ3yBqk7Kx4RRJZlmqqim_GIPo6lZKe4V9St4i_X6ZHiuv2kjL_pX2XJT0XZ4kPjsJLPhgknW5S3GdNyofxyKOSYP3UIV3WVs6TQa0FQ8n6iY8gfLTvVmZ4rhIdafOemoCKQOlnbc8uhXlhBcjI9ePmMFMa5AVSQ7zULO9o1A"}], "method": "PUT", "pathVariableData": [], "queryParams": [], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "type": "string"}]}, "events": null, "folder": null, "responses_order": [], "currentHelper": "<PERSON><PERSON><PERSON>", "helperAttributes": {"id": "bearer", "token": "*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, "collectionId": "84c6489f-ec63-4711-ab47-dec35208f664", "rawModeData": "{\n    \"id\": \"4782652\",\n    \"amount\": 1.2,\n    \"dueDate\": \"2021-04-13\",\n    \"displayId\": \"4782652\",\n    \"fromAccountId\": \"3DDA2029-305F-EF38-136C-32A99EBECFE3\",\n    \"fromAccountNumber\": \"X6648\",\n    \"fromAccountName\": \"FIFTH THIRD MOMENTUM CK PLUS\",\n    \"toAccountId\": \"1FA702D7-BDDB-E337-FDF8-8ED26C9391B7\",\n    \"toAccountNumber\": \"X9461\",\n    \"toAccountName\": \"5/3 PREFERRED CHECKING\",\n    \"expressDelivery\": false,\n    \"status\": \"4\",\n    \"displayStatus\": \"Scheduled\",\n    \"createTimestamp\": \"2021-04-09T13:25:31.000Z\",\n    \"editable\": true,\n    \"cancelable\": true,\n    \"type\": \"INTERNAL_TRANSFER\"\n}", "headers": "Authorization: Bearer ey<PERSON>hbGciOiJSUzI1NiIsImtpZCI6IjM5STRoOVl5anNRWTlpa0hhcWdOREQ3WHY4byIsInBpLmF0bSI6Im51eWoifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NLQnQV9wbLOC2lZnKBsc7p5KmC6s8cLEQst917RY4gdC0pmewyjKPA3ANBgSvgb36gFcBhdWsIP-useTEwJn28Q_kxdpdgW9j07OwFZZn8dWJ6yYjvY7sNHu9_Jhq0Wf8d9gEajAIvMsBGmkOk8b1ByCNH4l6xx5KmoDenXeKqW6ioQ3yBqk7Kx4RRJZlmqqim_GIPo6lZKe4V9St4i_X6ZHiuv2kjL_pX2XJT0XZ4kPjsJLPhgknW5S3GdNyofxyKOSYP3UIV3WVs6TQa0FQ8n6iY8gfLTvVmZ4rhIdafOemoCKQOlnbc8uhXlhBcjI9ePmMFMa5AVSQ7zULO9o1A\n", "pathVariables": {}}, {"id": "27438ec6-6e7d-4353-a9c3-85ae7d702e67", "uid": "0-27438ec6-6e7d-4353-a9c3-85ae7d702e67", "name": "billpay edit payee", "url": "{{protocol}}://{{host}}:{{port}}{{servletcontext}}{{context}}/tokenized/services/billpay/updatePayee", "description": null, "data": [], "dataOptions": {"raw": {"language": "json"}}, "dataMode": "raw", "headerData": [{"key": "Authorization", "type": "text", "value": "Bearer ey<PERSON>hbGciOiJSUzI1NiIsImtpZCI6IjM5STRoOVl5anNRWTlpa0hhcWdOREQ3WHY4byIsInBpLmF0bSI6Im51eWoifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NLQnQV9wbLOC2lZnKBsc7p5KmC6s8cLEQst917RY4gdC0pmewyjKPA3ANBgSvgb36gFcBhdWsIP-useTEwJn28Q_kxdpdgW9j07OwFZZn8dWJ6yYjvY7sNHu9_Jhq0Wf8d9gEajAIvMsBGmkOk8b1ByCNH4l6xx5KmoDenXeKqW6ioQ3yBqk7Kx4RRJZlmqqim_GIPo6lZKe4V9St4i_X6ZHiuv2kjL_pX2XJT0XZ4kPjsJLPhgknW5S3GdNyofxyKOSYP3UIV3WVs6TQa0FQ8n6iY8gfLTvVmZ4rhIdafOemoCKQOlnbc8uhXlhBcjI9ePmMFMa5AVSQ7zULO9o1A"}], "method": "PUT", "pathVariableData": [], "queryParams": [], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJSUzI1NiIsImtpZCI6IlFac0xydXlkWEdPQ01qaGk4OXdldGlKa25aVSIsInBpLmF0bSI6Im51eWoifQ.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.l5mmfSAlGEYFV4zj72AO3KsYNaZx8Li7xxqxvishixyIGCZjm4RQCcDLEXz13llJsod7m6FMZVSeBW84FmrWzK0j7SmGsNVIFEWygwlamI4Lt4QtaS7p38OeKMP1Gue5cxckASxKCC8B-mgJGwGS7_-9etyoVAXhsuP6uu3E91urcikM06S9I6fm5qBFRYZXQ5ffynO8SmhOhW9dcCcc9bm10Faj41tqxIyh6vJG6vGNs761cQX2TAo-_5cIsxT1OgI1lrvSXiTRSgjkDvlrlHgQYWeXbSABIGNdlDeTWyFJDo1KBzq1u4Ir-h78Psfu3zUnRfvLlKkxXQhQrCBjdg", "type": "string"}]}, "events": null, "folder": null, "responses_order": [], "currentHelper": "<PERSON><PERSON><PERSON>", "helperAttributes": {"id": "bearer", "token": "eyJhbGciOiJSUzI1NiIsImtpZCI6IlFac0xydXlkWEdPQ01qaGk4OXdldGlKa25aVSIsInBpLmF0bSI6Im51eWoifQ.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.l5mmfSAlGEYFV4zj72AO3KsYNaZx8Li7xxqxvishixyIGCZjm4RQCcDLEXz13llJsod7m6FMZVSeBW84FmrWzK0j7SmGsNVIFEWygwlamI4Lt4QtaS7p38OeKMP1Gue5cxckASxKCC8B-mgJGwGS7_-9etyoVAXhsuP6uu3E91urcikM06S9I6fm5qBFRYZXQ5ffynO8SmhOhW9dcCcc9bm10Faj41tqxIyh6vJG6vGNs761cQX2TAo-_5cIsxT1OgI1lrvSXiTRSgjkDvlrlHgQYWeXbSABIGNdlDeTWyFJDo1KBzq1u4Ir-h78Psfu3zUnRfvLlKkxXQhQrCBjdg"}, "collectionId": "84c6489f-ec63-4711-ab47-dec35208f664", "rawModeData": "{\n    \"cesPayee\": {\n        \"TOKENIZED_FIELDS\": {\n            \"accountNumber\": {\n                \"KEY_ID\": {\n                    \"KEY_NAME\": \"co2009/unbound/public.cloud/crit-key\",\n                    \"VERSION\": 1\n                },\n                \"TWEAK\": {\n                    \"VALUE\": \"co2009/unbound/public.cloud/app-tweak\",\n                    \"VERSION\": 1\n                },\n                \"ENCRYPTION_TYPE\": -7,\n                \"ENCRYPTED_ARRAY\": false,\n                \"ENCRYPTION_DATA_TYPE\": null\n            }\n        },\n        \"id\": \"8DFD67D4-9612-CD6D-62AF-716D589FF7B4\",\n        \"nickname\": \"twc-test\",\n        \"nearestNewPaymentDate\": null,\n        \"categoryTypeId\": \"1001\",\n        \"type\": \"PERSONAL\",\n        \"electronicBillingEnabled\": false,\n        \"accountNumber\": \"ģ鬒秮ℙ激挲罐愆\",\n        \"name\": \"Time Warner Cable\",\n        \"paymentCutoffTime\": \"14:00\",\n        \"cesAddress\": {\n            \"streetLine1\": \"7535 HARVESTHOME LN\",\n            \"city\": \"FLORENCE\",\n            \"stateOrProvince\": \"KY\",\n            \"postalCode\": \"41042\",\n            \"badAddressIndicator\": false,\n            \"country\": \"US\"\n        }\n    }\n}", "headers": "Authorization: Bearer ey<PERSON>hbGciOiJSUzI1NiIsImtpZCI6IjM5STRoOVl5anNRWTlpa0hhcWdOREQ3WHY4byIsInBpLmF0bSI6Im51eWoifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NLQnQV9wbLOC2lZnKBsc7p5KmC6s8cLEQst917RY4gdC0pmewyjKPA3ANBgSvgb36gFcBhdWsIP-useTEwJn28Q_kxdpdgW9j07OwFZZn8dWJ6yYjvY7sNHu9_Jhq0Wf8d9gEajAIvMsBGmkOk8b1ByCNH4l6xx5KmoDenXeKqW6ioQ3yBqk7Kx4RRJZlmqqim_GIPo6lZKe4V9St4i_X6ZHiuv2kjL_pX2XJT0XZ4kPjsJLPhgknW5S3GdNyofxyKOSYP3UIV3WVs6TQa0FQ8n6iY8gfLTvVmZ4rhIdafOemoCKQOlnbc8uhXlhBcjI9ePmMFMa5AVSQ7zULO9o1A\n", "pathVariables": {}}, {"id": "47e1ecd1-7345-4bc0-85d8-488c410035de", "uid": "0-47e1ecd1-7345-4bc0-85d8-488c410035de", "name": "local - actuator/health", "url": "{{protocol}}://{{host}}:{{port}}{{servletcontext}}{{context}}/actuator/health", "description": null, "data": [], "dataOptions": {"raw": {"language": "json"}}, "dataMode": "raw", "headerData": [], "method": "GET", "pathVariableData": [], "queryParams": [], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJSUzI1NiIsImtpZCI6IlFac0xydXlkWEdPQ01qaGk4OXdldGlKa25aVSIsInBpLmF0bSI6Im51eWoifQ.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Qz8FVkV_UssMZ_cpRKS9XqDNKpModnCl1U2uOKCbSG3C9D08ZHxgPZ8I01lUE1FRJFGkgw259rDL6JCTvm_PTJvQcZK4EgTWWLSHaU1K-wDpDeVu7PYCkbpp958kSm347LKz69iSSM7e9Pdmk_R7l-1hhv6ZsgxxgFAKQGafqxKXEfC5Xoqi9-U9yCICPODqFquUwXh8IIGu_LiyBJ14iDeaIt0ZcB3Js1MrJq11VWtZ-1k85aQTn9Pp_l7X2v1A2uBB2ItVCCmXE8GcBaBdh7dwjymqkPujzW4qyK2xDn-OrcgDbOcrhur4DE5LcyF9Kx7UtK813Rmq7CPDntOLJg", "type": "string"}]}, "events": null, "folder": null, "responses_order": [], "protocolProfileBehavior": {"disableBodyPruning": true}, "currentHelper": "<PERSON><PERSON><PERSON>", "helperAttributes": {"id": "bearer", "token": "eyJhbGciOiJSUzI1NiIsImtpZCI6IlFac0xydXlkWEdPQ01qaGk4OXdldGlKa25aVSIsInBpLmF0bSI6Im51eWoifQ.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Qz8FVkV_UssMZ_cpRKS9XqDNKpModnCl1U2uOKCbSG3C9D08ZHxgPZ8I01lUE1FRJFGkgw259rDL6JCTvm_PTJvQcZK4EgTWWLSHaU1K-wDpDeVu7PYCkbpp958kSm347LKz69iSSM7e9Pdmk_R7l-1hhv6ZsgxxgFAKQGafqxKXEfC5Xoqi9-U9yCICPODqFquUwXh8IIGu_LiyBJ14iDeaIt0ZcB3Js1MrJq11VWtZ-1k85aQTn9Pp_l7X2v1A2uBB2ItVCCmXE8GcBaBdh7dwjymqkPujzW4qyK2xDn-OrcgDbOcrhur4DE5LcyF9Kx7UtK813Rmq7CPDntOLJg"}, "collectionId": "84c6489f-ec63-4711-ab47-dec35208f664", "rawModeData": "{\n    \"username\": \"dillon\"\n}", "headers": "", "pathVariables": {}}, {"id": "527ac280-265b-42cf-94f0-844ed2f7a381", "uid": "0-527ac280-265b-42cf-94f0-844ed2f7a381", "name": "billpay get account", "url": "{{protocol}}://{{host}}:{{port}}{{servletcontext}}{{context}}/services/billpay/payee/account?payeeId=81F3CDEC-CAB5-6214-3AAE-F2D76570ED4F", "description": null, "data": [], "dataOptions": {"raw": {"language": "json"}}, "dataMode": "raw", "headerData": [{"key": "Authorization", "type": "text", "value": "Bearer ey<PERSON>hbGciOiJSUzI1NiIsImtpZCI6IjM5STRoOVl5anNRWTlpa0hhcWdOREQ3WHY4byIsInBpLmF0bSI6Im51eWoifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NLQnQV9wbLOC2lZnKBsc7p5KmC6s8cLEQst917RY4gdC0pmewyjKPA3ANBgSvgb36gFcBhdWsIP-useTEwJn28Q_kxdpdgW9j07OwFZZn8dWJ6yYjvY7sNHu9_Jhq0Wf8d9gEajAIvMsBGmkOk8b1ByCNH4l6xx5KmoDenXeKqW6ioQ3yBqk7Kx4RRJZlmqqim_GIPo6lZKe4V9St4i_X6ZHiuv2kjL_pX2XJT0XZ4kPjsJLPhgknW5S3GdNyofxyKOSYP3UIV3WVs6TQa0FQ8n6iY8gfLTvVmZ4rhIdafOemoCKQOlnbc8uhXlhBcjI9ePmMFMa5AVSQ7zULO9o1A"}], "method": "GET", "pathVariableData": [], "queryParams": [{"key": "payeeId", "value": "81F3CDEC-CAB5-6214-3AAE-F2D76570ED4F"}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "type": "string"}]}, "events": null, "folder": null, "responses_order": [], "protocolProfileBehavior": {"disableBodyPruning": true}, "currentHelper": "<PERSON><PERSON><PERSON>", "helperAttributes": {"id": "bearer", "token": "*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, "collectionId": "84c6489f-ec63-4711-ab47-dec35208f664", "rawModeData": "", "headers": "Authorization: Bearer ey<PERSON>hbGciOiJSUzI1NiIsImtpZCI6IjM5STRoOVl5anNRWTlpa0hhcWdOREQ3WHY4byIsInBpLmF0bSI6Im51eWoifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NLQnQV9wbLOC2lZnKBsc7p5KmC6s8cLEQst917RY4gdC0pmewyjKPA3ANBgSvgb36gFcBhdWsIP-useTEwJn28Q_kxdpdgW9j07OwFZZn8dWJ6yYjvY7sNHu9_Jhq0Wf8d9gEajAIvMsBGmkOk8b1ByCNH4l6xx5KmoDenXeKqW6ioQ3yBqk7Kx4RRJZlmqqim_GIPo6lZKe4V9St4i_X6ZHiuv2kjL_pX2XJT0XZ4kPjsJLPhgknW5S3GdNyofxyKOSYP3UIV3WVs6TQa0FQ8n6iY8gfLTvVmZ4rhIdafOemoCKQOlnbc8uhXlhBcjI9ePmMFMa5AVSQ7zULO9o1A\n", "pathVariables": {}}, {"id": "58e81f6c-c51c-4e3b-b6eb-33f08244d9c2", "uid": "0-58e81f6c-c51c-4e3b-b6eb-33f08244d9c2", "name": "global payee list", "url": "{{protocol}}://{{host}}:{{port}}{{servletcontext}}{{context}}/services/billpay/globalPayees", "description": null, "data": [], "dataOptions": {"raw": {"language": "json"}}, "dataMode": "raw", "headerData": [{"key": "Authorization", "type": "text", "value": "Bearer ey<PERSON>hbGciOiJSUzI1NiIsImtpZCI6IjM5STRoOVl5anNRWTlpa0hhcWdOREQ3WHY4byIsInBpLmF0bSI6Im51eWoifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NLQnQV9wbLOC2lZnKBsc7p5KmC6s8cLEQst917RY4gdC0pmewyjKPA3ANBgSvgb36gFcBhdWsIP-useTEwJn28Q_kxdpdgW9j07OwFZZn8dWJ6yYjvY7sNHu9_Jhq0Wf8d9gEajAIvMsBGmkOk8b1ByCNH4l6xx5KmoDenXeKqW6ioQ3yBqk7Kx4RRJZlmqqim_GIPo6lZKe4V9St4i_X6ZHiuv2kjL_pX2XJT0XZ4kPjsJLPhgknW5S3GdNyofxyKOSYP3UIV3WVs6TQa0FQ8n6iY8gfLTvVmZ4rhIdafOemoCKQOlnbc8uhXlhBcjI9ePmMFMa5AVSQ7zULO9o1A"}], "method": "GET", "pathVariableData": [], "queryParams": [], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "type": "string"}]}, "events": null, "folder": null, "responses_order": [], "protocolProfileBehavior": {"disableBodyPruning": true}, "currentHelper": "<PERSON><PERSON><PERSON>", "helperAttributes": {"id": "bearer", "token": "*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, "collectionId": "84c6489f-ec63-4711-ab47-dec35208f664", "rawModeData": "{\n    \"username\": \"dillon\"\n}", "headers": "Authorization: Bearer ey<PERSON>hbGciOiJSUzI1NiIsImtpZCI6IjM5STRoOVl5anNRWTlpa0hhcWdOREQ3WHY4byIsInBpLmF0bSI6Im51eWoifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NLQnQV9wbLOC2lZnKBsc7p5KmC6s8cLEQst917RY4gdC0pmewyjKPA3ANBgSvgb36gFcBhdWsIP-useTEwJn28Q_kxdpdgW9j07OwFZZn8dWJ6yYjvY7sNHu9_Jhq0Wf8d9gEajAIvMsBGmkOk8b1ByCNH4l6xx5KmoDenXeKqW6ioQ3yBqk7Kx4RRJZlmqqim_GIPo6lZKe4V9St4i_X6ZHiuv2kjL_pX2XJT0XZ4kPjsJLPhgknW5S3GdNyofxyKOSYP3UIV3WVs6TQa0FQ8n6iY8gfLTvVmZ4rhIdafOemoCKQOlnbc8uhXlhBcjI9ePmMFMa5AVSQ7zULO9o1A\n", "pathVariables": {}}, {"id": "5de37a1d-f381-4daa-8c7c-12c7c69f5549", "uid": "0-5de37a1d-f381-4daa-8c7c-12c7c69f5549", "name": "transfer limits", "url": "{{protocol}}://{{host}}:{{port}}{{servletcontext}}{{context}}/services/externaltransfer/limits?toAcctId=07596D9E-3283-0726-5C75-3A21598A319E&fromAcctId=3ce65e9a-41af-4a74-ad94-ee67fe7a5c97", "description": null, "data": [], "dataOptions": {"raw": {"language": "json"}}, "dataMode": "raw", "headerData": [{"key": "x-device-uid", "value": "asdfasdfasdf", "type": "text"}], "method": "GET", "pathVariableData": [], "queryParams": [{"key": "toAcctId", "value": "07596D9E-3283-0726-5C75-3A21598A319E"}, {"key": "fromAcctId", "value": "3ce65e9a-41af-4a74-ad94-ee67fe7a5c97"}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJSUzI1NiIsImtpZCI6IlFac0xydXlkWEdPQ01qaGk4OXdldGlKa25aVSIsInBpLmF0bSI6Im51eWoifQ.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Ce5UMnGZAFXpRwXFFqMafqqbk6AZ95Aa18GJYNOUKeL29vetY2KzDVw-_Sb_-dwwEfeNfTj_I8cb5XxBiWVqHIhJ-H6sBSjhFPCNR6FPsFMCrs_kmwU9m_YACkfY3CmNr2Cs4KCcmeBqVZhWQWWIdrBb1mcYSIqmMdG1Kzn9_hVZrRrNVjodFl5vlyToFS6M4-RBnS0QQ8wu6MNrYpYR9xmjqvghvy0ke3o9H7c3Ma4o6WbNgoSnAP2rPvZXigtIm-CMjC0lB-DrFlUlUawMuG1NNXWPC7zXFeYKP6nVt9BPvJIm5_dQVOPj4v4A-R0cwsf7291udx5X75TzNsTmdQ", "type": "string"}]}, "events": null, "folder": null, "responses_order": [], "protocolProfileBehavior": {"disableBodyPruning": true}, "currentHelper": "<PERSON><PERSON><PERSON>", "helperAttributes": {"id": "bearer", "token": "eyJhbGciOiJSUzI1NiIsImtpZCI6IlFac0xydXlkWEdPQ01qaGk4OXdldGlKa25aVSIsInBpLmF0bSI6Im51eWoifQ.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Ce5UMnGZAFXpRwXFFqMafqqbk6AZ95Aa18GJYNOUKeL29vetY2KzDVw-_Sb_-dwwEfeNfTj_I8cb5XxBiWVqHIhJ-H6sBSjhFPCNR6FPsFMCrs_kmwU9m_YACkfY3CmNr2Cs4KCcmeBqVZhWQWWIdrBb1mcYSIqmMdG1Kzn9_hVZrRrNVjodFl5vlyToFS6M4-RBnS0QQ8wu6MNrYpYR9xmjqvghvy0ke3o9H7c3Ma4o6WbNgoSnAP2rPvZXigtIm-CMjC0lB-DrFlUlUawMuG1NNXWPC7zXFeYKP6nVt9BPvJIm5_dQVOPj4v4A-R0cwsf7291udx5X75TzNsTmdQ"}, "collectionId": "84c6489f-ec63-4711-ab47-dec35208f664", "rawModeData": "{\n    \"username\": \"dillon\"\n}", "headers": "x-device-uid: asdfasdfasdf\n", "pathVariables": {}}, {"id": "5fff9c6d-2bd4-427e-8969-04859fe90463", "uid": "0-5fff9c6d-2bd4-427e-8969-04859fe90463", "name": "billpay profile", "url": "{{protocol}}://{{host}}:{{port}}{{servletcontext}}{{context}}/services/billpay/profile", "description": null, "data": [], "dataOptions": {"raw": {"language": "json"}}, "dataMode": "raw", "headerData": [{"key": "Authorization", "type": "text", "value": "Bearer ey<PERSON>hbGciOiJSUzI1NiIsImtpZCI6IjM5STRoOVl5anNRWTlpa0hhcWdOREQ3WHY4byIsInBpLmF0bSI6Im51eWoifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NLQnQV9wbLOC2lZnKBsc7p5KmC6s8cLEQst917RY4gdC0pmewyjKPA3ANBgSvgb36gFcBhdWsIP-useTEwJn28Q_kxdpdgW9j07OwFZZn8dWJ6yYjvY7sNHu9_Jhq0Wf8d9gEajAIvMsBGmkOk8b1ByCNH4l6xx5KmoDenXeKqW6ioQ3yBqk7Kx4RRJZlmqqim_GIPo6lZKe4V9St4i_X6ZHiuv2kjL_pX2XJT0XZ4kPjsJLPhgknW5S3GdNyofxyKOSYP3UIV3WVs6TQa0FQ8n6iY8gfLTvVmZ4rhIdafOemoCKQOlnbc8uhXlhBcjI9ePmMFMa5AVSQ7zULO9o1A"}], "method": "GET", "pathVariableData": [], "queryParams": [], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "events": null, "folder": null, "responses_order": [], "protocolProfileBehavior": {"disableBodyPruning": true}, "currentHelper": "<PERSON><PERSON><PERSON>", "helperAttributes": {"id": "bearer", "token": "{{auth_token}}"}, "collectionId": "84c6489f-ec63-4711-ab47-dec35208f664", "rawModeData": "", "headers": "Authorization: Bearer ey<PERSON>hbGciOiJSUzI1NiIsImtpZCI6IjM5STRoOVl5anNRWTlpa0hhcWdOREQ3WHY4byIsInBpLmF0bSI6Im51eWoifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NLQnQV9wbLOC2lZnKBsc7p5KmC6s8cLEQst917RY4gdC0pmewyjKPA3ANBgSvgb36gFcBhdWsIP-useTEwJn28Q_kxdpdgW9j07OwFZZn8dWJ6yYjvY7sNHu9_Jhq0Wf8d9gEajAIvMsBGmkOk8b1ByCNH4l6xx5KmoDenXeKqW6ioQ3yBqk7Kx4RRJZlmqqim_GIPo6lZKe4V9St4i_X6ZHiuv2kjL_pX2XJT0XZ4kPjsJLPhgknW5S3GdNyofxyKOSYP3UIV3WVs6TQa0FQ8n6iY8gfLTvVmZ4rhIdafOemoCKQOlnbc8uhXlhBcjI9ePmMFMa5AVSQ7zULO9o1A\n", "pathVariables": {}}, {"id": "66d1f06d-**************-a5bd5419be98", "uid": "0-66d1f06d-**************-a5bd5419be98", "name": "addExternalAccount Instant", "url": "{{protocol}}://{{host}}:{{port}}{{servletcontext}}{{context}}/tokenized/services/externaltransfer/addExternalAccountByTrialDeposits", "description": null, "data": [], "dataOptions": {"raw": {"language": "json"}}, "dataMode": "raw", "headerData": [{"key": "Authorization", "type": "text", "value": "Bearer ey<PERSON>hbGciOiJSUzI1NiIsImtpZCI6IjM5STRoOVl5anNRWTlpa0hhcWdOREQ3WHY4byIsInBpLmF0bSI6Im51eWoifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NLQnQV9wbLOC2lZnKBsc7p5KmC6s8cLEQst917RY4gdC0pmewyjKPA3ANBgSvgb36gFcBhdWsIP-useTEwJn28Q_kxdpdgW9j07OwFZZn8dWJ6yYjvY7sNHu9_Jhq0Wf8d9gEajAIvMsBGmkOk8b1ByCNH4l6xx5KmoDenXeKqW6ioQ3yBqk7Kx4RRJZlmqqim_GIPo6lZKe4V9St4i_X6ZHiuv2kjL_pX2XJT0XZ4kPjsJLPhgknW5S3GdNyofxyKOSYP3UIV3WVs6TQa0FQ8n6iY8gfLTvVmZ4rhIdafOemoCKQOlnbc8uhXlhBcjI9ePmMFMa5AVSQ7zULO9o1A"}], "method": "POST", "pathVariableData": [], "queryParams": [], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJSUzI1NiIsImtpZCI6IlFac0xydXlkWEdPQ01qaGk4OXdldGlKa25aVSIsInBpLmF0bSI6Im51eWoifQ.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.hd-GpyfsTRmVAM-VQcOtrE1qDcf6u1BIWsqO4K72Jb_Wd-zlUlR_10P25OV4ZaXGv73zWtmTepOI9ssynwdAJ-pTjp32A-m1DpZDyhgt7nVf3gdH8XKiJj7E3lY7qaMw1xmFlyg9IwfRXyERKlrBGhoQZhazqpIKEvuXNCoNyeon-66VE04PtZ23waHe2FiPMPd8p-VFhnOiGEzI5undT12cIiGfKjCzkeORZeTlfrVyDjKoLk11Z22JOSQs2-Sg2JOJrj9W7FLSlE0roBLkQhVrpx8KYcIVhFrpmwMPcZSlFwLVePqffVIyXlPlsZ-puwoh6GFD1945ox3WdBTHjA", "type": "string"}]}, "events": null, "folder": null, "responses_order": [], "currentHelper": "<PERSON><PERSON><PERSON>", "helperAttributes": {"id": "bearer", "token": "eyJhbGciOiJSUzI1NiIsImtpZCI6IlFac0xydXlkWEdPQ01qaGk4OXdldGlKa25aVSIsInBpLmF0bSI6Im51eWoifQ.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.hd-GpyfsTRmVAM-VQcOtrE1qDcf6u1BIWsqO4K72Jb_Wd-zlUlR_10P25OV4ZaXGv73zWtmTepOI9ssynwdAJ-pTjp32A-m1DpZDyhgt7nVf3gdH8XKiJj7E3lY7qaMw1xmFlyg9IwfRXyERKlrBGhoQZhazqpIKEvuXNCoNyeon-66VE04PtZ23waHe2FiPMPd8p-VFhnOiGEzI5undT12cIiGfKjCzkeORZeTlfrVyDjKoLk11Z22JOSQs2-Sg2JOJrj9W7FLSlE0roBLkQhVrpx8KYcIVhFrpmwMPcZSlFwLVePqffVIyXlPlsZ-puwoh6GFD1945ox3WdBTHjA"}, "collectionId": "84c6489f-ec63-4711-ab47-dec35208f664", "rawModeData": "{\n    \"accountId\": \"\",\n    \"TOKENIZED_FIELDS\": {}\n}", "headers": "Authorization: Bearer ey<PERSON>hbGciOiJSUzI1NiIsImtpZCI6IjM5STRoOVl5anNRWTlpa0hhcWdOREQ3WHY4byIsInBpLmF0bSI6Im51eWoifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NLQnQV9wbLOC2lZnKBsc7p5KmC6s8cLEQst917RY4gdC0pmewyjKPA3ANBgSvgb36gFcBhdWsIP-useTEwJn28Q_kxdpdgW9j07OwFZZn8dWJ6yYjvY7sNHu9_Jhq0Wf8d9gEajAIvMsBGmkOk8b1ByCNH4l6xx5KmoDenXeKqW6ioQ3yBqk7Kx4RRJZlmqqim_GIPo6lZKe4V9St4i_X6ZHiuv2kjL_pX2XJT0XZ4kPjsJLPhgknW5S3GdNyofxyKOSYP3UIV3WVs6TQa0FQ8n6iY8gfLTvVmZ4rhIdafOemoCKQOlnbc8uhXlhBcjI9ePmMFMa5AVSQ7zULO9o1A\n", "pathVariables": {}}, {"id": "73ce403b-db54-4bad-9db4-efd508ae760a", "uid": "0-73ce403b-db54-4bad-9db4-efd508ae760a", "name": "billpay payee account", "url": "{{protocol}}://{{host}}:{{port}}{{servletcontext}}{{context}}/tokenized/services/billpay/payee/account?payeeId=", "description": null, "data": [], "dataOptions": {"raw": {"language": "json"}}, "dataMode": "raw", "headerData": [{"key": "Authorization", "type": "text", "value": "Bearer ey<PERSON>hbGciOiJSUzI1NiIsImtpZCI6IjM5STRoOVl5anNRWTlpa0hhcWdOREQ3WHY4byIsInBpLmF0bSI6Im51eWoifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NLQnQV9wbLOC2lZnKBsc7p5KmC6s8cLEQst917RY4gdC0pmewyjKPA3ANBgSvgb36gFcBhdWsIP-useTEwJn28Q_kxdpdgW9j07OwFZZn8dWJ6yYjvY7sNHu9_Jhq0Wf8d9gEajAIvMsBGmkOk8b1ByCNH4l6xx5KmoDenXeKqW6ioQ3yBqk7Kx4RRJZlmqqim_GIPo6lZKe4V9St4i_X6ZHiuv2kjL_pX2XJT0XZ4kPjsJLPhgknW5S3GdNyofxyKOSYP3UIV3WVs6TQa0FQ8n6iY8gfLTvVmZ4rhIdafOemoCKQOlnbc8uhXlhBcjI9ePmMFMa5AVSQ7zULO9o1A"}], "method": "GET", "pathVariableData": [], "queryParams": [{"key": "payeeId", "value": ""}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "type": "string"}]}, "events": null, "folder": null, "responses_order": [], "protocolProfileBehavior": {"disableBodyPruning": true}, "currentHelper": "<PERSON><PERSON><PERSON>", "helperAttributes": {"id": "bearer", "token": "*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, "collectionId": "84c6489f-ec63-4711-ab47-dec35208f664", "rawModeData": "", "headers": "Authorization: Bearer ey<PERSON>hbGciOiJSUzI1NiIsImtpZCI6IjM5STRoOVl5anNRWTlpa0hhcWdOREQ3WHY4byIsInBpLmF0bSI6Im51eWoifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NLQnQV9wbLOC2lZnKBsc7p5KmC6s8cLEQst917RY4gdC0pmewyjKPA3ANBgSvgb36gFcBhdWsIP-useTEwJn28Q_kxdpdgW9j07OwFZZn8dWJ6yYjvY7sNHu9_Jhq0Wf8d9gEajAIvMsBGmkOk8b1ByCNH4l6xx5KmoDenXeKqW6ioQ3yBqk7Kx4RRJZlmqqim_GIPo6lZKe4V9St4i_X6ZHiuv2kjL_pX2XJT0XZ4kPjsJLPhgknW5S3GdNyofxyKOSYP3UIV3WVs6TQa0FQ8n6iY8gfLTvVmZ4rhIdafOemoCKQOlnbc8uhXlhBcjI9ePmMFMa5AVSQ7zULO9o1A\n", "pathVariables": {}}, {"id": "7c313d0f-b3c0-42a4-8da7-859daf06228a", "uid": "0-7c313d0f-b3c0-42a4-8da7-859daf06228a", "name": "billpay add payee", "url": "{{protocol}}://{{host}}:{{port}}{{servletcontext}}{{context}}/tokenized/services/billpay/addPayee", "description": null, "data": [], "dataOptions": {"raw": {"language": "json"}}, "dataMode": "raw", "headerData": [{"key": "Authorization", "type": "text", "value": "Bearer ey<PERSON>hbGciOiJSUzI1NiIsImtpZCI6IjM5STRoOVl5anNRWTlpa0hhcWdOREQ3WHY4byIsInBpLmF0bSI6Im51eWoifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NLQnQV9wbLOC2lZnKBsc7p5KmC6s8cLEQst917RY4gdC0pmewyjKPA3ANBgSvgb36gFcBhdWsIP-useTEwJn28Q_kxdpdgW9j07OwFZZn8dWJ6yYjvY7sNHu9_Jhq0Wf8d9gEajAIvMsBGmkOk8b1ByCNH4l6xx5KmoDenXeKqW6ioQ3yBqk7Kx4RRJZlmqqim_GIPo6lZKe4V9St4i_X6ZHiuv2kjL_pX2XJT0XZ4kPjsJLPhgknW5S3GdNyofxyKOSYP3UIV3WVs6TQa0FQ8n6iY8gfLTvVmZ4rhIdafOemoCKQOlnbc8uhXlhBcjI9ePmMFMa5AVSQ7zULO9o1A"}], "method": "POST", "pathVariableData": [], "queryParams": [], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJSUzI1NiIsImtpZCI6IlFac0xydXlkWEdPQ01qaGk4OXdldGlKa25aVSIsInBpLmF0bSI6Im51eWoifQ.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.hd-GpyfsTRmVAM-VQcOtrE1qDcf6u1BIWsqO4K72Jb_Wd-zlUlR_10P25OV4ZaXGv73zWtmTepOI9ssynwdAJ-pTjp32A-m1DpZDyhgt7nVf3gdH8XKiJj7E3lY7qaMw1xmFlyg9IwfRXyERKlrBGhoQZhazqpIKEvuXNCoNyeon-66VE04PtZ23waHe2FiPMPd8p-VFhnOiGEzI5undT12cIiGfKjCzkeORZeTlfrVyDjKoLk11Z22JOSQs2-Sg2JOJrj9W7FLSlE0roBLkQhVrpx8KYcIVhFrpmwMPcZSlFwLVePqffVIyXlPlsZ-puwoh6GFD1945ox3WdBTHjA", "type": "string"}]}, "events": null, "folder": null, "responses_order": [], "currentHelper": "<PERSON><PERSON><PERSON>", "helperAttributes": {"id": "bearer", "token": "eyJhbGciOiJSUzI1NiIsImtpZCI6IlFac0xydXlkWEdPQ01qaGk4OXdldGlKa25aVSIsInBpLmF0bSI6Im51eWoifQ.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.hd-GpyfsTRmVAM-VQcOtrE1qDcf6u1BIWsqO4K72Jb_Wd-zlUlR_10P25OV4ZaXGv73zWtmTepOI9ssynwdAJ-pTjp32A-m1DpZDyhgt7nVf3gdH8XKiJj7E3lY7qaMw1xmFlyg9IwfRXyERKlrBGhoQZhazqpIKEvuXNCoNyeon-66VE04PtZ23waHe2FiPMPd8p-VFhnOiGEzI5undT12cIiGfKjCzkeORZeTlfrVyDjKoLk11Z22JOSQs2-Sg2JOJrj9W7FLSlE0roBLkQhVrpx8KYcIVhFrpmwMPcZSlFwLVePqffVIyXlPlsZ-puwoh6GFD1945ox3WdBTHjA"}, "collectionId": "84c6489f-ec63-4711-ab47-dec35208f664", "rawModeData": "{\n    \"cesPayee\": {\n        \"name\": \"my house30\",\n        \"cesPayeeType\": \"PERSONAL_PAYEE\",\n        \"cesAddress\": {\n            \"@type\": \"CesAddress\",\n            \"streetLine1\": \"5267 joseph lane\",\n            \"city\": \"Mason\",\n            \"stateOrProvince\": \"OH\",\n            \"postalCode\": \"45040\"\n        },\n        \"TOKENIZED_FIELDS\": {\n            \"accountNumber\": {\n                \"KEY_ID\": {\n                    \"KEY_NAME\": \"co2009/unbound/public.cloud/crit-key\",\n                    \"VERSION\": 1\n                },\n                \"TWEAK\": {\n                    \"VALUE\": \"co2009/unbound/public.cloud/app-tweak\",\n                    \"VERSION\": 1\n                },\n                \"ENCRYPTION_TYPE\": -7,\n                \"ENCRYPTED_ARRAY\": false,\n                \"ENCRYPTION_DATA_TYPE\": null\n            }\n        },\n        \"accountNumber\": \"´衲澖ᗿᰢ뜈卜\"\n    }\n}", "headers": "Authorization: Bearer ey<PERSON>hbGciOiJSUzI1NiIsImtpZCI6IjM5STRoOVl5anNRWTlpa0hhcWdOREQ3WHY4byIsInBpLmF0bSI6Im51eWoifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NLQnQV9wbLOC2lZnKBsc7p5KmC6s8cLEQst917RY4gdC0pmewyjKPA3ANBgSvgb36gFcBhdWsIP-useTEwJn28Q_kxdpdgW9j07OwFZZn8dWJ6yYjvY7sNHu9_Jhq0Wf8d9gEajAIvMsBGmkOk8b1ByCNH4l6xx5KmoDenXeKqW6ioQ3yBqk7Kx4RRJZlmqqim_GIPo6lZKe4V9St4i_X6ZHiuv2kjL_pX2XJT0XZ4kPjsJLPhgknW5S3GdNyofxyKOSYP3UIV3WVs6TQa0FQ8n6iY8gfLTvVmZ4rhIdafOemoCKQOlnbc8uhXlhBcjI9ePmMFMa5AVSQ7zULO9o1A\n", "pathVariables": {}}, {"id": "8c31ce1e-b3ca-401a-b82a-0eab89105839", "uid": "0-8c31ce1e-b3ca-401a-b82a-0eab89105839", "name": "account/list", "url": "{{protocol}}://{{host}}:{{port}}{{servletcontext}}{{context}}/services/account/list", "description": null, "data": [], "dataOptions": {"raw": {"language": "json"}}, "dataMode": "raw", "headerData": [{"warning": "This is a duplicate header and will be overridden by the Authorization header generated by Postman.", "key": "Authorization", "value": "Bearer ey<PERSON>hbGciOiJSUzI1NiIsImtpZCI6IjM5STRoOVl5anNRWTlpa0hhcWdOREQ3WHY4byIsInBpLmF0bSI6Im51eWoifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NLQnQV9wbLOC2lZnKBsc7p5KmC6s8cLEQst917RY4gdC0pmewyjKPA3ANBgSvgb36gFcBhdWsIP-useTEwJn28Q_kxdpdgW9j07OwFZZn8dWJ6yYjvY7sNHu9_Jhq0Wf8d9gEajAIvMsBGmkOk8b1ByCNH4l6xx5KmoDenXeKqW6ioQ3yBqk7Kx4RRJZlmqqim_GIPo6lZKe4V9St4i_X6ZHiuv2kjL_pX2XJT0XZ4kPjsJLPhgknW5S3GdNyofxyKOSYP3UIV3WVs6TQa0FQ8n6iY8gfLTvVmZ4rhIdafOemoCKQOlnbc8uhXlhBcjI9ePmMFMa5AVSQ7zULO9o1A", "type": "text"}, {"key": "x-device-uid", "value": "asdfasdfasdf", "type": "text"}, {"key": "x-client-mode", "value": "NORMAL", "type": "text"}, {"key": "x-manufacturer", "value": "Apple", "type": "text"}, {"key": "x-device-fingerprint", "value": "version=1", "type": "text", "enabled": false}, {"key": "x-device-uid", "value": "asdfasdfasdf", "type": "text"}, {"key": "x-api-key", "value": "postman", "type": "text"}, {"key": "x-client-version", "value": "4.0", "type": "text"}, {"key": "x-platform-version", "value": "9.3.1", "type": "text"}, {"key": "x-platform", "value": "Apple", "type": "text"}, {"key": "x-application", "value": "Apple", "type": "text", "enabled": false}], "method": "GET", "pathVariableData": [], "queryParams": [], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "events": null, "folder": null, "responses_order": [], "protocolProfileBehavior": {"disableBodyPruning": true}, "currentHelper": "<PERSON><PERSON><PERSON>", "helperAttributes": {"id": "bearer", "token": "{{auth_token}}"}, "collectionId": "84c6489f-ec63-4711-ab47-dec35208f664", "rawModeData": "{\n    \"username\": \"dillon\"\n}", "headers": "Authorization: Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6IjM5STRoOVl5anNRWTlpa0hhcWdOREQ3WHY4byIsInBpLmF0bSI6Im51eWoifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NLQnQV9wbLOC2lZnKBsc7p5KmC6s8cLEQst917RY4gdC0pmewyjKPA3ANBgSvgb36gFcBhdWsIP-useTEwJn28Q_kxdpdgW9j07OwFZZn8dWJ6yYjvY7sNHu9_Jhq0Wf8d9gEajAIvMsBGmkOk8b1ByCNH4l6xx5KmoDenXeKqW6ioQ3yBqk7Kx4RRJZlmqqim_GIPo6lZKe4V9St4i_X6ZHiuv2kjL_pX2XJT0XZ4kPjsJLPhgknW5S3GdNyofxyKOSYP3UIV3WVs6TQa0FQ8n6iY8gfLTvVmZ4rhIdafOemoCKQOlnbc8uhXlhBcjI9ePmMFMa5AVSQ7zULO9o1A\nx-device-uid: asdfasdfasdf\nx-client-mode: NORMAL\nx-manufacturer: Apple\n//x-device-fingerprint: version=1\nx-device-uid: asdfasdfasdf\nx-api-key: postman\nx-client-version: 4.0\nx-platform-version: 9.3.1\nx-platform: Apple\n//x-application: Apple\n", "pathVariables": {}}, {"id": "9a884b4e-301e-4883-b5db-b544cb4cae80", "uid": "0-9a884b4e-301e-4883-b5db-b544cb4cae80", "name": "addExternalAccount", "url": "{{protocol}}://{{host}}:{{port}}{{servletcontext}}{{context}}/tokenized/services/externaltransfer/addExternalAccountByTrialDeposits", "description": null, "data": [], "dataOptions": {"raw": {"language": "json"}}, "dataMode": "raw", "headerData": [{"key": "Authorization", "type": "text", "value": "Bearer ey<PERSON>hbGciOiJSUzI1NiIsImtpZCI6IjM5STRoOVl5anNRWTlpa0hhcWdOREQ3WHY4byIsInBpLmF0bSI6Im51eWoifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NLQnQV9wbLOC2lZnKBsc7p5KmC6s8cLEQst917RY4gdC0pmewyjKPA3ANBgSvgb36gFcBhdWsIP-useTEwJn28Q_kxdpdgW9j07OwFZZn8dWJ6yYjvY7sNHu9_Jhq0Wf8d9gEajAIvMsBGmkOk8b1ByCNH4l6xx5KmoDenXeKqW6ioQ3yBqk7Kx4RRJZlmqqim_GIPo6lZKe4V9St4i_X6ZHiuv2kjL_pX2XJT0XZ4kPjsJLPhgknW5S3GdNyofxyKOSYP3UIV3WVs6TQa0FQ8n6iY8gfLTvVmZ4rhIdafOemoCKQOlnbc8uhXlhBcjI9ePmMFMa5AVSQ7zULO9o1A"}], "method": "POST", "pathVariableData": [], "queryParams": [], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJSUzI1NiIsImtpZCI6IlFac0xydXlkWEdPQ01qaGk4OXdldGlKa25aVSIsInBpLmF0bSI6Im51eWoifQ.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.hd-GpyfsTRmVAM-VQcOtrE1qDcf6u1BIWsqO4K72Jb_Wd-zlUlR_10P25OV4ZaXGv73zWtmTepOI9ssynwdAJ-pTjp32A-m1DpZDyhgt7nVf3gdH8XKiJj7E3lY7qaMw1xmFlyg9IwfRXyERKlrBGhoQZhazqpIKEvuXNCoNyeon-66VE04PtZ23waHe2FiPMPd8p-VFhnOiGEzI5undT12cIiGfKjCzkeORZeTlfrVyDjKoLk11Z22JOSQs2-Sg2JOJrj9W7FLSlE0roBLkQhVrpx8KYcIVhFrpmwMPcZSlFwLVePqffVIyXlPlsZ-puwoh6GFD1945ox3WdBTHjA", "type": "string"}]}, "events": null, "folder": null, "responses_order": [], "currentHelper": "<PERSON><PERSON><PERSON>", "helperAttributes": {"id": "bearer", "token": "eyJhbGciOiJSUzI1NiIsImtpZCI6IlFac0xydXlkWEdPQ01qaGk4OXdldGlKa25aVSIsInBpLmF0bSI6Im51eWoifQ.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.hd-GpyfsTRmVAM-VQcOtrE1qDcf6u1BIWsqO4K72Jb_Wd-zlUlR_10P25OV4ZaXGv73zWtmTepOI9ssynwdAJ-pTjp32A-m1DpZDyhgt7nVf3gdH8XKiJj7E3lY7qaMw1xmFlyg9IwfRXyERKlrBGhoQZhazqpIKEvuXNCoNyeon-66VE04PtZ23waHe2FiPMPd8p-VFhnOiGEzI5undT12cIiGfKjCzkeORZeTlfrVyDjKoLk11Z22JOSQs2-Sg2JOJrj9W7FLSlE0roBLkQhVrpx8KYcIVhFrpmwMPcZSlFwLVePqffVIyXlPlsZ-puwoh6GFD1945ox3WdBTHjA"}, "collectionId": "84c6489f-ec63-4711-ab47-dec35208f664", "rawModeData": "{\n    \"accountId\": \"\",\n    \"TOKENIZED_FIELDS\": {}\n}", "headers": "Authorization: Bearer ey<PERSON>hbGciOiJSUzI1NiIsImtpZCI6IjM5STRoOVl5anNRWTlpa0hhcWdOREQ3WHY4byIsInBpLmF0bSI6Im51eWoifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NLQnQV9wbLOC2lZnKBsc7p5KmC6s8cLEQst917RY4gdC0pmewyjKPA3ANBgSvgb36gFcBhdWsIP-useTEwJn28Q_kxdpdgW9j07OwFZZn8dWJ6yYjvY7sNHu9_Jhq0Wf8d9gEajAIvMsBGmkOk8b1ByCNH4l6xx5KmoDenXeKqW6ioQ3yBqk7Kx4RRJZlmqqim_GIPo6lZKe4V9St4i_X6ZHiuv2kjL_pX2XJT0XZ4kPjsJLPhgknW5S3GdNyofxyKOSYP3UIV3WVs6TQa0FQ8n6iY8gfLTvVmZ4rhIdafOemoCKQOlnbc8uhXlhBcjI9ePmMFMa5AVSQ7zULO9o1A\n", "pathVariables": {}}, {"id": "9ee65888-0059-4912-98d0-b0cb26c16e03", "uid": "0-9ee65888-0059-4912-98d0-b0cb26c16e03", "name": "transferandpay/activity", "url": "{{protocol}}://{{host}}:{{port}}{{servletcontext}}{{context}}/services/transferandpay/activity?activityId=13561513", "description": null, "data": null, "dataOptions": null, "dataMode": null, "headerData": [{"key": "Authorization", "value": "Bearer ey<PERSON>hbGciOiJSUzI1NiIsImtpZCI6IjM5STRoOVl5anNRWTlpa0hhcWdOREQ3WHY4byIsInBpLmF0bSI6Im51eWoifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NLQnQV9wbLOC2lZnKBsc7p5KmC6s8cLEQst917RY4gdC0pmewyjKPA3ANBgSvgb36gFcBhdWsIP-useTEwJn28Q_kxdpdgW9j07OwFZZn8dWJ6yYjvY7sNHu9_Jhq0Wf8d9gEajAIvMsBGmkOk8b1ByCNH4l6xx5KmoDenXeKqW6ioQ3yBqk7Kx4RRJZlmqqim_GIPo6lZKe4V9St4i_X6ZHiuv2kjL_pX2XJT0XZ4kPjsJLPhgknW5S3GdNyofxyKOSYP3UIV3WVs6TQa0FQ8n6iY8gfLTvVmZ4rhIdafOemoCKQOlnbc8uhXlhBcjI9ePmMFMa5AVSQ7zULO9o1A", "type": "text"}], "method": "DELETE", "pathVariableData": [], "queryParams": [{"key": "activityId", "value": "13561513"}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "type": "string"}]}, "events": null, "folder": null, "responses_order": [], "currentHelper": "<PERSON><PERSON><PERSON>", "helperAttributes": {"id": "bearer", "token": "*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, "collectionId": "84c6489f-ec63-4711-ab47-dec35208f664", "headers": "Authorization: Bearer ey<PERSON>hbGciOiJSUzI1NiIsImtpZCI6IjM5STRoOVl5anNRWTlpa0hhcWdOREQ3WHY4byIsInBpLmF0bSI6Im51eWoifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NLQnQV9wbLOC2lZnKBsc7p5KmC6s8cLEQst917RY4gdC0pmewyjKPA3ANBgSvgb36gFcBhdWsIP-useTEwJn28Q_kxdpdgW9j07OwFZZn8dWJ6yYjvY7sNHu9_Jhq0Wf8d9gEajAIvMsBGmkOk8b1ByCNH4l6xx5KmoDenXeKqW6ioQ3yBqk7Kx4RRJZlmqqim_GIPo6lZKe4V9St4i_X6ZHiuv2kjL_pX2XJT0XZ4kPjsJLPhgknW5S3GdNyofxyKOSYP3UIV3WVs6TQa0FQ8n6iY8gfLTvVmZ4rhIdafOemoCKQOlnbc8uhXlhBcjI9ePmMFMa5AVSQ7zULO9o1A\n", "pathVariables": {}}, {"id": "a055d0b6-9ea4-4311-bc7f-20a164caafa0", "uid": "0-a055d0b6-9ea4-4311-bc7f-20a164caafa0", "name": "addExternal<PERSON><PERSON>unt <PERSON><PERSON>", "url": "{{protocol}}://{{host}}:{{port}}{{servletcontext}}{{context}}/tokenized/services/billpay/addPayee", "description": null, "data": [], "dataOptions": {"raw": {"language": "json"}}, "dataMode": "raw", "headerData": [{"key": "Authorization", "type": "text", "value": "Bearer ey<PERSON>hbGciOiJSUzI1NiIsImtpZCI6IjM5STRoOVl5anNRWTlpa0hhcWdOREQ3WHY4byIsInBpLmF0bSI6Im51eWoifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NLQnQV9wbLOC2lZnKBsc7p5KmC6s8cLEQst917RY4gdC0pmewyjKPA3ANBgSvgb36gFcBhdWsIP-useTEwJn28Q_kxdpdgW9j07OwFZZn8dWJ6yYjvY7sNHu9_Jhq0Wf8d9gEajAIvMsBGmkOk8b1ByCNH4l6xx5KmoDenXeKqW6ioQ3yBqk7Kx4RRJZlmqqim_GIPo6lZKe4V9St4i_X6ZHiuv2kjL_pX2XJT0XZ4kPjsJLPhgknW5S3GdNyofxyKOSYP3UIV3WVs6TQa0FQ8n6iY8gfLTvVmZ4rhIdafOemoCKQOlnbc8uhXlhBcjI9ePmMFMa5AVSQ7zULO9o1A"}], "method": "POST", "pathVariableData": [], "queryParams": [], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJSUzI1NiIsImtpZCI6IlFac0xydXlkWEdPQ01qaGk4OXdldGlKa25aVSIsInBpLmF0bSI6Im51eWoifQ.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.hd-GpyfsTRmVAM-VQcOtrE1qDcf6u1BIWsqO4K72Jb_Wd-zlUlR_10P25OV4ZaXGv73zWtmTepOI9ssynwdAJ-pTjp32A-m1DpZDyhgt7nVf3gdH8XKiJj7E3lY7qaMw1xmFlyg9IwfRXyERKlrBGhoQZhazqpIKEvuXNCoNyeon-66VE04PtZ23waHe2FiPMPd8p-VFhnOiGEzI5undT12cIiGfKjCzkeORZeTlfrVyDjKoLk11Z22JOSQs2-Sg2JOJrj9W7FLSlE0roBLkQhVrpx8KYcIVhFrpmwMPcZSlFwLVePqffVIyXlPlsZ-puwoh6GFD1945ox3WdBTHjA", "type": "string"}]}, "events": null, "folder": null, "responses_order": [], "currentHelper": "<PERSON><PERSON><PERSON>", "helperAttributes": {"id": "bearer", "token": "eyJhbGciOiJSUzI1NiIsImtpZCI6IlFac0xydXlkWEdPQ01qaGk4OXdldGlKa25aVSIsInBpLmF0bSI6Im51eWoifQ.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.hd-GpyfsTRmVAM-VQcOtrE1qDcf6u1BIWsqO4K72Jb_Wd-zlUlR_10P25OV4ZaXGv73zWtmTepOI9ssynwdAJ-pTjp32A-m1DpZDyhgt7nVf3gdH8XKiJj7E3lY7qaMw1xmFlyg9IwfRXyERKlrBGhoQZhazqpIKEvuXNCoNyeon-66VE04PtZ23waHe2FiPMPd8p-VFhnOiGEzI5undT12cIiGfKjCzkeORZeTlfrVyDjKoLk11Z22JOSQs2-Sg2JOJrj9W7FLSlE0roBLkQhVrpx8KYcIVhFrpmwMPcZSlFwLVePqffVIyXlPlsZ-puwoh6GFD1945ox3WdBTHjA"}, "collectionId": "84c6489f-ec63-4711-ab47-dec35208f664", "rawModeData": "{\n    \"cesPayee\": {\n        \"name\": \"my house30\",\n        \"cesPayeeType\": \"PERSONAL_PAYEE\",\n        \"cesAddress\": {\n            \"@type\": \"CesAddress\",\n            \"streetLine1\": \"5267 joseph lane\",\n            \"city\": \"Mason\",\n            \"stateOrProvince\": \"OH\",\n            \"postalCode\": \"45040\"\n        },\n        \"TOKENIZED_FIELDS\": {\n            \"accountNumber\": {\n                \"KEY_ID\": {\n                    \"KEY_NAME\": \"co2009/unbound/public.cloud/crit-key\",\n                    \"VERSION\": 1\n                },\n                \"TWEAK\": {\n                    \"VALUE\": \"co2009/unbound/public.cloud/app-tweak\",\n                    \"VERSION\": 1\n                },\n                \"ENCRYPTION_TYPE\": -7,\n                \"ENCRYPTED_ARRAY\": false,\n                \"ENCRYPTION_DATA_TYPE\": null\n            }\n        },\n        \"accountNumber\": \"´衲澖ᗿᰢ뜈卜\"\n    }\n}", "headers": "Authorization: Bearer ey<PERSON>hbGciOiJSUzI1NiIsImtpZCI6IjM5STRoOVl5anNRWTlpa0hhcWdOREQ3WHY4byIsInBpLmF0bSI6Im51eWoifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NLQnQV9wbLOC2lZnKBsc7p5KmC6s8cLEQst917RY4gdC0pmewyjKPA3ANBgSvgb36gFcBhdWsIP-useTEwJn28Q_kxdpdgW9j07OwFZZn8dWJ6yYjvY7sNHu9_Jhq0Wf8d9gEajAIvMsBGmkOk8b1ByCNH4l6xx5KmoDenXeKqW6ioQ3yBqk7Kx4RRJZlmqqim_GIPo6lZKe4V9St4i_X6ZHiuv2kjL_pX2XJT0XZ4kPjsJLPhgknW5S3GdNyofxyKOSYP3UIV3WVs6TQa0FQ8n6iY8gfLTvVmZ4rhIdafOemoCKQOlnbc8uhXlhBcjI9ePmMFMa5AVSQ7zULO9o1A\n", "pathVariables": {}}, {"id": "a5561f24-f0d7-463b-80b2-68d261ea4cf3", "uid": "0-a5561f24-f0d7-463b-80b2-68d261ea4cf3", "name": "transferandpay/activity", "url": "{{protocol}}://{{host}}:{{port}}{{servletcontext}}{{context}}/services/transferandpay/activity", "description": null, "data": [], "dataOptions": {"raw": {"language": "json"}}, "dataMode": "raw", "headerData": [{"key": "Authorization", "value": "Bearer ey<PERSON>hbGciOiJSUzI1NiIsImtpZCI6IjM5STRoOVl5anNRWTlpa0hhcWdOREQ3WHY4byIsInBpLmF0bSI6Im51eWoifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NLQnQV9wbLOC2lZnKBsc7p5KmC6s8cLEQst917RY4gdC0pmewyjKPA3ANBgSvgb36gFcBhdWsIP-useTEwJn28Q_kxdpdgW9j07OwFZZn8dWJ6yYjvY7sNHu9_Jhq0Wf8d9gEajAIvMsBGmkOk8b1ByCNH4l6xx5KmoDenXeKqW6ioQ3yBqk7Kx4RRJZlmqqim_GIPo6lZKe4V9St4i_X6ZHiuv2kjL_pX2XJT0XZ4kPjsJLPhgknW5S3GdNyofxyKOSYP3UIV3WVs6TQa0FQ8n6iY8gfLTvVmZ4rhIdafOemoCKQOlnbc8uhXlhBcjI9ePmMFMa5AVSQ7zULO9o1A", "type": "text"}], "method": "POST", "pathVariableData": [], "queryParams": [], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJSUzI1NiIsImtpZCI6IlFac0xydXlkWEdPQ01qaGk4OXdldGlKa25aVSIsInBpLmF0bSI6Im51eWoifQ.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.v-oXZdWSST_MR1zCtQYZ0lWC8mYnU1OPAiuwYK5oxNqBqsjVz05Ha_wKdMH4Z_L_Vh4TM4zdM8Gp7nHK1xgmptMC6qjafguc3v6jctlisBiD1R5gMmytWzgKZwxynxR5Z0XIYR1BZMUtUq1eOD3x8O1BDPJUyhW_AFBT-zvGU7pydZbTL85v9O4QzGD10ukmzr1a791sOJ0GmOhOWyfFiSNH55hhw-AAh9yxRjEnTJuj_tso8ZWQnha8FQL3ETA6-cmEAs0i0I0YIHQFncphK67LNgH-dycK2VtDdVCyhsWO4iQvcPhXZ9GqxJAmZLpG36j6ISA1y2lSQ4vKVKz-8w", "type": "string"}]}, "events": null, "folder": null, "responses_order": [], "currentHelper": "<PERSON><PERSON><PERSON>", "helperAttributes": {"id": "bearer", "token": "eyJhbGciOiJSUzI1NiIsImtpZCI6IlFac0xydXlkWEdPQ01qaGk4OXdldGlKa25aVSIsInBpLmF0bSI6Im51eWoifQ.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.v-oXZdWSST_MR1zCtQYZ0lWC8mYnU1OPAiuwYK5oxNqBqsjVz05Ha_wKdMH4Z_L_Vh4TM4zdM8Gp7nHK1xgmptMC6qjafguc3v6jctlisBiD1R5gMmytWzgKZwxynxR5Z0XIYR1BZMUtUq1eOD3x8O1BDPJUyhW_AFBT-zvGU7pydZbTL85v9O4QzGD10ukmzr1a791sOJ0GmOhOWyfFiSNH55hhw-AAh9yxRjEnTJuj_tso8ZWQnha8FQL3ETA6-cmEAs0i0I0YIHQFncphK67LNgH-dycK2VtDdVCyhsWO4iQvcPhXZ9GqxJAmZLpG36j6ISA1y2lSQ4vKVKz-8w"}, "collectionId": "84c6489f-ec63-4711-ab47-dec35208f664", "rawModeData": "{\n    \"fromAccountId\": \"df78b74e-455f-4e6c-9ff4-8e8fe8aa8149\",\n    \"toAccountId\": \"de90b8b6-377d-4052-8bb9-8ef6b111d9c7\",\n    \"amount\": 6.00,\n    \"frequency\": \"ONE_TIME\",\n    \"dueDate\": \"2022-01-05\",\n    \"numberOfTransactions\": null,\n    \"memo\": null,\n    \"additionalPrincipleAmount\": null,\n    \"displayId\": null,\n    \"checkNumber\": null,\n    \"id\": null,\n    \"recurringId\": null,\n    \"requestGuid\": \"EE1B2252-2092-46AF-59CC-1634A250191C\"\n}", "headers": "Authorization: Bearer ey<PERSON>hbGciOiJSUzI1NiIsImtpZCI6IjM5STRoOVl5anNRWTlpa0hhcWdOREQ3WHY4byIsInBpLmF0bSI6Im51eWoifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NLQnQV9wbLOC2lZnKBsc7p5KmC6s8cLEQst917RY4gdC0pmewyjKPA3ANBgSvgb36gFcBhdWsIP-useTEwJn28Q_kxdpdgW9j07OwFZZn8dWJ6yYjvY7sNHu9_Jhq0Wf8d9gEajAIvMsBGmkOk8b1ByCNH4l6xx5KmoDenXeKqW6ioQ3yBqk7Kx4RRJZlmqqim_GIPo6lZKe4V9St4i_X6ZHiuv2kjL_pX2XJT0XZ4kPjsJLPhgknW5S3GdNyofxyKOSYP3UIV3WVs6TQa0FQ8n6iY8gfLTvVmZ4rhIdafOemoCKQOlnbc8uhXlhBcjI9ePmMFMa5AVSQ7zULO9o1A\n", "pathVariables": {}}, {"id": "a6be53c2-54fc-4978-ab07-094954484e4c", "uid": "0-a6be53c2-54fc-4978-ab07-094954484e4c", "name": "login", "url": "{{protocol}}://{{host}}:{{port}}{{servletcontext}}{{context}}/auth/login", "description": null, "data": [], "dataOptions": {"raw": {"language": "json"}}, "dataMode": "raw", "headerData": [], "method": "GET", "pathVariableData": [], "queryParams": [], "auth": null, "events": null, "folder": null, "responses_order": [], "protocolProfileBehavior": {"disableBodyPruning": true}, "currentHelper": null, "helperAttributes": null, "collectionId": "84c6489f-ec63-4711-ab47-dec35208f664", "rawModeData": "{\n    \"username\": \"dillon\"\n}", "headers": "", "pathVariables": {}}, {"id": "a937d480-7229-4bcf-9a5d-ce8d4ab3af37", "uid": "0-a937d480-7229-4bcf-9a5d-ce8d4ab3af37", "name": "account/detail", "url": "{{protocol}}://{{host}}:{{port}}{{servletcontext}}{{context}}/services/account/detail?id=A2CCAEA1-217E-E3CE-0EE2-50D0BB1D5D74", "description": null, "data": [], "dataOptions": {"raw": {"language": "json"}}, "dataMode": "raw", "headerData": [{"key": "Authorization", "type": "text", "value": "Bearer ey<PERSON>hbGciOiJSUzI1NiIsImtpZCI6IjM5STRoOVl5anNRWTlpa0hhcWdOREQ3WHY4byIsInBpLmF0bSI6Im51eWoifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NLQnQV9wbLOC2lZnKBsc7p5KmC6s8cLEQst917RY4gdC0pmewyjKPA3ANBgSvgb36gFcBhdWsIP-useTEwJn28Q_kxdpdgW9j07OwFZZn8dWJ6yYjvY7sNHu9_Jhq0Wf8d9gEajAIvMsBGmkOk8b1ByCNH4l6xx5KmoDenXeKqW6ioQ3yBqk7Kx4RRJZlmqqim_GIPo6lZKe4V9St4i_X6ZHiuv2kjL_pX2XJT0XZ4kPjsJLPhgknW5S3GdNyofxyKOSYP3UIV3WVs6TQa0FQ8n6iY8gfLTvVmZ4rhIdafOemoCKQOlnbc8uhXlhBcjI9ePmMFMa5AVSQ7zULO9o1A"}], "method": "GET", "pathVariableData": [], "queryParams": [{"key": "id", "value": "A2CCAEA1-217E-E3CE-0EE2-50D0BB1D5D74"}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJSUzI1NiIsImtpZCI6IlFac0xydXlkWEdPQ01qaGk4OXdldGlKa25aVSIsInBpLmF0bSI6Im51eWoifQ.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.PWfi32su3Xa8VNIXoAbuHw9SqLdHLVXefb8LwrnfkbjCDgHT3PXoEMQG78WTaxlIR4Blx3LMFScyWZuVj47kx7Q0OOS0_lOkEg9z5Q70LvgHXXdovjRbIWp4SMhDFmvUKwNDeqteIksXhU9TO_lrDL7BR7CWK8yN1lp0HzmdFs9m_fhUzolokmbbOmCV4OSyUkAweaAAlFnu6fZ2zVUsHyTj9uzb8znSa7ajxNbw_mcTztAPiV79EHM7dOUogrw4xs7pUisAjRQuKhzFDJC8JbeW-i5Nlaq846cRjeXSgpgUGTUmmhGa6uzj_YffZO2sTG6YOtTgcYhUnBlHklzvCQ", "type": "string"}]}, "events": null, "folder": null, "responses_order": [], "protocolProfileBehavior": {"disableBodyPruning": true}, "currentHelper": "<PERSON><PERSON><PERSON>", "helperAttributes": {"id": "bearer", "token": "eyJhbGciOiJSUzI1NiIsImtpZCI6IlFac0xydXlkWEdPQ01qaGk4OXdldGlKa25aVSIsInBpLmF0bSI6Im51eWoifQ.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.PWfi32su3Xa8VNIXoAbuHw9SqLdHLVXefb8LwrnfkbjCDgHT3PXoEMQG78WTaxlIR4Blx3LMFScyWZuVj47kx7Q0OOS0_lOkEg9z5Q70LvgHXXdovjRbIWp4SMhDFmvUKwNDeqteIksXhU9TO_lrDL7BR7CWK8yN1lp0HzmdFs9m_fhUzolokmbbOmCV4OSyUkAweaAAlFnu6fZ2zVUsHyTj9uzb8znSa7ajxNbw_mcTztAPiV79EHM7dOUogrw4xs7pUisAjRQuKhzFDJC8JbeW-i5Nlaq846cRjeXSgpgUGTUmmhGa6uzj_YffZO2sTG6YOtTgcYhUnBlHklzvCQ"}, "collectionId": "84c6489f-ec63-4711-ab47-dec35208f664", "rawModeData": "{\n    \"username\": \"dillon\"\n}", "headers": "Authorization: Bearer ey<PERSON>hbGciOiJSUzI1NiIsImtpZCI6IjM5STRoOVl5anNRWTlpa0hhcWdOREQ3WHY4byIsInBpLmF0bSI6Im51eWoifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NLQnQV9wbLOC2lZnKBsc7p5KmC6s8cLEQst917RY4gdC0pmewyjKPA3ANBgSvgb36gFcBhdWsIP-useTEwJn28Q_kxdpdgW9j07OwFZZn8dWJ6yYjvY7sNHu9_Jhq0Wf8d9gEajAIvMsBGmkOk8b1ByCNH4l6xx5KmoDenXeKqW6ioQ3yBqk7Kx4RRJZlmqqim_GIPo6lZKe4V9St4i_X6ZHiuv2kjL_pX2XJT0XZ4kPjsJLPhgknW5S3GdNyofxyKOSYP3UIV3WVs6TQa0FQ8n6iY8gfLTvVmZ4rhIdafOemoCKQOlnbc8uhXlhBcjI9ePmMFMa5AVSQ7zULO9o1A\n", "pathVariables": {}}, {"id": "c9380520-74a5-4e22-8271-c32234a1a05a", "uid": "0-c9380520-74a5-4e22-8271-c32234a1a05a", "name": "webpayment bank lookup", "url": "{{protocol}}://{{host}}:{{port}}{{servletcontext}}{{context}}/services//webpayment/bank?routingNumber=*********", "description": null, "data": [], "dataOptions": {"raw": {"language": "json"}}, "dataMode": "raw", "headerData": [{"key": "Authorization", "type": "text", "value": "Bearer ey<PERSON>hbGciOiJSUzI1NiIsImtpZCI6IjM5STRoOVl5anNRWTlpa0hhcWdOREQ3WHY4byIsInBpLmF0bSI6Im51eWoifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NLQnQV9wbLOC2lZnKBsc7p5KmC6s8cLEQst917RY4gdC0pmewyjKPA3ANBgSvgb36gFcBhdWsIP-useTEwJn28Q_kxdpdgW9j07OwFZZn8dWJ6yYjvY7sNHu9_Jhq0Wf8d9gEajAIvMsBGmkOk8b1ByCNH4l6xx5KmoDenXeKqW6ioQ3yBqk7Kx4RRJZlmqqim_GIPo6lZKe4V9St4i_X6ZHiuv2kjL_pX2XJT0XZ4kPjsJLPhgknW5S3GdNyofxyKOSYP3UIV3WVs6TQa0FQ8n6iY8gfLTvVmZ4rhIdafOemoCKQOlnbc8uhXlhBcjI9ePmMFMa5AVSQ7zULO9o1A"}], "method": "GET", "pathVariableData": [], "queryParams": [{"key": "routingNumber", "value": "*********"}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJSUzI1NiIsImtpZCI6InA4TWtaQjFROW40WGRzdEZlcVlqWjY2bnpPWSIsInBpLmF0bSI6Im51eWoifQ.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.B05sQwcRIlNJIAKF2LPdcAYevfDAVuS7A0uaweB8z4RxzUv5OqFe2wJp4w2CykK3iyikPyLQKb89giSYm0Vlj485Uqzmv9WA0PtD8zQylHYp5z0N3I_8B7M-WqzHBBlrrYOqbTBQxX6HN6mJuxssfJAYtnswTaKDeLBf3Fd34gC5OnQDqIbtG7y6-a1CdEU-vpadgjT8cBA5FblVxlmcYltF_QRWlOx-tg8m0sfaGWqBXWZ3iD1BnqkYSAvMTyeaew9s5C99yPrJ4wjihPb0PPRNKNRAmgVhD9oho-sWKFCNIZrOg7yp8pXIx5x109g8YmvOEieUybLQAe4U3BCDJg", "type": "string"}]}, "events": null, "folder": null, "responses_order": [], "protocolProfileBehavior": {"disableBodyPruning": true}, "currentHelper": "<PERSON><PERSON><PERSON>", "helperAttributes": {"id": "bearer", "token": "eyJhbGciOiJSUzI1NiIsImtpZCI6InA4TWtaQjFROW40WGRzdEZlcVlqWjY2bnpPWSIsInBpLmF0bSI6Im51eWoifQ.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.B05sQwcRIlNJIAKF2LPdcAYevfDAVuS7A0uaweB8z4RxzUv5OqFe2wJp4w2CykK3iyikPyLQKb89giSYm0Vlj485Uqzmv9WA0PtD8zQylHYp5z0N3I_8B7M-WqzHBBlrrYOqbTBQxX6HN6mJuxssfJAYtnswTaKDeLBf3Fd34gC5OnQDqIbtG7y6-a1CdEU-vpadgjT8cBA5FblVxlmcYltF_QRWlOx-tg8m0sfaGWqBXWZ3iD1BnqkYSAvMTyeaew9s5C99yPrJ4wjihPb0PPRNKNRAmgVhD9oho-sWKFCNIZrOg7yp8pXIx5x109g8YmvOEieUybLQAe4U3BCDJg"}, "collectionId": "84c6489f-ec63-4711-ab47-dec35208f664", "rawModeData": "{\n    \"username\": \"dillon\"\n}", "headers": "Authorization: Bearer ey<PERSON>hbGciOiJSUzI1NiIsImtpZCI6IjM5STRoOVl5anNRWTlpa0hhcWdOREQ3WHY4byIsInBpLmF0bSI6Im51eWoifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NLQnQV9wbLOC2lZnKBsc7p5KmC6s8cLEQst917RY4gdC0pmewyjKPA3ANBgSvgb36gFcBhdWsIP-useTEwJn28Q_kxdpdgW9j07OwFZZn8dWJ6yYjvY7sNHu9_Jhq0Wf8d9gEajAIvMsBGmkOk8b1ByCNH4l6xx5KmoDenXeKqW6ioQ3yBqk7Kx4RRJZlmqqim_GIPo6lZKe4V9St4i_X6ZHiuv2kjL_pX2XJT0XZ4kPjsJLPhgknW5S3GdNyofxyKOSYP3UIV3WVs6TQa0FQ8n6iY8gfLTvVmZ4rhIdafOemoCKQOlnbc8uhXlhBcjI9ePmMFMa5AVSQ7zULO9o1A\n", "pathVariables": {}}, {"id": "cde27074-117f-47fc-b80e-688b3ed086e2", "uid": "0-cde27074-117f-47fc-b80e-688b3ed086e2", "name": "logout", "url": "{{protocol}}://{{host}}:{{port}}{{servletcontext}}{{context}}/auth/logout", "description": null, "data": [], "dataOptions": {"raw": {"language": "json"}}, "dataMode": "raw", "headerData": [{"key": "x-device-uid", "value": "asdfasdfasdf", "type": "text"}, {"key": "x-api-key", "value": "postman", "type": "text"}], "method": "DELETE", "pathVariableData": [], "queryParams": [], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "events": null, "folder": null, "responses_order": [], "currentHelper": "<PERSON><PERSON><PERSON>", "helperAttributes": {"id": "bearer", "token": "{{auth_token}}"}, "collectionId": "84c6489f-ec63-4711-ab47-dec35208f664", "rawModeData": "{\n    \"username\": \"dillon\"\n}", "headers": "x-device-uid: asdfasdfasdf\nx-api-key: postman\n", "pathVariables": {}}, {"id": "e44b2f6b-3c06-475d-8e47-efef895305b8", "uid": "0-e44b2f6b-3c06-475d-8e47-efef895305b8", "name": "transferandpay/activity", "url": "{{protocol}}://{{host}}:{{port}}{{servletcontext}}{{context}}/services/transferandpay/activity", "description": null, "data": null, "dataOptions": null, "dataMode": null, "headerData": [{"warning": "This is a duplicate header and will be overridden by the Authorization header generated by Postman.", "key": "Authorization", "value": "Bearer ey<PERSON>hbGciOiJSUzI1NiIsImtpZCI6IjM5STRoOVl5anNRWTlpa0hhcWdOREQ3WHY4byIsInBpLmF0bSI6Im51eWoifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NLQnQV9wbLOC2lZnKBsc7p5KmC6s8cLEQst917RY4gdC0pmewyjKPA3ANBgSvgb36gFcBhdWsIP-useTEwJn28Q_kxdpdgW9j07OwFZZn8dWJ6yYjvY7sNHu9_Jhq0Wf8d9gEajAIvMsBGmkOk8b1ByCNH4l6xx5KmoDenXeKqW6ioQ3yBqk7Kx4RRJZlmqqim_GIPo6lZKe4V9St4i_X6ZHiuv2kjL_pX2XJT0XZ4kPjsJLPhgknW5S3GdNyofxyKOSYP3UIV3WVs6TQa0FQ8n6iY8gfLTvVmZ4rhIdafOemoCKQOlnbc8uhXlhBcjI9ePmMFMa5AVSQ7zULO9o1A", "type": "text"}, {"key": "x-device-uid", "value": "asdfasdfasdf, asdfasdfasdf", "type": "text"}], "method": "GET", "pathVariableData": [], "queryParams": [], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "events": null, "folder": null, "responses_order": [], "currentHelper": "<PERSON><PERSON><PERSON>", "helperAttributes": {"id": "bearer", "token": "{{auth_token}}"}, "collectionId": "84c6489f-ec63-4711-ab47-dec35208f664", "headers": "Authorization: Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6IjM5STRoOVl5anNRWTlpa0hhcWdOREQ3WHY4byIsInBpLmF0bSI6Im51eWoifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NLQnQV9wbLOC2lZnKBsc7p5KmC6s8cLEQst917RY4gdC0pmewyjKPA3ANBgSvgb36gFcBhdWsIP-useTEwJn28Q_kxdpdgW9j07OwFZZn8dWJ6yYjvY7sNHu9_Jhq0Wf8d9gEajAIvMsBGmkOk8b1ByCNH4l6xx5KmoDenXeKqW6ioQ3yBqk7Kx4RRJZlmqqim_GIPo6lZKe4V9St4i_X6ZHiuv2kjL_pX2XJT0XZ4kPjsJLPhgknW5S3GdNyofxyKOSYP3UIV3WVs6TQa0FQ8n6iY8gfLTvVmZ4rhIdafOemoCKQOlnbc8uhXlhBcjI9ePmMFMa5AVSQ7zULO9o1A\nx-device-uid: asdfasdfasdf, asdfasdfasdf\n", "pathVariables": {}}, {"id": "fad2e619-e147-4e39-b476-76889cc7a3fa", "uid": "0-fad2e619-e147-4e39-b476-76889cc7a3fa", "name": "tokenize", "url": "https://tokenization-qat1.ocpn.info53.com/v1/tokenize?keySet=ces", "description": null, "data": [], "dataOptions": {"raw": {"language": "json"}}, "dataMode": "raw", "headerData": [{"key": "Authorization", "type": "text", "value": "Bearer ey<PERSON>hbGciOiJSUzI1NiIsImtpZCI6IjM5STRoOVl5anNRWTlpa0hhcWdOREQ3WHY4byIsInBpLmF0bSI6Im51eWoifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NLQnQV9wbLOC2lZnKBsc7p5KmC6s8cLEQst917RY4gdC0pmewyjKPA3ANBgSvgb36gFcBhdWsIP-useTEwJn28Q_kxdpdgW9j07OwFZZn8dWJ6yYjvY7sNHu9_Jhq0Wf8d9gEajAIvMsBGmkOk8b1ByCNH4l6xx5KmoDenXeKqW6ioQ3yBqk7Kx4RRJZlmqqim_GIPo6lZKe4V9St4i_X6ZHiuv2kjL_pX2XJT0XZ4kPjsJLPhgknW5S3GdNyofxyKOSYP3UIV3WVs6TQa0FQ8n6iY8gfLTvVmZ4rhIdafOemoCKQOlnbc8uhXlhBcjI9ePmMFMa5AVSQ7zULO9o1A"}], "method": "POST", "pathVariableData": [], "queryParams": [{"key": "keySet", "value": "ces"}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJSUzI1NiIsImtpZCI6IlFac0xydXlkWEdPQ01qaGk4OXdldGlKa25aVSIsInBpLmF0bSI6Im51eWoifQ.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.dzeXmu8VjwU8oW1HYMzkVZBRpvXnA8gbZwtFq3i5HcgAyhNIH1QMTj4f1eoRhbddy-4EBUyZq1c4eFWUcs8LqbP6bbNzOCkkWiegY-xi587Wv4TGqnYwIEVLUvUMYQV1kwxVR82MScvkYTSWrzODGjNztnSYUB7hlIHFC6wEQi8wJBVWjrzQtfKq06Bfvzs24O8EWaJAjqMaL6m3GlJAISyylvLhavUiQ2yGcR9PxHPa1uTF1b47A8Y4ZofKHJHGUVTCDIZlbthprLrRLSpvMJjS6nEsR7zx9dqs22KFn5PqG5SQ_tt_JCglJWVl1HmJns7nhQx7D6P5gU_HKyQtgg", "type": "string"}]}, "events": null, "folder": null, "responses_order": [], "currentHelper": "<PERSON><PERSON><PERSON>", "helperAttributes": {"id": "bearer", "token": "eyJhbGciOiJSUzI1NiIsImtpZCI6IlFac0xydXlkWEdPQ01qaGk4OXdldGlKa25aVSIsInBpLmF0bSI6Im51eWoifQ.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.dzeXmu8VjwU8oW1HYMzkVZBRpvXnA8gbZwtFq3i5HcgAyhNIH1QMTj4f1eoRhbddy-4EBUyZq1c4eFWUcs8LqbP6bbNzOCkkWiegY-xi587Wv4TGqnYwIEVLUvUMYQV1kwxVR82MScvkYTSWrzODGjNztnSYUB7hlIHFC6wEQi8wJBVWjrzQtfKq06Bfvzs24O8EWaJAjqMaL6m3GlJAISyylvLhavUiQ2yGcR9PxHPa1uTF1b47A8Y4ZofKHJHGUVTCDIZlbthprLrRLSpvMJjS6nEsR7zx9dqs22KFn5PqG5SQ_tt_JCglJWVl1HmJns7nhQx7D6P5gU_HKyQtgg"}, "collectionId": "84c6489f-ec63-4711-ab47-dec35208f664", "rawModeData": "{\n    \"accountNumber\": {\n        \"purpose\": \"CRIT\",\n        \"value\": \"1234567\"\n    }\n}", "headers": "Authorization: Bearer ey<PERSON>hbGciOiJSUzI1NiIsImtpZCI6IjM5STRoOVl5anNRWTlpa0hhcWdOREQ3WHY4byIsInBpLmF0bSI6Im51eWoifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NLQnQV9wbLOC2lZnKBsc7p5KmC6s8cLEQst917RY4gdC0pmewyjKPA3ANBgSvgb36gFcBhdWsIP-useTEwJn28Q_kxdpdgW9j07OwFZZn8dWJ6yYjvY7sNHu9_Jhq0Wf8d9gEajAIvMsBGmkOk8b1ByCNH4l6xx5KmoDenXeKqW6ioQ3yBqk7Kx4RRJZlmqqim_GIPo6lZKe4V9St4i_X6ZHiuv2kjL_pX2XJT0XZ4kPjsJLPhgknW5S3GdNyofxyKOSYP3UIV3WVs6TQa0FQ8n6iY8gfLTvVmZ4rhIdafOemoCKQOlnbc8uhXlhBcjI9ePmMFMa5AVSQ7zULO9o1A\n", "pathVariables": {}}]}], "environments": [{"id": "2b755ead-9dd7-42ad-882a-ddbdeb23b3a9", "name": "DEV-BFF", "values": [{"key": "host", "value": "api.consumer-mobile-dev.nube.53.com", "enabled": true}, {"key": "protocol", "value": "https", "enabled": true}, {"key": "port", "value": "443", "enabled": true}, {"key": "servletcontext", "value": "", "enabled": true}, {"key": "context", "value": "/move-money-bff", "enabled": true}]}, {"id": "2d850fda-2825-4943-8662-efa52b4ed3bd", "name": "LOCAL-CES-Proxy", "values": [{"key": "host", "value": "localhost", "enabled": true}, {"key": "protocol", "value": "http", "enabled": true}, {"key": "port", "value": "3000", "enabled": true}, {"key": "servletcontext", "value": "", "enabled": true}, {"key": "context", "value": "", "enabled": true}]}, {"id": "44e5a6f8-79f0-412c-8ac9-2e48eddd3a21", "name": "SIT-BFF", "values": [{"key": "host", "value": "api.consumermobile-sit.nube.53.com", "enabled": true}, {"key": "protocol", "value": "https", "enabled": true}, {"key": "port", "value": "443", "enabled": true}, {"key": "servletcontext", "value": "", "enabled": true}, {"key": "context", "value": "/move-money-bff", "enabled": true}, {"key": "device_uuid", "value": "", "enabled": true}]}, {"id": "453a11a2-6073-43b3-9876-39fb72ca47cd", "name": "PROD-BFF", "values": [{"key": "host", "value": "api.consumer-mobile.nube.53.com", "enabled": true}, {"key": "protocol", "value": "https", "enabled": true}, {"key": "port", "value": "443", "enabled": true}, {"key": "servletcontext", "value": "", "enabled": true}, {"key": "context", "value": "/move-money-bff", "enabled": true}, {"key": "device_uuid", "value": "", "enabled": true}]}, {"id": "678cde4a-ac9c-441b-ad8b-0d382577f548", "name": "LOCAL-BFF", "values": [{"key": "host", "value": "localhost", "enabled": true}, {"key": "protocol", "value": "http", "enabled": true}, {"key": "port", "value": "8080", "enabled": true}, {"key": "servletcontext", "value": "/move-money-bff", "enabled": true}, {"key": "context", "value": "", "enabled": true}, {"key": "device_uuid", "value": "", "type": "any", "enabled": true}]}, {"id": "7610ea8f-25d5-4c97-9b54-e5fc123969fe", "name": "SIT-CES-Proxy", "values": [{"key": "host", "value": "dywal6wff9-vpce-04e3fad85e181e1ea.execute-api.us-east-2.amazonaws.com", "enabled": true}, {"key": "protocol", "value": "https", "enabled": true}, {"key": "port", "value": "443", "enabled": true}, {"key": "servletcontext", "value": "/api", "enabled": true}, {"key": "context", "value": "/ces-proxy", "enabled": true}, {"key": "device_uuid", "value": "", "enabled": true}]}, {"id": "5a0792f3-e48e-4282-b470-964198a24427", "name": "My Workspace - globals", "values": [{"key": "auth_token", "value": "", "type": "any", "enabled": true}, {"key": "risk_token", "value": "", "type": "any", "enabled": true}, {"key": "analyzeRiskScoreIst", "value": "", "type": "any", "enabled": true}]}], "headerPresets": [], "globals": []}