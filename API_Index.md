# Move Money BFF API Documentation Index

## Welcome to the Move Money BFF API Documentation

This documentation provides comprehensive information about the Move Money Backend for Frontend (BFF) service APIs. The service enables money movement operations including transfers, bill payments, external account management, and Certificate of Deposit operations.

---

## 📋 Quick Navigation

### 🏠 [Main API Specification](./API_Specification.md)
Complete API reference with all endpoints, request/response schemas, and examples.

---

## 🎯 API Categories

### 💰 **Activity Management**
Manage transfer and bill payment activities
- [Create Activity](./API_Specification.md#11-create-activity) - `POST /activity`
- [Update Activity](./API_Specification.md#12-update-activity) - `PUT /activity`
- [Get Activity (V2)](./API_Specification.md#13-get-activity-v2) - `GET /v2/activity`
- [Get Transfer Limits](./API_Specification.md#14-get-transfer-limits) - `GET /activity/transferlimits`

### 🏦 **External Transfer Management**
Handle external account transfers and verification
- [Get Account Verification Info](./API_Specification.md#21-get-account-verification-info) - `GET /externaltransfer/accountVerificationInfo`
- [Get Bank Information](./API_Specification.md#22-get-bank-information) - `GET /externaltransfer/bankInfo`
- [Add External Account](./API_Specification.md#23-add-external-account) - `POST /externaltransfer/addAccount`
- [Delete External Account](./API_Specification.md#24-delete-external-account) - `DELETE /externaltransfer/deleteAccount`
- [Verify Account Real Time](./API_Specification.md#25-verify-account-real-time) - `POST /externaltransfer/verifyAccountRealTime`
- [Verify Trial Deposits](./API_Specification.md#26-verify-trial-deposits) - `POST /externaltransfer/verifyTrialDeposits`
- [Update Account Nickname](./API_Specification.md#27-update-account-nickname) - `PUT /externaltransfer/updateAccountNickname`

### 🔗 **External Account Management**
Retrieve external account information
- [Get External Accounts](./API_Specification.md#31-get-external-accounts) - `GET /external/accounts`

### 💳 **Bill Payment Management**
Manage payees and bill payment operations
- [Get Global Payees](./API_Specification.md#41-get-global-payees) - `GET /billpay/globalpayees`
- [Get Payee Account Number](./API_Specification.md#42-get-payee-account-number) - `GET /billpay/payee/account`
- [Add Payee](./API_Specification.md#43-add-payee) - `POST /billpay/addpayee`
- [Edit Payee](./API_Specification.md#44-edit-payee) - `PUT /billpay/editpayee`
- [Get Bill Pay Profile](./API_Specification.md#45-get-bill-pay-profile) - `GET /billpay/profile`

### 📊 **Transfer Information**
Get account and transfer information
- [Get Transfer Accounts](./API_Specification.md#51-get-transfer-accounts) - `GET /transfer/info`

### 🏛️ **Certificate of Deposit (CD) Operations**
Handle CD-related transactions
- [Redeem CD](./API_Specification.md#611-redeem-cd) - `POST /cd/redeem`
- [Get CD Funding Accounts](./API_Specification.md#621-get-cd-funding-accounts) - `GET /cd/funding-accounts`

---

## 🚀 Getting Started

### Prerequisites
- Valid authentication credentials
- Access to the appropriate environment (development/production)
- Understanding of REST API principles

### Base URLs
- **Development**: `https://developer-stg.info53.com`
- **Production**: `https://developer.info53.com`

### Authentication
All API endpoints require proper authentication. Ensure you have valid credentials before making requests.

---

## 📖 Documentation Sections

### 🔧 **Technical Reference**
- [Error Handling](./API_Specification.md#error-handling) - Standard error responses and codes
- [Data Models](./API_Specification.md#data-models) - Common object structures
- [Response Codes](./API_Specification.md#common-response-codes) - HTTP status codes used

### 📝 **Request/Response Examples**
Each endpoint in the main specification includes:
- Complete request schemas with required/optional fields
- Sample JSON request bodies
- Detailed response structures
- Error response examples

---

## 🏗️ **API Architecture**

### Controllers Overview
The Move Money BFF service is organized into the following controllers:

| Controller | Purpose | Base Path |
|------------|---------|-----------|
| **MmActivityController** | Activity management | `/activity`, `/v2/activity` |
| **MmExternalTransferController** | External transfers | `/externaltransfer` |
| **MmExternalAccountController** | External accounts | `/external` |
| **MmBillpayController** | Bill payments | `/billpay` |
| **MmTransferInfoController** | Transfer information | `/transfer` |
| **MmCDFundsTransferController** | CD operations | `/cd` |
| **MmCDFundingAccountController** | CD funding accounts | `/cd` |

### Feature Flags
The service uses feature flags to control routing between different implementations:
- `ORCHESTRATOR_ENABLED` - Controls service orchestration behavior

### Risk Scoring
Certain endpoints include risk scoring for fraud prevention:
- External transfer operations
- Account additions
- Payee management

---

## 🔍 **Common Use Cases**

### 1. **Setting up External Transfers**
1. [Get Bank Information](./API_Specification.md#22-get-bank-information) to validate routing number
2. [Add External Account](./API_Specification.md#23-add-external-account) to register the account
3. [Verify Account](./API_Specification.md#25-verify-account-real-time) using real-time or trial deposits
4. [Create Activity](./API_Specification.md#11-create-activity) to initiate transfer

### 2. **Managing Bill Payments**
1. [Get Global Payees](./API_Specification.md#41-get-global-payees) to see available payees
2. [Add Payee](./API_Specification.md#43-add-payee) if needed
3. [Get Bill Pay Profile](./API_Specification.md#45-get-bill-pay-profile) to see funding accounts
4. [Create Activity](./API_Specification.md#11-create-activity) to schedule payment

### 3. **Viewing Account Activity**
1. [Get Transfer Accounts](./API_Specification.md#51-get-transfer-accounts) to see available accounts
2. [Get Activity](./API_Specification.md#13-get-activity-v2) to view recent and upcoming transactions
3. [Get Transfer Limits](./API_Specification.md#14-get-transfer-limits) to check limits between accounts

---

## 📞 **Support & Resources**

### Development Environment
- Staging URL: `https://developer-stg.info53.com`
- CORS enabled for development origins

### Production Environment
- Production URL: `https://developer.info53.com`
- Enhanced security and monitoring

### Additional Resources
- [Main API Specification](./API_Specification.md) - Complete technical documentation
- Internal documentation links (if available)
- Support contact information (if available)

---

## 📋 **Version Information**

This documentation covers the current version of the Move Money BFF API service. The service includes both V1 and V2 implementations for certain endpoints, with feature flags controlling the routing.

### API Versioning
- Activity endpoints support both V1 and V2 implementations
- V2 endpoints are recommended for new integrations
- Legacy V1 support maintained for backward compatibility

---

*Last Updated: [Current Date]*
*Documentation Version: 1.0*
