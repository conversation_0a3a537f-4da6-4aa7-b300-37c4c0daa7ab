/* Copyright 2022 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.bff.external.transfer.responses;

import com.fitb.digital.bff.movemoney.model.bff.BffSimpleResponse;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class BffFinancialInstitutionInfo extends BffSimpleResponse {
  private String institutionName;
  private boolean isHostInstitution;
  private boolean trialDepositVerification;
  private boolean realTimeVerification;
  private List<BffFinancialInstitutionLoginInfo> finInsLoginInfoList;
  private List<String> instantVerificationEnabledAcctTypes;
}
