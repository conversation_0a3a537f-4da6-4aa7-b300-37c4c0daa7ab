/* Copyright 2022 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.bff.external.transfer.responses;

import com.fitb.digital.bff.movemoney.model.bff.external.transfer.ExternalAccountType;
import com.fitb.digital.bff.movemoney.util.NameNormalizer;
import java.math.BigDecimal;
import java.time.LocalDate;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class BffExternalAccount implements TrialDepositExternalAccount {
  private String id;
  private String institutionName;
  private String accountStatus;
  private ExternalAccountType accountType;
  private BigDecimal availableBalance;
  private String accountNickName;
  private String isHostAccount;
  private String accountGroup;
  private String realTimeVerificationStatus;
  private Integer realTimeRemainingAttempts;
  private Integer trialDepositRemainingAttempts;
  private LocalDate trialDepositStartDate;
  private String routingNumber;
  private String displayAccountNumber;

  public String getAccountNickName() {
    return NameNormalizer.normalizeNickname(accountNickName);
  }
}
