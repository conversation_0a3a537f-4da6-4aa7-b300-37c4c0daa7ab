/* Copyright 2022 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.bff.external.transfer.responses;

import com.fitb.digital.bff.movemoney.model.bff.BffSimpleResponse;
import com.fitb.digital.bff.movemoney.model.bff.external.transfer.requests.BffRealTimeVerifyStatus;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class BffRealTimeVerificationResponse extends BffSimpleResponse {
  BffRealTimeVerifyStatus verificationStatus;
  List<AuthenticationParam> parameters;
}
