/* Copyright 2022 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.service.transferinfo;

import com.fitb.digital.bff.movemoney.model.client.account.responses.CesGoal;
import com.fitb.digital.bff.movemoney.model.client.account.responses.ListResponse;
import java.util.*;
import org.jetbrains.annotations.NotNull;

class AccountListMapper {
  private final ListResponse accountList;
  private Map<String, String> goalIdsToParentAccountIds;
  private Map<String, List<String>> parentToGoalIdsSorted;
  private final Map<String, CesGoal> momentumSavingsIdsToGeneralSavings = new HashMap<>();
  private final List<String> generalSavingsIdsFromProfile = new ArrayList<>();

  public AccountListMapper(ListResponse accountList) {
    this.accountList = accountList;
  }

  public Map<String, String> getGoalIdsToParentAccountIds() {
    return goalIdsToParentAccountIds;
  }

  public Map<String, List<String>> getParentToGoalIdsSorted() {
    return parentToGoalIdsSorted;
  }

  public List<String> getGeneralSavingsIdsFromProfile() {
    return generalSavingsIdsFromProfile;
  }

  public Map<String, CesGoal> getMomentumSavingsIdsToGeneralSavings() {
    return momentumSavingsIdsToGeneralSavings;
  }

  public void addGenerarlSavingsIdFromProfile(String id) {
    generalSavingsIdsFromProfile.add(id);
  }

  public AccountListMapper invoke() {
    // Create a map of momentum savings parent accounts to a list of its goals
    var momentumSavingsToGoals = new HashMap<String, List<CesGoal>>();
    goalIdsToParentAccountIds = new HashMap<>();
    for (CesGoal cesGoal : accountList.getGoals()) {
      if (Boolean.TRUE.equals(cesGoal.getGeneralSavings())) {
        momentumSavingsIdsToGeneralSavings.put(cesGoal.getAccountId(), cesGoal);
      } else {
        var goalList =
            momentumSavingsToGoals.getOrDefault(cesGoal.getAccountId(), new ArrayList<>());
        goalList.add(cesGoal);
        momentumSavingsToGoals.put(cesGoal.getAccountId(), goalList);
        goalIdsToParentAccountIds.put(cesGoal.getId(), cesGoal.getAccountId());
      }
    }

    parentToGoalIdsSorted = getParentToGoalIdsSorted(momentumSavingsToGoals);

    return this;
  }

  @NotNull
  private HashMap<String, List<String>> getParentToGoalIdsSorted(
      HashMap<String, List<CesGoal>> momentumSavingsToGoals) {
    var momentumSavingsToGoalIdsSorted = new HashMap<String, List<String>>();
    for (List<CesGoal> goals : momentumSavingsToGoals.values()) {
      goals.sort(Comparator.comparing(CesGoal::getTargetDate));
      goals.stream()
          .findFirst()
          .ifPresent(
              g ->
                  momentumSavingsToGoalIdsSorted.put(
                      g.getAccountId(), goals.stream().map(CesGoal::getId).toList()));
    }
    return momentumSavingsToGoalIdsSorted;
  }
}
