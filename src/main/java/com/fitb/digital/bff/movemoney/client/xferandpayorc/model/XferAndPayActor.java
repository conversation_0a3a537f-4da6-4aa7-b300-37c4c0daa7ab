/* Copyright 2023 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.client.xferandpayorc.model;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;

@Data
public class XferAndPayActor {
  private String id;
  private boolean active = true;
  // TODO do we need this? I thougth we were going to get away from file type? 7/28/2023
  private String type;
  private boolean transferSource;
  private boolean paymentTarget;
  private String displayName;
  private String displayAccountNumber;
  private BigDecimal displaySortOrder;
  private boolean canAddRecurring;
  private final List<XferAndPayAuthority> availableSources = new ArrayList<>();
  private final List<XferAndPayAuthority> availableRecipients = new ArrayList<>();
  private boolean electronicBillingEnabled;
  private String status;
  private BigDecimal availableBalance;
  private BigDecimal ledgerBalance;
  private boolean accountInternal;
  private boolean invoiceEnabled;

  // BillPayee
  private String nickname;
  private String paymentCutoffTime;
  private LocalDate nearestNewPaymentDate;
  private String categoryTypeId;
  private String eBillsStatus;
  private BigDecimal lastPaymentAmount;
  private LocalDate lastPaymentDate;

  // External Transfer
  private boolean verificationRequired;

  // Internal Account Actor
  //    private final AccountDto account; TODO: We know we need to fix this
  private boolean paymentRecipient;
  private boolean canAddPayee;
  private boolean canAddPayAccount;
  private boolean canAddTransferAccount;
  private BigDecimal goalAmountNotAllocated;
  private boolean externalTransferEligible;

  // Goal Actor
  private String goalCategory;
  private BigDecimal goalTargetAmount;
  private String goalParentId;
  private LocalDate goalTargetDate;
}
