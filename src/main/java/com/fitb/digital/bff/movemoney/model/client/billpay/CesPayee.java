/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.client.billpay;

import static com.fitb.digital.lib.tokenization.SensitiveDataType.CRIT;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fitb.digital.bff.movemoney.model.*;
import com.fitb.digital.bff.movemoney.model.bff.billpay.BffPayee;
import com.fitb.digital.bff.movemoney.model.bff.billpay.requests.BffAddPayeeRequest;
import com.fitb.digital.lib.tokenization.SensitiveData;
import com.fitb.digital.lib.tokenization.model.KeyMetadata;
import com.fitb.digital.lib.tokenization.model.Tokenizable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.Map;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.modelmapper.ModelMapper;

@Data
@NoArgsConstructor
@SuppressWarnings("java:S1948") // Tokenizable extends Serializable, go home SonarQube.
public class CesPayee implements Tokenizable {
  // Note: this class will be expanded as we add more bill pay capabilities.
  @SensitiveData(CRIT)
  private String accountNumber;

  @JsonProperty("TOKENIZED_FIELDS")
  protected Map<String, KeyMetadata> keyMetadata;

  private String id;
  private String name;
  private String cesAccountType;

  private String phoneNumber;
  private PayeeAddress cesAddress;

  private String nickname;
  private LocalTime paymentCutoffTime;
  private LocalDate nearestNewPaymentDate;
  private String categoryTypeId;
  private Boolean electronicBillingEnabled;

  private FISPayeeType type;
  private CesPayeeType cesPayeeType;
  private Payment recurringPayment;
  private BillingReminder eBillReminder;
  private BillingRule eBillRule;
  private BigDecimal lastPaymentAmount;
  private LocalDate lastPaymentDate;

  public static CesPayee from(BffAddPayeeRequest addPayeeRequest) {
    CesPayee cesPayee = new CesPayee();
    cesPayee.setName(addPayeeRequest.getName());
    cesPayee.setNickname(addPayeeRequest.getNickname());
    cesPayee.setAccountNumber(addPayeeRequest.getAccountNumber());

    if (addPayeeRequest.isPersonalPayee()) {
      cesPayee.setCesPayeeType(CesPayeeType.PERSONAL_PAYEE);
    } else if (addPayeeRequest.getAddress() == null) {
      cesPayee.setCesPayeeType(CesPayeeType.TWOFACTOR);
    } else {
      if (addPayeeRequest.getAddress().getStreetLine1() != null) {
        cesPayee.setCesPayeeType(CesPayeeType.SIXFACTOR);
      } else {
        cesPayee.setCesPayeeType(CesPayeeType.THREEFACTOR);
      }
    }

    if (addPayeeRequest.getAddress() != null) {
      cesPayee.setCesAddress(new PayeeAddress(addPayeeRequest.getAddress()));
    }

    return cesPayee;
  }

  public static CesPayee from(BffPayee bffPayee) {
    CesPayee cesPayee = new CesPayee();
    ModelMapper mapper = new ModelMapper();
    mapper.getConfiguration().setAmbiguityIgnored(true);
    mapper.map(bffPayee, cesPayee);

    cesPayee.setType(bffPayee.getFisPayeeType());
    cesPayee.setCesAddress(bffPayee.getAddress());

    return cesPayee;
  }
}
