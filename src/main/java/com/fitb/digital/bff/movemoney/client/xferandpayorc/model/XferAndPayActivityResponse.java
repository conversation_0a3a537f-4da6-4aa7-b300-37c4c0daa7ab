/* Copyright 2023 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.client.xferandpayorc.model;

import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@RequiredArgsConstructor
public class XferAndPayActivityResponse {
  private final List<XferAndPayActivity> recentActivities = new ArrayList<>();
  private final List<XferAndPayActivity> upcomingActivities = new ArrayList<>();
  private final List<XferAndPayActivity> recurringActivities = new ArrayList<>();
  private final List<String> retrievalErrors = new ArrayList<>();
}
