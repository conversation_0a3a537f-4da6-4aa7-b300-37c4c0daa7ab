/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model;

import com.fitb.digital.bff.movemoney.model.bff.activity.RecurringActivityFrequency;
import com.fitb.digital.bff.movemoney.util.NameNormalizer;
import com.openpojo.business.annotation.BusinessKey;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.text.WordUtils;

@Data
@NoArgsConstructor
public class ActivityBase {
  /** Transfer from 5/3 account to 5/3 account */
  public static final String INTERNAL_TRANSFER = "INTERNAL_TRANSFER";

  /** Transfer from 5/3 account to non 5/3 account, or from non 5/3 account to 5/3 account */
  public static final String EXTERNAL_TRANSFER = "EXTERNAL_TRANSFER";

  /** Payment from 5/3 account to billpay customer */
  public static final String BILLPAY = "BILLPAY";

  /** Transfer from non 5/3 account to 5/3 loan account */
  public static final String WEB_PAYMENTS = "WEB_PAYMENTS";

  /**
   * Transfer from non 5/3 account to 5/3 credit card account (one time) or from 5/3 account to 5/3
   * credit card account (automatic)
   */
  public static final String INVOICED_PAYMENTS = "INVOICED_PAYMENT";

  /** Transfer from 5/3 account to goal */
  public static final String GOALS = "GOALS";

  public static final String COMPLETED_STATUS = "Completed";
  public static final String CANCELED_STATUS = "Canceled";
  public static final String IN_PROCESS_STATUS = "In Process";
  public static final String UNSUCCESSFUL_STATUS = "Unsuccessful";
  public static final String SCHEDULED_STATUS = "Scheduled";

  private String id;
  private String displayId;
  private String fromAccountId;
  private String fromAccountNumber;

  @BusinessKey(caseSensitive = false)
  private String fromAccountName;

  private String toAccountId;
  private String toAccountNumber;
  private String toAccountType;

  @BusinessKey(caseSensitive = false)
  private String toAccountName; // Nick name/Name

  private Double amount;
  private boolean expressDelivery;
  private BigDecimal additionalPrincipleAmount;
  private String memo;
  private LocalDate creationDate;
  private LocalDate deliveryDate;
  private LocalDate endDate;
  private LocalDate dueDate;
  private RecurringActivityFrequency frequency;
  private Integer numberOfActivities;
  private Integer numberOfRemainingActivities;
  private String status;
  private String displayStatus;

  /** Transactis related properties */
  private String paymentAmountType;

  private String payOnType;
  private Integer dayToPay;

  public String getNormalizedDisplayStatus() {
    if (this.displayStatus == null) {
      return null;
    } else if (this.displayStatus.length() > 1) {
      return WordUtils.capitalizeFully(displayStatus.replace("_", " "));
    } else {
      return this.displayStatus;
    }
  }

  private LocalDateTime createTimestamp;
  private boolean editable;
  private boolean cancelable;

  // This is an enum of type CesActivityType on CES
  @Deprecated(since = "Replaced by activityType")
  private String type;

  private String activityType;
  private String checkNumber;
  // A different id is used for recurring activities.
  private String recurringId;

  public String getToAccountName() {
    return NameNormalizer.normalizeAccountName(toAccountName);
  }

  public String getFromAccountName() {
    return NameNormalizer.normalizeAccountName(fromAccountName);
  }
}
