/* Copyright 2023 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.client.xferandpayorc.model;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Optional;
import lombok.Data;

@Data
public class XferAndPayActivityRequest {
  private String id;
  private XferAndPayActivityType activityType;
  private String fromAccountId;
  private String toAccountId;
  private BigDecimal amount;
  private LocalDate dueDate;
  private XferAndPayRecurringFrequencyType frequency;
  private BigDecimal additionalPrincipleAmount;
  private Integer numberOfTransactions;
  private boolean expressDelivery;
  private String memo;
  private String fromGoalParentAccountId;
  private String toGoalParentAccountId;
  // The following are missing, do we need them?
  private String recurringId;
  private LocalDate endDate;

  public XferAndPayRecurringFrequencyType getFrequency() {
    return Optional.ofNullable(frequency).orElse(XferAndPayRecurringFrequencyType.ONE_TIME);
  }

  public void setFrequency(XferAndPayRecurringFrequencyType frequency) {
    this.frequency =
        Optional.ofNullable(frequency).orElse(XferAndPayRecurringFrequencyType.ONE_TIME);
  }
}
