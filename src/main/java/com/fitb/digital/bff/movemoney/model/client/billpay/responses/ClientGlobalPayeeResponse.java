/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.client.billpay.responses;

import com.fitb.digital.bff.movemoney.model.client.CesBaseResponse;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@SuppressWarnings("squid:S1068")
public class ClientGlobalPayeeResponse extends CesBaseResponse {
  private List<ClientGlobalPayee> globalPayees = new ArrayList<>();

  public ClientGlobalPayeeResponse(String status) {
    setStatus(status);
  }
}
