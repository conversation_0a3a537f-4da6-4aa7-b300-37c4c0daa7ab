/* Copyright 2024 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.bff.cd.responses;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class ExternalFundingAccount {

  private String externalAccountId;

  // accountNumber is currently tokenized from service and we are not detokenizing it as we dont
  // need it.
  private String accountNumber;

  private String accountNumberLast4;

  private String accountType;

  private String bankName;

  private String accountRoutingNumber;
}
