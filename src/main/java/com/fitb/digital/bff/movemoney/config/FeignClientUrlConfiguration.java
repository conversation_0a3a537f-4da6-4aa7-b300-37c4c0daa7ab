/* Copyright 2024 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.config;

import com.fitb.digital.lib.featureflag.service.FeatureFlagService;
import feign.Client;
import feign.Request;
import feign.Response;
import java.io.IOException;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

@Configuration
public class FeignClientUrlConfiguration implements InitializingBean, DisposableBean {
  private final FeatureFlagService featureFlagService;
  private final Environment environment;
  protected final Map<String, String> flagToUrlPropMap;
  protected final Map<String, String> urlReplacementMap;

  private final Logger log = LoggerFactory.getLogger(this.getClass());

  protected static final String PROPERTY_SUFFIX_DEFAULT = ".url";
  protected static final String PROPERTY_SUFFIX_LEGACY = ".legacy";
  protected static final String PROPERTY_SUFFIX_SCF = ".scf";

  @Autowired
  public FeignClientUrlConfiguration(
      FeatureFlagService featureFlagService, Environment environment) {
    this.featureFlagService = featureFlagService;
    this.environment = environment;
    this.urlReplacementMap = new ConcurrentHashMap<>();

    flagToUrlPropMap = new ConcurrentHashMap<>();

    // Add feign clients here. Launch Darkly Flag, application.yaml property
    flagToUrlPropMap.put("domain-xfer-and-pay-scf-enabled", "feign.client.config.xfer-and-pay-orc");
    flagToUrlPropMap.put("domain-ces-proxy-scf-enabled", "feign.client.config.ces-proxy");
  }

  @Override
  public void afterPropertiesSet() {
    // Subscribe to flag value changes
    featureFlagService.addFlagChangeListener(this::onFlagChange);

    // Set initial values from flags
    flagToUrlPropMap.forEach(this::updateUrlFromFlag);
  }

  @Override
  public void destroy() {
    // Remove listener when this bean is destroyed
    featureFlagService.removeFlagChangeListener(this::onFlagChange);
  }

  protected void onFlagChange(String flag) {
    if (flagToUrlPropMap.containsKey(flag)) {
      String urlProp = flagToUrlPropMap.get(flag);
      updateUrlFromFlag(flag, urlProp);
    }
  }

  private void updateUrlFromFlag(String flag, String urlProp) {
    String defaultUrl = environment.getProperty(urlProp + PROPERTY_SUFFIX_DEFAULT, "");
    String legacyUrl = environment.getProperty(urlProp + PROPERTY_SUFFIX_LEGACY);
    String scfUrl = environment.getProperty(urlProp + PROPERTY_SUFFIX_SCF);

    String url = isScfEnabled(flag, scfUrl, defaultUrl) ? scfUrl : legacyUrl;
    urlReplacementMap.put(defaultUrl, url);
    log.info("Updating {} to {} based on launch darkly flag", urlProp, url);
  }

  private boolean isScfEnabled(String flag, String scfUrl, String defaultUrl) {
    return featureFlagService.isFeatureEnabled(flag, defaultUrl.equalsIgnoreCase(scfUrl));
  }

  @Bean
  public Client feignClient() {
    return new Client.Default(null, null) {
      @Override
      public Response execute(Request request, Request.Options options) throws IOException {

        Optional<String> urlKey =
            urlReplacementMap.keySet().stream()
                .filter(oldUrl -> request.url().startsWith(oldUrl))
                .findFirst();

        String url =
            urlKey
                .map(s -> request.url().replace(s, urlReplacementMap.get(urlKey.get())))
                .orElseGet(request::url);
        log.debug("Making request to url {}", url);

        return super.execute(
            Request.create(
                request.httpMethod(), url, request.headers(), request.body(), request.charset()),
            options);
      }
    };
  }
}
