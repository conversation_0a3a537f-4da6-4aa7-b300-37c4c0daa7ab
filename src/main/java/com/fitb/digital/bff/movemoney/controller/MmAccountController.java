/* Copyright 2020 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.controller;

import com.fitb.digital.bff.movemoney.model.bff.account.responses.BffAccountDetailResponse;
import com.fitb.digital.bff.movemoney.service.account.AccountService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@CrossOrigin(
    origins = {
      "http://localhost:8080",
      "https://slapdpd002.info53.com",
      "https://developer-stg.info53.com",
      "https://developer.info53.com"
    })
// TODO: @Api(tags = "Transfer Account Information")
public class MmAccountController {
  private final AccountService accountService;

  @GetMapping(value = "/account/detail", produces = MediaType.APPLICATION_JSON_VALUE)
  @Operation(summary = "Return details for the provided transfer account.")
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = "401",
            description =
                "Unauthorized. Request was not processed because invalid authentication provided to access the target resource."),
        @ApiResponse(
            responseCode = "403",
            description =
                "Request is forbidden. Requested operation is not authorized for this user"),
        @ApiResponse(responseCode = "500", description = "Server error")
      })
  public BffAccountDetailResponse getAccountDetail(@RequestParam(value = "id") String accountId) {
    return accountService.getAccountDetails(accountId);
  }
}
