/* Copyright 2022 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.bff.invoicedpayment.response;

import com.fitb.digital.bff.movemoney.model.bff.BffResponse;
import com.fitb.digital.bff.movemoney.model.bff.invoicedpayment.BffInvoicedPaymentAccount;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class BffInvoicedPaymentAccountResponse implements BffResponse {
  private String status;
  private String statusCode;
  private String statusReason;
  private List<String> retrievalErrors = new ArrayList<>(); // TODO: Do we actually need this?

  private List<BffInvoicedPaymentAccount> accounts;
}
