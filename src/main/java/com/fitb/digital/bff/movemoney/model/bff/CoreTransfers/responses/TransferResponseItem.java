/* Copyright 2024 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.bff.CoreTransfers.responses;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class TransferResponseItem {

  private String id;

  private String accountId;

  private String accountSource;

  private String description;

  private BigDecimal amount;

  private String transferType;

  private Date expectedPostingDate;

  private String createdDate;

  private WorkdayTags workdayTags;
}
