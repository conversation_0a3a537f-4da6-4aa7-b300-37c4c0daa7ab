/* Copyright 2022 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.service.transferinfo;

import com.fitb.digital.bff.movemoney.model.service.transferinfo.TransferListsResponse;
import com.fitb.digital.bff.movemoney.service.account.AccountServiceAsync;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

public class TransferListsFetcher {
  private final AccountServiceAsync accountServiceAsync;

  public TransferListsFetcher(AccountServiceAsync accountServiceAsync) {
    this.accountServiceAsync = accountServiceAsync;
  }

  public CompletableFuture<TransferListsResponse> getTransferLists(
      Boolean useAccountCache, Boolean useProfileCache)
      throws ExecutionException, InterruptedException {
    var completedFuture = new CompletableFuture<TransferListsResponse>();
    var transferListsResponse = new TransferListsResponse();

    transferListsResponse.setListResponseResult(
        accountServiceAsync.getAccountListAsync(useAccountCache).get());
    transferListsResponse.setProfileResponseResult(
        accountServiceAsync.getProfileAsync(useProfileCache).get());
    completedFuture.complete(transferListsResponse);

    return completedFuture;
  }
}
