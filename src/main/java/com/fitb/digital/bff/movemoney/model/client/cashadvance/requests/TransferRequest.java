/* Copyright 2022 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.client.cashadvance.requests;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.UUID;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class TransferRequest {
  private String requestGuid = UUID.randomUUID().toString();
  // private String id;

  private String toAccountId;

  private String fromAccountId;

  private BigDecimal amount;

  private LocalDate dueDate;
}
