/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.controller;

import com.fitb.digital.bff.movemoney.constants.ExceptionStatus;
import com.fitb.digital.bff.movemoney.exceptions.BffException;
import com.fitb.digital.bff.movemoney.model.bff.BffSimpleResponse;
import com.fitb.digital.bff.movemoney.model.bff.billpay.BffPayee;
import com.fitb.digital.bff.movemoney.model.bff.billpay.requests.BffAddPayeeRequest;
import com.fitb.digital.bff.movemoney.model.bff.billpay.responses.BffBillPayeeAccountResponse;
import com.fitb.digital.bff.movemoney.model.bff.billpay.responses.BffBillpayProfile;
import com.fitb.digital.bff.movemoney.model.bff.billpay.responses.BffGlobalPayeeResponse;
import com.fitb.digital.bff.movemoney.model.bff.billpay.responses.BffPayeeResponse;
import com.fitb.digital.bff.movemoney.service.BillpayService;
import com.fitb.digital.lib.risk.RiskScore;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("billpay")
@Slf4j
@RequiredArgsConstructor
@CrossOrigin(
    origins = {
      "http://localhost:8080",
      "https://slapdpd002.info53.com",
      "https://developer-stg.info53.com",
      "https://developer.info53.com"
    })
// TODO: @Api(tags = "Bill Payment Payee Management")
public class MmBillpayController {
  private final BillpayService billpayService;

  @GetMapping(value = "/globalpayees", produces = MediaType.APPLICATION_JSON_VALUE)
  @Operation(summary = "Return a list of all global payees with their default input model.")
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = "401",
            description =
                "Unauthorized. Request was not processed because invalid authentication provided to access the target resource."),
        @ApiResponse(
            responseCode = "403",
            description =
                "Request is forbidden. Requested operation is not authorized for this user"),
        @ApiResponse(responseCode = "500", description = "Server error")
      })
  public BffGlobalPayeeResponse globalPayees() {
    return billpayService.getGlobalPayees();
  }

  @GetMapping(value = "/payee/account", produces = MediaType.APPLICATION_JSON_VALUE)
  @Operation(summary = "Return the complete payee account number for the given personal payee.")
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = "401",
            description =
                "Unauthorized. Request was not processed because invalid authentication provided to access the target resource."),
        @ApiResponse(
            responseCode = "403",
            description =
                "Request is forbidden. Requested operation is not authorized for this user"),
        @ApiResponse(responseCode = "500", description = "Server error")
      })
  public BffBillPayeeAccountResponse getPayeeAccountNumber(@RequestParam String payeeId) {
    return billpayService.getTokenizedPayeeAccount(payeeId);
  }

  @RiskScore(activityType = "ADD_PAYEE")
  @PostMapping(
      value = "/addpayee",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  @Operation(summary = "Add a new global or custom payee to personal list.")
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = "401",
            description =
                "Unauthorized. Request was not processed because invalid authentication provided to access the target resource."),
        @ApiResponse(
            responseCode = "403",
            description =
                "Request is forbidden. Requested operation is not authorized for this user"),
        @ApiResponse(
            responseCode = "422",
            description = "Additional data required, see status code in response for more detail."),
        @ApiResponse(responseCode = "500", description = "Server error")
      })
  public ResponseEntity<BffPayeeResponse> addPayee(
      @Valid @RequestBody final BffAddPayeeRequest request) {
    // log.debug("Add Payee Start", kv("request_body", request));

    if (request.getAccountNumber() == null || request.getAccountNumber().isEmpty()) {
      if (!request.isPersonalPayee()) {
        throw BffException.badRequest(ExceptionStatus.ADD_PAYEE_CUSTOM_PAYEE);
      }

      request.setAccountNumber("");
    }

    var addPayeeResponse = billpayService.createPayee(request);
    // TODO error response - error response
    return new ResponseEntity<>(addPayeeResponse, addPayeeResponse.getCesHttpStatus());
  }

  @RiskScore(activityType = "EDIT_PAYEE")
  @PutMapping(
      value = "/editpayee",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  @Operation(summary = "Update a global or custom payee in the payee list.")
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = "401",
            description =
                "Unauthorized. Request was not processed because invalid authentication provided to access the target resource."),
        @ApiResponse(
            responseCode = "403",
            description =
                "Request is forbidden. Requested operation is not authorized for this user"),
        @ApiResponse(
            responseCode = "422",
            description = "Additional data required, see status code in response for more detail."),
        @ApiResponse(responseCode = "500", description = "Server error")
      })
  BffPayeeResponse editPayee(@Valid @RequestBody BffPayee request) {
    // log.debug("Edit Payee Start", kv("request_body", request));
    return billpayService.updatePayee(request);
  }

  @Operation(summary = "Remove a payee from the personal payee list.")
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = "401",
            description =
                "Unauthorized. Request was not processed because invalid authentication provided to access the target resource."),
        @ApiResponse(
            responseCode = "403",
            description =
                "Request is forbidden. Requested operation is not authorized for this user"),
        @ApiResponse(responseCode = "500", description = "Server error")
      })
  @DeleteMapping(value = "deletepayee", produces = MediaType.APPLICATION_JSON_VALUE)
  BffSimpleResponse deletePayee(@RequestParam String payeeId) {
    return billpayService.deletePayee(payeeId);
  }

  @Operation(
      summary = "Return a list of accounts that are eligible to participate in bill payment.")
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = "401",
            description =
                "Unauthorized. Request was not processed because invalid authentication provided to access the target resource."),
        @ApiResponse(
            responseCode = "403",
            description =
                "Request is forbidden. Requested operation is not authorized for this user"),
        @ApiResponse(responseCode = "500", description = "Server error")
      })
  @GetMapping(value = "/profile", produces = MediaType.APPLICATION_JSON_VALUE)
  public BffBillpayProfile getProfile() {
    return billpayService.getProfile();
  }
}
