/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.bff.billpay.requests;

import com.fitb.digital.bff.movemoney.model.PayeeAddress;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class BffAddPayeeRequest {
  @NotNull private String name;

  private String nickname;

  private String accountNumber;

  private PayeeAddress address;

  private boolean personalPayee = false;
}
