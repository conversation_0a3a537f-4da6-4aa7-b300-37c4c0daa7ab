/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.client.account.responses;

import java.math.BigDecimal;
import java.time.LocalDate;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class InternalAccount {
  private boolean goodFunds;
  private boolean authUserMultiCardAccount;
  private boolean cashAdvanceAccount;
  private String goalCategory;
  private BigDecimal goalTargetAmount;
  private LocalDate goalTargetDate;
  private String id;
  private boolean sweepAccount;
  private String accountType;
  private BigDecimal interestOnLastStatement;
  private boolean remoteAccountDetailsTemplate;
  private String accountDescription;
  private boolean brokerageAccount;
  private boolean displayFullAccountAndRoutingOk;
  private boolean accountDetailsExternal;
  private boolean reloadablePrepaidCard;
  private boolean linkedPca;
  private boolean hasParentAccountNumber;
  private boolean activitySettingManageAlertsOk;
  private Boolean displayCardManagementOk;
  private boolean closed;
  private boolean checksAccount;
  private BigDecimal interestYtd;
  private boolean bankEmployeeAccount;
  private boolean business;
  private boolean revolvingLineOfCreditAccount;
  private boolean expressChecking;
  private boolean basicChecking;
  private String affiliate;
  private boolean searchableOk;
  private LocalDate openDate;
  private boolean statementEligible;
  private BigDecimal ledgerBalance;
  private BigDecimal availableBalance;
  private BigDecimal interestSinceLastStatement;
  private boolean customerDataVendorManaged;
  private boolean activitySettingActivateCardOk;
  private boolean checksReorder;
  private boolean transferToOk;
  private boolean displayTransactionOk;
  private BigDecimal interestLastYear;
  private boolean activitySettingChangePinOk;
  private boolean billPayOk;
  private boolean blockedForCredit;
  private boolean supportsStatementCycles;
  private String displayName;
  private boolean hidden;
  private boolean displayAccountDetailsOk;
  private boolean cashAdvanceEligible;
  private String shortName;
  private long sortOrder;
  private boolean liability;
  private boolean noBalanceDisplay;
  private boolean depositAccount;
  private String displayAccountNumber;
  private boolean ableChecking;
  private boolean fromTransferOk;
  private boolean changeAddressEligible;
  private boolean creditCardAccount;
  private String cardReplacementCode;
  private boolean trustAccount;
  private boolean transactionExportEligible;
  private boolean externalTransferEligible;
  private Long advanceFeePercentage;
  private String nickName;
  private Long maxCreditLimit;
  private LocalDate lastPaymentDate;
  private BigDecimal lastPaymentAmount;
  private LocalDate nextPaymentDate;
}
