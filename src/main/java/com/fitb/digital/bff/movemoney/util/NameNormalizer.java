/* Copyright 2022 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.util;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class NameNormalizer {
  private static final Map<String, String> substitutions =
      new HashMap<String, String>() {
        {
          put("MyAdvance", "MyAdvance™");
        }
      };

  private static final List<Pattern> reservedTitleCase =
      new ArrayList<Pattern>() {
        private static final long serialVersionUID = 104037702840576240L;

        {
          add("CC");
          add("PCA");
          add("EAX");
          add("US");
          add("CK");
          add("IRS");
          add("DX");
          add("DDA");
          add("ATM");
          add("SAV");
          add("SV");
          add("UPS");
          add("USPS");
          add("FedEx");
          add("LLC");
          add("iTunes");
          add("PayPal");
          add("a");
          add("of");
          add("the");
          add("and");
          add("to");
          add("in");
          add("ACH");
          add("LN");
          add("IDAlert");
          add("LOC");
          add("ACCT");
          add("ML");
          add("BM");
          add("N.A.");
          // State Abbreviations
          add("AL");
          add("AK");
          add("AZ");
          add("AR");
          add("CA");
          add("CO");
          add("CT");
          add("DE");
          add("FL");
          add("GA");
          add("HI");
          add("ID");
          add("IL");
          add("IN");
          add("IA");
          add("KS");
          add("KY");
          add("LA");
          add("ME");
          add("MD");
          add("MA");
          add("MI");
          add("MN");
          add("MS");
          add("MO");
          add("MT");
          add("NE");
          add("NV");
          add("NH");
          add("NJ");
          add("NM");
          add("NY");
          add("NC");
          add("ND");
          add("OH");
          add("OK");
          add("OR");
          add("PA");
          add("RI");
          add("SC");
          add("SD");
          add("TN");
          add("TX");
          add("UT");
          add("VT");
          add("VA");
          add("WA");
          add("WV");
          add("WI");
          add("WY");
          add("MyAdvance");
        }

        public boolean add(String reservedWord) {
          return add(
              Pattern.compile("(^|\\W)(" + reservedWord + ")(\\W|$)", Pattern.CASE_INSENSITIVE));
        }
      };

  public static String stringCleaning(String givenString) {
    if (givenString == null) {
      return null;
    }
    /*
    If the string is not all upper case, return this string (no title casing needed),
    it has allready been processed (the services
    return strings in all upper case unless they're custom nickname fields)
    */
    if (!givenString.equals(givenString.toUpperCase(Locale.getDefault()))) {
      return givenString;
    }

    // replace all duplicate whitespace and replace tabs with spaces.
    givenString = givenString.replace("\t", " ");

    // check for repeating white space
    givenString = givenString.replaceAll("(\\s){2,}+", " ");

    // title case the string
    givenString =
        capitalize(givenString.toLowerCase(Locale.US), ' ', '\t', '|', '-', '_', '/', '\n');

    // check for repeating characters (x)...
    Pattern p = Pattern.compile("(x{2,}+)");
    Matcher m = p.matcher(givenString);
    while (m.find()) {
      String theMatch = m.group(1);
      givenString = givenString.replace(theMatch, theMatch.toUpperCase(Locale.US));
    }

    // check for reserved words
    for (Pattern reservedWordPattern : reservedTitleCase) {
      m = reservedWordPattern.matcher(givenString);
      while (m.find()) {
        String theMatch = m.group(2);
        String theReplacement = reservedWordPattern.pattern();
        theReplacement = theReplacement.replace("(^|\\W)(", "");
        theReplacement = theReplacement.replace(")(\\W|$)", "");
        givenString = givenString.replace(theMatch, theReplacement);
      }
    }

    StringBuffer givenStringBuffer = new StringBuffer(givenString);

    for (Map.Entry<String, String> substitution : substitutions.entrySet()) {
      int startIndex = givenStringBuffer.indexOf(substitution.getKey());

      if (startIndex >= 0) {
        int endIndex = startIndex + substitution.getKey().length();
        String substitutionText = substitution.getValue();
        givenStringBuffer.replace(startIndex, endIndex, substitutionText);
      }
    }

    givenString = givenStringBuffer.toString();

    // regardless of all other rules, except i*, the first letter should be uppercase
    if (givenString != null && givenString.length() > 0 && givenString.charAt(0) != 'i') {
      String firstLetter = givenString.substring(0, 1).toUpperCase(Locale.US);
      String restOfString = givenString.substring(1);
      givenString = firstLetter + restOfString;
    }

    return givenString;
  }

  /**
   * Applies normal capitalization rules to account names.
   *
   * @param accountName current account name.
   * @return title cased account name.
   */
  public static String normalizeAccountName(String accountName) {
    return stringCleaning(accountName);
  }

  /**
   * Title case upper-cased words, do not mess with lower cased words.
   *
   * @param nickName current nickname.
   * @return Title cased words.
   */
  public static String normalizeNickname(String nickName) {
    return nickName;
  }

  /**
   * Pulled from Apache Word utils, i didnt want to pull in the whole library
   * -http://commons.apache.org/proper/commons-lang/apidocs/src-html/org/apache/commons/lang3/text/WordUtils.html
   * -Rusty Weneck
   *
   * <p>Capitalizes all the delimiter separated words in a String. Only the first letter of each
   * word is changed.
   *
   * <p>The delimiters represent a set of characters understood to separate words. The first string
   * character and the first non-delimiter character after a delimiter will be capitalized.
   *
   * <p>A <code>null</code> input String returns <code>null</code>. Capitalization uses the Unicode
   * title case, normally equivalent to upper case.
   *
   * <pre>
   * WordUtils.capitalize(null, *)            = null
   * WordUtils.capitalize("", *)              = ""
   * WordUtils.capitalize(*, new char[0])     = *
   * WordUtils.capitalize("i am fine", null)  = "I Am Fine"
   * WordUtils.capitalize("i aM.fine", {'.'}) = "I aM.Fine"
   * </pre>
   *
   * @param str the String to capitalize, may be null
   * @param delimiters set of characters to determine capitalization, null means whitespace
   * @return capitalized String, <code>null</code> if null String input
   * @see #uncapitalize(String)
   * @see #capitalizeFully(String)
   * @since 2.1
   */
  private static String capitalize(final String str, final char... delimiters) {
    final int delimLen = delimiters == null ? -1 : delimiters.length;
    if (str == null || str.equals("") || delimLen == 0) {
      return str;
    }
    final char[] buffer = str.toCharArray();
    boolean capitalizeNext = true;
    for (int i = 0; i < buffer.length; i++) {
      final char ch = buffer[i];
      if (isDelimiter(ch, delimiters)) {
        capitalizeNext = true;
      } else if (capitalizeNext) {
        buffer[i] = Character.toTitleCase(ch);
        capitalizeNext = false;
      }
    }
    return new String(buffer);
  }

  /**
   * Is the character a delimiter.
   *
   * @param ch the character to check
   * @param delimiters the delimiters
   * @return true if it is a delimiter
   */
  private static boolean isDelimiter(final char ch, final char[] delimiters) {
    if (delimiters == null) {
      return Character.isWhitespace(ch);
    }
    for (final char delimiter : delimiters) {
      if (ch == delimiter) {
        return true;
      }
    }
    return false;
  }
}
