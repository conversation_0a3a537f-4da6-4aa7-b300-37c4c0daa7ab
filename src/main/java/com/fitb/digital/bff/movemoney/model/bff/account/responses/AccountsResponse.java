/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.bff.account.responses;

import com.fitb.digital.bff.movemoney.model.bff.BffResponse;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class AccountsResponse implements BffResponse {
  private String statusCode;
  private String status;
  private String statusReason;
  private Integer maxExternalTransferAccounts;
  private Integer currentExternalTransferAccounts;
  private Integer memoFieldMaximumLength = 50;
  private List<String> retrievalErrors = new ArrayList<>();
  private List<Account> accounts = new ArrayList<>();
  private List<LocalDate> blackoutDates = new ArrayList<>();
}
