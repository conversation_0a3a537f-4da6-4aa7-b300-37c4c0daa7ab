/* Copyright 2019 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.config;

import com.fasterxml.jackson.core.JsonProcessingException;
import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.ExternalDocumentation;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.*;
import io.swagger.v3.oas.models.servers.Server;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import java.net.MalformedURLException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.utils.SpringDocUtils;
import org.springdoc.webmvc.api.OpenApiWebMvcResource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.actuate.endpoint.annotation.Endpoint;
import org.springframework.boot.actuate.endpoint.annotation.ReadOperation;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.http.HttpMethod;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

/**
 * This configures Springdoc OpenAPI, which generates an OpenAPI contract for us. For the UI, see:
 * https://localhost:8443/swagger-ui/index.html
 *
 * <p>https://localhost:8443/v3/api-docs for the OpenAPI JSON contract,
 *
 * <p>and: /v3/api-docs.yaml for the contract in YAML format
 *
 * <p>This config makes the following assumptions: <br>
 * 1. You are using OAuth2 to secure your services <br>
 * 2. You are using the B2C OAuth2 model for the scope, authorization endpoints
 *
 * <p>You will not be able to fill in the VPC Endpoint URLs ahead of the first deployment, but based
 * on your AWS account and service you can probably fill in the API-GW URLs
 *
 * <p>If you would like to hit your SIT/Production services using local Swagger-UI, you will need to
 * contact the Product Owner for the Auth & Auth squad
 * (https://confluence.info53.com/pages/viewpage.action?pageId=********) and request a new clientId
 * and secret with the following information (only one environment per request):
 *
 * <ul>
 *   <li>Ping Environment (e.g., B2C Ping Non-Prod)
 *   <li>CMDB ID
 *   <li>scope: openid (or whatever scope(s) your client requires)
 *   <li>redirectURIs: https://localhost:8443/swagger-ui/oauth2-redirect.html,
 *       https://oauth.pstmn.io/v1/callback
 * </ul>
 *
 * We typically store the clientID and secret in the appropriate HashiCorp Vault environment (i.e.,
 * non-prod client in non-prod vault, prod client in prod vault) as a new secret
 */
@Slf4j
@Configuration
public class SwaggerConfig {
  // Link to all openid-config:
  // cognito-idp.us-east-2.amazonaws.com/us-east-2_eb2b33PaG/.well-known/openid-configuration
  private static final String COGNITO_TOKEN_URL =
      "https://consumer-mobile-dev.auth.us-east-2.amazoncognito.com/oauth2/token";
  private static final String COGNITO_AUTH_URL =
      "https://consumer-mobile-dev.auth.us-east-2.amazoncognito.com/oauth2/authorize";

  // Link to all openid-config: b2c-sso-dev.auth2-dev.nube.53.com/.well-known/openid-configuration
  private static final String PING_NONPROD_TOKEN_URL =
      "https://b2c-sso-dev.auth2-dev.nube.53.com/as/token.oauth2";
  private static final String PING_NONPROD_AUTH_URL =
      "https://b2c-sso-dev.auth2-dev.nube.53.com/as/authorization.oauth2";

  // Link to all openid-config:
  // b2c-sso-prod.auth-b2c-prod.nube.53.com/.well-known/openid-configuration
  private static final String PING_PROD_TOKEN_URL =
      "https://b2c-sso-prod.auth-b2c-prod.nube.53.com/as/token.oauth2";
  private static final String PING_PROD_AUTH_URL =
      "https://b2c-sso-prod.auth-b2c-prod.nube.53.com/as/authorization.oauth2";
  private static final String SCOPE_OPEN_ID = "openid";

  @Bean
  public SwaggerEndpoint swaggerEndpoint() {
    return new SwaggerEndpoint();
  }

  @Bean
  public OpenAPI apiContract() {
    SpringDocUtils.getConfig().addRequestWrapperToIgnore(JwtAuthenticationToken.class);

    var openAPI =
        new OpenAPI()
            .info(
                new Info()
                    .title("Planet API")
                    .description("Planetary operations")
                    .version("v1.0.0")
                    .contact(
                        new Contact()
                            // TODO: fill in squad and contact info
                            .name("Squad Name Here")
                            .url("https://confluence.info53.com/")
                            .email("<EMAIL>"))
                    .license(new License().name("Apache 2.0").url("http://springdoc.org")))
            .components(
                (new Components())
                    .addSecuritySchemes(
                        "auth_server_DEV",
                        buildOAuth2CodeGrantScheme(COGNITO_TOKEN_URL, COGNITO_AUTH_URL))
                    .addSecuritySchemes(
                        "auth_server_IST",
                        buildOAuth2CodeGrantScheme(PING_NONPROD_TOKEN_URL, PING_NONPROD_AUTH_URL))
                    .addSecuritySchemes(
                        "auth_server_PROD",
                        buildOAuth2CodeGrantScheme(PING_PROD_TOKEN_URL, PING_PROD_AUTH_URL)))
            .externalDocs(
                // TODO: fill in wiki link
                new ExternalDocumentation()
                    .description("Service Wiki Documentation")
                    .url("https://confluence.info53.com/pages/viewpage.action?pageId=25101819"));

    openAPI.setServers(getServers());
    openAPI.setSecurity( // global security requirements - applies to ALL API endpoints
        Arrays.asList(
            new SecurityRequirement().addList("auth_server_DEV", SCOPE_OPEN_ID),
            new SecurityRequirement().addList("auth_server_IST", SCOPE_OPEN_ID),
            new SecurityRequirement().addList("auth_server_PROD", SCOPE_OPEN_ID)));
    return openAPI;
  }

  private io.swagger.v3.oas.models.security.SecurityScheme buildOAuth2CodeGrantScheme(
      String tokenURL, String authURL) {
    return new io.swagger.v3.oas.models.security.SecurityScheme()
        .type(SecurityScheme.Type.OAUTH2)
        .flows(
            new OAuthFlows()
                .authorizationCode(
                    new OAuthFlow()
                        .scopes(new Scopes().addString(SCOPE_OPEN_ID, ""))
                        .authorizationUrl(authURL)
                        .tokenUrl(tokenURL)));
  }

  @Value("${spring.boot.admin.client.url:#{null}}")
  private String springBootAdminUrl;

  @Value("${spring-boot-admin-public-url:#{null}}")
  private String springBootAdminPublicUrl;

  /**
   * Requires http security config to add .cors().and() in order to take effect.
   *
   * @return
   */
  @Bean
  public CorsConfigurationSource corsConfigurationSource() {
    var configuration = new CorsConfiguration();
    configuration.applyPermitDefaultValues();
    List<String> methods =
        Arrays.asList(
            HttpMethod.GET.name(),
            HttpMethod.POST.name(),
            HttpMethod.PUT.name(),
            HttpMethod.OPTIONS.name());
    configuration.setAllowedMethods(methods);
    List<String> origins =
        getServers().stream()
            .map(Server::getUrl)
            .map(this::getOrigin)
            .map(opt -> opt.orElse(null))
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    if (springBootAdminUrl != null) {
      var origin = getOrigin(springBootAdminUrl);
      if (origin.isPresent()) {
        log.debug("Adding spring boot admin url: " + origin);
        origins.add(origin.get());
      }
    }
    if (springBootAdminPublicUrl != null) {
      var origin = getOrigin(springBootAdminPublicUrl);
      if (origin.isPresent()) {
        log.debug("Adding spring boot admin public url: " + origin);
        origins.add(origin.get());
      }
    }
    configuration.setAllowedOrigins(origins);
    var source = new UrlBasedCorsConfigurationSource();
    source.registerCorsConfiguration("/**", configuration);
    return source;
  }

  private Optional<String> getOrigin(String url) {
    try {
      var u = new URL(url);
      var builder = new StringBuilder();
      builder.append(u.getProtocol());
      builder.append("://");
      builder.append(u.getHost());
      if (u.getPort() != -1) {
        builder.append(":");
        builder.append(u.getPort());
      }
      builder.append("/");
      return Optional.of(builder.toString());
    } catch (MalformedURLException e) {
      return Optional.empty();
    }
  }

  private List<Server> getServers() {
    return Arrays.asList(
        local(),
        localSSL(),
        devApiGW(),
        devVpcLink(),
        istApiGW(),
        istVpcLink(),
        prodApiGW(),
        prodVpcLink());
  }

  private Server local() {
    return new Server().url("http://localhost:8080").description("local");
  }

  private Server localSSL() {
    return new Server().url("https://localhost:8443").description("local");
  }

  private Server devApiGW() {
    return new Server()
        // TODO: fill in with your dev URL
        .url("https://planet.internal.planet-services-dev.nube.53.com")
        .description("DEV (API-GW Endpoint, unreachable outside of 5/3 VPN or AWS)");
  }

  private Server devVpcLink() {
    // TODO: fill in with your dev URL
    return new Server()
        .url(
            "https://moz7e6detd-vpce-04e3fad85e181e1ea.execute-api.us-east-2.amazonaws.com/api/planet-service")
        .description("DEV (VPC Endpoint, unreachable outside of AWS)");
  }

  private Server istApiGW() {
    // TODO: fill in with SIT URL
    return new Server()
        .url("https://planet.internal.planet-services-sit.nube.53.com")
        .description("SIT (API-GW Endpoint, unreachable outside of 5/3 VPN or AWS)");
  }

  private Server istVpcLink() {
    // TODO: fill in with SIT URL
    return new Server()
        .url(
            "https://xm5p8137c2-vpce-04e3fad85e181e1ea.execute-api.us-east-2.amazonaws.com/api/planet-service")
        .description("SIT (VPC Endpoint, unreachable outside of AWS)");
  }

  private Server prodVpcLink() {
    // TODO: fill in with prod URL
    return new Server()
        .url(
            "https://1x6wl1whh6-vpce-04e3fad85e181e1ea.execute-api.us-east-2.amazonaws.com/api/planet-service")
        .description("PROD (VPC Endpoint, unreachable outside of AWS)");
  }

  private Server prodApiGW() {
    return new Server()
        // TODO: fill in with prod URL
        .url("https://planet.internal.planet-services.nube.53.com")
        .description("PROD (API-GW Endpoint, unreachable outside of 5/3 VPN or AWS)");
  }

  /** Creates an actuator endpoint to remotely expose the Open API contract for display in SBA. */
  @Endpoint(id = "api-docs")
  public class SwaggerEndpoint {
    @Value("${springdoc.swagger-ui.url}")
    private String springDocUrl;

    @Resource private HttpServletRequest servletRequest;

    @Autowired private OpenApiWebMvcResource openApiWebMvcResource;

    @ReadOperation(produces = "application/json")
    public String loadApiDocs() throws JsonProcessingException {
      return new String(
          openApiWebMvcResource.openapiJson(
              servletRequest, springDocUrl, LocaleContextHolder.getLocale()),
          StandardCharsets.UTF_8);
    }
  }
}
