/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.client.profile.responses;

import com.fitb.digital.bff.movemoney.util.AccountTypeMapper;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class Actor {
  private String id;
  private boolean canAddPayAccount;
  private String displayAccountNumber;
  private List<AvailableSource> availableSources = new ArrayList<>();
  private boolean active;
  private List<AvailableRecipient> availableRecipients = new ArrayList<>();
  private boolean paymentRecipient;
  private boolean canAddPayee;
  private boolean canAddRecurring;
  private String type;
  private boolean verificationRequired;
  private boolean transferSource;
  private boolean electronicBillingEnabled;
  private boolean canAddTransferAccount;
  private String displayName;
  private LocalDate lastPaymentDate;
  private BigDecimal lastPaymentAmount;
  // private String eBillsStatus;
  private Reminder reminder;
  private boolean invoiceEnabled;

  private InvoiceDetails invoiceDetails;

  public boolean isGoal() {
    return AccountTypeMapper.mapTextType(this.getType()) == AccountTypeMapper.AccountType.GOAL;
  }
}
