/* Copyright 2022 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.client.external.transfer.responses;

import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class AuthorizedBrokerage {
  private String name;
  private String routingNumber;
  private String creditRoutingNumber;
  private List<String> fields;
  private List<String> fieldTips;
}
