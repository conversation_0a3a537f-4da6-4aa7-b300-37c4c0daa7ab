/* Copyright 2022 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.service.activity;

import static com.fitb.digital.bff.movemoney.util.CesBffActivityTypes.STATUS_IN_PROCESS;
import static com.fitb.digital.bff.movemoney.util.CesBffActivityTypes.STATUS_SCHEDULED;

import com.fitb.digital.bff.movemoney.model.ActivityBase;
import com.fitb.digital.bff.movemoney.model.bff.activity.BffActivity;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

public class ActivityUtilities {
  protected static void moveCompleteActivitiesToHistory(
      List<BffActivity> activities, List<BffActivity> recurringActivities) {
    List<BffActivity> clonedList = new ArrayList<>(recurringActivities);
    for (BffActivity activity : clonedList) {
      if (ActivityBase.CANCELED_STATUS.equals(activity.getDisplayStatus())
          || ActivityBase.COMPLETED_STATUS.equals(activity.getDisplayStatus())) {
        activities.add(activity);
        recurringActivities.remove(activity);
      }
    }
  }

  protected static List<BffActivity> getRecentList(List<BffActivity> activities) {
    var filteredList =
        activities.stream()
            .filter(
                a ->
                    !(a.getDisplayStatus().equalsIgnoreCase(STATUS_IN_PROCESS)
                        || a.getDisplayStatus().equalsIgnoreCase(STATUS_SCHEDULED)))
            .toList();

    var recentComparator =
        Comparator.comparing(
                BffActivity::getDueDate, Comparator.nullsFirst(Comparator.naturalOrder()))
            .reversed()
            .thenComparing(
                Comparator.comparing(
                        BffActivity::getCreateTimestamp,
                        Comparator.nullsFirst(Comparator.naturalOrder()))
                    .reversed());

    return geSortedList(filteredList, recentComparator);
  }

  protected static List<BffActivity> getUpcomingList(List<BffActivity> activities) {
    var filteredList =
        activities.stream()
            .filter(
                a ->
                    a.getDisplayStatus().equalsIgnoreCase(STATUS_SCHEDULED)
                        || a.getDisplayStatus().equalsIgnoreCase(STATUS_IN_PROCESS))
            .toList();

    Comparator<BffActivity> upcomingCompatator =
        Comparator.comparing(
                BffActivity::getDueDate, Comparator.nullsLast(Comparator.naturalOrder()))
            .thenComparing(
                BffActivity::getCreateTimestamp, Comparator.nullsLast(Comparator.naturalOrder()));

    return geSortedList(filteredList, upcomingCompatator);
  }

  protected static List<BffActivity> geSortedList(
      List<BffActivity> activities, Comparator<BffActivity> comparator) {

    return activities.stream().sorted(comparator).toList();
  }
}
