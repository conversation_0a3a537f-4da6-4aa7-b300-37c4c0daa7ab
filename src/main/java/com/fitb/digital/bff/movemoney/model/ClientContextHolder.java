/* Copyright 2020 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;

/**
 * Associates a given {@link com.fitb.digital.bff.movemoney.model.ClientContext} with the current
 * execution thread and hides the details of working with a {@link ThreadLocal}. It is important for
 * clients to call clearContext() when the thread is finished processing a request (i.e., when the
 * thread-specific data falls out of scope). An example of this would be a servlet filter that sets
 * the context prior to handling the request, then clears it when request handling is complete.
 *
 * <p>Using a {@link ThreadLocal} instead of {@link InheritableThreadLocal} here b/c it has fewer
 * JVM compatibility issues. Note that this means threads spawned from the current thread will NOT
 * inherit the current thread's {@link com.fitb.digital.bff.movemoney.model.ClientContext} value in
 * their thread local storage.
 *
 * <p>TODO Should we support multiple modes (ala, <PERSON><PERSON>'s SecurityContext) for the {@link
 * ThreadLocal} handling (i.e., {@link ThreadLocal}, {@link InheritableThreadLocal}, and global)?
 */
public class ClientContextHolder {

  private static final Logger LOGGER = LoggerFactory.getLogger(ClientContextHolder.class);

  /**
   * The {@link ThreadLocal} used to store the {@link
   * com.fitb.digital.bff.movemoney.model.ClientContext}
   */
  private static final ThreadLocal<com.fitb.digital.bff.movemoney.model.ClientContext> CTX_HOLDER =
      new ThreadLocal<com.fitb.digital.bff.movemoney.model.ClientContext>();

  /** Sets the {@link com.fitb.digital.bff.movemoney.model.ClientContext} on the current thread. */
  public static void setContext(com.fitb.digital.bff.movemoney.model.ClientContext context) {
    Assert.notNull(context, "Only non-null ClientContext instances are permitted");
    if (LOGGER.isDebugEnabled()) {
      LOGGER.debug("Setting ClientContext on current thread: " + context);
    }
    CTX_HOLDER.set(context);
  }

  /**
   * Retrieves the {@link com.fitb.digital.bff.movemoney.model.ClientContext} associated with the
   * current thread.
   *
   * @param createDefault If true, a default ClientContext instance will be created and returned if
   *     there is none currently associated with the current thread.
   * @return {@link com.fitb.digital.bff.movemoney.model.ClientContext} associated with the current
   *     thread, or null if none exists and the createDefault parameter is set to false
   */
  public static com.fitb.digital.bff.movemoney.model.ClientContext getContext(
      boolean createDefault) {
    if (CTX_HOLDER.get() == null && createDefault) {
      LOGGER.warn("No ClientContext associated with executing thread.  Creating default instance.");
      CTX_HOLDER.set(new com.fitb.digital.bff.movemoney.model.ClientContext());
    }
    return CTX_HOLDER.get();
  }

  /** Explicitly clears the context value from the current thread. */
  public static void clearContext() {
    LOGGER.debug("Clearing ClientContext from current thread.");
    CTX_HOLDER.remove();
  }
}
