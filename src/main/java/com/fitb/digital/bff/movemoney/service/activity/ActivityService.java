/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.service.activity;

import com.fitb.digital.bff.movemoney.client.xferandpayorc.XferAndPayOrcClient;
import com.fitb.digital.bff.movemoney.client.xferandpayorc.model.XferAndPayActivity;
import com.fitb.digital.bff.movemoney.client.xferandpayorc.model.XferAndPayActivityRequest;
import com.fitb.digital.bff.movemoney.client.xferandpayorc.model.XferAndPayActivityResponse;
import com.fitb.digital.bff.movemoney.model.bff.BffResponse;
import com.fitb.digital.bff.movemoney.model.bff.BffSimpleResponse;
import com.fitb.digital.bff.movemoney.model.bff.activity.BffActivity;
import com.fitb.digital.bff.movemoney.model.bff.activity.requests.BffActivityRequest;
import com.fitb.digital.bff.movemoney.model.bff.activity.responses.BffAddActivityResponse;
import com.fitb.digital.bff.movemoney.model.bff.activity.responses.BffGetActivityResponse;
import jakarta.annotation.PostConstruct;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@Data
@RequiredArgsConstructor
public class ActivityService {
  private final XferAndPayOrcClient xferAndPayOrcClient;

  private ModelMapper activityMapper;
  private ModelMapper modelMapper;

  @PostConstruct
  void postConstructor() {
    modelMapper = new ModelMapper();
    modelMapper
        .typeMap(XferAndPayActivity.class, BffActivity.class)
        .addMapping(XferAndPayActivity::getActivityType, BffActivity::setActivityType);
    activityMapper = new ModelMapper();
  }

  public BffAddActivityResponse addActivity(BffActivityRequest addActivity) {
    var xferAndPayActivtyRequest = modelMapper.map(addActivity, XferAndPayActivityRequest.class);

    var addActivityResult =
        xferAndPayOrcClient.postTransferAndPayActivity(xferAndPayActivtyRequest);

    // TODO: Test feign client errors and make sure they get bubbled up and handled in
    // ControllerAdvice

    var bffResult = new BffAddActivityResponse();
    bffResult.setStatus(BffResponse.SUCCESS);
    bffResult.setActivity(modelMapper.map(addActivityResult, BffActivity.class));

    return bffResult;
  }

  public BffAddActivityResponse editActivity(BffActivityRequest editActivity) {
    var editActivityRequest = modelMapper.map(editActivity, XferAndPayActivityRequest.class);
    var editActivityResult = xferAndPayOrcClient.editTransferAndPayActivity(editActivityRequest);

    var bffResult = new BffAddActivityResponse();
    bffResult.setStatus(BffResponse.SUCCESS);
    bffResult.setActivity(modelMapper.map(editActivityResult, BffActivity.class));

    return bffResult;
  }

  public BffResponse cancelActivity(String activityId, String activityType) {
    xferAndPayOrcClient.cancelTransferAndPayActivity(activityId, activityType);
    var bffResponse = new BffSimpleResponse();
    bffResponse.setStatus(BffResponse.SUCCESS);
    return bffResponse;
  }

  public BffGetActivityResponse getActivity(Integer recentLimit, Integer upcomingLimit) {
    var bffGetActivityResponse = new BffGetActivityResponse();

    XferAndPayActivityResponse activityResponse = xferAndPayOrcClient.getTransferAndPayActivity();

    activityMapper.map(activityResponse, bffGetActivityResponse);
    limitActivity(recentLimit, upcomingLimit, bffGetActivityResponse);
    return bffGetActivityResponse;
  }

  protected static BffGetActivityResponse limitActivity(
      Integer recentLimit, Integer upcomingLimit, BffGetActivityResponse bffGetActivityResponse) {
    if (recentLimit != null) {
      if (bffGetActivityResponse.getRecentActivities().size() > recentLimit)
        bffGetActivityResponse.setRecentTruncated(true);

      bffGetActivityResponse.setRecentActivities(
          bffGetActivityResponse.getRecentActivities().stream().limit(recentLimit).toList());
    }

    if (upcomingLimit != null) {
      if (bffGetActivityResponse.getUpcomingActivities().size() > upcomingLimit)
        bffGetActivityResponse.setUpcomingTruncated(true);

      bffGetActivityResponse.setUpcomingActivities(
          bffGetActivityResponse.getUpcomingActivities().stream().limit(upcomingLimit).toList());
    }

    return bffGetActivityResponse;
  }
}
