/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.client.configuration;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fitb.digital.bff.movemoney.exceptions.CesFeignException;
import com.fitb.digital.bff.movemoney.model.client.CesBaseResponse;
import com.fitb.digital.bff.movemoney.model.client.CesResponse;
import com.fitb.digital.bff.movemoney.model.client.billpay.responses.CesProfileResponse;
import feign.Response;
import feign.codec.ErrorDecoder;
import java.io.IOException;
import java.nio.charset.Charset;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class CesErrorDecoder implements ErrorDecoder {

  public static final String CES_CLIENT_GET_PROFILE = "CesClient#getProfile()";
  public static final String CES_CLIENT_UPDATE_EXTERN_NICKNAME =
      "CesClient#updateExternalTranfserAccountNickname(CesUpdateExtAccountNickNameRequest)";
  public static final String CES_CLIENT_ADD_PAYEE = "CesClient#addPayee(CesPayeeRequest)";
  public static final String CES_CLIENT_UPDATE_PAYEE = "CesClient#updatePayee(CesPayeeRequest)";
  private final ObjectMapper jsonMapper =
      new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

  @Override
  public Exception decode(String methodKey, Response response) {
    int httpStatus = response.status();
    String responseBodyText = getResponseBodyText(response);
    CesResponse cesResponse = new CesBaseResponse(CesResponse.ERROR, "UNKNOWN_ERROR", methodKey);
    if (HttpStatus.Series.resolve(httpStatus) == HttpStatus.Series.CLIENT_ERROR) {

      try {
        if (responseBodyText.startsWith("{")) {
          cesResponse = jsonMapper.readValue(responseBodyText, CesBaseResponse.class);
        }
        log.debug("Error text: {}", responseBodyText);
      } catch (IOException e) {
        cesResponse = getBaseResponseWithUnmappableBody(responseBodyText);
        log.debug("Returning unmappable body.");
      }
      // The mobile clients return the user to the login screen anytime we return a 401.
      // In this case it should be a 400.
      if (CES_CLIENT_GET_PROFILE.equalsIgnoreCase(methodKey) && httpStatus == 401) {
        httpStatus = 200;
        cesResponse = new CesProfileResponse(cesResponse);
      }
      // Use a more specific status code when we have a duplicate nickname.
      else if (CES_CLIENT_UPDATE_EXTERN_NICKNAME.equalsIgnoreCase(methodKey)
          && cesResponse.getStatus().equals("CASHEDGE_ERROR")
          && cesResponse.getStatusReason().contains("already has been added")) {
        cesResponse.setStatusCode("DUPLICATE_NICKNAME");
      } else if ((CES_CLIENT_ADD_PAYEE.equalsIgnoreCase(methodKey)
              || CES_CLIENT_UPDATE_PAYEE.equalsIgnoreCase(methodKey))
          && httpStatus == HttpStatus.UNPROCESSABLE_ENTITY.value()) {
        cesResponse = getBaseResponseWithUnmappableBody(responseBodyText);
      }

      return new CesFeignException(HttpStatus.valueOf(httpStatus), response.reason(), cesResponse);
    }

    return new CesFeignException(HttpStatus.resolve(httpStatus), responseBodyText, cesResponse);
  }

  private CesBaseResponse getBaseResponseWithUnmappableBody(String bodyText) {
    var cesResponse = new CesBaseResponse();
    if (bodyText != null) {
      cesResponse.setUnmappableBody(bodyText);
    }

    return cesResponse;
  }

  private String getResponseBodyText(Response response) {
    String responseBodyText = null;
    try {
      responseBodyText = IOUtils.toString(response.body().asReader(Charset.defaultCharset()));
    } catch (Exception ignored) {
      responseBodyText = response.toString();
      log.error(
          "Ignored exception reading body for feign error: " + responseBodyText + ".", ignored);
    }
    return responseBodyText;
  }
}
