/* Copyright 2023 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.client.xferandpayorc.model;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Data;

@Data
public class XferAndPayActivity {
  private String id;
  private String displayId;

  private String fromAccountId;
  private String fromAccountNumber;
  private String fromAccountName;
  private String fromGoalParentAccountId;

  private String toAccountId;
  private String toAccountNumber;
  // TODO wire
  private String toAccountType;
  private String toAccountName;
  private String toGoalParentAccountId;

  private BigDecimal amount;
  private boolean expressDelivery;
  private BigDecimal additionalPrincipleAmount;
  private String memo;
  // TODO wire
  LocalDate creationDate;
  private LocalDate deliveryDate;
  // TODO wire
  private LocalDate endDate;

  private LocalDate dueDate;
  private XferAndPayRecurringFrequencyType frequency;
  // TODO wire
  private Integer numberOfActivities;
  // TODO wire
  private Integer numberOfRemainingActivities;
  private String status;
  private String displayStatus;
  // TODO wire
  private String paymentAmountType;
  // TODO wire
  private String payOnType;
  // TODO wire
  private String dayToPay;
  private LocalDateTime createTimestamp;
  private boolean editable;
  private boolean cancelable;
  private String activityType;
  private String checkNumber; // TODO: Is this relevant?
  private String recurringId;

  private String processingStatus;
  private String processingDate;
  private Boolean immediate;
  private boolean scheduled;
  private boolean inProcess;
  private boolean pending;
  private BigDecimal totalTransferAmount;
  private BigDecimal nextTransferAmount;
  private String description;
  private boolean isSeriesTemplate;

  // TODO: MISSING FIELDS
  //  private String toAccountType; //Why do we need this?
}
