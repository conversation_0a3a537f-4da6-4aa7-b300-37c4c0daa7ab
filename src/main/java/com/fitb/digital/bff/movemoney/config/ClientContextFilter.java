/* Copyright 2020 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.config;

import com.fitb.digital.bff.movemoney.model.ClientContext;
import com.fitb.digital.bff.movemoney.model.ClientContextHolder;
import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.lang.NonNull;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMethod;

@Component
@Slf4j
public class ClientContextFilter implements Filter, ApplicationContextAware {
  private ApplicationContext applicationContext;

  @Value("${fitb.digital.bff.movemoney.client-context-filter.disabledPaths}")
  private List<String> disabledPaths;

  @Override
  public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
    this.applicationContext = applicationContext;
  }

  @Override
  public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
      throws IOException, ServletException {
    HttpServletRequest httpRequest = (HttpServletRequest) request;
    /** Skip any paths that do not require the any client context */
    String requestUri = httpRequest.getRequestURI();
    String requestMethod = httpRequest.getMethod();
    //  not every path should have/needs a ClientContext (i.e., requests that are unauthenticated)
    for (String skippedPath : disabledPaths) {
      if (StringUtils.contains(requestUri, skippedPath)) {
        chain.doFilter(request, response);
        return;
      }
    }

    /** Skip OPTIONS calls */
    if (requestMethod.equals(RequestMethod.OPTIONS.name())) {
      chain.doFilter(request, response);
      return;
    }

    ClientContext context = ClientContextHolder.getContext(false);
    if (context == null) {
      context = new ClientContext();
      ClientContextHolder.setContext(context);
    }

    /*
     * Mark the start of this request context
     */
    context.setApplicationContext(applicationContext);
    context.setJwtSubId(getJwtSubjectFromSecurityContext(SecurityContextHolder.getContext()));
    context.setDirectoryId(getDirectoryIdFromToken(SecurityContextHolder.getContext()));
    context.setEcifId(getEcifIdFromToken(SecurityContextHolder.getContext()));
    context.setUsername(
        getMaskedUsername(getUsernameFromToken(SecurityContextHolder.getContext())));
    context.setAffiliate(getAffiliateFromToken(SecurityContextHolder.getContext()));

    context.setSessionExpiration(
        getExpirationFromSecurityContext(SecurityContextHolder.getContext()));

    chain.doFilter(request, response);
  }

  private String getJwtSubjectFromSecurityContext(SecurityContext securityContext) {
    if (securityContext == null
        || securityContext.getAuthentication() == null
        || securityContext.getAuthentication().getPrincipal() == null) {
      return null;
    }
    var jwt = getJwt(securityContext);
    if (jwt != null && jwt.getSubject() != null) {
      return jwt.getSubject();
    }
    logWarning(securityContext, "subject");
    return null;
  }

  private Map<String, Object> getTokenAttributes(SecurityContext securityContext) {
    if (securityContext == null
        || securityContext.getAuthentication() == null
        || securityContext.getAuthentication().getPrincipal() == null) {
      return null;
    }
    var jwt = getJwt(securityContext);
    if (jwt != null && jwt.getSubject() != null) {
      return new JwtAuthenticationToken(jwt).getTokenAttributes();
    }
    logWarning(securityContext, "token attributes");
    return null;
  }

  private String getDirectoryIdFromToken(SecurityContext securityContext) {
    Map<String, Object> tokenData = getTokenAttributes(securityContext);
    if (tokenData != null) {
      return tokenData.get("directoryId") != null ? tokenData.get("directoryId").toString() : null;
    }
    return null;
  }

  private String getEcifIdFromToken(SecurityContext securityContext) {
    Map<String, Object> tokenData = getTokenAttributes(securityContext);
    if (tokenData != null) {
      return tokenData.get("ecifId") != null ? tokenData.get("ecifId").toString() : null;
    }
    return null;
  }

  private String getUsernameFromToken(SecurityContext securityContext) {
    Map<String, Object> tokenData = getTokenAttributes(securityContext);
    if (tokenData != null) {
      return tokenData.get("username") != null ? tokenData.get("username").toString() : null;
    }
    return null;
  }

  private String getMaskedUsername(String userName) {
    if (userName != null) {
      return (userName.length() > 4) ? userName.substring(0, 4) + "****" : userName;
    }
    return null;
  }

  private String getAffiliateFromToken(SecurityContext securityContext) {
    Map<String, Object> tokenData = getTokenAttributes(securityContext);
    if (tokenData != null) {
      return tokenData.get("affiliate") != null ? tokenData.get("affiliate").toString() : null;
    }
    return null;
  }

  private void logWarning(@NonNull SecurityContext securityContext, @NonNull String fieldName) {
    var principal = securityContext.getAuthentication().getPrincipal();
    log.warn(
        "Cannot find attribute"
            + fieldName
            + " for principal type: "
            + principal.getClass().getName());
  }

  private Long getExpirationFromSecurityContext(SecurityContext securityContext) {
    if (securityContext == null
        || securityContext.getAuthentication() == null
        || securityContext.getAuthentication().getPrincipal() == null) {
      return null;
    }
    var jwt = getJwt(securityContext);
    if (jwt != null && jwt.getExpiresAt() != null) {
      return jwt.getExpiresAt().toEpochMilli();
    }
    logWarning(securityContext, "expires");
    return null;
  }

  private Jwt getJwt(@NonNull SecurityContext securityContext) {
    var principal = securityContext.getAuthentication().getPrincipal();
    if (principal instanceof Jwt) {
      return (Jwt) principal;
    }
    return null;
  }
}
