/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.client.configuration;

// TODO do we need this for formatting feign logs
// import ch.qos.logback.access.tomcat.LogbackValve;
import feign.RequestInterceptor;
import feign.Retryer;
import jakarta.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

@Slf4j
@Configuration
public class FeignClientConfiguration {
  @Value("${spring.profiles.active:default}")
  private String activeProfile = "default";

  private static final String X_API_KEY = "X-API-KEY";
  public static final String MOVE_MONEY_BFF = "MoveMoneyBFF";
  private static final ArrayList HEADERS_TO_FORWARD =
      new ArrayList<>(List.of("true-client-ip", "user-agent"));

  //  @Bean
  //  public WebServerFactoryCustomizer<ConfigurableTomcatWebServerFactory> accessLogConfiguration()
  // {
  //    return new WebServerFactoryCustomizer<ConfigurableTomcatWebServerFactory>() {
  //      @Override
  //      public void customize(ConfigurableTomcatWebServerFactory factory) {
  //        var logbackValve = new LogbackValve();
  //        logbackValve.setFilename("logback-access.xml");
  //        factory.addEngineValves(logbackValve);
  //      }
  //    };
  //  }

  @Bean
  public RequestInterceptor requestTokenBearerInterceptor() {
    return requestTemplate -> {
      Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
      if (authentication instanceof JwtAuthenticationToken jwt) {
        requestTemplate.header("Authorization", "Bearer " + jwt.getToken().getTokenValue());
      }
    };
  }

  @Bean
  public Retryer retryer() {
    return new Retryer.Default(1, 1, 1);
  }

  @Bean
  public RequestInterceptor requestHeaderInterceptor() {
    return requestTemplate -> {

      // If there are no request attributes, i.e. no request, just return.
      if (RequestContextHolder.getRequestAttributes() == null) {
        return;
      }

      HttpServletRequest request =
          ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
      boolean encounteredXForwarded = false;
      Enumeration<String> headerNames = request.getHeaderNames();
      while (headerNames.hasMoreElements()) {
        String headerName = headerNames.nextElement();
        String headerValue = request.getHeader(headerName);

        if (shouldForwardHeader(headerName)) {
          if (headerName.toLowerCase().startsWith("x-amzn")) continue;

          if (headerName.equalsIgnoreCase("x-forwarded-for")) {
            encounteredXForwarded = true;
            headerValue += ", " + request.getLocalAddr();
          }

          requestTemplate.header(headerName, headerValue);
        }
      }

      // Add our API key name if it is not present.
      if (request.getHeader(X_API_KEY) == null) {
        requestTemplate.header(X_API_KEY, MOVE_MONEY_BFF);
      }

      // Add our IP if it is not present in the forward.
      if (!encounteredXForwarded && !activeProfile.contains("local-sit")) {
        // Create one and add this servers ip.
        requestTemplate.header(
            "X-Forwarded-For", request.getRemoteAddr() + ", " + request.getLocalAddr());
      }
    };
  }

  private boolean shouldForwardHeader(String headerName) {
    var lowerCaseHeaderName = headerName.toLowerCase();
    return (lowerCaseHeaderName.startsWith("x-")
        || HEADERS_TO_FORWARD.contains(lowerCaseHeaderName));
  }
}
