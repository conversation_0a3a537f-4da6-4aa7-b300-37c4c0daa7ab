/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.client;

import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class CesBaseResponse implements CesResponse {
  private String status;
  private String statusCode;
  private String statusReason;
  private List<String> retrievalErrors = new ArrayList<>();
  private String unmappableBody;

  public CesBaseResponse(String status, String statusCode, String statusReason) {
    this.status = status;
    this.statusCode = statusCode;
    this.statusReason = statusReason;
  }
}
