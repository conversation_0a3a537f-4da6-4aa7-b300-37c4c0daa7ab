/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.client.account.responses;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class CesGoal {
  private String id;
  private String accountId;
  private String displayAccountNumber;
  private String name;
  private String category;
  private String imageLocation;
  private BigDecimal targetAmount;
  private BigDecimal progressAmount;

  @JsonFormat(pattern = "yyyy-MM-dd")
  private LocalDate targetDate;

  private LocalDate created;
  private LocalDateTime updated;
  private Boolean editable;
  private Boolean generalSavings;
}
