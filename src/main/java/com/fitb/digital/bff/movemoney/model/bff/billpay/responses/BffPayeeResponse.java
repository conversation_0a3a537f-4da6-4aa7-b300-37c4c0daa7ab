/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.bff.billpay.responses;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fitb.digital.bff.movemoney.model.bff.BffSimpleResponse;
import com.fitb.digital.bff.movemoney.model.bff.billpay.BffPayee;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.http.HttpStatusCode;

@Data
@NoArgsConstructor
public class BffPayeeResponse extends BffSimpleResponse {
  private BffPayee bffPayee;

  @JsonIgnore private HttpStatusCode cesHttpStatus;
}
