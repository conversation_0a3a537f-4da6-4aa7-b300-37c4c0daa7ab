/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.bff;

import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class BffSimpleResponse implements BffResponse {
  private String status;
  private String statusCode;
  private String statusReason;
  private List<String> retrievalErrors = new ArrayList<>();

  public BffSimpleResponse status(String status) {
    this.status = status;
    return this;
  }

  public BffSimpleResponse statusCode(String statusCode) {
    this.statusCode = statusCode;
    return this;
  }

  public BffSimpleResponse statusReason(String statusReason) {
    this.statusReason = statusReason;
    return this;
  }
}
