/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.bff.billpay;

import com.fitb.digital.bff.movemoney.model.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class BffPayee implements Serializable {
  private String accountNumber; // parital, last four digits.
  private String id;
  private String name;
  private String cesAccountType;

  private String phoneNumber;
  private PayeeAddress address;

  private String nickname;
  private LocalTime paymentCutoffTime;
  private LocalDate nearestNewPaymentDate;
  private String categoryTypeId;
  private Boolean electronicBillingEnabled;

  private FISPayeeType fisPayeeType;
  private CesPayeeType cesPayeeType;
  private Payment recurringPayment;
  private BillingReminder eBillReminder;
  private BillingRule eBillRule;
  private BigDecimal lastPaymentAmount;
  private LocalDate lastPaymentDate;
}
