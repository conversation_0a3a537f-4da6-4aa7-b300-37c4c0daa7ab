/* Copyright 2025 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.bff.CoreTransfers.responses;

import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class TDSCoreTransferActivityResponse {
  private List<TDSCoreTransferActivity> transferActivities = new ArrayList<>();
  private ErrorResponse error;

  @Data
  @NoArgsConstructor
  public static class ErrorResponse {
    private String code;
    private String message;
    private String target;
  }
}
