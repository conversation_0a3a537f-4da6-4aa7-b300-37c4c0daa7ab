/* Copyright 2022 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.client.external.transfer;

import static com.fitb.digital.lib.tokenization.SensitiveDataType.CRIT;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fitb.digital.lib.tokenization.SensitiveData;
import com.fitb.digital.lib.tokenization.model.KeyMetadata;
import com.fitb.digital.lib.tokenization.model.Tokenizable;
import java.util.Map;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class CesAuthenticationParam implements Tokenizable {
  private String authId;

  @SensitiveData(CRIT)
  private String authValue;

  @JsonProperty("TOKENIZED_FIELDS")
  protected Map<String, KeyMetadata> keyMetadata;

  public CesAuthenticationParam(String name, String value) {
    this.authId = name;
    this.authValue = value;
  }
}
