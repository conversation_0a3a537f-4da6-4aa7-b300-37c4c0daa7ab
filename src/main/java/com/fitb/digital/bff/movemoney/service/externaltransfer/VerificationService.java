/* Copyright 2022 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.service.externaltransfer;

import static com.fitb.digital.bff.movemoney.constants.TokenizationProfiles.CES_PROFILE;
import static com.fitb.digital.bff.movemoney.util.StatusMapper.mapCesToBffResponse;

import com.fitb.digital.bff.movemoney.client.CesClient;
import com.fitb.digital.bff.movemoney.exceptions.CesFeignException;
import com.fitb.digital.bff.movemoney.model.bff.BffSimpleResponse;
import com.fitb.digital.bff.movemoney.model.bff.external.transfer.requests.BffRealTimeVerificationRequest;
import com.fitb.digital.bff.movemoney.model.bff.external.transfer.requests.BffRealTimeVerifyStatus;
import com.fitb.digital.bff.movemoney.model.bff.external.transfer.requests.BffVerifyTrialDepositsRequest;
import com.fitb.digital.bff.movemoney.model.bff.external.transfer.responses.*;
import com.fitb.digital.bff.movemoney.model.client.external.transfer.CesAuthenticationParam;
import com.fitb.digital.bff.movemoney.model.client.external.transfer.requests.CesAddExternalAccountRealTimeRequest;
import com.fitb.digital.bff.movemoney.model.client.external.transfer.requests.CesAddExternalAccountRequest;
import com.fitb.digital.bff.movemoney.model.client.external.transfer.requests.CesVerifyTrialDepositsRequest;
import com.fitb.digital.bff.movemoney.model.client.external.transfer.responses.*;
import com.fitb.digital.bff.movemoney.service.CesClientService;
import com.fitb.digital.lib.tokenization.model.KeyMetadata;
import com.fitb.digital.lib.tokenization.tokenizer.Tokenizer;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.modelmapper.ModelMapper;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class VerificationService extends CesClientService {
  private final String FIID_SEED_DATA_ERROR = "FIID_NOT_FOUND";

  private final Tokenizer tokenizer;
  private final ExternalTransferService externalTransferService;

  private final ModelMapper cesToBffRealTimeMapper = new ModelMapper();
  private final ModelMapper cesToBffAccountResponse = new ModelMapper();

  public VerificationService(
      CesClient cesClient, ExternalTransferService externalTransferService, Tokenizer tokenizer) {
    super(cesClient);
    this.externalTransferService = externalTransferService;
    this.tokenizer = tokenizer;
  }

  public BffRealTimeVerificationResponse verifyExternalAccountRealTime(
      BffRealTimeVerificationRequest request) {
    ModelMapper mapper = new ModelMapper();
    CesAddExternalAccountRealTimeRequest cesRequest =
        mapper.map(request, CesAddExternalAccountRealTimeRequest.class);
    cesRequest.setCesAuthenticationParams(
        request.getVerificationParameters().stream()
            .map(v -> new CesAuthenticationParam(v.getName(), v.getValue()))
            .collect(Collectors.toList()));
    var cesResponseEntity = cesAddExternalAccountRealTime(cesRequest);
    return mapCesAddAccountRealTimeResponse(cesResponseEntity);
  }

  public BffSimpleResponse verifyTrialDeposits(
      BffVerifyTrialDepositsRequest verifyTrialDepositsRequest) {
    CesVerifyTrialDepositsRequest cesRequest =
        getCesVerifyTrialDepositsRequest(verifyTrialDepositsRequest);

    var cesResponse = cesClient.verifyTrialDepositAmounts(cesRequest);

    return (BffSimpleResponse) mapCesToBffResponse(cesResponse, new BffSimpleResponse());
  }

  public BffAddAccountResponse initiateTrialDeposits(
      String accountId, String brokerageName, String routingNumber) {
    var cesRequest = new CesAddExternalAccountRequest();
    cesRequest.setAccountId(accountId);
    // Setting these two 'sensitive' properties to empty strings is required for tokenization to
    // work properly
    // The BFF is required to call the tokenized endpoint on CES even though we only pass
    // non-sensitive information
    cesRequest.setAccountNumber("");
    cesRequest.setCreditAccountNumber("");
    var cesTokenizedRequest = getTokenizedInitiateTrialDepositRequest(cesRequest);
    cesRequest.setAccountNumber(null);
    cesRequest.setCreditAccountNumber(null);
    cesRequest.setKeyMetadata(new HashMap<String, KeyMetadata>());

    CesAddExternalAccountResponse cesResponseEntity;
    try {
      cesResponseEntity = cesAddExternalAccountByTrialDeposit(cesTokenizedRequest);
    } catch (CesFeignException ex) {
      // look for specific error from CES that tells us we need to 'seed' the session data
      if (ex.getStatusCode() == HttpStatus.NOT_FOUND
          && ex.getCesResponse().getStatusCode().equals(FIID_SEED_DATA_ERROR)) {
        // Based on whether this is a brokerage or a typical account
        if (brokerageName != null) {
          externalTransferService.getFinancialInstitutionInfoForBrokerageName(
              brokerageName); // Result does not matter, just seeding data
        } else {
          externalTransferService.getFinancialInstitutionInfoForRoutingNumber(
              routingNumber); // Result does not matter, just seeding data
        }

        cesResponseEntity = cesAddExternalAccountByTrialDeposit(cesTokenizedRequest);
      } else {
        throw ex; // rethrow because we didn't handle it.
      }
    }

    return mapCesToBffAddAccountResponse(cesResponseEntity);
  }

  private CesAddExternalAccountRequest getTokenizedInitiateTrialDepositRequest(
      CesAddExternalAccountRequest cesRequest) {
    return tokenizer.tokenize(CES_PROFILE, cesRequest);
  }

  CesAddExternalAccountRealTimeResponse cesAddExternalAccountRealTime(
      CesAddExternalAccountRealTimeRequest request) {
    return super.callClient(() -> cesClient.addExternalAccountByRealTime(request));
  }

  BffAddAccountResponse mapCesToBffAddAccountResponse(CesAddExternalAccountResponse cesResponse) {
    BffAddAccountResponse bffResponse = new BffAddAccountResponse();
    mapCesToBffResponse(cesResponse, bffResponse);
    bffResponse.setExternalAccountVerificationStatus(
        cesToBffAccountResponse.map(
            cesResponse.getCesExternalAccountVerificationStatus(),
            ExternalAccountVerificationStatus.class));
    return bffResponse;
  }

  CesAddExternalAccountResponse cesAddExternalAccountByTrialDeposit(
      CesAddExternalAccountRequest request) {
    return super.callClient(() -> cesClient.addExternalAccountByTrialDeposit(request));
  }

  @NotNull
  CesVerifyTrialDepositsRequest getCesVerifyTrialDepositsRequest(
      BffVerifyTrialDepositsRequest verifyTrialDepositsRequest) {
    CesVerifyTrialDepositsRequest cesRequest = new CesVerifyTrialDepositsRequest();
    cesRequest.setAccountId(verifyTrialDepositsRequest.getAccountId());

    var depositAmounts =
        Arrays.asList(
            verifyTrialDepositsRequest.getAmountOne(), verifyTrialDepositsRequest.getAmountTwo());
    cesRequest.setAmounts(depositAmounts);
    return cesRequest;
  }

  @NotNull
  private BffRealTimeVerificationResponse mapCesAddAccountRealTimeResponse(
      CesAddExternalAccountRealTimeResponse cesResponse) {
    BffRealTimeVerificationResponse bffResponse = new BffRealTimeVerificationResponse();
    mapCesToBffResponse(cesResponse, bffResponse);
    bffResponse.setVerificationStatus(
        cesToBffRealTimeMapper.map(
            cesResponse.getCesRealTimeAccountVerificationStatus(), BffRealTimeVerifyStatus.class));
    bffResponse.setParameters(
        mapCesAuthenticationParams(
            cesResponse
                .getCesRealTimeAccountVerificationStatus()
                .getTwoFactorAccountVerificationParam()));
    return bffResponse;
  }

  @NotNull
  private List<AuthenticationParam> mapCesAuthenticationParams(
      List<CesAuthenticationParam> twoFactorAccountVerificationParam) {
    return twoFactorAccountVerificationParam.stream()
        .map(ces -> new AuthenticationParam(ces.getAuthId(), ces.getAuthValue()))
        .collect(Collectors.toList());
  }
}
