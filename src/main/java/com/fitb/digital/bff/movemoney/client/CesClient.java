/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.client;

import com.fitb.digital.bff.movemoney.client.configuration.CesFeignClientConfiguration;
import com.fitb.digital.bff.movemoney.model.client.CesBaseResponse;
import com.fitb.digital.bff.movemoney.model.client.account.responses.ClientAccountDetailResponse;
import com.fitb.digital.bff.movemoney.model.client.account.responses.ListResponse;
import com.fitb.digital.bff.movemoney.model.client.activity.requests.ClientActivityRequest;
import com.fitb.digital.bff.movemoney.model.client.activity.responses.ClientActivityResponse;
import com.fitb.digital.bff.movemoney.model.client.activity.responses.TransferLimitsResponse;
import com.fitb.digital.bff.movemoney.model.client.billpay.requests.CesPayeeRequest;
import com.fitb.digital.bff.movemoney.model.client.billpay.responses.*;
import com.fitb.digital.bff.movemoney.model.client.cashadvance.requests.TransferRequest;
import com.fitb.digital.bff.movemoney.model.client.cashadvance.responses.CesCashAdvanceResponse;
import com.fitb.digital.bff.movemoney.model.client.external.transfer.requests.*;
import com.fitb.digital.bff.movemoney.model.client.external.transfer.responses.*;
import com.fitb.digital.bff.movemoney.model.client.invoicedpayment.responses.CesInvoicedPaymentAccountResponse;
import com.fitb.digital.bff.movemoney.model.client.profile.responses.ProfileResponse;
import com.fitb.digital.bff.movemoney.model.client.webpayment.responses.CesWebpaymentProfileResponse;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

@RefreshScope
@FeignClient(
    value = CesClient.CLIENT_NAME,
    configuration = CesFeignClientConfiguration.class,
    url = "${feign.client.config.ces-proxy.url}")
@SuppressWarnings("java:S1214")
public interface CesClient {
  String CLIENT_NAME = "Ces";

  @RequestMapping(method = RequestMethod.GET, value = "/services/account/list")
  ListResponse getAccountsList(@RequestParam Boolean useCachedAccounts);

  @RequestMapping(method = RequestMethod.GET, value = "/services/account/detail")
  ClientAccountDetailResponse getAccountDetail(@RequestParam String id);

  @GetMapping(value = "/services/billpay/profile")
  CesProfileResponse getProfile();

  @GetMapping(value = "/services/billpay/globalPayees")
  ClientGlobalPayeeResponse getGlobalPayees();

  @GetMapping(value = "/tokenized/services/billpay/payee/account")
  CesPayeeAccountResponse getTokenizedPayeeAccount(@RequestParam String payeeId);

  @PostMapping(value = "/tokenized/services/billpay/addPayee")
  CesPayeeResponse addPayee(CesPayeeRequest addPayeeRequest);

  @PutMapping(value = "/tokenized/services/billpay/updatePayee")
  CesPayeeResponse updatePayee(CesPayeeRequest addPayeeRequest);

  @DeleteMapping(value = "/services/billpay/payee")
  CesDeletePayeeResponse deletePayee(@RequestParam String payeeId);

  @GetMapping(
      value = "/services/transferandpay/profile") // profile-movemoney to test delivery options.
  ProfileResponse getTransferAndPayProfile(@RequestParam Boolean refresh);

  @PostMapping(value = "/services/transferandpay/activity")
  ClientActivityResponse postTransferAndPayActivity(
      @RequestBody ClientActivityRequest clientActivityRequest);

  @DeleteMapping(value = "/services/transferandpay/activity")
  String cancelTransferAndPayActivity(@RequestParam String activityId);

  @PutMapping(value = "/services/transferandpay/activity")
  ClientActivityResponse editTransferAndPayActivity(
      @RequestBody ClientActivityRequest clientActivityRequest);

  @GetMapping(value = "/services/transferandpay/activity")
  ClientActivityResponse getTransferAndPayActivity();

  @GetMapping(value = "/services/externaltransfer/limits")
  TransferLimitsResponse getTransferLimits(
      @RequestParam String fromAcctId, @RequestParam String toAcctId);

  @GetMapping(value = "/services/transferandpay/activity")
  ClientActivityResponse getTransferAndPayActivity(
      @RequestParam String accountId, @RequestParam String dateRange);

  @PostMapping(value = "/services/cashAdvance")
  CesCashAdvanceResponse postCashAdvanceActivity(@RequestBody TransferRequest transferRequest);

  @GetMapping(value = "/services/externaltransfer/brokerages")
  AuthorizedBrokerageListResponse getBrokerages();

  @GetMapping(value = "/services/externaltransfer/fiInfo")
  CesFinancialInstitutionInfoResponse getFinancialInstitutionInfo(
      @RequestParam String routingNumber);

  @GetMapping(value = "/services/externaltransfer/fiBrokerageRoutingInfo")
  CesFinancialInstitutionInfoResponse getBrokerageInfo(@RequestParam String routingName);

  @PostMapping(value = "/tokenized/services/externaltransfer/addExternalAccountByInstant")
  CesAddExternalAccountByInstantResponse addExternalAccountByInstant(
      @RequestBody CesAddExternalAccountByInstantRequest request);

  @PostMapping(value = "/tokenized/services/externaltransfer/addExternalAccountByRealTime")
  CesAddExternalAccountRealTimeResponse addExternalAccountByRealTime(
      @RequestBody CesAddExternalAccountRealTimeRequest request);

  @PostMapping(value = "/tokenized/services/externaltransfer/addExternalAccountByTrialDeposit")
  CesAddExternalAccountResponse addExternalAccountByTrialDeposit(
      CesAddExternalAccountRequest cesAddExternalAccountRequest);

  @PostMapping(value = "/services/externaltransfer/verifyTrialDepositAmounts")
  CesBaseResponse verifyTrialDepositAmounts(@RequestBody CesVerifyTrialDepositsRequest request);

  @PostMapping(value = "/services/externaltransfer/updateNickName")
  CesBaseResponse updateExternalTranfserAccountNickname(
      @RequestBody CesUpdateExtAccountNickNameRequest request);

  @GetMapping(value = "/services/webpayment/profile")
  CesWebpaymentProfileResponse getWebPaymentProfile();

  @DeleteMapping(value = "/services/externaltransfer/deleteExternalAccount")
  CesBaseResponse deleteExternalAccount(@RequestParam String accountId);

  @GetMapping(value = "/services/invoicedPayment/paymentAccount")
  CesInvoicedPaymentAccountResponse getFundingAccount();
}
