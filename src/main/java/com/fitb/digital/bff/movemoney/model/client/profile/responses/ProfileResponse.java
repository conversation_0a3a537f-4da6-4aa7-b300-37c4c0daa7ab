/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.client.profile.responses;

import com.fitb.digital.bff.movemoney.model.client.CesBaseResponse;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class ProfileResponse extends CesBaseResponse {
  private List<String> retrievalErrors = new ArrayList<>();
  private List<Actor> actors = new ArrayList<>();
}
