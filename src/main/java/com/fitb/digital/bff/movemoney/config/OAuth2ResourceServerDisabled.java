/* Copyright 2019 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.config;

import com.fitb.digital.lib.oauth.FitbOAuth2ResourceServerDisabled;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;

/**
 * If the resource server config is not present (local and test profiles), then allow any user to
 * hit the actuator and swagger endpoints.
 *
 * <p>For customer endpoints: scopes can be specified with a header named "scope", delimited by a
 * space (to match a jwt token). username by username, sub by sub, iss by iss, groups can be
 * specified with a header named "group", which can be a single string, or an array of strings, to
 * match the claims we get from b2e. I.e. the header becomes the claim to match what a JWT would
 * have.
 */
// @ConditionalOnMissingBean(OAuth2ResourceServer.OAuth2ResourceServerProperties.class)
@Profile({"local", "unit-test"})
@SuppressWarnings("java:S1192") // String constants for access vals
@Configuration
@EnableGlobalMethodSecurity(prePostEnabled = true)
public class OAuth2ResourceServerDisabled extends FitbOAuth2ResourceServerDisabled {}
