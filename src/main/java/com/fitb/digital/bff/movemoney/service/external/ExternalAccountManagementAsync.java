/* Copyright 2022 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.service.external;

import com.fitb.digital.bff.movemoney.client.CesClient;
import com.fitb.digital.bff.movemoney.client.CesClientShortTimeout;
import com.fitb.digital.bff.movemoney.model.bff.external.transfer.responses.BffExternalAccount;
import com.fitb.digital.bff.movemoney.model.client.invoicedpayment.responses.CesInvoicedPaymentAccountResponse;
import com.fitb.digital.bff.movemoney.model.client.webpayment.responses.CesWebpaymentProfileResponse;
import com.fitb.digital.bff.movemoney.service.CesClientService;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.Converter;
import org.modelmapper.ModelMapper;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * @Async methods separated from the service because they cannot be self-referenced in the service
 * class or the proxy is bypassed.
 */
@Component
@Slf4j
public class ExternalAccountManagementAsync extends CesClientService {

  private final CesClientShortTimeout cesClientShortTimeout;
  private final ModelMapper mapper;

  public ExternalAccountManagementAsync(
      final CesClient cesClient, final CesClientShortTimeout cesClientShortTimeout) {
    super(cesClient);
    this.cesClientShortTimeout = cesClientShortTimeout;
    this.mapper = new ModelMapper();
    this.mapper.addConverter(getStringToBigDecimalConverter());
  }

  @Async
  public CompletableFuture<List<BffExternalAccount>> getExternalTransferAccounts() {
    return CompletableFuture.completedFuture(getExternalTransferAccountsInternal());
  }

  private List<BffExternalAccount> getExternalTransferAccountsInternal() {
    var response = super.callClient(cesClientShortTimeout::getExternalTransferUserAccounts);

    if (response.getAccountDataList() == null) {
      return Collections.emptyList();
    } else {
      return response.getAccountDataList().stream()
          .filter(
              acct ->
                  !acct.getIsHostAccount()
                      .equals("1")) // Host accounts are internal to 5/3, do not display to user
          .map(
              account -> {
                // TODO replace with simple map once other verification methods are supported.
                var bff = mapper.map(account, BffExternalAccount.class);
                bff.patchTrialDepositStatus();
                return bff;
              })
          .collect(Collectors.toList());
    }
  }

  private Converter<String, BigDecimal> getStringToBigDecimalConverter() {
    return context -> new BigDecimal(context.getSource().replace(",", ""));
  }

  @Async
  public CompletableFuture<CesWebpaymentProfileResponse> getWebPaymentProfile() {
    return CompletableFuture.completedFuture(super.callClient(cesClient::getWebPaymentProfile));
  }

  @Async
  public CompletableFuture<CesInvoicedPaymentAccountResponse> getInvoicedPaymentProfile() {
    return CompletableFuture.completedFuture(super.callClient(cesClient::getFundingAccount));
  }
}
