/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.local;

import java.time.LocalDate;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class TransferDetails {
  public TransferDetails(LocalDate earliestAvailableDate, LocalDate latestAvailableDate) {
    this.earliestAvailableDate = earliestAvailableDate;
    this.latestAvailableDate = latestAvailableDate;
  }

  private LocalDate earliestAvailableDate;
  private LocalDate latestAvailableDate;
}
