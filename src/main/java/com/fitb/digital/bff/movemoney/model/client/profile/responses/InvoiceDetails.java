/* Copyright 2022 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.client.profile.responses;

import java.math.BigDecimal;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class InvoiceDetails {

  private BigDecimal totalAccountBalancePayment;
  private BigDecimal minimumPaymentAmount;
  private BigDecimal amountDuePayment;
  private BigDecimal otherAmountPayment;
  private String invoiceId;
}
