/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.client.profile.responses;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class AvailableRecipient {
  private double minimumAmount;
  private long maximumAmount;
  private String id;
  private LocalDate latestAvailableDate;
  private List<String> additionalAmountFields = new ArrayList<>();
  private List<ClientDeliverySpeedOption> deliverySpeedOptions = new ArrayList<>();
  private String activityType;
  private boolean recurringSupported;
  private LocalDate earliestAvailableDate;
  private LocalDate earliestAvailableDateRecurring;
  private boolean memoSupported;
}
