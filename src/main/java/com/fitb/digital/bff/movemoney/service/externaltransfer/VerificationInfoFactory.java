/* Copyright 2022 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.service.externaltransfer;

import com.fitb.digital.bff.movemoney.model.bff.external.transfer.responses.BffExternalAccount;
import com.fitb.digital.bff.movemoney.model.bff.external.transfer.responses.BffExternalTransferAccountVerificationInfo;
import com.fitb.digital.bff.movemoney.model.bff.external.transfer.responses.BffFinancialInstitutionInfo;
import org.modelmapper.ModelMapper;
import org.modelmapper.convention.MatchingStrategies;

public class VerificationInfoFactory {
  public BffExternalTransferAccountVerificationInfo create(
      BffFinancialInstitutionInfo financialInstitutionInfo, BffExternalAccount externalAccount) {
    var mapper = new ModelMapper();
    mapper.getConfiguration().setMatchingStrategy(MatchingStrategies.STRICT);
    var etaAccountVerificationInfo =
        mapper.map(externalAccount, BffExternalTransferAccountVerificationInfo.class);

    etaAccountVerificationInfo.setFinInsLoginInfoList(
        financialInstitutionInfo.getFinInsLoginInfoList());
    etaAccountVerificationInfo.setInstantVerificationEnabledAcctTypes(
        financialInstitutionInfo.getInstantVerificationEnabledAcctTypes());
    etaAccountVerificationInfo.setHostInstitution(financialInstitutionInfo.isHostInstitution());
    etaAccountVerificationInfo.setTrialDepositVerification(
        financialInstitutionInfo.isTrialDepositVerification());
    // N.B. Forcing RT verification to false.
    etaAccountVerificationInfo.setRealTimeVerification(false);
    etaAccountVerificationInfo.setStatus(financialInstitutionInfo.getStatus());
    return etaAccountVerificationInfo;
  }
}
