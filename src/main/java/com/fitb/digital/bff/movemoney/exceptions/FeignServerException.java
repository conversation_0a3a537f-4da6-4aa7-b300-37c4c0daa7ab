/* Copyright 2024 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.exceptions;

import com.fitb.digital.lib.response.Error;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;
import org.springframework.http.HttpStatus;

@Getter
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class FeignServerException extends FeignException {

  @Builder
  public FeignServerException(final HttpStatus httpStatus, final Error error) {
    super(httpStatus, error);
  }
}
