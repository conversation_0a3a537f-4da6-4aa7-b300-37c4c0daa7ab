/* Copyright 2022 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.controller.externaltransfer;

import com.fitb.digital.bff.movemoney.exceptions.BffException;
import com.fitb.digital.bff.movemoney.model.bff.external.transfer.requests.BffAddAccountRequest;
import java.util.regex.Pattern;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Component
public class ExternalTransferInputValidator {
  private static final String ROUTING_NUMBER_REGEX = "^[0-9]{9}$";
  private static final String ACCOUNT_NUMBER_REGEX = "^[0-9]{5,17}$";
  private static final Pattern ROUTING_NUMBER_PATTERN = Pattern.compile(ROUTING_NUMBER_REGEX);
  private static final Pattern ACCOUNT_NUMBER_PATTERN = Pattern.compile(ACCOUNT_NUMBER_REGEX);

  static final String ROUTING_NUMBER_INVALID_MESSAGE = "Routing numbers must be 9 digits";
  static final String ACCOUNT_NUMBER_INVALID_MESSAGE =
      "Account Numbers must be between 5 and 17 digits";
  static final String BOTH_BLANK_MESSAGE =
      "Must supply either a routingNumber or brokerageName query string parameter.";
  static final String BOTH_NOT_BLANK_MESSAGE =
      "Must supply only a routingNumber or brokerageName query string parameter, not both.";

  public void validateGetBankInfoInput(final String routingNumber, final String brokerageName) {
    if (StringUtils.isAllBlank(routingNumber, brokerageName)) {
      throw BffException.badRequest(BOTH_BLANK_MESSAGE);
    }

    if (StringUtils.isNoneBlank(routingNumber, brokerageName)) {
      throw BffException.badRequest(BOTH_NOT_BLANK_MESSAGE);
    }

    if (StringUtils.isNotBlank(routingNumber)
        && !ROUTING_NUMBER_PATTERN.matcher(routingNumber).matches()) {
      throw BffException.badRequest(ROUTING_NUMBER_INVALID_MESSAGE);
    }
  }

  public void validateAddAccountInput(final BffAddAccountRequest request) {
    if (!ROUTING_NUMBER_PATTERN.matcher(request.getRoutingNumber()).matches()) {
      throw BffException.badRequest(ROUTING_NUMBER_INVALID_MESSAGE);
    }

    if (request.getCreditRoutingNumber() != null
        && !ROUTING_NUMBER_PATTERN.matcher(request.getCreditRoutingNumber()).matches()) {
      throw BffException.badRequest(ROUTING_NUMBER_INVALID_MESSAGE);
    }

    if (!ACCOUNT_NUMBER_PATTERN.matcher(request.getAccountNumber()).matches()) {
      throw BffException.badRequest(ACCOUNT_NUMBER_INVALID_MESSAGE);
    }

    if (request.getCreditAccountNumber() != null
        && !ACCOUNT_NUMBER_PATTERN.matcher(request.getCreditAccountNumber()).matches()) {
      throw BffException.badRequest(ACCOUNT_NUMBER_INVALID_MESSAGE);
    }
  }
}
