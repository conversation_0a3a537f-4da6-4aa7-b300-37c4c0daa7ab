/* Copyright 2022 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.client.external.transfer.requests;

import static com.fitb.digital.lib.tokenization.SensitiveDataType.CRIT;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fitb.digital.lib.tokenization.SensitiveData;
import com.fitb.digital.lib.tokenization.model.KeyMetadata;
import com.fitb.digital.lib.tokenization.model.Tokenizable;
import java.util.Map;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@SuppressWarnings("java:S1948") // Tokenizable extends Serializable, not today SonarQube.
public class CesAddExternalAccountRequest implements Tokenizable {
  private String requestGuid;
  private String accountId;

  @SensitiveData(CRIT)
  private String accountNumber;

  @SensitiveData(CRIT)
  private String creditAccountNumber;

  private String routingNumber;
  private String creditRoutingNumber;
  private String accountTypeCode;
  private String accountNickName;

  @JsonProperty("TOKENIZED_FIELDS")
  protected Map<String, KeyMetadata> keyMetadata;
}
