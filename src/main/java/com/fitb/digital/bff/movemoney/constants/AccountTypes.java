/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.constants;

import java.util.ArrayList;
import java.util.List;

@SuppressWarnings("java:S3008")
public class AccountTypes {
  private AccountTypes() {
    throw new IllegalStateException("Utility Class");
  }

  public static final List<String> DEPOSITS_INVESTMENTS_TYPES =
      new ArrayList<>(List.of("DDA", "SAV", "TRA", "CDS", "BRK", "SBA"));
  public static final List<String> PREPAID_CARDS_TYPES =
      new ArrayList<>(List.of("GPR", "GPRC", "TPP"));

  public static final List<String> ACCESS_360_ACCT_TYPES = new ArrayList<>(List.of("GPR", "GPRC"));
}
