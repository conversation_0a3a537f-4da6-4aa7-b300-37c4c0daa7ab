/* Copyright 2020 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.exceptions;

import com.fitb.digital.bff.movemoney.constants.ExceptionStatus;
import com.fitb.digital.bff.movemoney.model.bff.BffResponse;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;
import org.springframework.http.HttpStatus;

/**
 * This class exists to support the legacy way of passing custom errors in status, statusCode and
 * statusReason as well as the new error field. This in combination with the feign configuration
 * wraps non 200's in this exceptions so that they are not counted as circuit breaking or retry
 * exceptions.
 */
// java:S110 - I'm extending framework code, I'm not worried about ancestors.
// java:S1068 - False positive
@SuppressWarnings({"java:S110", "java:S1068"})
@Builder
@Getter
@EqualsAndHashCode(callSuper = false)
@ToString
public class BffException extends RuntimeException {
  private final String status;
  private final String statusCode;
  private final String statusReason;
  private final HttpStatus httpStatus;

  public static BffException badRequest(final ExceptionStatus exceptionStatus) {
    return toBffException(exceptionStatus, HttpStatus.BAD_REQUEST);
  }

  public static BffException badRequest(final String message) {
    return toBffException(message, HttpStatus.BAD_REQUEST);
  }

  public static BffException notFound(final ExceptionStatus exceptionStatus) {
    return toBffException(exceptionStatus, HttpStatus.NOT_FOUND);
  }

  public static BffException serviceUnavailable(final ExceptionStatus exceptionStatus) {
    return toBffException(exceptionStatus, HttpStatus.SERVICE_UNAVAILABLE);
  }

  public static BffException serviceUnavailable(final String message) {
    return toBffException(message, HttpStatus.SERVICE_UNAVAILABLE);
  }

  public static BffException internalServerError(final String message) {
    return toBffException(message, HttpStatus.INTERNAL_SERVER_ERROR);
  }

  private static BffException toBffException(
      final ExceptionStatus exceptionStatus, final HttpStatus httpStatus) {
    return BffException.builder()
        .status(exceptionStatus.getStatus())
        .statusCode(exceptionStatus.getCode())
        .statusReason(exceptionStatus.getReason())
        .httpStatus(httpStatus)
        .build();
  }

  private static BffException toBffException(final String message, final HttpStatus httpStatus) {
    return BffException.builder()
        .status(BffResponse.BFF_ERROR)
        .statusCode(httpStatus.toString())
        .statusReason(message)
        .httpStatus(httpStatus)
        .build();
  }
}
