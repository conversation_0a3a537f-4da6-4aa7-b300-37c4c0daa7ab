/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.util;

import com.fitb.digital.bff.movemoney.model.bff.BffResponse;
import com.fitb.digital.bff.movemoney.model.client.CesResponse;

public class StatusMapper {
  public static BffResponse mapCesToBffResponse(CesResponse ces, BffResponse bff) {
    bff.setStatus(ces.getStatus());
    bff.setStatusCode(ces.getStatusCode());
    bff.setStatusReason(ces.getStatusReason());

    return bff;
  }
}
