/* Copyright 2022 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.controller;

import com.fitb.digital.bff.movemoney.model.bff.external.account.responses.BffExternalUserAccountsResponse;
import com.fitb.digital.bff.movemoney.service.external.ExternalAccountManagementService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("external")
@Slf4j
@RequiredArgsConstructor
@CrossOrigin(
    origins = {
      "http://localhost:8080",
      "https://slapdpd002.info53.com",
      "https://developer-stg.info53.com",
      "https://developer.info53.com"
    })
public class MmExternalAccountController {
  private final ExternalAccountManagementService externalAccountManagementService;

  @GetMapping("accounts")
  public BffExternalUserAccountsResponse getAccounts() {
    return externalAccountManagementService.getAccounts();
  }
}
