/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model;

import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class BillingRule implements Serializable {
  // some kind of enum to represent type, which FIS has created empty classes
  // for.
  private String ruleType;

  //
  // If creating your own rule, these fields define what that rule does.
  // Choose your own adventure style.
  //

  // the amount to use to compare which rule to engage
  private BigDecimal comparisonAmount;
  private BillingRule lessThanRule;
  private BillingRule greaterThanRule;
  // if the rule declares a fixed amount to pay, use this field
  // this helps aleviate the confusion between this field and comparison
  // amount.
  private BigDecimal paymentAmount;
  private boolean sendOnArrival;
  private boolean sendOnDueDate;
  private MinimalAccount fromAccount;
}
