/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.bff.activity.responses;

import com.fitb.digital.bff.movemoney.model.bff.BffResponse;
import com.fitb.digital.bff.movemoney.model.bff.activity.BffLimit;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@SuppressWarnings("java:S1068")
public class BffGetTransferLimitsResponse implements BffResponse {
  private String status;

  private String statusCode;
  private List<String> retrievalErrors = new ArrayList<>();

  private String statusReason;

  private List<BffLimit> limits = new ArrayList<>();
}
