/* Copyright 2024 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.client;

import com.fitb.digital.bff.movemoney.client.configuration.DefaultFeignClientConfiguration;
import com.fitb.digital.bff.movemoney.model.bff.CoreTransfers.requests.TDSCoreTransferRequest;
import com.fitb.digital.bff.movemoney.model.bff.CoreTransfers.responses.TDSCoreTransferActivityResponse;
import com.fitb.digital.bff.movemoney.model.bff.CoreTransfers.responses.TDSCoreTransferResponse;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@RefreshScope
@FeignClient(
    value = CoreTransfersClient.CLIENT_NAME,
    configuration = DefaultFeignClientConfiguration.class,
    url = "${feign.client.config.core-transfer-tds.url}")
public interface CoreTransfersClient {
  String CLIENT_NAME = "TDS";

  @PostMapping(value = "/funds-transfer")
  TDSCoreTransferResponse tdsCoreTransfer(@RequestBody TDSCoreTransferRequest coreTransferRequest);

  @GetMapping(value = "/transfer-activity")
  TDSCoreTransferActivityResponse getTransferActivity();
}
