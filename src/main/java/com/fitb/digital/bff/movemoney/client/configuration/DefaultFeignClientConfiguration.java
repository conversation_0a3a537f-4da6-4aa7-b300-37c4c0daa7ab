/* Copyright 2024 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.client.configuration;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class DefaultFeignClientConfiguration {

  @Bean
  public DefaultErrorDecoder defaultErrorDecoder(final ObjectMapper objectMapper) {
    return new DefaultErrorDecoder(objectMapper);
  }
}
