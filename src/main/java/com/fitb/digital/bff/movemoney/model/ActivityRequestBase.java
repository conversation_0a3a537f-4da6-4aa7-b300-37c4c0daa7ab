/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model;

import com.fitb.digital.bff.movemoney.model.bff.activity.RecurringActivityFrequency;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.math.BigDecimal;
import java.time.LocalDate;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class ActivityRequestBase {
  private String id;

  private String requestGuid;

  @NotNull private String toAccountId;

  @NotNull private String fromAccountId;

  @NotNull private String activityType;

  @NotNull private BigDecimal amount;

  private BigDecimal additionalPrincipleAmount;

  @Size(max = 50, message = "Memo field must be 50 characters or less.")
  private String memo;

  private LocalDate dueDate;

  private LocalDate endDate;

  private RecurringActivityFrequency frequency;

  private Integer numberOfTransactions;

  private boolean expressDelivery;

  // Added to support goals with xfer and pay orc
  private String fromGoalParentAccountId;
  private String toGoalParentAccountId;

  /** Transactis related properties */
  private String paymentName;

  private String type;

  private boolean invoicedRecurringPayment;

  private String recurringPaymentStopType;

  private String payThisAmount;

  private String payThisBill;

  private Integer dayToPay;

  private String dueToStart;

  private String fromAccountType;

  private String toAccountType;

  private boolean isImmediate;
}
