/* Copyright 2022 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.service.external;

import static com.fitb.digital.bff.movemoney.service.external.ExternalAccountResponseStatus.*;

import com.fitb.digital.bff.movemoney.exceptions.BffException;
import com.fitb.digital.bff.movemoney.exceptions.CesFeignException;
import com.fitb.digital.bff.movemoney.model.bff.external.account.responses.BffExternalUserAccountsResponse;
import com.fitb.digital.bff.movemoney.model.bff.external.payment.BffPaymentAccount;
import com.fitb.digital.bff.movemoney.model.bff.external.transfer.responses.BffExternalAccount;
import com.fitb.digital.bff.movemoney.model.bff.invoicedpayment.BffInvoicedPaymentAccount;
import com.fitb.digital.bff.movemoney.model.client.invoicedpayment.responses.CesInvoicedPaymentAccountResponse;
import com.fitb.digital.bff.movemoney.model.client.webpayment.responses.CesWebpaymentProfileResponse;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ExternalAccountManagementService {
  private final ExternalAccountManagementAsync externalAccountManagementAsync;

  private Integer maxExternalTransferAccounts;

  @Autowired
  public ExternalAccountManagementService(
      ExternalAccountManagementAsync externalAccountManagementAsync,
      @Value("${fitb.digital.bff.movemoney.external-transfer-accounts.max:5}")
          Integer maxExternalTransferAccounts) {
    this.externalAccountManagementAsync = externalAccountManagementAsync;
    this.maxExternalTransferAccounts = maxExternalTransferAccounts;
  }

  public BffExternalUserAccountsResponse getAccounts() {
    CompletableFuture<List<BffExternalAccount>> externalTransferAccountsFork =
        externalAccountManagementAsync.getExternalTransferAccounts();
    CompletableFuture<CesWebpaymentProfileResponse> webPaymentProfileFork =
        externalAccountManagementAsync.getWebPaymentProfile();
    CompletableFuture<CesInvoicedPaymentAccountResponse> invoicedPaymentProfileFork =
        externalAccountManagementAsync.getInvoicedPaymentProfile();
    List<BffExternalAccount> externalTransferAccountsList = null;
    CesWebpaymentProfileResponse webPaymentProfileResult = null;
    CesInvoicedPaymentAccountResponse invoicedPaymentProfileResult = null;
    List<String> overallStatus = new ArrayList<>();
    try {
      externalTransferAccountsList = externalTransferAccountsFork.get();
    } catch (InterruptedException e) {
      processError(overallStatus, e, EXTERNAL_TRANSFER_FAILURE);
      Thread.currentThread().interrupt();
    } catch (Exception e) {
      if (wasErrorThrownBecauseUserWasNotEligible(e)) {
        externalTransferAccountsList = new ArrayList<>();
      } else {
        processError(overallStatus, e, EXTERNAL_TRANSFER_FAILURE);
      }
    }

    try {
      webPaymentProfileResult = webPaymentProfileFork.get();
    } catch (InterruptedException e) {
      processError(overallStatus, e, WEB_PAYMENT_FAILURE);
      Thread.currentThread().interrupt();
    } catch (Exception e) {
      processError(overallStatus, e, WEB_PAYMENT_FAILURE);
    }

    try {
      invoicedPaymentProfileResult = invoicedPaymentProfileFork.get();
    } catch (Exception e) {
      processError(overallStatus, e, ExternalAccountResponseStatus.INVOICED_PAYMENT_FAILURE);
      if (overallStatus.size() >= 3) { // All requests have failed, fail the entire request
        throw BffException.serviceUnavailable("Failed to retrieve results for Accounts");
      }
    }

    var results =
        combineExternalAccountResults(
            externalTransferAccountsList != null
                ? externalTransferAccountsList
                : Collections.emptyList(),
            webPaymentProfileResult,
            invoicedPaymentProfileResult,
            overallStatus.isEmpty()
                ? ExternalAccountResponseStatus.SUCCESS.toString()
                : overallStatus.stream().map(String::toUpperCase).collect(Collectors.joining(",")));
    results.setMaxExternalTransferAccounts(maxExternalTransferAccounts);
    return results;
  }

  private boolean wasErrorThrownBecauseUserWasNotEligible(Exception e) {
    if (!(e.getCause() instanceof CesFeignException)) {
      return false;
    }

    var cesResponse = ((CesFeignException) e.getCause()).getCesResponse();
    return (cesResponse != null
        && "NOT_ELIGIBLE".equals(cesResponse.getStatusCode())
        && "INVALID_REQUEST".equals(cesResponse.getStatus()));
  }

  private void processError(
      List<String> overallStatus, Exception e, ExternalAccountResponseStatus status) {
    if (status == WEB_PAYMENT_FAILURE) {
      log.warn("Unable to fetch web payment accounts", e);
    } else if (status == EXTERNAL_TRANSFER_FAILURE) {
      log.warn("Unable to fetch external transfer accounts", e);
    } else if (status == INVOICED_PAYMENT_FAILURE) {
      log.warn("Unable to fetch Invoiced Payment accounts");
    }
    overallStatus.add(status.toString());
  }

  private BffExternalUserAccountsResponse combineExternalAccountResults(
      List<BffExternalAccount> transferAccountsList,
      CesWebpaymentProfileResponse webpaymentAccountsResponse,
      CesInvoicedPaymentAccountResponse invoicedPaymentAccountResponse,
      String status) {

    var bffExternalUserAccountsResponse = new BffExternalUserAccountsResponse();
    bffExternalUserAccountsResponse.setStatus(status);

    bffExternalUserAccountsResponse.setTransferAccounts(transferAccountsList);

    if (webpaymentAccountsResponse == null) {
      bffExternalUserAccountsResponse.setPaymentAccounts(Collections.emptyList());
    } else {
      bffExternalUserAccountsResponse.setPaymentAccounts(
          webpaymentAccountsResponse.getAccounts().stream()
              .map(account -> BffPaymentAccount.fromCesWebPaymentAccount(account))
              .collect(Collectors.toList()));
    }
    if (invoicedPaymentAccountResponse == null) {
      bffExternalUserAccountsResponse.setInvoicedPaymentAccounts(Collections.emptyList());
    } else {
      bffExternalUserAccountsResponse.setInvoicedPaymentAccounts(
          invoicedPaymentAccountResponse.getAccounts().stream()
              .map(account -> BffInvoicedPaymentAccount.mapInvoicedPaymentAccount(account))
              .collect(Collectors.toList()));
    }

    return bffExternalUserAccountsResponse;
  }
}
