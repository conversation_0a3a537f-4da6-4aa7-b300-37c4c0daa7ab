/* Copyright 2025 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.bff.CoreTransfers.responses;

import java.math.BigDecimal;
import java.time.LocalDate;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class TDSCoreTransferActivity {
  private String referenceId;
  private String fromAccountId;
  private String fromAccountType;
  private String toAccountId;
  private String toAccountType;
  private BigDecimal amount;
  private String transferStatus;
  private LocalDate createdDate;
  private LocalDate expectedPostingDate;
}
