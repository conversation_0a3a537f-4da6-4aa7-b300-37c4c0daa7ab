/* Copyright 2022 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.bff.external.transfer;

public enum ExternalAccountType {
  CHECKING("Checking"),
  SAVINGS("Savings"),
  MMA_CHECKING("MMA Checking"),
  MMA_SAVINGS("MMA Savings"),
  CASH("Cash");

  private String code;

  private ExternalAccountType(String code) {
    this.code = code;
  }

  public String getCode() {
    return this.code;
  }

  public static ExternalAccountType fromStringIgnoreCase(String s) {
    if (s == null) return null;
    return ExternalAccountType.valueOf(s.toUpperCase().replace(" ", "_"));
  }
}
