/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.client.billpay.responses;

import static com.fitb.digital.lib.tokenization.SensitiveDataType.CRIT;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fitb.digital.bff.movemoney.model.client.CesBaseResponse;
import com.fitb.digital.lib.tokenization.SensitiveData;
import com.fitb.digital.lib.tokenization.model.KeyMetadata;
import com.fitb.digital.lib.tokenization.model.Tokenizable;
import java.util.Map;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class CesPayeeAccountResponse extends CesBaseResponse implements Tokenizable {
  @SensitiveData(CRIT)
  private String accountNumber;

  @JsonProperty("TOKENIZED_FIELDS")
  protected Map<String, KeyMetadata> keyMetadata;
}
