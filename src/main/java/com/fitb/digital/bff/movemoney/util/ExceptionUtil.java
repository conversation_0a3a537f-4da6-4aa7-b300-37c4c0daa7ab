/* Copyright 2022 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.util;

import com.fitb.digital.bff.movemoney.constants.ExceptionStatus;
import com.fitb.digital.bff.movemoney.exceptions.BffException;
import com.fitb.digital.bff.movemoney.exceptions.CesFeignException;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ExceptionUtil {
  public static void handleAsyncExceptions(
      final Exception e, final String feignLog, final String genericLog) {
    if (e.getCause() instanceof CesFeignException cause) {
      log.error(feignLog, cause);
      throw cause;
    }
    log.error(genericLog, e.getCause());
    throw BffException.serviceUnavailable(ExceptionStatus.SERVICE_UNAVAILABLE);
  }
}
