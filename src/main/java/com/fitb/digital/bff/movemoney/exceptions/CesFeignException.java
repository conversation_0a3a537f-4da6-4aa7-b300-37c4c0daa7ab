/* Copyright 2020 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.exceptions;

import com.fitb.digital.bff.movemoney.model.client.CesResponse;
import lombok.Getter;
import lombok.Setter;
import org.springframework.http.HttpStatus;
import org.springframework.web.server.ResponseStatusException;

/**
 * An adaption from the HystrixBadRequestException, this in combination with the feign configuration
 * wraps 400's in this exceptions so that they are not counted as circuit breaking or retry
 * exceptions.
 */

// DO NOT DELETE THIS - It is required for Feign Client
// java:S110 - I'm extending framework code, I'm not worried about ancestors.
// java:S1068 - False positive
@SuppressWarnings({"java:S110", "java:S1068"})
@Setter
@Getter
public class CesFeignException extends ResponseStatusException {
  private CesResponse cesResponse;

  public CesFeignException(HttpStatus status, String reason) {
    this(status, reason, null);
  }

  public CesFeignException(HttpStatus status, String reason, CesResponse cesResponse) {
    super(status, reason);
    setCesResponse(cesResponse);
  }
}
