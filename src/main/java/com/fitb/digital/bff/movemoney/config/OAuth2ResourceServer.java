/* Copyright 2019 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.config;

import com.fitb.digital.lib.oauth.FitbOAuth2ResourceServer;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.http.HttpMethod;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.access.expression.WebExpressionAuthorizationManager;

@Profile({"!local & !unit-test & !newman-test & !local-sit"})
@Slf4j
@Configuration
@EnableGlobalMethodSecurity(prePostEnabled = true)
public class OAuth2ResourceServer extends FitbOAuth2ResourceServer {

  @Value("${spring-boot-actuator.scopes.read}")
  private String actuatorReadScope;

  @Value("${spring-boot-actuator.scopes.write}")
  private String actuatorWriteScope;

  @Value("${spring-boot-actuator.permissions.read}")
  private String actuatorReadPermission;

  @Value("${spring-boot-actuator.permissions.write}")
  private String actuatorWritePermission;

  @Value("${ftib.digital.bff.movemoney.threads.core-count:10}")
  private int coreSize;

  @Value("${ftib.digital.bff.movemoney.threads.max-count:30}")
  private int maxSize;

  @Value("${ftib.digital.bff.movemoney.threads.queue-count:10}")
  private int queueSize;

  @SuppressWarnings("java:S1192") // String constants for access vals
  @Override
  public HttpSecurity config(final HttpSecurity http) throws Exception {
    final WebExpressionAuthorizationManager readAccess =
        new WebExpressionAuthorizationManager(
            "hasAuthority('ISSUER_B2E') and hasAuthority('SCOPE_"
                + actuatorReadScope
                + "') and hasAuthority('GROUP_"
                + actuatorReadPermission
                + "') and hasAnyAuthority('ISSUER_B2E', 'ISSUER_B2E_M2M', 'ISSUER_B2E_OIDC', 'ISSUER_B2E_MULTI')");
    final WebExpressionAuthorizationManager writeAccess =
        new WebExpressionAuthorizationManager(
            "hasAuthority('ISSUER_B2E') and hasAuthority('SCOPE_"
                + actuatorWriteScope
                + "') and hasAuthority('GROUP_"
                + actuatorWritePermission
                + "') and hasAnyAuthority('ISSUER_B2E', 'ISSUER_B2E_M2M', 'ISSUER_B2E_OIDC', 'ISSUER_B2E_MULTI')");

    return http.httpBasic(AbstractHttpConfigurer::disable)
        .sessionManagement(
            configurer -> configurer.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
        .csrf(
            configurer ->
                configurer.ignoringRequestMatchers(
                    "/actuator/**")) // https://codecentric.github.io/spring-boot-admin/current/#_csrf_on_actuator_endpoints
        .exceptionHandling(
            configurer ->
                configurer.authenticationEntryPoint(
                    (req, rsp, e) -> rsp.sendError(HttpServletResponse.SC_UNAUTHORIZED)))
        .authorizeHttpRequests(
            configurer ->
                configurer
                    .requestMatchers(HttpMethod.GET, "/actuator/health")
                    .permitAll()
                    .requestMatchers(HttpMethod.GET, "/actuator/**")
                    .access(readAccess)
                    .requestMatchers("/actuator/**")
                    .access(writeAccess)
                    // We may need to reduce swagger access to sbaRead if the client credential cant
                    // get the
                    // group assigned.
                    .requestMatchers("/swagger-ui.html")
                    .access(readAccess)
                    .requestMatchers("/swagger-ui/**")
                    .access(readAccess)
                    .requestMatchers("/swagger-resources/**")
                    .access(readAccess)
                    .requestMatchers("/webjars/swagger-ui/**")
                    .access(readAccess)
                    .requestMatchers("/v3/api-docs/**")
                    .access(readAccess)
                    .requestMatchers("/v3/api-docs.yaml")
                    .access(readAccess)
                    //        .requestMatchers("/service/**")
                    //        .hasAuthority("ISSUER_B2E")
                    .requestMatchers("/b2e/**")
                    .hasAnyAuthority(
                        "ISSUER_B2E", "ISSUER_B2E_M2M", "ISSUER_B2E_OIDC", "ISSUER_B2E_MULTI")
                    // Any additional request must require a B2C issued source
                    .anyRequest()
                    .hasAnyAuthority("ISSUER_B2C", "ISSUER_B2C_OLD"));
  }
}
