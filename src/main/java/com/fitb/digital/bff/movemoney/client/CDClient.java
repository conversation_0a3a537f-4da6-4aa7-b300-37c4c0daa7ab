/* Copyright 2024 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.client;

import com.fitb.digital.bff.movemoney.client.configuration.DefaultFeignClientConfiguration;
import com.fitb.digital.bff.movemoney.model.bff.cd.requests.CDFundTransferRequest;
import com.fitb.digital.bff.movemoney.model.bff.cd.responses.CDFundTransferResponse;
import com.fitb.digital.bff.movemoney.model.bff.cd.responses.CDFundingAccountsListResponse;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

@RefreshScope
@FeignClient(
    value = CDClient.CLIENT_NAME,
    configuration = DefaultFeignClientConfiguration.class,
    url = "${feign.client.config.transfer-cd.url}")
public interface CDClient {
  String CLIENT_NAME = "CD";

  @GetMapping(value = "/b2c/all-funding-accounts")
  CDFundingAccountsListResponse getCDFundingAccounts();

  @PostMapping(value = "/b2c/cd-redeem")
  CDFundTransferResponse redeemCD(@RequestBody CDFundTransferRequest cdFundTransferRequest);
}
