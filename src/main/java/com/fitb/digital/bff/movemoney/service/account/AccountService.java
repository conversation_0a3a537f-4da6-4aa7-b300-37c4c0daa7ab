/* Copyright 2020 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.service.account;

import com.fitb.digital.bff.movemoney.client.CesClient;
import com.fitb.digital.bff.movemoney.model.bff.account.responses.BffAccountDetail;
import com.fitb.digital.bff.movemoney.model.bff.account.responses.BffAccountDetailResponse;
import com.fitb.digital.bff.movemoney.model.client.account.responses.ClientAccountDetailResponse;
import com.fitb.digital.bff.movemoney.model.client.profile.responses.Actor;
import com.fitb.digital.bff.movemoney.model.client.profile.responses.AvailableRecipient;
import com.fitb.digital.bff.movemoney.model.client.profile.responses.ProfileResponse;
import com.fitb.digital.bff.movemoney.model.local.TransferDetails;
import com.fitb.digital.bff.movemoney.service.CesClientService;
import com.fitb.digital.bff.movemoney.util.AccountTypeMapper;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class AccountService extends CesClientService {
  private static final int INTERNAL_ACCOUNT_TYPE_LENGTH = 3;
  private final ModelMapper mapper = new ModelMapper();

  public AccountService(CesClient cesClient) {
    super(cesClient);
  }

  public Optional<TransferDetails> getTransferDetails(
      String fromAccount, String toAccount, ProfileResponse profileResponse) {
    Optional<TransferDetails> details = Optional.empty();

    var pair = getTransferPairDetails(profileResponse, fromAccount, toAccount);
    if (pair.isPresent()) {
      var availableRecipient = pair.get();
      details =
          Optional.of(
              new TransferDetails(
                  availableRecipient.getEarliestAvailableDate(),
                  availableRecipient.getLatestAvailableDate()));
    }

    return details;
  }

  public BffAccountDetailResponse getAccountDetails(String accountId) {
    var response = getClientAccountDetails(accountId);
    return mapCesResponse(response);
  }

  ClientAccountDetailResponse getClientAccountDetails(String accountId) {
    return super.callClient(() -> cesClient.getAccountDetail(accountId));
  }

  BffAccountDetailResponse mapCesResponse(ClientAccountDetailResponse response) {
    BffAccountDetailResponse bffResponse = new BffAccountDetailResponse();

    BffAccountDetail bffAccountDetail =
        mapper.map(response.getAccountDetail(), BffAccountDetail.class);
    switch (AccountTypeMapper.mapTextType(response.getAccountDetail().getAccountType())) {
      case LOAN:
        bffAccountDetail.setInitialPrincipal(response.getAccountDetail().getNoteAmount());
        break;

      case CREDIT_CARD:
        bffAccountDetail.setNextPaymentAmount(
            response.getAccountDetail().getLastStatementBalance());
        break;
      default:
        // nothing to see here. Shut up SonarQube.,
    }
    bffResponse.setStatusCode(response.getStatus());
    bffResponse.setAccountDetail(bffAccountDetail);
    return bffResponse;
  }

  public Boolean isAccountInternal(String type) {
    return type.length() == INTERNAL_ACCOUNT_TYPE_LENGTH || type.endsWith("_GOAL");
  }

  private Optional<AvailableRecipient> getTransferPairDetails(
      ProfileResponse profile, String fromAccount, String toAccount) {
    Actor from =
        profile.getActors().stream()
            .filter(actor -> actor.getId().equals(fromAccount))
            .findFirst()
            .orElse(new Actor());
    return from.getAvailableRecipients().stream()
        .filter(r -> r.getId().equals(toAccount))
        .findFirst();
  }
}
