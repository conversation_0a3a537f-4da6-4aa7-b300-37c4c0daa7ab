/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.client.account.responses;

import java.math.BigDecimal;
import java.time.LocalDate;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@SuppressWarnings("squid:1068")
public class ClientAccountDetail {
  private String accountDescription;
  private String displayAccountNumber;
  private String nickname;
  private String accountType;
  private String affiliate;
  private BigDecimal availableBalance;
  private BigDecimal ledgerBalance;
  private BigDecimal pendingAmount;
  private BigDecimal otherAmount;
  private LocalDate closeDate;
  private LocalDate openDate;
  private LocalDate postDate;
  private String privilegeCode;
  private String customerServiceDescription;
  private String productName;
  private BigDecimal interestSinceLastStatement;
  private BigDecimal interestOnLastStatement;
  private BigDecimal interestYtd;
  private BigDecimal interestLastYear;
  private Integer yearForInterestYtd;
  private Integer yearForInterestLastYear;
  private Boolean isHardshipAllowed = false;
  private Boolean isSmartSavingsEnabled = false;
  private Boolean isScheduledSavingsEnabled = false;
  private String collateralDescription;
  private BigDecimal noteAmount;
  private BigDecimal payoffAmount;
  private Integer termMonths;
  private BigDecimal interestRate;
  private LocalDate maturityDate;
  private BigDecimal nextPaymentAmount;
  private LocalDate nextPaymentDate;
  private BigDecimal interestDue;
  private BigDecimal interestPaidYTD;
  private Boolean interestReportableOnForm1098;
  private BigDecimal totalPaymentsPending;
  private BigDecimal escrowBalance;
  private BigDecimal piPayment;
  private BigDecimal escrowPayment;
  private BigDecimal optionalInsurancePayment;
  private BigDecimal subsidyPayment;
  private BigDecimal interestYearToDate;
  private String propertyAddressLine1;
  private String propertyAddressLine2;
  private String propertyAddressLine3;
  private Boolean availableForOnlinePayment;
  private BigDecimal lastStatementBalance;
  private LocalDate lastStatementDate;
  private BigDecimal minimumPaymentAmount;
  private BigDecimal cashAdvanceRate;
  private BigDecimal purchaseRate;
  private BigDecimal cashLimitAmount;
  private BigDecimal cardLimitAmount;
  private BigDecimal maximumCashLimitAmount;
  private BigDecimal maximumCardLimitAmount;
}
