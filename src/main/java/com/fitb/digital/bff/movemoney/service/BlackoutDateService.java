/* Copyright 2020 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fitb.digital.bff.movemoney.model.local.BlackoutDatesResponse;
import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class BlackoutDateService {
  private List<LocalDate> localDates;

  public List<LocalDate> getBlackoutDates(URL blackoutDatesUrl, File localBackupBlackoutDatesFile) {
    if (localDates != null) {
      return localDates;
    }

    localDates = getLocalDatesFromUrl(blackoutDatesUrl);

    if (localDates != null) {
      log.info("Sucessfully read blackout dates from URL");
      return localDates;
    }

    log.info("Unable to read blackout dates from URL, falling back to local file");
    localDates = getLocalBlackoutDatesFromFile(localBackupBlackoutDatesFile);

    return localDates;
  }

  private List<LocalDate> getLocalDatesFromUrl(URL url) {
    var mapper = new ObjectMapper();
    try {
      return getDatesListFromResponse(mapper.readValue(url, BlackoutDatesResponse.class));
    } catch (Exception e) {
      log.error("EXCEPTION READING FROM BLACKOUT DATES URL", e);
      return null;
    }
  }

  private List<LocalDate> getLocalBlackoutDatesFromFile(File file) {
    var mapper = new ObjectMapper();
    try {
      return getDatesListFromResponse(mapper.readValue(file, BlackoutDatesResponse.class));
    } catch (IOException e) {
      log.error("EXCEPTION READING FROM BLACKOUT DATES FILE", e);
      return null;
    }
  }

  private List<LocalDate> getDatesListFromResponse(BlackoutDatesResponse blackoutDatesResponse) {
    var dateFormatters =
        Arrays.asList(
            DateTimeFormatter.ofPattern(blackoutDatesResponse.getDateFormat()),
            DateTimeFormatter.ofPattern(
                "M/d/yyyy")); // This can be removed once the static content is updated
    // appropriately
    List<LocalDate> blackoutDates = new ArrayList<>();
    for (String date : blackoutDatesResponse.getBlackoutDates()) {

      var localDate = parseDate(dateFormatters, date);
      blackoutDates.add(localDate);
    }

    return blackoutDates;
  }

  private LocalDate parseDate(List<DateTimeFormatter> formatters, String date) {
    for (var formatter : formatters) {
      try {
        return LocalDate.parse(date, formatter);
      } catch (DateTimeParseException ex) {

      }
    }
    return null;
  }
}
