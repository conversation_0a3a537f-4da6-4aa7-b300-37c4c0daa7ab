/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.bff.account.responses;

import com.fitb.digital.bff.movemoney.client.xferandpayorc.model.XferAndPayActor;
import com.fitb.digital.bff.movemoney.constants.AccountTypes;
import com.fitb.digital.bff.movemoney.model.client.profile.responses.InvoiceDetails;
import com.fitb.digital.bff.movemoney.util.AccountTypeMapper;
import com.fitb.digital.bff.movemoney.util.NameNormalizer;
import com.openpojo.business.annotation.BusinessKey;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.modelmapper.ModelMapper;

@Data
@NoArgsConstructor
@SuppressWarnings("squid:S1068")
public class Account {
  private String id;

  @BusinessKey(caseSensitive = false)
  private String displayName;

  private String displayAccountNumber;
  private String accountType;
  private BigDecimal availableBalance;
  private BigDecimal ledgerBalance;
  private String goalCategory;
  private BigDecimal goalTargetAmount;
  private LocalDate goalTargetDate;
  private BigDecimal goalAmountNotAllocated;
  private String goalParentId;

  private BigDecimal displaySortOrder;

  private boolean accountInternal;
  private boolean transferSource;
  private boolean paymentTarget;
  private LocalDate lastPaymentDate;
  private BigDecimal lastPaymentAmount;
  private boolean cashAdvanceEligible;
  private boolean electronicBillingEnabled;
  private boolean canAddRecurring;
  private boolean verificationRequired;
  private boolean externalTransferEligible;
  // from accounts
  private boolean invoiceEnabled;

  private List<Recipient> recipients = new ArrayList<>();
  private List<Source> sources = new ArrayList<>();

  private InvoiceDetails invoiceDetails; // TODO need to pass from orc

  public boolean getCashAdvanceEligible() {
    return isTransferSource()
        && isAccountInternal()
        && !AccountTypes.PREPAID_CARDS_TYPES.contains(getAccountType())
        && !AccountTypes.DEPOSITS_INVESTMENTS_TYPES.contains(getAccountType());
  }

  public boolean isGoal() {
    return AccountTypeMapper.mapTextType(this.getAccountType())
        == AccountTypeMapper.AccountType.GOAL;
  }

  public String getDisplayName() {
    return NameNormalizer.normalizeAccountName(displayName);
  }

  public static Account fromXferAndPayActor(XferAndPayActor xferAndPayActor) {
    var modelMapper = new ModelMapper();
    modelMapper
        .typeMap(XferAndPayActor.class, Account.class)
        .addMapping(XferAndPayActor::getType, Account::setAccountType);

    modelMapper
        .typeMap(XferAndPayActor.class, Account.class)
        .addMapping(XferAndPayActor::getAvailableSources, Account::setSources);

    modelMapper
        .typeMap(XferAndPayActor.class, Account.class)
        .addMapping(XferAndPayActor::getAvailableRecipients, Account::setRecipients);

    return modelMapper.map(xferAndPayActor, Account.class);
  }
}
