/* Copyright 2022 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.service.transferinfo;

import com.fitb.digital.bff.movemoney.constants.AccountTypes;
import com.fitb.digital.bff.movemoney.exceptions.BffException;
import com.fitb.digital.bff.movemoney.model.ActivityBase;
import com.fitb.digital.bff.movemoney.model.bff.account.responses.Account;
import com.fitb.digital.bff.movemoney.model.bff.account.responses.Recipient;
import com.fitb.digital.bff.movemoney.model.bff.account.responses.Source;
import com.fitb.digital.bff.movemoney.model.client.account.responses.CesGoal;
import com.fitb.digital.bff.movemoney.model.client.account.responses.InternalAccount;
import com.fitb.digital.bff.movemoney.model.client.account.responses.ListResponse;
import com.fitb.digital.bff.movemoney.model.client.profile.responses.Actor;
import com.fitb.digital.bff.movemoney.model.client.profile.responses.AvailableRecipient;
import com.fitb.digital.bff.movemoney.model.client.profile.responses.AvailableSource;
import com.fitb.digital.bff.movemoney.model.client.profile.responses.ProfileResponse;
import com.fitb.digital.bff.movemoney.service.account.AccountService;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.modelmapper.ModelMapper;

@Slf4j
class AccountCombiner {
  public static final String BILLPAY = "BILLPAY";

  private final AccountService accountService;
  private final Boolean access360Enabled;

  public AccountCombiner(AccountService accountService, Boolean access360Enabled) {
    this.access360Enabled = access360Enabled;
    this.accountService = accountService;
  }

  @NotNull
  public List<Account> combine(ProfileResponse profileResponse, ListResponse accountList) {
    var accountIdsToAccounts = new HashMap<String, Account>();
    var accountListMapper = new AccountListMapper(accountList).invoke();

    var accounts = new ArrayList<Account>();
    for (Actor actor : profileResponse.getActors()) {
      if (isValidActor(actor)) {
        var matchingAccount =
            actor.isGoal()
                ? matchingGoal(actor, accountList.getGoals())
                : matchingAccount(actor, accountList.getAccounts());
        var account = accountFromActor(actor, matchingAccount);
        accounts.add(account);
        accountIdsToAccounts.put(account.getId(), account);
      } else if (actor.isActive() && actorIsGoalGeneralSavings(actor)) {
        // Create a list of actor Ids to use on second pass
        // NOTE: The ID of a general savings account is different between transferandpay/profile and
        // /account/list
        accountListMapper.addGenerarlSavingsIdFromProfile(actor.getId());
      }
    }

    if (!accountList.getGoals().isEmpty()
        && accountListMapper.getGeneralSavingsIdsFromProfile().isEmpty()) {
      log.error("Account list contains goals but profile did not.");
      throw BffException.internalServerError("Account list contains goals but profile did not.");
    }

    filterAccountList(accountIdsToAccounts, accountListMapper, accounts);
    return accounts;
  }

  private boolean isValidActor(Actor actor) {
    if (!actor.isActive() || actorIsGoalGeneralSavings(actor)) {
      return false;
    }

    // No access 360
    return access360Enabled || !AccountTypes.ACCESS_360_ACCT_TYPES.contains(actor.getType());
  }

  private void filterAccountList(
      HashMap<String, Account> accountIdsToAccounts,
      AccountListMapper accountListMapper,
      ArrayList<Account> accounts) {
    // Loop through the accounts again
    for (Account account : accounts) {
      // If accountId is a parent momentum savings account (in the list of keys produced by
      // GoalMapper)
      // Set the goalBalanceNotAllocated field to the progressAmount from the general savings goal
      if (accountListMapper.getMomentumSavingsIdsToGeneralSavings().containsKey(account.getId())) {
        var goal = accountListMapper.getMomentumSavingsIdsToGeneralSavings().get(account.getId());
        account.setGoalAmountNotAllocated(goal.getProgressAmount());
      }

      if (account.isGoal()) {
        BigDecimal displayOrder =
            getGoalSortOrder(accountIdsToAccounts, accountListMapper, account);
        account.setDisplaySortOrder(displayOrder);
      }

      filterSourcesAndRecipients(accountListMapper.getGeneralSavingsIdsFromProfile(), account);
      setGoalTransferImmediateOnly(account);
    }
  }

  private void filterSourcesAndRecipients(List<String> generalSavingsIds, Account account) {
    account.getRecipients().removeIf(recipient -> generalSavingsIds.contains(recipient.getId()));
    account.getSources().removeIf(source -> generalSavingsIds.contains(source.getId()));
  }

  private void setGoalTransferImmediateOnly(Account account) {
    account
        .getRecipients()
        .forEach(
            recipient -> {
              if (recipient.getActivityType().equalsIgnoreCase(ActivityBase.GOALS)) {
                recipient.setLatestAvailableDate(recipient.getEarliestAvailableDate());
              }
            });

    account
        .getSources()
        .forEach(
            source -> {
              if (source.getActivityType().equalsIgnoreCase(ActivityBase.GOALS)) {
                source.setLatestAvailableDate(source.getEarliestAvailableDate());
              }
            });
  }

  @NotNull
  // Modify the sort order to be a BigDecimal with the left of the decimal place being sort order of
  // parent account
  // and the right of the decimal being sort order of goal based on targetDate from other goals with
  // same parent
  // Ex: 6.0001, 6.0002
  // Assumption is there are a max of 10,000 goals and the client can now sort more easily than a
  // string
  private BigDecimal getGoalSortOrder(
      Map<String, Account> accountIdsToAccounts,
      AccountListMapper accountListMapper,
      Account account) {
    var parentAccountId = accountListMapper.getGoalIdsToParentAccountIds().get(account.getId());
    if (!accountListMapper.getParentToGoalIdsSorted().containsKey(parentAccountId)) {
      log.error(
          "Account list does not contain goals for momentum savings account {}", parentAccountId);
      throw BffException.internalServerError(
          "Account list does not contain goals for momentum savings account.");
    }
    var rawIndex =
        new BigDecimal(
            accountListMapper
                    .getParentToGoalIdsSorted()
                    .get(parentAccountId)
                    .indexOf(account.getId())
                + 1);
    var maxGoals = new BigDecimal("10000");
    var computedIndex = rawIndex.divide(maxGoals, 4, RoundingMode.UNNECESSARY);
    var momentumSavingsAcct = accountIdsToAccounts.get(parentAccountId);

    return momentumSavingsAcct.getDisplaySortOrder().add(computedIndex);
  }

  // If an actor is the 'generated' General Savings account used for Goals we do not want to show it
  // to the user
  private boolean actorIsGoalGeneralSavings(Actor actor) {
    return actor.isGoal()
        && actor
            .getDisplayName()
            .equals("General Savings"); // This is the only way to check for this account
  }

  private Optional<InternalAccount> matchingAccount(
      Actor actor, List<InternalAccount> internalAccounts) {
    return internalAccounts.stream()
        .filter(internalAccount1 -> internalAccount1.getId().equals(actor.getId()))
        .findFirst();
  }

  private Optional<InternalAccount> matchingGoal(Actor actor, List<CesGoal> goals) {
    return goals.stream()
        .filter(goal -> goal.getId().equals(actor.getId()))
        .map(this::mapGoalToInternalAccount)
        .findFirst();
  }

  private InternalAccount mapGoalToInternalAccount(CesGoal goal) {
    var account = new InternalAccount();
    account.setId(goal.getId());
    account.setDisplayAccountNumber(goal.getDisplayAccountNumber());
    account.setDisplayName(goal.getName());
    account.setGoalCategory(goal.getCategory());
    account.setGoalTargetAmount(goal.getTargetAmount());
    account.setAvailableBalance(goal.getProgressAmount());
    account.setGoalTargetDate(goal.getTargetDate());
    account.setOpenDate(goal.getCreated());

    return account;
  }

  Account accountFromActor(Actor actor, Optional<InternalAccount> matchingAccount) {
    var account = new Account();
    account.setId(actor.getId());
    account.setDisplayName(actor.getDisplayName());
    account.setDisplayAccountNumber(actor.getDisplayAccountNumber());
    account.setAccountType(actor.getType());
    account.setVerificationRequired(actor.isVerificationRequired());
    // do we want an error if the matching account isn't found?
    matchingAccount.ifPresent(
        value -> {
          account.setAvailableBalance(value.getAvailableBalance());
          account.setLedgerBalance(value.getLedgerBalance());
          account.setGoalCategory(value.getGoalCategory());
          account.setGoalTargetAmount(value.getGoalTargetAmount());
          account.setGoalTargetDate(value.getGoalTargetDate());
          account.setExternalTransferEligible(value.isExternalTransferEligible());
          account.setDisplaySortOrder(new BigDecimal(value.getSortOrder()));
          if (account.getAccountType() == null) account.setAccountType(value.getAccountType());
        });
    account.setAccountInternal(accountService.isAccountInternal(actor.getType()));
    account.setTransferSource(actor.isTransferSource());
    account.setPaymentTarget(actor.isPaymentRecipient());
    account.setLastPaymentAmount(actor.getLastPaymentAmount());
    account.setLastPaymentDate(actor.getLastPaymentDate());
    account.setElectronicBillingEnabled(actor.isElectronicBillingEnabled());
    account.setCanAddRecurring(actor.isCanAddRecurring());
    account.setInvoiceEnabled(actor.isInvoiceEnabled());
    if (actor.isInvoiceEnabled()) {
      account.setInvoiceDetails(actor.getInvoiceDetails());
    }

    var mapper = new ModelMapper();
    for (AvailableRecipient availableRecipient : actor.getAvailableRecipients()) {
      if (!availableRecipient.getActivityType().equals(BILLPAY)) {
        account.getRecipients().add(mapper.map(availableRecipient, Recipient.class));
      }
    }
    for (AvailableSource availableSource : actor.getAvailableSources()) {
      account.getSources().add(mapper.map(availableSource, Source.class));
    }
    return account;
  }
}
