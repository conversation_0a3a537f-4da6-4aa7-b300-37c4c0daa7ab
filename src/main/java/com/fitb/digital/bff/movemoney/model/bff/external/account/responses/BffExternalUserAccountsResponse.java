/* Copyright 2022 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.bff.external.account.responses;

import com.fitb.digital.bff.movemoney.model.bff.BffSimpleResponse;
import com.fitb.digital.bff.movemoney.model.bff.external.payment.BffPaymentAccount;
import com.fitb.digital.bff.movemoney.model.bff.external.transfer.responses.BffExternalAccount;
import com.fitb.digital.bff.movemoney.model.bff.invoicedpayment.BffInvoicedPaymentAccount;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class BffExternalUserAccountsResponse extends BffSimpleResponse {
  private List<BffExternalAccount> transferAccounts;
  private List<BffPaymentAccount> paymentAccounts;
  private List<BffInvoicedPaymentAccount> invoicedPaymentAccounts;
  private Integer maxExternalTransferAccounts;
}
