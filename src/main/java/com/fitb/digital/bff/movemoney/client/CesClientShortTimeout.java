/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.client;

import com.fitb.digital.bff.movemoney.client.configuration.CesFeignClientConfiguration;
import com.fitb.digital.bff.movemoney.model.client.billpay.responses.*;
import com.fitb.digital.bff.movemoney.model.client.external.transfer.requests.*;
import com.fitb.digital.bff.movemoney.model.client.external.transfer.responses.*;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

@RefreshScope
@FeignClient(
    value = CesClientShortTimeout.CLIENT_NAME,
    configuration = CesFeignClientConfiguration.class,
    url = "${feign.client.config.ces-proxy.url}")
@SuppressWarnings("java:S1214")
public interface CesClientShortTimeout {
  String CLIENT_NAME = "CesShortTimeout";

  @GetMapping(value = "/services/externaltransfer/userAccounts")
  CesGetUserAccountsResponse getExternalTransferUserAccounts();
}
