/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.client.activity.responses;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum CesExternalTransferDirection {
  INBOUND("INBOUND", "Inbound Transfer"),
  OUTBOUND("OUTBOUND", "Outbound Transfer");

  private String code;
  private String label;

  @JsonValue
  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public String getLabel() {
    return label;
  }

  public void setLabel(String label) {
    this.label = label;
  }

  @JsonC<PERSON>(mode = JsonCreator.Mode.DELEGATING)
  public static CesExternalTransferDirection of(String value) {
    for (CesExternalTransferDirection e : values()) {
      if (e.code.equalsIgnoreCase(value)) {
        return e;
      }
    }

    throw new IllegalArgumentException("CesExternalTransferDirection: unknown value: " + value);
  }
}
