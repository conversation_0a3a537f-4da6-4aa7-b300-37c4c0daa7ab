/* Copyright 2022 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.controller.externaltransfer;

import com.fitb.digital.bff.movemoney.exceptions.BffException;
import com.fitb.digital.bff.movemoney.model.bff.BffResponse;
import com.fitb.digital.bff.movemoney.model.bff.BffSimpleResponse;
import com.fitb.digital.bff.movemoney.model.bff.external.transfer.requests.*;
import com.fitb.digital.bff.movemoney.model.bff.external.transfer.responses.*;
import com.fitb.digital.bff.movemoney.service.externaltransfer.ExternalTransferService;
import com.fitb.digital.bff.movemoney.service.externaltransfer.VerificationService;
import com.fitb.digital.lib.risk.RiskScore;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("externaltransfer")
@Slf4j
@RequiredArgsConstructor
@CrossOrigin(
    origins = {
      "http://localhost:8080",
      "https://slapdpd002.info53.com",
      "https://developer-stg.info53.com",
      "https://developer.info53.com"
    })
public class MmExternalTransferController {
  private final ExternalTransferService transferService;
  private final VerificationService verificationService;
  private final ExternalTransferInputValidator externalTransferInputValidator;

  @RiskScore(activityType = "EXTERNAL_TRANSFERS")
  @GetMapping("accountVerificationInfo")
  public BffExternalTransferAccountVerificationInfo getAccountVerificationInfo(
      @RequestParam String accountId) {
    return transferService.getAccountVerificationInfo(accountId);
  }

  @GetMapping("brokerages")
  public BffBrokerageListResponse getBrokerageList() {
    return transferService.getBrokerages();
  }

  @GetMapping("bankInfo")
  public ResponseEntity<BffFinancialInstitutionInfo> getBankInfo(
      @RequestParam(required = false) String routingNumber,
      @RequestParam(required = false) String brokerageName) {

    externalTransferInputValidator.validateGetBankInfoInput(routingNumber, brokerageName);

    final BffFinancialInstitutionInfo financialInstitutionInfo =
        routingNumber != null
            ? transferService.getFinancialInstitutionInfoForRoutingNumber(routingNumber)
            : transferService.getFinancialInstitutionInfoForBrokerageName(brokerageName);

    if (financialInstitutionInfo.getInstitutionName() == null) {
      throw BffException.badRequest("Institution name not found");
    }

    return new ResponseEntity<>(financialInstitutionInfo, HttpStatus.OK);
  }

  @RiskScore(activityType = "EXTERNAL_ACCOUNT")
  @PostMapping("addAccount")
  public ResponseEntity<BffAddAccountResponse> addAccount(
      @RequestBody BffAddAccountRequest bffAddAccountRequest) {
    // log.debug("Add External Transfer Account Start", kv("request_body", bffAddAccountRequest));

    externalTransferInputValidator.validateAddAccountInput(bffAddAccountRequest);

    var bffAddAccountResponse = transferService.addExternalAccount(bffAddAccountRequest);
    return new ResponseEntity<>(bffAddAccountResponse, HttpStatus.OK);
  }

  @DeleteMapping("deleteAccount")
  public BffResponse deleteAccount(@RequestParam String accountId) {
    // log.debug("Removing user defined external account id {}", accountId);
    return transferService.deleteExternalAccount(accountId);
  }

  @RiskScore(activityType = "EXTERNAL_TRANSFERS")
  @PostMapping("verifyAccountRealTime")
  public BffRealTimeVerificationResponse verifyAccount(
      @RequestBody BffRealTimeVerificationRequest bffRequest) {
    return verificationService.verifyExternalAccountRealTime(bffRequest);
  }

  @PostMapping("verifyTrialDeposits")
  public BffSimpleResponse verifyTrialDeposits(
      @Valid @RequestBody BffVerifyTrialDepositsRequest bffRequest) {
    return verificationService.verifyTrialDeposits(bffRequest);
  }

  @RiskScore(activityType = "EXTERNAL_TRANSFERS")
  @PostMapping("initiateTrialDeposits")
  public BffAddAccountResponse initiateTrialDeposits(
      @Valid @RequestBody BffInitiateTrialDepositsRequest bffRequest) {
    // TODO: Uncomment the below after clients have implemented changes
    //    if (bffRequest.getBrokerageName() == null && bffRequest.getRoutingNumber() == null) {
    //      var errorResponse = new BffAddAccountResponse();
    //      errorResponse.setStatus(BffResponse.ERROR);
    //      errorResponse.setStatusCode("BAD_REQUEST");
    //      errorResponse.setStatusReason("Must include either brokerageName or routingNumber");
    //
    //      return new ResponseEntity<>(errorResponse, HttpStatus.BAD_REQUEST);
    //    }

    return verificationService.initiateTrialDeposits(
        bffRequest.getAccountId(), bffRequest.getBrokerageName(), bffRequest.getRoutingNumber());
  }

  @PutMapping("updateAccountNickname")
  public ResponseEntity<BffSimpleResponse> updateAccountNickname(
      @Valid @RequestBody BffUpdateExtAccountNicknameRequest request) {
    var response = transferService.updateExternalAccountNickname(request);
    return new ResponseEntity<>(response, HttpStatus.OK);
  }
}
