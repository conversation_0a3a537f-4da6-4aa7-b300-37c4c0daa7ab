/* Copyright 2022 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.service;

import com.fitb.digital.bff.movemoney.client.CesClient;
import com.fitb.digital.bff.movemoney.model.bff.myadvance.requests.BffMyAdvanceRequest;
import com.fitb.digital.bff.movemoney.model.bff.myadvance.responses.BffMyAdvanceResponse;
import com.fitb.digital.bff.movemoney.model.client.cashadvance.requests.TransferRequest;
import java.time.*;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;

@Service
public class MyAdvanceService extends CesClientService {
  private final ModelMapper mapper = new ModelMapper();

  public MyAdvanceService(CesClient cesClient) {
    super(cesClient);
  }

  public BffMyAdvanceResponse performCashAdvance(BffMyAdvanceRequest myAdvanceRequest) {
    TransferRequest cesRequest = mapper.map(myAdvanceRequest, TransferRequest.class);
    final var easternDateTime = getCurrentEasternZonedDateTime();
    cesRequest.setDueDate(easternDateTime.toLocalDate());

    var response = super.callClient(() -> cesClient.postCashAdvanceActivity(cesRequest));

    mapper.getConfiguration().setAmbiguityIgnored(true);
    var bffMyAdvanceResponse = mapper.map(response.getTransfer(), BffMyAdvanceResponse.class);
    bffMyAdvanceResponse.setStatus(response.getStatus());
    return bffMyAdvanceResponse;
  }

  protected ZonedDateTime getCurrentEasternZonedDateTime() {
    final var utcCurrentDateTime = Instant.now();
    return ZonedDateTime.ofInstant(utcCurrentDateTime, ZoneId.of("America/New_York"));
  }
}
