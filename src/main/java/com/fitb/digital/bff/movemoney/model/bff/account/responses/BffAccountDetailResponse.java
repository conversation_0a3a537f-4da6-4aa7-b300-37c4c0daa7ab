/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.bff.account.responses;

import com.fitb.digital.bff.movemoney.model.bff.BffResponse;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;

@Data
public class BffAccountDetailResponse implements BffResponse {
  private String status;
  private String statusCode;
  private String statusReason;
  private List<String> retrievalErrors = new ArrayList<>();
  private BffAccountDetail accountDetail;
}
