/* Copyright 2025 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.service.coretransfers;

import com.fitb.digital.bff.movemoney.model.ActivityBase;
import com.fitb.digital.bff.movemoney.model.bff.CoreTransfers.responses.TDSCoreTransferActivity;
import com.fitb.digital.bff.movemoney.model.bff.activity.BffActivity;
import com.fitb.digital.bff.movemoney.model.client.account.responses.InternalAccount;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class CoreTransfersActivityMapper {

  private static final String CT_SUCCESS_STATUS = "SUCCESS";
  private static final String CT_PENDING_STATUS = "PENDING";
  private static final String BFF_UNSUCCESSFUL_STATUS_CODE = "1";
  private static final String BFF_PROCESSED_STATUS_CODE = "3";
  private static final String BFF_IN_PROCESS_STATUS_CODE = "5";

  /**
   * Maps Core Transfers activities to BffActivity format
   *
   * @param coreTransferActivities List of Core Transfer activities
   * @return List of BffActivity objects
   */
  public List<BffActivity> mapCoreTransferActivities(
      List<TDSCoreTransferActivity> coreTransferActivities) {
    return coreTransferActivities.stream()
        .map(activity -> mapSingleActivity(activity, null))
        .collect(Collectors.toList());
  }

  /**
   * Maps Core Transfers activities to BffActivity format with provided account list
   *
   * @param coreTransferActivities List of Core Transfer activities
   * @param accountList List of internal accounts for mapping account details
   * @return List of BffActivity objects
   */
  public List<BffActivity> mapCoreTransferActivities(
      List<TDSCoreTransferActivity> coreTransferActivities, List<InternalAccount> accountList) {
    return coreTransferActivities.stream()
        .map(activity -> mapSingleActivity(activity, accountList))
        .collect(Collectors.toList());
  }

  /**
   * Maps a single Core Transfer activity to BffActivity
   *
   * @param coreActivity Core Transfer activity
   * @param accountList List of internal accounts for mapping account details (null to fetch from
   *     service)
   * @return BffActivity object
   */
  private BffActivity mapSingleActivity(
      TDSCoreTransferActivity coreActivity, List<InternalAccount> accountList) {
    BffActivity bffActivity = new BffActivity();

    // Map basic fields
    bffActivity.setId(coreActivity.getReferenceId());
    bffActivity.setDisplayId(coreActivity.getReferenceId());
    bffActivity.setFromAccountId(coreActivity.getFromAccountId());
    bffActivity.setToAccountId(coreActivity.getToAccountId());
    bffActivity.setToAccountType(coreActivity.getToAccountType());

    if (coreActivity.getAmount() != null) {
      bffActivity.setAmount(coreActivity.getAmount().doubleValue());
    }

    // Map dates
    bffActivity.setCreationDate(coreActivity.getCreatedDate());
    bffActivity.setDueDate(coreActivity.getExpectedPostingDate());

    // Set creation timestamp if createdDate is available
    if (coreActivity.getCreatedDate() != null) {
      bffActivity.setCreateTimestamp(coreActivity.getCreatedDate().atStartOfDay());
    }

    // Map status based on transferStatus
    mapStatus(coreActivity, bffActivity);

    // Set activity type to INTERNAL_TRANSFER for Core Transfers
    bffActivity.setActivityType(ActivityBase.INTERNAL_TRANSFER);
    bffActivity.setType(ActivityBase.INTERNAL_TRANSFER);

    populateAccountDetails(bffActivity, accountList);
    setDefaultValues(bffActivity);

    return bffActivity;
  }

  /**
   * Maps transferActivity status to BffActivity status and display status Based on new
   * requirements: - All core transfers are immediate transfers and go to recentActivities
   * regardless of status - If transferStatus is "SUCCESS" → status "Completed" and status code "3"
   * - If transferStatus is "PENDING" → status "Completed" and status code "3" - Otherwise → status
   * "Unsuccessful" and status code "1"
   *
   * @param coreActivity Core Transfer activity
   * @param bffActivity BffActivity to update
   */
  private void mapStatus(TDSCoreTransferActivity coreActivity, BffActivity bffActivity) {
    String transferStatus = coreActivity.getTransferStatus();

    // If transferStatus is PENDING, batch settle has not happened yet on UDS - treat as completed
    // in BFF
    if (CT_SUCCESS_STATUS.equals(transferStatus) || CT_PENDING_STATUS.equals(transferStatus)) {
      // SUCCESS status → Completed with status code 3
      // PENDING status → Completed with status code 3 -> This is done to keep parity with CES
      bffActivity.setDisplayStatus(ActivityBase.COMPLETED_STATUS);
      bffActivity.setStatus(BFF_PROCESSED_STATUS_CODE);
    } else {
      // All other statuses → Unsuccessful with status code 1
      bffActivity.setDisplayStatus(ActivityBase.UNSUCCESSFUL_STATUS);
      bffActivity.setStatus(BFF_UNSUCCESSFUL_STATUS_CODE);
    }
  }

  /**
   * Sets default values for fields that cannot be mapped from Core Transfers Fetches account
   * details from AccountServiceAsync for account numbers and names
   *
   * @param bffActivity BffActivity to update
   */
  private void setDefaultValues(BffActivity bffActivity) {
    // Set other unmappable fields to default values
    bffActivity.setExpressDelivery(false);
    bffActivity.setAdditionalPrincipleAmount(null);
    bffActivity.setMemo(null);
    bffActivity.setDeliveryDate(null);
    bffActivity.setEndDate(null);
    bffActivity.setFrequency(null);
    bffActivity.setNumberOfActivities(null);
    bffActivity.setNumberOfRemainingActivities(null);
    bffActivity.setPaymentAmountType(null);
    bffActivity.setPayOnType(null);
    bffActivity.setDayToPay(null);
    bffActivity.setEditable(false);
    bffActivity.setCancelable(false);
    bffActivity.setCheckNumber(null);
    bffActivity.setRecurringId(null);
    bffActivity.setToAccountInternal(true);
    bffActivity.setFromAccountInternal(true);
    bffActivity.setAutomaticPaymentOn(false);
    bffActivity.setSeriesTemplate(false);
    bffActivity.setActivityClassification(null);
  }

  /**
   * Populates account details (number and name) from provided account list
   *
   * @param bffActivity BffActivity to update with account details
   * @param accountList List of internal accounts (can be null or empty)
   */
  private void populateAccountDetails(BffActivity bffActivity, List<InternalAccount> accountList) {
    List<InternalAccount> accounts = accountList != null ? accountList : new ArrayList<>();

    try {
      setAccountDetails(
          bffActivity.getFromAccountId(),
          accounts,
          (number, name) -> {
            bffActivity.setFromAccountNumber(number);
            bffActivity.setFromAccountName(name);
          });

      setAccountDetails(
          bffActivity.getToAccountId(),
          accounts,
          (number, name) -> {
            bffActivity.setToAccountNumber(number);
            bffActivity.setToAccountName(name);
          });

    } catch (Exception e) {
      // Set to null if account details mapping fails
      log.error("Failed to populate account details", e);
      bffActivity.setFromAccountNumber(null);
      bffActivity.setFromAccountName(null);
      bffActivity.setToAccountNumber(null);
      bffActivity.setToAccountName(null);
    }
  }

  /** Helper method to find account details by account ID and set them using the provided setter */
  private void setAccountDetails(
      String accountId, List<InternalAccount> accountList, AccountDetailsSetter setter) {
    if (accountId == null) {
      setter.setAccountDetails(null, null);
      return;
    }

    accountList.stream()
        .filter(account -> accountId.equalsIgnoreCase(account.getId()))
        .findFirst()
        .ifPresentOrElse(
            account ->
                setter.setAccountDetails(
                    account.getDisplayAccountNumber(), account.getDisplayName()),
            () -> setter.setAccountDetails(null, null));
  }

  /** Functional interface for setting account details */
  @FunctionalInterface
  private interface AccountDetailsSetter {
    void setAccountDetails(String accountNumber, String accountName);
  }
}
