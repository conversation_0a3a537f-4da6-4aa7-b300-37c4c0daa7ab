/* Copyright 2023 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.service.transferinfo;

import com.fitb.digital.bff.movemoney.client.xferandpayorc.XferAndPayOrcClient;
import com.fitb.digital.bff.movemoney.client.xferandpayorc.model.XferAndPayActor;
import com.fitb.digital.bff.movemoney.client.xferandpayorc.model.XferAndPayProfile;
import com.fitb.digital.bff.movemoney.model.bff.BffResponse;
import com.fitb.digital.bff.movemoney.model.bff.account.responses.Account;
import com.fitb.digital.bff.movemoney.model.bff.account.responses.AccountsResponse;
import com.fitb.digital.bff.movemoney.service.BlackoutDateService;
import java.io.File;
import java.math.BigDecimal;
import java.net.MalformedURLException;
import java.net.URL;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class TransferInfoService {
  private static final String EXTERNAL_TRANSFER_SUFFIX = "_XFER";
  private static final String BILLPAY = "BILLPAY";

  @Value("${fitb.digital.bff.movemoney.blackout-dates-filepath:/blackout_dates.json}")
  private String localBlackoutDatesFile = "default";

  @Value(
      "${fitb.digital.bff.movemoney.blackout-dates-url:https://onlinebanking.53.com/apps/ib/mbl/json/blackoutDates.json}")
  private String blackoutDatesUrl = "default";

  @Value("${fitb.digital.bff.movemoney.external-transfer-accounts.max:5}")
  private Integer maxExternalTransferAccounts = 5;

  private final XferAndPayOrcClient xferAndPayOrcClient;

  private final BlackoutDateService blackoutDateService;

  public TransferInfoService(
      XferAndPayOrcClient xferAndPayOrcClient, BlackoutDateService blackoutDateService) {
    this.xferAndPayOrcClient = xferAndPayOrcClient;
    this.blackoutDateService = blackoutDateService;
  }

  public AccountsResponse getTransferAccounts() {
    final XferAndPayProfile profileResponse = xferAndPayOrcClient.getTransferAndPayProfile();

    var accountsResponse = new AccountsResponse();

    accountsResponse.setStatus(BffResponse.SUCCESS);
    accountsResponse.setStatusCode(null);
    accountsResponse.setRetrievalErrors(new ArrayList<>());

    AccountListModel accountListModel = buildAccountsList(profileResponse.getActors());

    accountsResponse.setAccounts(accountListModel.getAccounts());
    accountsResponse.setMaxExternalTransferAccounts(maxExternalTransferAccounts);
    accountsResponse.setCurrentExternalTransferAccounts(accountListModel.getExternalAccounts());
    accountsResponse.setBlackoutDates(getBlackoutDates());
    accountsResponse.setRetrievalErrors(profileResponse.getRetrievalErrors());

    return accountsResponse;
  }

  private AccountListModel buildAccountsList(List<XferAndPayActor> actors) {
    var accounts = new ArrayList<Account>();

    // variables to track states so we only have to loop once over actors
    var externalTransferAccountCount = new AtomicInteger();
    var accountIdToGoalsMap = new HashMap<String, List<Account>>();

    actors.forEach(
        actor -> {
          var account = Account.fromXferAndPayActor(actor);
          addToExternalTransferAccountCount(account, externalTransferAccountCount);
          if (account.isAccountInternal()) {
            removeBillpayFromRecipients(account);
          }
          addToGoalMap(account, accountIdToGoalsMap);

          accounts.add(account);
        });

    var accountListModel = new AccountListModel();
    accountListModel.setAccounts(accounts);
    accountListModel.setExternalAccounts(externalTransferAccountCount.get());
    return accountListModel;
  }

  private static void removeBillpayFromRecipients(Account account) {
    account.getRecipients().removeIf(rec -> rec.getActivityType().equals(BILLPAY));
  }

  private static void handleInternalAccountDisplayOrder(
      Account account,
      AtomicInteger displayOrder,
      HashMap<String, BigDecimal> accountIdToSortOrderMap) {
    account.setDisplaySortOrder(new BigDecimal(displayOrder.get()));
    displayOrder.addAndGet(1);
    accountIdToSortOrderMap.put(account.getId(), account.getDisplaySortOrder());
  }

  private static void addToExternalTransferAccountCount(
      Account account, AtomicInteger externalTransferAccountCount) {
    if (account.getAccountType().endsWith(EXTERNAL_TRANSFER_SUFFIX))
      externalTransferAccountCount.addAndGet(1);
  }

  private static void addToGoalMap(
      Account account, HashMap<String, List<Account>> accountIdToGoalsMap) {
    if (account.isGoal()) {
      var accountGoals =
          accountIdToGoalsMap.getOrDefault(account.getGoalParentId(), new ArrayList<>());
      accountGoals.add(account);
      accountIdToGoalsMap.put(account.getGoalParentId(), accountGoals);
    }
  }

  private List<LocalDate> getBlackoutDates() {
    try {
      return blackoutDateService.getBlackoutDates(
          new URL(blackoutDatesUrl), new File(localBlackoutDatesFile));
    } catch (MalformedURLException e) {
      return blackoutDateService.getBlackoutDates(null, new File(localBlackoutDatesFile));
    }
  }

  @Data
  private static class AccountListModel {
    private List<Account> accounts;
    private int externalAccounts;
  }
}
