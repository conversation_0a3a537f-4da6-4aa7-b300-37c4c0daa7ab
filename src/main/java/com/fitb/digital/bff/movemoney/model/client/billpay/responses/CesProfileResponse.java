/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.client.billpay.responses;

import com.fitb.digital.bff.movemoney.model.FundingAccount;
import com.fitb.digital.bff.movemoney.model.client.CesBaseResponse;
import com.fitb.digital.bff.movemoney.model.client.CesResponse;
import com.fitb.digital.bff.movemoney.model.client.billpay.CesPayee;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class CesProfileResponse extends CesBaseResponse {
  private List<CesPayee> payees = new ArrayList<>();
  private List<FundingAccount> fundingAccounts = new ArrayList<>();

  public CesProfileResponse(CesResponse baseResponse) {
    this.setStatusCode(baseResponse.getStatusCode());
    this.setStatusReason(baseResponse.getStatusReason());
  }
}
