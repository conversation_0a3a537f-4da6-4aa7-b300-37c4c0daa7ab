/* Copyright 2024 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.bff;

import com.fitb.digital.lib.response.Error;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

// java:S1068 - False positive
@SuppressWarnings("java:S1068")
@AllArgsConstructor
@Builder
@Data
@NoArgsConstructor
public class BffErrorResponse implements BffResponse {
  private String status;
  private String statusCode;
  private String statusReason;
  private Error error;
  private List<String> retrievalErrors = new ArrayList<>();
}
