/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.util;

import java.util.List;

public class TDSCoreTransferAccountTypeCheck {
  private static final List<String> ALLOWED_ACCOUNTS = List.of("DDA", "SAV");

  public static boolean isAccountTypeAllowed(String fromAccountType, String toAccountType) {
    return ALLOWED_ACCOUNTS.contains(fromAccountType) && ALLOWED_ACCOUNTS.contains(toAccountType);
  }
}
