/* Copyright 2023 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.client.xferandpayorc.model;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;

@Data
public class XferAndPayAuthority {
  private String id;
  private XferAndPayActivityType activityType;
  private BigDecimal minimumAmount;
  private BigDecimal maximumAmount;
  private LocalDate earliestAvailableDate;
  private LocalDate earliestAvailableDateRecurring;
  private LocalDate latestAvailableDate;
  private boolean recurringSupported;
  private boolean memoSupported;
  private List<String> additionalAmountFields = new ArrayList<>();
  private List<XferAndPayDeliverySpeedOption> deliverySpeedOptions;
}
