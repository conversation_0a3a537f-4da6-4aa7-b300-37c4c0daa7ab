/* Copyright 2024 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.client.configuration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fitb.digital.bff.movemoney.exceptions.FeignClientException;
import com.fitb.digital.bff.movemoney.exceptions.FeignServerException;
import com.fitb.digital.lib.response.Error;
import com.fitb.digital.lib.response.ErrorResponse;
import feign.Response;
import feign.codec.ErrorDecoder;
import java.nio.charset.Charset;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.http.HttpStatus;

@RequiredArgsConstructor
@Slf4j
public class DefaultErrorDecoder implements ErrorDecoder {
  private final ObjectMapper objectMapper;

  @Override
  public Exception decode(final String methodKey, final Response response) {
    final HttpStatus httpStatus = HttpStatus.valueOf(response.status());
    final Error error = getError(response);
    // We are purposefully using separate exceptions for 4xx/5xx because we want 4xx responses to be
    // excluded
    // from retries/circuit breaks. See the resilience4j section of application.yml for more details
    return httpStatus.is4xxClientError()
        ? FeignClientException.builder().httpStatus(httpStatus).error(error).build()
        : FeignServerException.builder().httpStatus(httpStatus).error(error).build();
  }

  private Error getError(final Response response) {
    try {
      final String body = IOUtils.toString(response.body().asReader(Charset.defaultCharset()));
      log.debug("Raw response body from downstream service: {}", body);
      return objectMapper.readValue(body, ErrorResponse.class).getError();
    } catch (Exception e) {
      log.warn("Failed to deserialize error response from downstream service", e);
      return null;
    }
  }
}
