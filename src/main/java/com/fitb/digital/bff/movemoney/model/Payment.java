/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model;

import com.fasterxml.jackson.annotation.JsonAlias;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class Payment implements Serializable {
  private String id;

  /** CES Payee GUID for the payment */
  private String payeeId;

  private String payeeNickname;
  private BigDecimal amount;
  private LocalDate dueDate;
  private String recurringId;

  /** Memo is a small message given to the payee along with this payment */
  private String memo;

  /** The transaction frequency, FIS based. */
  private String frequency;

  /** The number of total transactions to execute as part of this schedule */
  private Integer numberOfTransactions;

  private Integer numberOfRemainingPayments;
  private LocalDate endDate;

  /** When the payment will be or was processed */
  private LocalDate clearedDate;

  /** The payment's current status, from FIS */
  private String processingStatus;

  @JsonAlias("eBillRule")
  private BillingRule billingRule;

  private boolean pending;
  private String confirmationNumber;
  private String checkNumber;
  private boolean editable;
  private boolean cancelable;
  private boolean inProcess;
}
