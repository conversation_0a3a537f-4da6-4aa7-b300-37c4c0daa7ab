/* Copyright 2022 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.bff.myadvance.responses;

import com.fitb.digital.bff.movemoney.model.bff.BffResponse;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class BffMyAdvanceResponse implements BffResponse {
  private String status;
  private String statusCode;
  private String statusReason;
  private List<String> retrievalErrors = new ArrayList<>(); // TODO: Do we actually need this?

  private String id;
  private String displayId;
  private BigDecimal amount;
  private String toAccountNumber;
  private String fromAccountNumber;
  private String toAccountDisplayName;
  private String fromAccountDisplayName;
  private String displayStatus;
}
