/* Copyright 2024 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.service.coretransfers;

import com.fitb.digital.bff.movemoney.client.CoreTransfersClient;
import com.fitb.digital.bff.movemoney.model.bff.BffResponse;
import com.fitb.digital.bff.movemoney.model.bff.CoreTransfers.requests.AccountIdTransferRequest;
import com.fitb.digital.bff.movemoney.model.bff.CoreTransfers.requests.AccountSource;
import com.fitb.digital.bff.movemoney.model.bff.CoreTransfers.requests.Channel;
import com.fitb.digital.bff.movemoney.model.bff.CoreTransfers.requests.TDSCoreTransferRequest;
import com.fitb.digital.bff.movemoney.model.bff.CoreTransfers.responses.TDSCoreTransferResponse;
import com.fitb.digital.bff.movemoney.model.bff.activity.BffActivity;
import com.fitb.digital.bff.movemoney.model.bff.activity.requests.BffActivityRequest;
import com.fitb.digital.bff.movemoney.model.bff.activity.responses.BffAddActivityResponse;
import com.fitb.digital.bff.movemoney.model.client.activity.responses.ClientActivity;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class CoreTransfersService {

  @Autowired private CoreTransfersClient coreTransfersClient;

  private final ModelMapper mapper = new ModelMapper();

  public BffAddActivityResponse addActivityTds(BffActivityRequest addActivity) {
    TDSCoreTransferRequest tdsCoreTransferRequest = new TDSCoreTransferRequest();
    tdsCoreTransferRequest.setChannel(Channel.MOBILE);
    tdsCoreTransferRequest.setReferenceId(addActivity.getRequestGuid());

    // fromAccount
    List<AccountIdTransferRequest> fromAccount = new ArrayList<>();
    AccountIdTransferRequest fromAccountRequest = new AccountIdTransferRequest();
    fromAccountRequest.setAccountSource(AccountSource.INTERNAL);
    fromAccountRequest.setDescription(addActivity.getActivityType());
    fromAccountRequest.setAmount(addActivity.getAmount().setScale(2, RoundingMode.DOWN));
    fromAccountRequest.setAccountId(addActivity.getFromAccountId());
    fromAccount.add(fromAccountRequest);
    tdsCoreTransferRequest.setFrom(fromAccount);

    // toAccount
    List<AccountIdTransferRequest> toAccount = new ArrayList<>();
    AccountIdTransferRequest toAccountRequest = new AccountIdTransferRequest();
    toAccountRequest.setAccountSource(AccountSource.INTERNAL);
    toAccountRequest.setDescription(addActivity.getActivityType());
    toAccountRequest.setAmount(addActivity.getAmount().setScale(2, RoundingMode.DOWN));
    toAccountRequest.setAccountId(addActivity.getToAccountId());
    toAccount.add(toAccountRequest);
    tdsCoreTransferRequest.setTo(toAccount);

    log.debug("tdsCoreTransferRequest: {}", tdsCoreTransferRequest);

    TDSCoreTransferResponse response = coreTransfersClient.tdsCoreTransfer(tdsCoreTransferRequest);

    log.debug("TDSCoreTransferResponse: {}", response);

    ClientActivity newClientActivity = getTDSCoreTransferResponse(response);

    BffActivity bffActivity = mapper.map(newClientActivity, BffActivity.class);

    var bffAddActivityResponse = new BffAddActivityResponse();
    bffAddActivityResponse.setActivity(bffActivity);

    // For one time immediate internal transfers, currently in prod below is set to false
    bffAddActivityResponse.getActivity().setAutomaticPaymentOn(false);
    bffAddActivityResponse.getActivity().setStatus("3");
    bffAddActivityResponse.getActivity().setDisplayStatus("Processed");
    bffAddActivityResponse.setStatus(BffResponse.SUCCESS);
    bffAddActivityResponse.getActivity().setType(addActivity.getActivityType());

    return bffAddActivityResponse;
  }

  private ClientActivity getTDSCoreTransferResponse(TDSCoreTransferResponse response) {
    ClientActivity clientActivity = new ClientActivity();

    // in BFF request we have bigdecimal and in response we have double
    if (response.getTransfers() != null && !response.getTransfers().isEmpty()) {
      clientActivity.setAmount(response.getTransfers().getFirst().getAmount().doubleValue());
      clientActivity.setCancelable(false);

      // Converting the string to LocalDateTime in EST
      LocalDateTime localDateTime =
          parseDateTime(response.getTransfers().getFirst().getCreatedDate());

      if (localDateTime != null) {
        LocalDateTime estDateTime =
            localDateTime
                .atZone(ZoneId.of("UTC"))
                .withZoneSameInstant(ZoneId.of("America/New_York"))
                .toLocalDateTime();
        clientActivity.setCreateTimestamp(estDateTime);
      } else {
        clientActivity.setCreateTimestamp(null);
      }

      clientActivity.setDisplayId(response.getTransfers().getFirst().getId());
      clientActivity.setEditable(false);
      clientActivity.setExpressDelivery(false);
      clientActivity.setId(response.getTransfers().getFirst().getId());
    }
    return clientActivity;
  }

  public static LocalDateTime parseDateTime(String dateTimeString) {
    try {
      // Truncate nanoseconds to milliseconds
      String truncatedDateTime = dateTimeString.substring(0, 23);
      DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS");
      return LocalDateTime.parse(truncatedDateTime, formatter);
    } catch (Exception e) {
      log.warn("Invalid date-time format dateTimeString: {}", dateTimeString);
    }
    return null;
  }
}
