/* Copyright 2022 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.service.account;

import com.fitb.digital.bff.movemoney.client.CesClient;
import com.fitb.digital.bff.movemoney.model.client.account.responses.ListResponse;
import com.fitb.digital.bff.movemoney.model.client.profile.responses.ProfileResponse;
import com.fitb.digital.bff.movemoney.service.CesClientService;
import java.util.concurrent.CompletableFuture;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Service
public class AccountServiceAsync extends CesClientService {
  public AccountServiceAsync(CesClient cesClient) {
    super(cesClient);
  }

  @Async
  public CompletableFuture<ProfileResponse> getProfileAsync(Boolean useProfileCache) {
    return CompletableFuture.completedFuture(
        super.callClient(() -> cesClient.getTransferAndPayProfile(!useProfileCache)));
  }

  @Async
  public CompletableFuture<ListResponse> getAccountListAsync(Boolean useAccountCache) {
    return CompletableFuture.completedFuture(
        super.callClient(() -> cesClient.getAccountsList(useAccountCache)));
  }
}
