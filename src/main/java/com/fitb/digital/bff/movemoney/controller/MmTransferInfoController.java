/* Copyright 2022 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.controller;

import static com.fitb.digital.bff.movemoney.featureflags.FeatureFlagNames.ORCHESTRATOR_ENABLED;

import com.fitb.digital.bff.movemoney.model.bff.account.responses.AccountsResponse;
import com.fitb.digital.bff.movemoney.service.transferinfo.TransferInfoService;
import com.fitb.digital.bff.movemoney.service.transferinfo.TransferInfoServiceV1;
import com.fitb.digital.lib.featureflag.service.FeatureFlagService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@CrossOrigin(
    origins = {
      "http://localhost:8080",
      "https://slapdpd002.info53.com",
      "https://developer-stg.info53.com",
      "https://developer.info53.com"
    })
public class MmTransferInfoController {
  private final TransferInfoServiceV1 transferInfoServiceV1;
  private final TransferInfoService transferInfoService;
  private final FeatureFlagService featureFlagService;

  @GetMapping(value = "/transfer/info", produces = MediaType.APPLICATION_JSON_VALUE)
  @Operation(summary = "Return all accounts that can be used in transfer operations.")
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = "401",
            description =
                "Unauthorized. Request was not processed because invalid authentication provided to access the target resource."),
        @ApiResponse(
            responseCode = "403",
            description =
                "Request is forbidden. Requested operation is not authorized for this user"),
        @ApiResponse(responseCode = "500", description = "Server error")
      })
  public AccountsResponse getTransferAccounts(
      @RequestParam(value = "useAccountCache", required = false, defaultValue = "false")
          Boolean useAccountCache,
      @RequestParam(value = "useProfileCache", required = false, defaultValue = "false")
          Boolean useProfileCache) {

    if (featureFlagService.isFeatureEnabled(ORCHESTRATOR_ENABLED)) {
      return transferInfoService.getTransferAccounts();
    } else {
      return transferInfoServiceV1.getTransferAccounts(useAccountCache, useProfileCache);
    }
  }
}
