/* Copyright 2022 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.bff.external.transfer.responses;

import com.fitb.digital.bff.movemoney.model.bff.BffSimpleResponse;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class BffExternalTransferAccountVerificationInfo extends BffSimpleResponse
    implements TrialDepositExternalAccount {
  private String id;
  private String institutionName;
  private boolean isHostInstitution;
  private boolean trialDepositVerification;
  private boolean realTimeVerification;
  private List<BffFinancialInstitutionLoginInfo> finInsLoginInfoList;
  private List<String> instantVerificationEnabledAcctTypes;
  private String accountStatus;
  private String accountType;
  private BigDecimal availableBalance;
  private String accountNickName;
  private String isHostAccount;
  private String accountGroup;
  private String realTimeVerificationStatus;
  private Integer realTimeRemainingAttempts;
  private Integer trialDepositRemainingAttempts;
  private LocalDate trialDepositStartDate;
  private String routingNumber;
  private String displayAccountNumber;
}
