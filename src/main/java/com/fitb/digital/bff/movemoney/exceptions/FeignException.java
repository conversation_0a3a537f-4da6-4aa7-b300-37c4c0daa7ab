/* Copyright 2024 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.exceptions;

import com.fitb.digital.lib.response.Error;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;
import org.springframework.http.HttpStatus;

// java:S1068 - False positive
@SuppressWarnings("java:S1068")
@AllArgsConstructor
@Getter
@ToString
@EqualsAndHashCode(callSuper = false)
public class FeignException extends RuntimeException {
  private final HttpStatus httpStatus;
  private final Error error;
}
