/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.service.activity;

import com.fitb.digital.bff.movemoney.model.bff.BffResponse;
import com.fitb.digital.bff.movemoney.model.bff.activity.BffActivity;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

@Data
@NoArgsConstructor
@SuppressWarnings("java:S1068")
public class ServiceGetActivityResponse implements BffResponse {
  private String status;

  private String statusCode;

  private String statusReason;

  @NonNull private List<String> retrievalErrors = new ArrayList<>();

  @NonNull private List<BffActivity> activities = new ArrayList<>();

  @NonNull private List<BffActivity> recurringActivities = new ArrayList<>();
}
