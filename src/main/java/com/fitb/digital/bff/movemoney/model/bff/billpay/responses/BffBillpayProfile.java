/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.bff.billpay.responses;

import com.fitb.digital.bff.movemoney.model.FundingAccount;
import com.fitb.digital.bff.movemoney.model.bff.BffSimpleResponse;
import com.fitb.digital.bff.movemoney.model.bff.billpay.BffPayee;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class BffBillpayProfile extends BffSimpleResponse {
  private List<BffPayee> payees = new ArrayList<>();
  private List<FundingAccount> fundingAccounts = new ArrayList<>();
}
