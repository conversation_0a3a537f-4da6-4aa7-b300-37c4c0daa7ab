/* Copyright 2023 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.featureflags;

import com.fitb.digital.bff.movemoney.constants.ExceptionStatus;
import com.fitb.digital.bff.movemoney.exceptions.BffException;
import com.fitb.digital.bff.movemoney.model.ActivityType;
import com.fitb.digital.lib.featureflag.service.FeatureFlagService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class ActivityFeatureFlagChecker {
  private final FeatureFlagService featureFlagService;

  public void checkFeatureEnabled(final String activityType) {
    final String paymentsFlag =
        featureFlagService.getFeatureVariant(
            FeatureFlagNames.PAYMENTS_STATUS, FeatureFlagVariants.ENABLED);
    final String transfersFlag =
        featureFlagService.getFeatureVariant(
            FeatureFlagNames.TRANSFERS_STATUS, FeatureFlagVariants.ENABLED);

    if (!paymentsFlag.equals(FeatureFlagVariants.ENABLED) && isPayments(activityType)) {
      throw BffException.serviceUnavailable(toExceptionStatus(paymentsFlag));
    } else if (!transfersFlag.equals(FeatureFlagVariants.ENABLED) && isTransfers(activityType)) {
      throw BffException.serviceUnavailable(toExceptionStatus(transfersFlag));
    }
  }

  private ExceptionStatus toExceptionStatus(final String featureFlag) {
    return switch (featureFlag) {
      case FeatureFlagVariants.PLANNED_MAINTENANCE -> ExceptionStatus.FEATURE_PLANNED_MAINTENANCE;
      case FeatureFlagVariants.OLB_REDIRECT -> ExceptionStatus.FEATURE_OLB_REDIRECT;
      default -> ExceptionStatus.FEATURE_HELP_CENTER_REDIRECT;
    };
  }

  private boolean isPayments(String activityType) {
    return StringUtils.equalsAny(
        activityType,
        ActivityType.WEB_PAYMENT,
        ActivityType.BILLPAY,
        ActivityType.INVOICED_PAYMENT,
        ActivityType.INTERNAL_TRANSFER);
  }

  private boolean isTransfers(String activityType) {
    return StringUtils.equalsAny(
        activityType,
        ActivityType.INTERNAL_TRANSFER,
        ActivityType.EXTERNAL_TRANSFER,
        ActivityType.INVOICED_PAYMENT);
  }
}
