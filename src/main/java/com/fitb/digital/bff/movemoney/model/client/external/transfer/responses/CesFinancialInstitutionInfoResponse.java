/* Copyright 2022 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.client.external.transfer.responses;

import com.fitb.digital.bff.movemoney.model.client.CesBaseResponse;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class CesFinancialInstitutionInfoResponse extends CesBaseResponse {
  private String institutionName;
  private boolean isHostInstitution;
  private boolean trialDepositVerification;
  private boolean realTimeVerification;
  private List<CesFinInsLoginInfo> finInsLoginInfoList;
  private List<String> instantVerificationEnabledAcctTypes;
}
