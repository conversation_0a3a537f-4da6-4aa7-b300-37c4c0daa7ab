/* Copyright 2020 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.BeanNotOfRequiredTypeException;
import org.springframework.beans.factory.NoSuchBeanDefinitionException;
import org.springframework.beans.factory.NoUniqueBeanDefinitionException;
import org.springframework.context.ApplicationContext;

@Slf4j
@Data
public class ClientContext {

  /**
   * The sub claim associated with this JWT for this request. Nullable. It could represent the cloud
   * user ID or the ECIF ID.
   */
  private String jwtSubId;

  private Long sessionExpiration;

  public Long getSessionExpiration() {
    if (sessionExpiration == null) {
      log.warn("null sessionExpiration, should never occur in AWS");
    }
    return sessionExpiration;
  }

  private ApplicationContext applicationContext;

  private String directoryId;
  private String ecifId;
  private String username;
  private String affiliate;

  /**
   * If something went wrong during processing (and we could intercept it using ExceptionConfig),
   * this will contain the root-cause of the error. Nullable.
   */
  private Throwable processingException;

  /**
   * Helper method to fetch a Spring bean by both name and type.
   *
   * @param beanName specific bean name to fetch
   * @param type expected type
   * @param <T> expected type
   * @return throws runtime exceptions if no bean was found or was of unexpected type; otherwise the
   *     spring bean requested
   * @throws NoSuchBeanDefinitionException if there is no such bean definition
   * @throws BeanNotOfRequiredTypeException if the bean is not of the required type
   */
  public <T> T getBean(String beanName, Class<T> type) {
    NoSuchBeanDefinitionException d;
    return applicationContext == null ? null : applicationContext.getBean(beanName, type);
  }

  /**
   * Helper method to fetch a Spring bean by type alone. For this method to work, there must be only
   * 1 bean of the matching type.
   *
   * @param type expected type
   * @param <T> expected type
   * @return throws runtime exceptions if no bean was found or was of unexpected type; otherwise the
   *     spring bean requested
   * @throws NoSuchBeanDefinitionException if no bean of the given type was found
   * @throws NoUniqueBeanDefinitionException if more than one bean of the given type was found
   */
  public <T> T getBean(Class<T> type) {
    return applicationContext == null ? null : applicationContext.getBean(type);
  }
}
