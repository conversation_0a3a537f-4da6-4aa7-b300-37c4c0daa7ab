/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.client.external.transfer.requests;

import static com.fitb.digital.lib.tokenization.SensitiveDataType.CRIT;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fitb.digital.lib.tokenization.SensitiveData;
import com.fitb.digital.lib.tokenization.model.KeyMetadata;
import com.fitb.digital.lib.tokenization.model.Tokenizable;
import java.util.Map;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@SuppressWarnings("java:S1948") // Tokenizable extends Serializable, not today SonarQube.
public class CesAddExternalAccountByInstantRequest implements Tokenizable {
  @SensitiveData(CRIT)
  private String accountNumber;

  @SensitiveData(CRIT)
  private String creditAccountNumber;

  private String accountNickName;
  private String accountTypeCode;
  private String creditRoutingNumber;
  private String routingNumber;

  @JsonProperty("TOKENIZED_FIELDS")
  protected Map<String, KeyMetadata> keyMetadata;
}
