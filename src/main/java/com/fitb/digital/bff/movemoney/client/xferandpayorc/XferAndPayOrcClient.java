/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.client.xferandpayorc;

import com.fitb.digital.bff.movemoney.client.configuration.DefaultFeignClientConfiguration;
import com.fitb.digital.bff.movemoney.client.xferandpayorc.model.XferAndPayActivity;
import com.fitb.digital.bff.movemoney.client.xferandpayorc.model.XferAndPayActivityRequest;
import com.fitb.digital.bff.movemoney.client.xferandpayorc.model.XferAndPayActivityResponse;
import com.fitb.digital.bff.movemoney.client.xferandpayorc.model.XferAndPayOrcBaseResponse;
import com.fitb.digital.bff.movemoney.client.xferandpayorc.model.XferAndPayProfile;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

@RefreshScope
@FeignClient(
    value = XferAndPayOrcClient.CLIENT_NAME,
    configuration = DefaultFeignClientConfiguration.class,
    url = "${feign.client.config.xfer-and-pay-orc.url}")
@SuppressWarnings("java:S1214")
public interface XferAndPayOrcClient {
  String CLIENT_NAME = "Xfer-and-Pay-Orc";

  @GetMapping(value = "/profile") // profile-movemoney to test delivery options.
  XferAndPayProfile getTransferAndPayProfile();

  @PostMapping(value = "/activity")
  XferAndPayActivity postTransferAndPayActivity(
      @RequestBody XferAndPayActivityRequest activityRequest);

  @DeleteMapping(value = "/activity")
  XferAndPayOrcBaseResponse cancelTransferAndPayActivity(
      @RequestParam String activityId, @RequestParam String activityType);

  @PutMapping(value = "/activity")
  XferAndPayActivity editTransferAndPayActivity(
      @RequestBody XferAndPayActivityRequest activityRequest);

  @GetMapping(value = "/activity")
  XferAndPayActivityResponse getTransferAndPayActivity();
}
