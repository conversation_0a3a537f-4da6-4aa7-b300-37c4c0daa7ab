/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.bff.account.responses;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class Recipient {
  private String id;
  private String activityType;
  private double minimumAmount;
  private long maximumAmount;
  private LocalDate earliestAvailableDate;
  private LocalDate earliestAvailableDateRecurring;
  private LocalDate latestAvailableDate;
  private List<BffDeliverySpeedOption> deliverySpeedOptions = new ArrayList<>();
  private boolean recurringSupported;
  private boolean memoSupported;
}
