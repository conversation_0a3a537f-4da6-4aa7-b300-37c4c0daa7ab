/* Copyright 2020 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.config;

import com.fitb.digital.bff.movemoney.exceptions.BffException;
import com.fitb.digital.bff.movemoney.exceptions.CesFeignException;
import com.fitb.digital.bff.movemoney.exceptions.FeignException;
import com.fitb.digital.bff.movemoney.model.ClientContextHolder;
import com.fitb.digital.bff.movemoney.model.bff.BffErrorResponse;
import com.fitb.digital.bff.movemoney.model.bff.BffResponse;
import com.fitb.digital.bff.movemoney.model.client.CesResponse;
import com.fitb.digital.lib.response.Error;
import com.fitb.digital.lib.response.InnerError;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.lang.Nullable;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.context.request.ServletWebRequest;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;
import org.springframework.web.util.HtmlUtils;

/** REST exception handlers defined at a global level for the application */
@SuppressWarnings({"squid:S1452"})
@ControllerAdvice
@Order(1)
@Slf4j
public class ExceptionConfig extends ResponseEntityExceptionHandler {

  @ExceptionHandler({CesFeignException.class})
  @ResponseBody
  public ResponseEntity<BffErrorResponse> cesFeignException(
      final CesFeignException e, final WebRequest request) {
    addExceptionToClientContext(e);
    log.error("CES Feign Exception Caught", e);
    final HttpStatus httpStatus = HttpStatus.valueOf(e.getStatusCode().value());
    final String innerCode =
        Optional.ofNullable(e.getCesResponse())
            .map(
                response ->
                    getInnerErrorCode(response.getStatus(), response.getStatusCode(), httpStatus))
            .orElse("ERROR");
    final BffErrorResponse response =
        BffErrorResponse.builder()
            .status(
                Optional.ofNullable(e.getCesResponse())
                    .map(CesResponse::getStatus)
                    .orElse("UNKNOWN_ERROR"))
            .statusCode(
                Optional.ofNullable(e.getCesResponse())
                    .map(CesResponse::getStatusCode)
                    .orElse("UNPROCESSABLE_CES_RESPONSE"))
            .statusReason(
                Optional.ofNullable(e.getCesResponse())
                    .map(CesResponse::getStatusReason)
                    .orElse("CES did not return a response the bff could process"))
            .error(
                Error.builder()
                    .code(httpStatus.name())
                    .message(e.getMessage())
                    .target(((ServletWebRequest) request).getRequest().getRequestURI())
                    .innerError(InnerError.builder().code(innerCode).build())
                    .build())
            .build();
    return new ResponseEntity<>(response, httpStatus);
  }

  @ExceptionHandler({FeignException.class})
  @ResponseBody
  public ResponseEntity<BffErrorResponse> feignException(
      final FeignException e, final WebRequest request) {
    addExceptionToClientContext(e);
    log.error("FeignException Caught", e);

    final Error error =
        Optional.ofNullable(e.getError())
            .orElseGet(
                () ->
                    Error.builder()
                        .code(e.getHttpStatus().name())
                        .message(e.getHttpStatus().getReasonPhrase())
                        .build());
    if (error.getTarget() == null) {
      error.setTarget(((ServletWebRequest) request).getRequest().getRequestURI());
    }
    final BffErrorResponse response =
        BffErrorResponse.builder()
            .status(e.getHttpStatus().name())
            .statusCode(e.getHttpStatus().value() + "")
            .statusReason(e.getHttpStatus().getReasonPhrase())
            .error(error)
            .build();
    return new ResponseEntity<>(response, e.getHttpStatus());
  }

  @ExceptionHandler({BffException.class})
  @ResponseBody
  public ResponseEntity<BffErrorResponse> bffException(
      final BffException e, final WebRequest request) {
    addExceptionToClientContext(e);
    log.error("BffException Caught", e);

    final BffErrorResponse response =
        BffErrorResponse.builder()
            .status(e.getStatus())
            .statusCode(e.getStatusCode())
            .statusReason(e.getStatusReason())
            .error(
                Error.builder()
                    .code(e.getHttpStatus().name())
                    .message(e.getStatusReason())
                    .target(((ServletWebRequest) request).getRequest().getRequestURI())
                    .innerError(
                        InnerError.builder()
                            .code(
                                getInnerErrorCode(
                                    e.getStatus(), e.getStatusCode(), e.getHttpStatus()))
                            .build())
                    .build())
            .build();
    return new ResponseEntity<>(response, e.getHttpStatus());
  }

  @Override
  protected ResponseEntity<Object> handleExceptionInternal(
      Exception ex,
      @Nullable Object body,
      HttpHeaders headers,
      HttpStatusCode status,
      WebRequest request) {
    addExceptionToClientContext(ex);
    if (ex instanceof MethodArgumentNotValidException mex) {
      return handleValidationExceptions(mex, request);
    } else {
      return super.handleExceptionInternal(ex, body, headers, status, request);
    }
  }

  private void addExceptionToClientContext(final Throwable throwable) {
    Optional.ofNullable(ClientContextHolder.getContext(false))
        .ifPresent(clientContext -> clientContext.setProcessingException(throwable));
  }

  private ResponseEntity<Object> handleValidationExceptions(
      final MethodArgumentNotValidException ex, final WebRequest request) {
    final List<String> errors =
        ex.getBindingResult().getAllErrors().stream()
            .map(
                error -> {
                  final String fieldName = ((FieldError) error).getField();
                  final String errorMessage = error.getDefaultMessage();
                  return "Field: %s, Error: %s".formatted(fieldName, errorMessage);
                })
            .toList();
    String requestURI = sanitize(((ServletWebRequest) request).getRequest().getRequestURI());
    final BffResponse response =
        BffErrorResponse.builder()
            .status(BffResponse.BFF_ERROR)
            .statusCode(HttpStatus.BAD_REQUEST.name())
            .retrievalErrors(errors)
            .error(
                Error.builder()
                    .code(HttpStatus.BAD_REQUEST.name())
                    .message(ex.getMessage())
                    .target(requestURI)
                    .innerError(InnerError.builder().code("FIELD_VALIDATION_ERROR").build())
                    .build())
            .build();
    return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
  }

  private String sanitize(String requestURI) {
    return HtmlUtils.htmlEscape(requestURI);
  }

  public String getInnerErrorCode(
      final String status, final String statusCode, final HttpStatus httpStatus) {
    // try to join for innerCode
    final String joined =
        Stream.of(status, statusCode)
            .filter(s -> s != null && !s.isEmpty())
            .collect(Collectors.joining("."));
    // both where null so we return httpStatus
    return joined.isEmpty() ? httpStatus.name() : joined;
  }
}
