/* Copyright 2019 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.config;

import static org.springframework.security.oauth2.jwt.NimbusJwtDecoder.withJwkSetUri;

import jakarta.servlet.http.HttpServletResponse;
import java.net.URI;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Predicate;
import lombok.extern.slf4j.Slf4j;
import org.apache.hc.client5.http.classic.HttpClient;
import org.apache.hc.client5.http.impl.classic.HttpClientBuilder;
import org.apache.hc.client5.http.impl.routing.DefaultProxyRoutePlanner;
import org.apache.hc.core5.http.HttpException;
import org.apache.hc.core5.http.HttpHost;
import org.apache.hc.core5.http.protocol.HttpContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Profile;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.convert.converter.Converter;
import org.springframework.http.HttpMethod;
import org.springframework.http.RequestEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.AuthenticationManagerResolver;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.oauth2.core.OAuth2TokenValidator;
import org.springframework.security.oauth2.jose.jws.SignatureAlgorithm;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.jwt.JwtValidators;
import org.springframework.security.oauth2.jwt.NimbusJwtDecoder;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationConverter;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationProvider;
import org.springframework.security.oauth2.server.resource.authentication.JwtGrantedAuthoritiesConverter;
import org.springframework.security.oauth2.server.resource.authentication.JwtIssuerAuthenticationManagerResolver;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.access.expression.WebExpressionAuthorizationManager;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

@Profile({"local-sit"})
@Slf4j
@Configuration
@EnableConfigurationProperties(value = OAuth2ResourceServerProperties.class)
public class LocalOAuth2ResourceServer {

  @SuppressWarnings("java:S3305") // sonar cant see the lambda invocation
  @Autowired
  com.fitb.digital.bff.movemoney.config.OAuth2ResourceServerProperties
      oauth2ResourceServerProperties;

  @Value("${spring-boot-actuator.scopes.read}")
  private String actuatorReadScope;

  @Value("${spring-boot-actuator.scopes.write}")
  private String actuatorWriteScope;

  @Value("${spring-boot-actuator.permissions.read}")
  private String actuatorReadPermission;

  @Value("${spring-boot-actuator.permissions.write}")
  private String actuatorWritePermission;

  @Lazy private static String hostname;

  @Lazy private static int port;

  @Value("${ftib.digital.bff.movemoney.threads.core-count:10}")
  private int coreSize;

  @Value("${ftib.digital.bff.movemoney.threads.max-count:30}")
  private int maxSize;

  @Value("${ftib.digital.bff.movemoney.threads.queue-count:10}")
  private int queueSize;

  @SuppressWarnings("java:S2696")
  @Value("${http.proxyHost:disabled}")
  public void setHostname(String hostname) {
    LocalOAuth2ResourceServer.hostname = hostname;
  }

  @SuppressWarnings("java:S2696")
  @Value("${http.proxyPort:8080}")
  public void setPort(int port) {
    LocalOAuth2ResourceServer.port = port;
  }

  private static RestTemplate getProxiedTemplate() {
    RestTemplate rest = null;
    if (!hostname.equals("disabled")) {
      HttpHost proxy = new HttpHost(hostname, port);

      HttpClient httpClient =
          HttpClientBuilder.create()
              .setRoutePlanner(
                  new DefaultProxyRoutePlanner(proxy) {

                    @Override
                    public HttpHost determineProxy(HttpHost target, HttpContext context)
                        throws HttpException {
                      return super.determineProxy(target, context);
                    }
                  })
              .build();
      rest =
          new RestTemplateBuilder(
                  restTemplate ->
                      restTemplate.setRequestFactory(
                          new HttpComponentsClientHttpRequestFactory(httpClient)))
              .build();
    }
    return rest;
  }

  @Bean
  SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
    return config(http)
        .oauth2ResourceServer(
            oauth2 -> oauth2.authenticationManagerResolver(authenticationManagerResolver()))
        .build();
  }

  @Bean
  JwtIssuerAuthenticationManagerResolver authenticationManagerResolver() {
    return new JwtIssuerAuthenticationManagerResolver(
        new TrustedIssuerJwtAuthenticationManagerResolver(
            Collections.unmodifiableCollection(oauth2ResourceServerProperties.getIssuers().values())
                ::contains));
  }

  @SuppressWarnings("java:S1192") // String constants for access vals
  public HttpSecurity config(HttpSecurity http) throws Exception {
    final WebExpressionAuthorizationManager readAccess =
        new WebExpressionAuthorizationManager(
            "hasAuthority('ISSUER_B2E') and hasAuthority('SCOPE_"
                + actuatorReadScope
                + "') and hasAuthority('GROUP_"
                + actuatorReadPermission
                + "') and hasAnyAuthority('ISSUER_B2E', 'ISSUER_B2E_M2M', 'ISSUER_B2E_OIDC', 'ISSUER_B2E_MULTI')");
    final WebExpressionAuthorizationManager writeAccess =
        new WebExpressionAuthorizationManager(
            "hasAuthority('ISSUER_B2E') and hasAuthority('SCOPE_"
                + actuatorWriteScope
                + "') and hasAuthority('GROUP_"
                + actuatorWritePermission
                + "') and hasAnyAuthority('ISSUER_B2E', 'ISSUER_B2E_M2M', 'ISSUER_B2E_OIDC', 'ISSUER_B2E_MULTI')");

    return http.httpBasic(AbstractHttpConfigurer::disable)
        .sessionManagement(
            configurer -> configurer.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
        .csrf(
            configurer ->
                configurer.ignoringRequestMatchers(
                    "/actuator/**")) // https://codecentric.github.io/spring-boot-admin/current/#_csrf_on_actuator_endpoints
        .exceptionHandling(
            configurer ->
                configurer.authenticationEntryPoint(
                    (req, rsp, e) -> rsp.sendError(HttpServletResponse.SC_UNAUTHORIZED)))
        .authorizeHttpRequests(
            configurer ->
                configurer
                    .requestMatchers(HttpMethod.GET, "/actuator/**")
                    .permitAll()
                    .requestMatchers(HttpMethod.GET, "/actuator/**")
                    // .access(readAccess)
                    .permitAll()
                    .requestMatchers("/actuator/**")
                    // .access(writeAccess)
                    .permitAll()
                    // We may need to reduce swagger access to sbaRead if the client credential cant
                    // get the
                    // group assigned.
                    .requestMatchers("/swagger-ui/**")
                    .access(readAccess)
                    .requestMatchers("/swagger-resources/**")
                    .access(readAccess)
                    .requestMatchers("/webjars/springfox-swagger-ui/**")
                    .access(readAccess)
                    .requestMatchers("/v3/api-docs")
                    .access(readAccess)
                    //        .requestMatchers("/service/**")
                    //        .hasAuthority("ISSUER_B2E")
                    .requestMatchers("/b2e/**")
                    .hasAnyAuthority(
                        "ISSUER_B2E", "ISSUER_B2E_M2M", "ISSUER_B2E_OIDC", "ISSUER_B2E_MULTI")
                    // Any additional request must require a B2C issued source
                    .anyRequest()
                    .hasAnyAuthority("ISSUER_B2C", "ISSUER_B2C_OLD"));
  }

  /**
   * Most of this is copy/pate from the JwtIssuerAuthenticationManagerResolver private class,
   * however we need to customize the JWT decoder to add the Es256 algorithm, since only RS256 is
   * supported by default.
   */
  class TrustedIssuerJwtAuthenticationManagerResolver
      implements AuthenticationManagerResolver<String> {

    private final Map<String, AuthenticationManager> authenticationManagers =
        new ConcurrentHashMap<>();
    private final Predicate<String> trustedIssuer;

    TrustedIssuerJwtAuthenticationManagerResolver(Predicate<String> trustedIssuer) {
      this.trustedIssuer = trustedIssuer;
    }

    @Override
    public AuthenticationManager resolve(String issuer) {
      if (this.trustedIssuer.test(issuer)) {
        return this.authenticationManagers.computeIfAbsent(
            issuer,
            k -> {
              Map<String, Object> configuration =
                  JwtDecoderProviderConfigurationUtils.getConfigurationForIssuerLocation(issuer);
              JwtDecoderProviderConfigurationUtils.validateIssuer(configuration, issuer);
              OAuth2TokenValidator<Jwt> jwtValidator =
                  JwtValidators.createDefaultWithIssuer(issuer);
              NimbusJwtDecoder.JwkSetUriJwtDecoderBuilder jwtDecoderBuilder =
                  withJwkSetUri(configuration.get("jwks_uri").toString());
              // Add each of the algorithms we need to support
              oauth2ResourceServerProperties.getAlgorithms().stream()
                  .forEach(
                      algorithm ->
                          jwtDecoderBuilder.jwsAlgorithm(SignatureAlgorithm.from(algorithm)));
              RestTemplate rest = getProxiedTemplate();
              NimbusJwtDecoder jwtDecoder =
                  (rest != null)
                      ? jwtDecoderBuilder.restOperations(rest).build()
                      : jwtDecoderBuilder.build();
              jwtDecoder.setJwtValidator(jwtValidator);

              // Add the additional granted authorities claims converter
              JwtAuthenticationProvider authenticationProvider =
                  new JwtAuthenticationProvider(jwtDecoder);
              JwtAuthenticationConverter jwtAuthenticationConverter =
                  new JwtAuthenticationConverter();
              authenticationProvider.setJwtAuthenticationConverter(jwtAuthenticationConverter);
              jwtAuthenticationConverter.setJwtGrantedAuthoritiesConverter(
                  new B2CB2EJwtGrantedAuthoritiesConverter());
              return authenticationProvider::authenticate;
            });
      }
      return null;
    }
  }

  class B2CB2EJwtGrantedAuthoritiesConverter
      implements Converter<Jwt, Collection<GrantedAuthority>> {
    private final Converter<Jwt, Collection<GrantedAuthority>> jwtGrantedAuthoritiesConverter =
        new JwtGrantedAuthoritiesConverter();

    @SuppressWarnings("java:S1192") // String constants for group
    @Override
    public Collection<GrantedAuthority> convert(Jwt source) {
      Collection<GrantedAuthority> authorities = jwtGrantedAuthoritiesConverter.convert(source);
      Optional<String> first =
          oauth2ResourceServerProperties.getIssuers().entrySet().stream()
              .filter(entry -> source.getIssuer().toString().equals(entry.getValue()))
              .map(Map.Entry::getKey)
              .findFirst();
      if (first.isPresent()) {
        authorities.add(new SimpleGrantedAuthority("ISSUER_" + first.get()));
      }
      // add the new authorities for B2E user_entitlements
      List<String> groups = Collections.emptyList();
      if (source.getClaim("group") instanceof List) {
        groups = (List<String>) source.getClaim("group");
      } else if (source.getClaim("group") instanceof String) {
        groups = new ArrayList<>();
        groups.add((String) source.getClaim("group"));
      } else if (source.getClaim("group") != null) {
        log.error("Group claim was not a list or a string: " + source.getClaim("group").toString());
      }
      groups.stream()
          .forEach(group -> authorities.add(new SimpleGrantedAuthority("GROUP_" + group)));
      return authorities;
    }
  }

  /**
   * Again copied from the spring source because its package level access there. This is a terrible
   * class to test.
   */
  @SuppressWarnings("java:S1075") // .well-known is... well known, no need to externlize.
  static class JwtDecoderProviderConfigurationUtils {
    private static final String OIDC_METADATA_PATH = "/.well-known/openid-configuration";
    private static final String OAUTH_METADATA_PATH = "/.well-known/oauth-authorization-server";
    private static RestTemplate proxiedRest = getProxiedTemplate();
    private static RestTemplate rest =
        (proxiedRest != null) ? proxiedRest : new RestTemplateBuilder().build();
    private static final ParameterizedTypeReference<Map<String, Object>> typeReference =
        new ParameterizedTypeReference<Map<String, Object>>() {};

    private JwtDecoderProviderConfigurationUtils() {
      throw new IllegalStateException("Utility class meant to be accessed statically.");
    }

    static Map<String, Object> getConfigurationForOidcIssuerLocation(String oidcIssuerLocation) {
      return getConfiguration(oidcIssuerLocation, oidc(URI.create(oidcIssuerLocation)));
    }

    static Map<String, Object> getConfigurationForIssuerLocation(String issuer) {
      URI uri = URI.create(issuer);
      return getConfiguration(issuer, oidc(uri), oidcRfc8414(uri), oauth(uri));
    }

    static void validateIssuer(Map<String, Object> configuration, String issuer) {
      String metadataIssuer = "(unavailable)";
      if (configuration.containsKey("issuer")) {
        metadataIssuer = configuration.get("issuer").toString();
      }
      if (!issuer.equals(metadataIssuer)) {
        throw new IllegalStateException(
            "The Issuer \""
                + metadataIssuer
                + "\" provided in the configuration did not "
                + "match the requested issuer \""
                + issuer
                + "\"");
      }
    }

    @SuppressWarnings("java:S1193") // This is Spring code, i'm not going to fix sonar here.
    private static Map<String, Object> getConfiguration(String issuer, URI... uris) {
      String errorMessage =
          "Unable to resolve the Configuration with the provided Issuer of " + "\"" + issuer + "\"";
      for (URI uri : uris) {
        try {
          RequestEntity<Void> request = RequestEntity.get(uri).build();
          ResponseEntity<Map<String, Object>> response = rest.exchange(request, typeReference);
          Map<String, Object> configuration = response.getBody();

          if (configuration.get("jwks_uri") == null) {
            throw new IllegalArgumentException("The public JWK set URI must not be null");
          }

          return configuration;
        } catch (IllegalArgumentException e) {
          throw e;
        } catch (RuntimeException e) {
          if (!(e instanceof HttpClientErrorException
              && ((HttpClientErrorException) e).getStatusCode().is4xxClientError())) {
            throw new IllegalArgumentException(errorMessage, e);
          }
          // else try another endpoint
        }
      }
      throw new IllegalArgumentException(errorMessage);
    }

    private static URI oidc(URI issuer) {
      return UriComponentsBuilder.fromUri(issuer)
          .replacePath(issuer.getPath() + OIDC_METADATA_PATH)
          .build(Collections.emptyMap());
    }

    private static URI oidcRfc8414(URI issuer) {
      return UriComponentsBuilder.fromUri(issuer)
          .replacePath(OIDC_METADATA_PATH + issuer.getPath())
          .build(Collections.emptyMap());
    }

    private static URI oauth(URI issuer) {
      return UriComponentsBuilder.fromUri(issuer)
          .replacePath(OAUTH_METADATA_PATH + issuer.getPath())
          .build(Collections.emptyMap());
    }
  }
}
