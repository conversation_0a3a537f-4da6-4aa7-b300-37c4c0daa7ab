/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.bff.account.responses;

import java.time.LocalDate;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class Source {
  private String id;
  private String activityType;
  private double minimumAmount;
  private long maximumAmount;
  private LocalDate earliestAvailableDate;
  private LocalDate earliestAvailableDateRecurring;
  private LocalDate latestAvailableDate;
  private boolean recurringSupported;
  private boolean memoSupported;
}
