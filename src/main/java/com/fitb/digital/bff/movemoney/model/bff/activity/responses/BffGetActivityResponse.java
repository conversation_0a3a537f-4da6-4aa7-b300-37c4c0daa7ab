/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.bff.activity.responses;

import com.fitb.digital.bff.movemoney.model.bff.BffResponse;
import com.fitb.digital.bff.movemoney.model.bff.activity.BffActivity;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@SuppressWarnings("java:S1068")
public class BffGetActivityResponse implements BffResponse {
  private String status;

  private String statusCode;
  private String statusReason;

  private List<String> retrievalErrors = new ArrayList<>();

  private List<BffActivity> recentActivities = new ArrayList<>();

  private List<BffActivity> upcomingActivities = new ArrayList<>();

  private List<BffActivity> recurringActivities = new ArrayList<>();

  private Boolean recentTruncated = false;

  private Boolean upcomingTruncated = false;
}
