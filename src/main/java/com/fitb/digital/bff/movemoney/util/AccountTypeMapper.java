/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.util;

import java.util.Map;

public class AccountTypeMapper {
  public enum AccountType {
    CHECKING,
    SAVINGS,
    CREDIT_CARD,
    LOAN,
    GOAL
  }

  private static final Map<String, AccountType> mapType =
      Map.of(
          "DDA", AccountType.CHECKING,
          "SAV", AccountType.SAVINGS,
          "I/L", AccountType.LOAN,
          "MLS", AccountType.LOAN,
          "MCC", AccountType.CREDIT_CARD,
          "MMC", AccountType.CREDIT_CARD,
          "VIS", AccountType.CREDIT_CARD,
          "SAV_GOAL", AccountType.GOAL);

  public static AccountType mapTextType(String type) {
    return mapType.get(type);
  }
}
