/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.bff.activity;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum RecurringActivityFrequency {
  ONE_TIME("ONE_TIME"),
  ONCE_A_WEEK("ONCE_A_WEEK"),
  ONCE_IN_2_WEEKS("ONCE_IN_2_WEEKS"),
  TWICE_A_MONTH("TWICE_A_MONTH"),
  ONCE_IN_3_WEEKS("ONCE_IN_3_WEEKS"),
  ONCE_IN_4_WEEKS("ONCE_IN_4_WEEKS"),
  ONCE_A_MONTH("ONCE_A_MONTH"),
  ONCE_IN_2_MONTHS("ONCE_IN_2_MONTHS"),
  ONCE_IN_3_MONTHS("ONCE_IN_3_MONTHS"),
  ONCE_IN_4_MONTHS("ONCE_IN_4_MONTHS"),
  ONCE_IN_6_MONTHS("ONCE_IN_6_MONTHS"),
  ONCE_A_YEAR("ONCE_A_YEAR"),
  /** External transfer only enum values */
  FIRST_BUSINESS_OF_EVERY_MONTH("FIRST_BUSINESS_OF_EVERY_MONTH"),
  LAST_BUSINESS_OF_EVERY_MONTH("LAST_BUSINESS_OF_EVERY_MONTH"),
  EVERY_3_WEEKS("EVERY_3_WEEKS"),
  FIRST_BUSINESS_DAY_OF_EVERY_2ND_MONTH("FIRST_BUSINESS_DAY_OF_EVERY_2ND_MONTH"),
  LAST_BUSINESS_DAY_OF_EVERY_2ND_MONTH("LAST_BUSINESS_DAY_OF_EVERY_2ND_MONTH"),
  FIRST_BUSINESS_DAY_OF_EVERY_3RD_MONTH("FIRST_BUSINESS_DAY_OF_EVERY_3RD_MONTH"),
  LAST_BUSINESS_DAY_OF_EVERY_3RD_MONTH("LAST_BUSINESS_DAY_OF_EVERY_3RD_MONTH"),
  FIRST_BUSINESS_DAY_OF_EVERY_6TH_MONTH("FIRST_BUSINESS_DAY_OF_EVERY_6TH_MONTH"),
  LAST_BUSINESS_DAY_OF_EVERY_6TH_MONTH("LAST_BUSINESS_DAY_OF_EVERY_6TH_MONTH"),
  FIRST_BUSINESS_DAY_OF_EVERY_12TH_MONTH("FIRST_BUSINESS_DAY_OF_EVERY_12TH_MONTH"),
  LAST_BUSINESS_DAY_OF_EVERY_12TH_MONTH("LAST_BUSINESS_DAY_OF_EVERY_12TH_MONTH");

  private final String value;

  @JsonValue
  public String getValue() {
    return value;
  }

  @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
  public static RecurringActivityFrequency of(String value) {
    for (RecurringActivityFrequency e : values()) {
      if (e.value.equalsIgnoreCase(value)) {
        return e;
      }
    }

    throw new IllegalArgumentException("RecurringActivityFrequency: unknown value: " + value);
  }
}
