/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.config;

import java.util.List;
import java.util.Map;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

// Only used by the local OAuth for SIT implemenation
@Data
@ConfigurationProperties("spring.security.oauth2.resourceserver.jwt")
public class OAuth2ResourceServerProperties {
  private Map<String, String> issuers;
  private List<String> algorithms;
}
