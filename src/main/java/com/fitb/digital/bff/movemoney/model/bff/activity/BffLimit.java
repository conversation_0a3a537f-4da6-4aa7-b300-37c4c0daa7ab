/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.bff.activity;

import com.fitb.digital.bff.movemoney.model.client.activity.responses.CesExternalTransferDirection;
import com.fitb.digital.bff.movemoney.model.client.activity.responses.CesExternalTransferSpeed;
import java.math.BigDecimal;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class BffLimit {
  private String hostAccountId;
  private String nonHostAccountId;
  private CesExternalTransferDirection direction;
  private CesExternalTransferSpeed speed;
  private BigDecimal transactionLimit;
  private BigDecimal dailyLimit;
  private BigDecimal remainingToday;
  private BigDecimal monthlyLimit;
  private BigDecimal remainingThisMonth;
  private BigDecimal outstandingLimit;
}
