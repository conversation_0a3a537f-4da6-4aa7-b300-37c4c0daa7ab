/* Copyright 2022 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.client.cashadvance.responses;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class CesTransfer {
  private String id;
  private String displayId;
  private LocalDateTime createTimestamp;
  private String processingStatus = "POSTED";
  private LocalDate processingDate;
  private boolean editable;
  private boolean immediate;
  private boolean cancelable;
  private boolean scheduled;
  private BigDecimal amount;
  private BigDecimal totalTransferAmount;
  private String toAccountNumber;
  private String fromAccountNumber;
  private String toAccountDisplayName;
  private String fromAccountDisplayName;
  private String displayStatus;
}
