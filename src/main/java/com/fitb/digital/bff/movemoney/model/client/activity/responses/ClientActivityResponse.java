/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.client.activity.responses;

import com.fitb.digital.bff.movemoney.model.client.CesBaseResponse;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class ClientActivityResponse extends CesBaseResponse {
  public ClientActivityResponse(String status, String statusCode, String statusReason) {
    super(status, statusCode, statusReason);
  }

  private List<String> retrievalErrors = new ArrayList<String>();

  private List<ClientActivity> activities = new ArrayList<>();

  private List<ClientActivity> recurringActivities = new ArrayList<>();
}
