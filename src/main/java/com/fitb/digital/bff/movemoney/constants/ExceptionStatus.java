/* Copyright 2024 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.constants;

import com.fitb.digital.bff.movemoney.featureflags.FeatureFlagVariants;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

// java:S1192 - Nothing wrong with keeping these values duplicated here. Rule not worth it
@SuppressWarnings("java:S1192")
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum ExceptionStatus {
  INVALID_DUE_DATE(
      "ERROR",
      "INVALID_DUE_DATE",
      "Due date is more than one day outside of today's date in eastern time for 'scheduleImmediately'"),
  ADD_PAYEE_CUSTOM_PAYEE(
      "BILL_PAY_ERROR",
      "PERSONAL_PAYEE_FLAG_NOT_TRUE",
      "Custom payees with no account number must set personalPayee flag to true."),
  FEATURE_PLANNED_MAINTENANCE(
      "ACTION_UNAVAILABLE",
      FeatureFlagVariants.PLANNED_MAINTENANCE,
      "Activity is unavailable due to planned maintenance."),
  FEATURE_OLB_REDIRECT(
      "ACTION_UNAVAILABLE",
      FeatureFlagVariants.OLB_REDIRECT,
      "Mobile payments are unavailable. Use online banking."),
  FEATURE_HELP_CENTER_REDIRECT(
      "ACTION_UNAVAILABLE",
      FeatureFlagVariants.HELP_CENTER_REDIRECT,
      "Mobile payments are unavailable. Please call the helpdesk, if you need assistance."),
  UNABLE_TO_FIND_ACCOUNT("ERROR", "UNABLE_TO_FIND_ACCOUNT", "Unable to find account with given ID"),
  SERVICE_UNAVAILABLE("ERROR", "SERVICE_UNAVAILABLE", "Service unavailable");

  private final String status;
  private final String code;
  private final String reason;
}
