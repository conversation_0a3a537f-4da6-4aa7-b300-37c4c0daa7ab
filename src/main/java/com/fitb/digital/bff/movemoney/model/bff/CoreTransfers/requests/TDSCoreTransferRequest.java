/* Copyright 2024 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.bff.CoreTransfers.requests;

import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class TDSCoreTransferRequest {

  @NotNull private Channel channel = Channel.MOBILE;
  @NotNull private String referenceId;
  private List<AccountIdTransferRequest> from;
  private List<AccountIdTransferRequest> to;
}
