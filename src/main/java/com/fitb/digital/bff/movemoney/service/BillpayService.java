/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.service;

import static com.fitb.digital.bff.movemoney.constants.TokenizationProfiles.CES_PROFILE;
import static com.fitb.digital.bff.movemoney.util.StatusMapper.mapCesToBffResponse;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fitb.digital.bff.movemoney.client.CesClient;
import com.fitb.digital.bff.movemoney.exceptions.CesFeignException;
import com.fitb.digital.bff.movemoney.model.FundingAccount;
import com.fitb.digital.bff.movemoney.model.PayeeAddress;
import com.fitb.digital.bff.movemoney.model.bff.BffSimpleResponse;
import com.fitb.digital.bff.movemoney.model.bff.billpay.BffPayee;
import com.fitb.digital.bff.movemoney.model.bff.billpay.requests.BffAddPayeeRequest;
import com.fitb.digital.bff.movemoney.model.bff.billpay.responses.*;
import com.fitb.digital.bff.movemoney.model.client.CesResponse;
import com.fitb.digital.bff.movemoney.model.client.billpay.CesPayee;
import com.fitb.digital.bff.movemoney.model.client.billpay.requests.CesPayeeRequest;
import com.fitb.digital.bff.movemoney.model.client.billpay.responses.*;
import com.fitb.digital.bff.movemoney.util.StatusMapper;
import com.fitb.digital.lib.tokenization.tokenizer.Tokenizer;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.modelmapper.ModelMapper;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

/*
 CES Billpay status codes:

 UNEXPECTED_ERROR,
 INELIGIBLE_FUNDING_ACCOUNT,
 NOT_ENROLLED_FOR_BILL_PAY,
 NO_PAYMENT_LIST_FOUND,
 NO_PAYMENT_FOUND,
 NO_PAYEE_FOUND,
 NO_PAYEE_LIST_FOUND,
 VERIFY_PAYMENT_CANCELLATION,
 VERIFY_PAYMENT_SCHEDULED,
 NO_BILL_PAY_PROFILE,
 NO_GLOBAL_PAYEES,
 NO_ADD_PAYEE,
 NO_UPDATE_PAYEE,
 NO_GLOBAL_PAYEE_ID,
 NO_FUNDING_ACCOUNTS,
 NO_CHANGES_MADE,
 FIELD_NOT_EDITABLE,
 NO_MULTIPLE_RECURRING,
 NO_MULTIPLE_RULES,
 ADDRESS_NOT_FOUND,
 ADDRESS_INVALID,
 ADDRESS_CHANGED,
 VALIDATION_REQUIRED,
 NOT_ELIGIBLE_FOR_EBILLS;

*/
@Service
@Slf4j
public class BillpayService extends CesClientService {
  private final Tokenizer tokenizer;

  public BillpayService(Tokenizer tokenizer, CesClient cesClient) {
    super(cesClient);
    this.tokenizer = tokenizer;
  }

  public BffPayeeResponse createPayee(BffAddPayeeRequest request) {
    if (StringUtils.isBlank(request.getNickname())) {
      request.setNickname(request.getName());
    }

    var cesPayee = CesPayee.from(request);

    var cesRequest = new CesPayeeRequest();
    cesRequest.setCesPayee(cesPayee);

    var response = new BffPayeeResponse();
    CesPayeeResponse cesPayeeResponse;
    try {
      cesPayeeResponse =
          super.callClient(() -> cesClient.addPayee(getTokenizedPayeeRequest(cesRequest)));
    } catch (CesFeignException ex) {
      if (ex.getStatusCode() == HttpStatus.UNPROCESSABLE_ENTITY) {

        // TODO - mapunproccessableEntity exists
        var responseEntity = mapUnprocessableEntity(ex.getCesResponse());
        responseEntity.setCesHttpStatus(ex.getStatusCode());
        // throw new UnprocessablePayeeException(responseEntity);
        return responseEntity;
      }

      throw ex; // rethrow this if it wasn't an unprocessable entity because we didn't handle it.
    }

    var mapper = new ModelMapper();
    var bffPayee = mapPayee(cesPayeeResponse.getCesPayee(), mapper);
    response.setBffPayee(bffPayee);
    StatusMapper.mapCesToBffResponse(cesPayeeResponse, response);
    response.setCesHttpStatus(HttpStatus.OK);

    return response;
  }

  public BffPayeeResponse updatePayee(BffPayee request) {
    var cesPayee = CesPayee.from(request);

    // The following fields are intentionally set due to a bug detokenizing date fields in
    // CES.
    // This should be fixed in a future release and we can remove this code.
    cesPayee.setLastPaymentDate(null);
    cesPayee.setNearestNewPaymentDate(null);
    cesPayee.setPaymentCutoffTime(null);
    if (cesPayee.getAccountNumber() == null) {
      cesPayee.setAccountNumber("");
    }

    var cesPayeeRequest = new CesPayeeRequest();
    cesPayeeRequest.setCesPayee(cesPayee);

    CesPayeeResponse payeeResponse =
        super.callClient(() -> cesClient.updatePayee(getTokenizedPayeeRequest(cesPayeeRequest)));

    var mapper = new ModelMapper();
    var bffPayee = mapPayee(payeeResponse.getCesPayee(), mapper);

    var bffPayeeResponse = new BffPayeeResponse();
    StatusMapper.mapCesToBffResponse(payeeResponse, bffPayeeResponse);
    bffPayeeResponse.setBffPayee(bffPayee);
    bffPayeeResponse.setCesHttpStatus(HttpStatus.OK);

    return bffPayeeResponse;
  }

  public BffSimpleResponse deletePayee(String payeeId) {
    CesDeletePayeeResponse payeeResponseEntity =
        super.callClient(() -> cesClient.deletePayee(payeeId));

    var bffResponse = new BffSimpleResponse();
    bffResponse.setStatus("SUCCESS");

    if (payeeResponseEntity != null) {
      StatusMapper.mapCesToBffResponse(payeeResponseEntity, bffResponse);
    }

    return bffResponse;
  }

  public BffGlobalPayeeResponse getGlobalPayees() {
    var clientGlobalPayees = super.callClient(cesClient::getGlobalPayees);

    var bffGlobalPayees = new BffGlobalPayeeResponse();

    mapCesToBffResponse(clientGlobalPayees, bffGlobalPayees);

    bffGlobalPayees.setGlobalPayees(mapClientPayees(clientGlobalPayees.getGlobalPayees()));

    return bffGlobalPayees;
  }

  private BffPayee mapPayee(CesPayee cesPayee, ModelMapper mapper) {
    BffPayee bff = new BffPayee();
    bff.setId(cesPayee.getId());
    bff.setName(cesPayee.getName());
    bff.setNickname(cesPayee.getNickname());
    if (cesPayee.getCesAddress() != null) {
      bff.setAddress(mapper.map(cesPayee.getCesAddress(), PayeeAddress.class));
    }
    bff.setAccountNumber(cesPayee.getAccountNumber());
    bff.setPhoneNumber(cesPayee.getPhoneNumber());
    if (cesPayee.getCesPayeeType() != null) {
      bff.setCesPayeeType(cesPayee.getCesPayeeType());
    }
    bff.setCategoryTypeId(cesPayee.getCategoryTypeId());
    if (cesPayee.getElectronicBillingEnabled() != null) {
      bff.setElectronicBillingEnabled(cesPayee.getElectronicBillingEnabled());
    }
    bff.setNearestNewPaymentDate(cesPayee.getNearestNewPaymentDate());
    bff.setPaymentCutoffTime(cesPayee.getPaymentCutoffTime());
    bff.setFisPayeeType(cesPayee.getType());
    return bff;
  }

  private List<BffGlobalPayee> mapClientPayees(List<ClientGlobalPayee> clientPayees) {
    return clientPayees.stream()
        .map(client -> new BffGlobalPayee(client.getName(), client.getCesPayeeType()))
        .collect(Collectors.toList());
  }

  public BffBillPayeeAccountResponse getTokenizedPayeeAccount(String payeeId) {
    var tokenizedAccountResponse =
        super.callClient(() -> cesClient.getTokenizedPayeeAccount(payeeId));

    var deTokenizedAccountResponse = getDetokenizedPayeeAccount(tokenizedAccountResponse);

    var bffBillPayeeAccount = new BffBillPayeeAccountResponse();
    mapCesToBffResponse(tokenizedAccountResponse, bffBillPayeeAccount);
    bffBillPayeeAccount.setAccountNumber(deTokenizedAccountResponse.getAccountNumber());

    return bffBillPayeeAccount;
  }

  public BffBillpayProfile getProfile() {
    var cesResponse = super.callClient(cesClient::getProfile);

    var bffResponse = new BffBillpayProfile();

    StatusMapper.mapCesToBffResponse(cesResponse, bffResponse);

    var mapper = new ModelMapper();
    bffResponse.setPayees(
        cesResponse.getPayees().stream()
            .map(cesPayee -> mapPayee(cesPayee, mapper))
            .collect(Collectors.toList()));
    bffResponse.setFundingAccounts(
        cesResponse.getFundingAccounts().stream()
            .map(cesAccount -> mapper.map(cesAccount, FundingAccount.class))
            .collect(Collectors.toList()));

    return bffResponse;
  }

  BffPayeeResponse mapUnprocessableEntity(CesResponse response) {
    var objectMapper = new ObjectMapper();
    objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    try {
      if (response != null) {
        var payeeResponse = new BffPayeeResponse();
        var body = response.getUnmappableBody();
        if (body != null) {
          log.debug("Unmappable Body: " + body);
          var cesPayeeResponse = objectMapper.readValue(body, CesPayeeResponse.class);
          if (cesPayeeResponse.getCesPayee() != null) {
            var mapper = new ModelMapper();
            var bffPayee = mapPayee(cesPayeeResponse.getCesPayee(), mapper);
            payeeResponse.setBffPayee(bffPayee);
          }
          StatusMapper.mapCesToBffResponse(cesPayeeResponse, payeeResponse);
        } else {
          payeeResponse.setStatus(response.getStatus());
          payeeResponse.setStatusCode(response.getStatusCode());
          payeeResponse.setStatusReason(response.getStatusReason());
        }
        return payeeResponse;
      }
    } catch (JsonProcessingException e) {
      log.error("Unable to Map Unprocessable Entity - JSON Exception", e);
    }
    return null;
  }

  private CesPayeeRequest getTokenizedPayeeRequest(CesPayeeRequest cesRequest) {
    return tokenizer.tokenize(CES_PROFILE, cesRequest);
  }

  private CesPayeeAccountResponse getDetokenizedPayeeAccount(
      CesPayeeAccountResponse payeeAccountResponse) {
    return tokenizer.detokenize(payeeAccountResponse);
  }
}
