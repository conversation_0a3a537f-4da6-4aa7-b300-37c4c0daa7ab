/* Copyright 2022 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.service.activity;

import com.fitb.digital.bff.movemoney.client.CesClient;
import com.fitb.digital.bff.movemoney.model.client.activity.responses.ClientActivityResponse;
import com.fitb.digital.bff.movemoney.service.CesClientService;
import java.util.concurrent.CompletableFuture;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Service
public class ActivityServiceAsync extends CesClientService {
  public ActivityServiceAsync(CesClient cesClient) {
    super(cesClient);
  }

  @Async
  public CompletableFuture<ClientActivityResponse> getActivityAsync() {
    return CompletableFuture.completedFuture(
        super.callClient(() -> cesClient.getTransferAndPayActivity()));
  }
}
