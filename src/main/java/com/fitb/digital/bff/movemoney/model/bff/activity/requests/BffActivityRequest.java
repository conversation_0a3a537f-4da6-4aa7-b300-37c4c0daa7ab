/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.bff.activity.requests;

import com.fitb.digital.bff.movemoney.model.ActivityRequestBase;
import java.util.UUID;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class BffActivityRequest extends ActivityRequestBase {
  private String requestGuid = UUID.randomUUID().toString();

  private boolean expressDelivery = false;

  private boolean scheduleImmediately = false;
}
