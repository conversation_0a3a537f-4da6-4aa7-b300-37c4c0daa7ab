/* Copyright 2022 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.controller;

import com.fitb.digital.bff.movemoney.model.bff.myadvance.requests.BffMyAdvanceRequest;
import com.fitb.digital.bff.movemoney.model.bff.myadvance.responses.BffMyAdvanceResponse;
import com.fitb.digital.bff.movemoney.service.MyAdvanceService;
import com.fitb.digital.lib.risk.RiskScore;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/myadvance")
@RequiredArgsConstructor
// TODO: @Api(tags = "My Advance")
public class MmMyAdvanceController {
  private final MyAdvanceService service;

  @RiskScore(activityType = "INTERNAL_TRANSFER")
  @PostMapping(value = "", produces = MediaType.APPLICATION_JSON_VALUE)
  public BffMyAdvanceResponse initiateAdvance(@RequestBody BffMyAdvanceRequest myAdvanceRequest) {
    // log.debug("My Advance Start", kv("request_body", myAdvanceRequest));

    return service.performCashAdvance(myAdvanceRequest);
  }
}
