/* Copyright 2024 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.controller.cd;

import com.fitb.digital.bff.movemoney.model.bff.cd.responses.CDFundingAccountsListResponse;
import com.fitb.digital.bff.movemoney.service.cd.CDFundingAccountService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@Slf4j
public class MmCDFundingAccountController {

  @Autowired private CDFundingAccountService cdfundingAccountService;

  /*
  look for documentation for transfer service call
  https://github.info53.com/pages/fitb-enterprise-software/transfer-cd-service/api-doc/#tag/Customer-Initiated-Funding-Accounts-API/operation/getAllEligibleFundingAccounts_1
   */
  @GetMapping(value = "/cd/funding-accounts", produces = MediaType.APPLICATION_JSON_VALUE)
  @Operation(summary = "Return eligible funding accounts for CD.")
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = "401",
            description =
                "Unauthorized. Request was not processed because invalid authentication provided to access the target resource."),
        @ApiResponse(
            responseCode = "403",
            description =
                "Request is forbidden. Requested operation is not authorized for this user"),
        @ApiResponse(responseCode = "500", description = "Server error")
      })
  public CDFundingAccountsListResponse getCDFundingAccounts() {
    return cdfundingAccountService.getCDFundingAccounts();
  }
}
