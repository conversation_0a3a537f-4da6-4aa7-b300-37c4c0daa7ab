/* Copyright 2022 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.service.externaltransfer;

import static com.fitb.digital.bff.movemoney.constants.TokenizationProfiles.CES_PROFILE;
import static com.fitb.digital.bff.movemoney.util.StatusMapper.mapCesToBffResponse;

import com.fitb.digital.bff.movemoney.client.CesClient;
import com.fitb.digital.bff.movemoney.constants.ExceptionStatus;
import com.fitb.digital.bff.movemoney.exceptions.BffException;
import com.fitb.digital.bff.movemoney.model.bff.BffResponse;
import com.fitb.digital.bff.movemoney.model.bff.BffSimpleResponse;
import com.fitb.digital.bff.movemoney.model.bff.external.transfer.ExternalAccountType;
import com.fitb.digital.bff.movemoney.model.bff.external.transfer.requests.BffAddAccountRequest;
import com.fitb.digital.bff.movemoney.model.bff.external.transfer.requests.BffUpdateExtAccountNicknameRequest;
import com.fitb.digital.bff.movemoney.model.bff.external.transfer.requests.BffVerifyTrialDepositsRequest;
import com.fitb.digital.bff.movemoney.model.bff.external.transfer.responses.*;
import com.fitb.digital.bff.movemoney.model.client.CesBaseResponse;
import com.fitb.digital.bff.movemoney.model.client.external.transfer.requests.CesAddExternalAccountByInstantRequest;
import com.fitb.digital.bff.movemoney.model.client.external.transfer.requests.CesAddExternalAccountRealTimeRequest;
import com.fitb.digital.bff.movemoney.model.client.external.transfer.requests.CesUpdateExtAccountNickNameRequest;
import com.fitb.digital.bff.movemoney.model.client.external.transfer.requests.CesVerifyTrialDepositsRequest;
import com.fitb.digital.bff.movemoney.model.client.external.transfer.responses.AuthorizedBrokerage;
import com.fitb.digital.bff.movemoney.model.client.external.transfer.responses.CesAddExternalAccountByInstantResponse;
import com.fitb.digital.bff.movemoney.model.client.external.transfer.responses.CesAddExternalAccountRealTimeResponse;
import com.fitb.digital.bff.movemoney.model.client.external.transfer.responses.CesFinancialInstitutionInfoResponse;
import com.fitb.digital.bff.movemoney.service.CesClientService;
import com.fitb.digital.bff.movemoney.service.external.ExternalAccountManagementAsync;
import com.fitb.digital.bff.movemoney.util.ExceptionUtil;
import com.fitb.digital.lib.tokenization.tokenizer.Tokenizer;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.modelmapper.Converter;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestParam;

@Slf4j
@Service
public class ExternalTransferService extends CesClientService {
  private final Tokenizer tokenizer;
  private final ExternalAccountManagementAsync externalAccountManagementAsync;
  private final VerificationInfoFactory verificationInfoFactory;

  public ExternalTransferService(
      CesClient cesClient,
      Tokenizer tokenizer,
      ExternalAccountManagementAsync externalAccountManagementAsync) {
    super(cesClient);
    this.tokenizer = tokenizer;
    this.externalAccountManagementAsync = externalAccountManagementAsync;
    this.verificationInfoFactory = new VerificationInfoFactory();
  }

  public BffBrokerageListResponse getBrokerages() {
    final var authorizedBrokerageListResponse = super.callClient(cesClient::getBrokerages);
    final var bffBrokerageListResponse = new BffBrokerageListResponse();

    mapCesToBffResponse(authorizedBrokerageListResponse, bffBrokerageListResponse);

    bffBrokerageListResponse.setBrokerages(
        authorizedBrokerageListResponse.getBrokerages().stream()
            .map(this::mapAuthorizedBrokerage)
            .collect(Collectors.toList()));

    return bffBrokerageListResponse;
  }

  public BffExternalTransferAccountVerificationInfo getAccountVerificationInfo(
      @RequestParam final String accountId) {
    final List<BffExternalAccount> externalTransferAccountsList = getExternalTransferAccounts();
    final BffExternalAccount transferAccount =
        externalTransferAccountsList.stream()
            .filter(account -> account.getId().equalsIgnoreCase(accountId))
            .findFirst()
            .orElseThrow(() -> BffException.notFound(ExceptionStatus.UNABLE_TO_FIND_ACCOUNT));

    final BffFinancialInstitutionInfo bankInfo =
        transferAccount.getAccountGroup().equals("Investment")
            ? getFinancialInstitutionInfoForBrokerageName(transferAccount.getInstitutionName())
            : getFinancialInstitutionInfoForRoutingNumber(transferAccount.getRoutingNumber());

    return verificationInfoFactory.create(bankInfo, transferAccount);
  }

  private List<BffExternalAccount> getExternalTransferAccounts() {
    try {
      return externalAccountManagementAsync.getExternalTransferAccounts().get();
    } catch (Exception e) {
      if (e.getCause() instanceof InterruptedException) {
        Thread.currentThread().interrupt();
      }
      ExceptionUtil.handleAsyncExceptions(
          e,
          "Feign Client exception getting external transfer accounts",
          "Exception getting external transfer accounts");
      return Collections.emptyList();
    }
  }

  public BffFinancialInstitutionInfo getFinancialInstitutionInfoForRoutingNumber(
      String routingNumber) {
    CesFinancialInstitutionInfoResponse response =
        super.callClient(() -> cesClient.getFinancialInstitutionInfo(routingNumber));

    var mapper = new ModelMapper();
    var fiInfo = mapper.map(response, BffFinancialInstitutionInfo.class);
    // Disable RT verification for now.
    fiInfo.setRealTimeVerification(false);
    return fiInfo;
  }

  public BffFinancialInstitutionInfo getFinancialInstitutionInfoForBrokerageName(
      String brokerageName) {
    CesFinancialInstitutionInfoResponse response =
        super.callClient(() -> cesClient.getBrokerageInfo(brokerageName));

    var mapper = new ModelMapper();
    var fiInfo = mapper.map(response, BffFinancialInstitutionInfo.class);

    // Disable RT verification for now.
    fiInfo.setRealTimeVerification(false);
    return fiInfo;
  }

  public BffAddAccountResponse addExternalAccount(BffAddAccountRequest request) {
    var mapper = new ModelMapper();
    var cesRequestBody = mapper.map(request, CesAddExternalAccountByInstantRequest.class);
    cesRequestBody.setAccountTypeCode(
        request.getAccountTypeCode().getCode()); // Formats account type to type expected by CES

    CesAddExternalAccountByInstantResponse response =
        super.callClient(
            () ->
                cesClient.addExternalAccountByInstant(
                    getTokenizedAddAccountRequest(cesRequestBody)));

    mapper = new ModelMapper();
    mapper.addConverter(getAccountTypeConverter());

    return mapper.map(response, BffAddAccountResponse.class);
  }

  public BffSimpleResponse updateExternalAccountNickname(
      BffUpdateExtAccountNicknameRequest request) {
    var mapper = new ModelMapper();
    var cesRequestBody = mapper.map(request, CesUpdateExtAccountNickNameRequest.class);

    CesBaseResponse response =
        super.callClient(() -> cesClient.updateExternalTranfserAccountNickname(cesRequestBody));

    return mapper.map(response, BffSimpleResponse.class);
  }

  public BffResponse deleteExternalAccount(String accountId) {
    var cesResponse = super.callClient(() -> cesClient.deleteExternalAccount(accountId));
    var cesResult = Objects.requireNonNull(cesResponse);
    var bffResult = new BffSimpleResponse();
    return mapCesToBffResponse(cesResult, bffResult);
  }

  CesAddExternalAccountRealTimeResponse cesAddExternalAccountRealTime(
      CesAddExternalAccountRealTimeRequest request) {
    return super.callClient(
        () -> cesClient.addExternalAccountByRealTime(tokenizer.tokenize(CES_PROFILE, request)));
  }

  @NotNull
  CesVerifyTrialDepositsRequest getCesVerifyTrialDepositsRequest(
      BffVerifyTrialDepositsRequest verifyTrialDepositsRequest) {
    CesVerifyTrialDepositsRequest cesRequest = new CesVerifyTrialDepositsRequest();
    cesRequest.setAccountId(verifyTrialDepositsRequest.getAccountId());

    var depositAmounts =
        Arrays.asList(
            verifyTrialDepositsRequest.getAmountOne(), verifyTrialDepositsRequest.getAmountTwo());
    cesRequest.setAmounts(depositAmounts);
    return cesRequest;
  }

  private Converter<String, ExternalAccountType> getAccountTypeConverter() {
    return context -> ExternalAccountType.fromStringIgnoreCase(context.getSource());
  }

  private CesAddExternalAccountByInstantRequest getTokenizedAddAccountRequest(
      CesAddExternalAccountByInstantRequest cesRequest) {
    return tokenizer.tokenize(CES_PROFILE, cesRequest);
  }

  private AccountFieldDetail getCheckingFieldDetail(AuthorizedBrokerage client) {
    int indexOfAccount = client.getFields().indexOf("checkingAccount");
    if (indexOfAccount != -1) {
      var detail = new AccountFieldDetail();
      if (client.getName().equals("Charles Schwab Brokerage")) {
        detail.setOptional(true);
        if (client.getFields().size() > indexOfAccount) {
          detail.setMessage(client.getFieldTips().get(indexOfAccount));
        }
      } else {
        if (client.getFieldTips().size() > indexOfAccount) {
          var toolTip = client.getFieldTips().get(indexOfAccount);
          detail.setToolTip(toolTip.isEmpty() ? null : toolTip);
        }
      }

      return detail;
    }

    return null;
  }

  private AccountFieldDetail getBrokerageFieldDetail(AuthorizedBrokerage client) {
    int indexOfAccount = client.getFields().indexOf("brokerageAccount");
    if (indexOfAccount != -1) {
      var detail = new AccountFieldDetail();

      var toolTip = client.getFieldTips().get(indexOfAccount);
      detail.setToolTip(toolTip.isEmpty() ? null : toolTip);

      return detail;
    }

    return null;
  }

  BrokerageEntry mapAuthorizedBrokerage(AuthorizedBrokerage client) {
    var entry = new BrokerageEntry();
    entry.setName(client.getName());
    entry.setRoutingNumber(client.getRoutingNumber());
    entry.setCreditRoutingNumber(client.getCreditRoutingNumber());

    AccountFieldDetail brokerageDetail = getBrokerageFieldDetail(client);
    AccountFieldDetail checkingDetail = getCheckingFieldDetail(client);

    entry.setBrokerageAccount(brokerageDetail);
    entry.setCheckingAccount(checkingDetail);

    return entry;
  }
}
