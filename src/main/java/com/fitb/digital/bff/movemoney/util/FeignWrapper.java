/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.util;

import com.fitb.digital.bff.movemoney.exceptions.CesFeignException;
import com.fitb.digital.bff.movemoney.model.bff.BffResponse;
import com.fitb.digital.bff.movemoney.model.bff.BffSimpleResponse;
import com.fitb.digital.bff.movemoney.model.client.CesResponse;
import java.util.function.Supplier;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class FeignWrapper {

  public static final String FEIGN_CLIENT_EXCEPTION = "FeignClientException";

  public static <T extends BffResponse> T mapFeignClientException(
      Supplier<T> allocator, CesFeignException e) {
    var response = e.getCesResponse();

    var result = allocator.get();
    if (response != null) {
      mapResponse(result, response);
    } else {
      result.setStatus(BffResponse.CLIENT_EXCEPTION);
      result.setStatusReason(e.getReason());
    }
    return result;
  }

  public static BffResponse fromCesResponse(CesResponse cesResponse) {
    BffResponse bffResponse = new BffSimpleResponse();
    mapResponse(bffResponse, cesResponse);
    return bffResponse;
  }

  private static void mapResponse(BffResponse bffResponse, CesResponse cesResponse) {
    bffResponse.setStatus(cesResponse.getStatus());
    bffResponse.setStatusCode(cesResponse.getStatusCode());
    bffResponse.setStatusReason(cesResponse.getStatusReason());
  }
}
