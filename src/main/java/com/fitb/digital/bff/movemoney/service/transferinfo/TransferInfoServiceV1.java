/* Copyright 2022 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.service.transferinfo;

import static com.fitb.digital.bff.movemoney.util.StatusMapper.mapCesToBffResponse;

import com.fitb.digital.bff.movemoney.exceptions.BffException;
import com.fitb.digital.bff.movemoney.featureflags.FeatureFlagNames;
import com.fitb.digital.bff.movemoney.model.bff.account.responses.Account;
import com.fitb.digital.bff.movemoney.model.bff.account.responses.AccountsResponse;
import com.fitb.digital.bff.movemoney.model.client.CesResponse;
import com.fitb.digital.bff.movemoney.model.client.account.responses.ListResponse;
import com.fitb.digital.bff.movemoney.model.client.profile.responses.ProfileResponse;
import com.fitb.digital.bff.movemoney.model.service.account.ServiceGetAccountResponse;
import com.fitb.digital.bff.movemoney.model.service.transferinfo.TransferListsResponse;
import com.fitb.digital.bff.movemoney.service.BlackoutDateService;
import com.fitb.digital.bff.movemoney.service.account.AccountService;
import com.fitb.digital.bff.movemoney.service.account.AccountServiceAsync;
import com.fitb.digital.bff.movemoney.util.ExceptionUtil;
import com.fitb.digital.lib.featureflag.service.FeatureFlagService;
import java.io.File;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class TransferInfoServiceV1 {
  private static final String EXTERNAL_TRANSFER_SUFFIX = "_XFER";

  @Value("${fitb.digital.bff.movemoney.blackout-dates-filepath:/blackout_dates.json}")
  private String localBlackoutDatesFile = "default";

  @Value(
      "${fitb.digital.bff.movemoney.blackout-dates-url:https://onlinebanking.53.com/apps/ib/mbl/json/blackoutDates.json}")
  private String blackoutDatesUrl = "default";

  private final Integer maxExternalTransferAccounts;

  private final AccountService accountService;
  private final AccountServiceAsync accountServiceAsync;
  private final BlackoutDateService blackoutDateService;

  private final FeatureFlagService featureFlagService;

  @Autowired
  public TransferInfoServiceV1(
      AccountService accountService,
      AccountServiceAsync accountServiceAsync,
      BlackoutDateService blackoutDateService,
      FeatureFlagService featureFlagService,
      @Value("${fitb.digital.bff.movemoney.external-transfer-accounts.max:5}")
          Integer maxExternalTransferAccounts) {
    this.accountService = accountService;
    this.accountServiceAsync = accountServiceAsync;
    this.blackoutDateService = blackoutDateService;
    this.maxExternalTransferAccounts = maxExternalTransferAccounts;
    this.featureFlagService = featureFlagService;
  }

  public AccountsResponse getTransferAccounts(Boolean useAccountCache, Boolean useProfileCache) {
    var transferListFetcher = new TransferListsFetcher(accountServiceAsync);
    var transferListsResponse = new TransferListsResponse();
    try {
      var accountListFuture =
          transferListFetcher.getTransferLists(useAccountCache, useProfileCache);
      transferListsResponse = accountListFuture.get();
    } catch (Exception e) {
      Thread.currentThread().interrupt();
      ExceptionUtil.handleAsyncExceptions(
          e,
          "FeignClient exception while building account list.",
          "Exception while building account list.");
    }

    var serviceResponse =
        combineResults(
            transferListsResponse.getProfileResponseResult(),
            transferListsResponse.getListResponseResult());
    var accountsResponse = new AccountsResponse();

    accountsResponse.setStatus(serviceResponse.getStatus());
    accountsResponse.setStatusCode(serviceResponse.getStatusCode());
    accountsResponse.setRetrievalErrors(serviceResponse.getRetrievalErrors());
    accountsResponse.setAccounts(serviceResponse.getAccounts());
    accountsResponse.setMaxExternalTransferAccounts(maxExternalTransferAccounts);
    accountsResponse.setCurrentExternalTransferAccounts(
        (int)
            accountsResponse.getAccounts().stream()
                .filter(acct -> acct.getAccountType().endsWith(EXTERNAL_TRANSFER_SUFFIX))
                .count());
    try {
      accountsResponse.setBlackoutDates(
          blackoutDateService.getBlackoutDates(
              new URL(blackoutDatesUrl), new File(localBlackoutDatesFile)));
    } catch (MalformedURLException e) {
      accountsResponse.setBlackoutDates(
          blackoutDateService.getBlackoutDates(null, new File(localBlackoutDatesFile)));
    }

    return accountsResponse;
  }

  @NotNull
  ServiceGetAccountResponse combineResults(
      ProfileResponse profileResponse, ListResponse accountList) {

    if (profileResponse == null || accountList == null) {
      throw BffException.internalServerError("Missing client results.");
    }

    var errorResponse = getCombineErrorResponse(profileResponse, accountList);
    if (errorResponse != null) return errorResponse;

    List<Account> accounts =
        new AccountCombiner(
                accountService,
                featureFlagService.isFeatureEnabled(FeatureFlagNames.ENABLE_ACCESS_360))
            .combine(profileResponse, accountList);

    var serviceGetAccountResponse = new ServiceGetAccountResponse();
    serviceGetAccountResponse.setAccounts(accounts);
    serviceGetAccountResponse.setStatus(CesResponse.SUCCESS);
    serviceGetAccountResponse.setRetrievalErrors(profileResponse.getRetrievalErrors());

    return serviceGetAccountResponse;
  }

  @Nullable
  private ServiceGetAccountResponse getCombineErrorResponse(
      ProfileResponse profileResponse, ListResponse accountList) {
    if (!CesResponse.SUCCESS.equals(profileResponse.getStatus())) {
      var serviceGetAccountResponse = new ServiceGetAccountResponse();
      mapCesToBffResponse(profileResponse, serviceGetAccountResponse);
      serviceGetAccountResponse.setRetrievalErrors(profileResponse.getRetrievalErrors());
      return serviceGetAccountResponse;
    }

    if (!CesResponse.SUCCESS.equals(accountList.getStatus())) {
      var serviceGetAccountResponse = new ServiceGetAccountResponse();
      mapCesToBffResponse(accountList, serviceGetAccountResponse);
      return serviceGetAccountResponse;
    }

    return null;
  }
}
