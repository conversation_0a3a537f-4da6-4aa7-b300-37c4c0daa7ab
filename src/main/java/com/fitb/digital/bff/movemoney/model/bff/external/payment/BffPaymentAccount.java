/* Copyright 2022 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.bff.external.payment;

import com.fitb.digital.bff.movemoney.model.client.webpayment.responses.CesWebpaymentFundingAccount;
import com.fitb.digital.bff.movemoney.util.NameNormalizer;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class BffPaymentAccount {
  private String id;
  private String accountType;
  private String displayAccountNumber; // accountNumber
  private String institutionName; // bankName
  private String accountNickName; // name
  private String routingNumber; // preferredRoutingNumber

  public static BffPaymentAccount fromCesWebPaymentAccount(CesWebpaymentFundingAccount acct) {
    var bffPaymentAccount = new BffPaymentAccount();

    bffPaymentAccount.setId(acct.getId());
    bffPaymentAccount.setAccountType(acct.getAccountType());
    bffPaymentAccount.setDisplayAccountNumber(acct.getAccountNumber());
    bffPaymentAccount.setInstitutionName(acct.getBankName());
    bffPaymentAccount.setAccountNickName(acct.getName());
    bffPaymentAccount.setRoutingNumber(acct.getPreferredRoutingNumber());

    return bffPaymentAccount;
  }

  public String getAccountNickName() {
    return NameNormalizer.normalizeAccountName(accountNickName);
  }
}
