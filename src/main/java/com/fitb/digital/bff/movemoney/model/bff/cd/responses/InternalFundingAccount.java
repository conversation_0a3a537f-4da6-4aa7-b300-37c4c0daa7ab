/* Copyright 2024 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.bff.cd.responses;

import java.math.BigDecimal;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class InternalFundingAccount {

  private String cloudAccountId;

  // accountNumber is currently tokenized from service and we are not detokenizing it as we dont
  // need it.
  private String accountNumber;

  private String accountNumberLast4;

  private String accountType;

  private String accountDisplayName;

  private String accountRoutingNumber;

  private BigDecimal availableBalance;
}
