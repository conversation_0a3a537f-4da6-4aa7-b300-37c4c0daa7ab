/* Copyright 2022 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.client.invoicedpayment.responses;

import java.time.LocalDate;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class CesInvoicedPaymentAccount {
  private String id;
  private String accountType;
  private String accountNumber;
  private String name;
  private String bankName;
  private String preferredRoutingNumber;
  private LocalDate accountSetupDate;
}
