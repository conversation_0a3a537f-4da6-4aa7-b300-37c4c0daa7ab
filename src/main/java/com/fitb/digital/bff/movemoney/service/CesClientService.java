/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.service;

import com.fitb.digital.bff.movemoney.client.CesClient;
import com.fitb.digital.bff.movemoney.exceptions.CesFeignException;
import com.fitb.digital.bff.movemoney.model.ClientContext;
import com.fitb.digital.bff.movemoney.model.ClientContextHolder;
import com.fitb.digital.bff.movemoney.model.client.CesResponse;
import java.util.function.Supplier;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class CesClientService {
  protected final CesClient cesClient;
  public static final String FEIGN_CLIENT_EXCEPTION = "FeignClientException";

  @SuppressWarnings("squid:S1452")
  public <T extends CesResponse> T callClient(Supplier<T> client) {
    try {
      return client.get();
    } catch (CesFeignException e) {
      ClientContext context = ClientContextHolder.getContext(false);
      log.error(
          String.format(
              "FeignClient caught an error. UID: %s Http Status: %d, message: %s ",
              (context != null) ? context.getJwtSubId() : "null",
              e.getStatusCode().value(),
              e.getMessage()));
      throw e;
    }
  }

  public String callGenericClient(Supplier<String> client) {
    try {
      return client.get();
    } catch (CesFeignException e) {
      ClientContext context = ClientContextHolder.getContext(false);
      log.error(
          String.format(
              "FeignClient caught an error. UID: %s Http Status: %d, message: %s ",
              (context != null) ? context.getJwtSubId() : "null",
              e.getStatusCode().value(),
              e.getMessage()));
      throw e;
    }
  }
}
