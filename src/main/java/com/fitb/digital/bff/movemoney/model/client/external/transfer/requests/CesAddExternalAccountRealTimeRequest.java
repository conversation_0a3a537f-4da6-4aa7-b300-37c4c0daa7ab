/* Copyright 2022 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.client.external.transfer.requests;

import com.fitb.digital.bff.movemoney.model.client.external.transfer.CesAuthenticationParam;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class CesAddExternalAccountRealTimeRequest extends CesAddExternalAccountRequest {
  private List<CesAuthenticationParam> cesAuthenticationParams;
}
