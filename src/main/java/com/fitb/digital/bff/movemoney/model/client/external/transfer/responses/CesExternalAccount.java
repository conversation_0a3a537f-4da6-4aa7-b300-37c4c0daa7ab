/* Copyright 2022 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.client.external.transfer.responses;

import java.time.LocalDate;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class CesExternalAccount {
  private String id;
  private String institutionName;
  private String accountStatus;
  private String accountType;
  private String availableBalance;
  private String accountNickName;
  private String isHostAccount;
  private String accountGroup;
  private String realTimeVerificationStatus;
  private Integer realTimeRemainingAttempts;
  private Integer trialDepositRemainingAttempts;
  private LocalDate trialDepositStartDate;
  private String routingNumber;
  private String displayAccountNumber;
}
