/* Copyright 2022 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.bff.invoicedpayment;

import com.fitb.digital.bff.movemoney.model.client.invoicedpayment.responses.CesInvoicedPaymentAccount;
import java.time.LocalDate;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class BffInvoicedPaymentAccount {
  private String id;
  private String accountType;
  private String displayAccountNumber;
  private String accountNickName;
  private String institutionName;
  private String routingNumber;
  private LocalDate accountSetupDate;

  public static BffInvoicedPaymentAccount mapInvoicedPaymentAccount(
      CesInvoicedPaymentAccount account) {
    var invoicedAccount = new BffInvoicedPaymentAccount();
    invoicedAccount.setId(account.getId());
    invoicedAccount.setAccountType(account.getAccountType());
    invoicedAccount.setDisplayAccountNumber(account.getAccountNumber());
    invoicedAccount.setAccountNickName(account.getName());
    invoicedAccount.setInstitutionName(account.getBankName());
    invoicedAccount.setRoutingNumber(account.getPreferredRoutingNumber());
    invoicedAccount.setAccountSetupDate(account.getAccountSetupDate());
    return invoicedAccount;
  }
}
