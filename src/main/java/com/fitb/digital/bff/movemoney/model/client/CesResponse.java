/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.client;

public interface CesResponse {
  String SUCCESS = "SUCCESS";
  String ERROR = "ERROR";
  String CLIENT_EXCEPTION = "EXCEPTION";

  String getUnmappableBody();

  void setUnmappableBody(String unmappableBody);

  String getStatus();

  void setStatus(String status);

  String getStatusCode();

  void setStatusCode(String statusCode);

  String getStatusReason();

  void setStatusReason(String statusReason);
}
