/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.bff.account.responses;

import com.fitb.digital.bff.movemoney.util.NameNormalizer;
import java.math.BigDecimal;
import java.time.LocalDate;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@SuppressWarnings("squid:S1068")
public class BffAccountDetail {
  private String accountDescription;
  private String displayAccountNumber;
  private String nickname;
  private String accountType;
  private String affiliate;
  private BigDecimal availableBalance;
  private BigDecimal ledgerBalance;
  private BigDecimal nextPaymentAmount;
  private LocalDate nextPaymentDate;
  private BigDecimal minimumPaymentAmount; // CC
  private BigDecimal lastStatementBalance; // CC
  private LocalDate lastStatementDate; // CC
  private BigDecimal initialPrincipal; // Loan
  private BigDecimal lastPaymentAmount;
  private LocalDate lastPaymentDate;
  private BigDecimal cashAdvanceRate;
  private BigDecimal purchaseRate;
  private BigDecimal cashLimitAmount;
  private BigDecimal cardLimitAmount;
  private BigDecimal interestRate;
  private BigDecimal maximumCashLimitAmount;
  private BigDecimal maximumCardLimitAmount;
  private BigDecimal payoffAmount;

  public String getNickname() {
    return NameNormalizer.normalizeNickname(nickname);
  }
}
