/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.client.activity.responses;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum CesExternalTransferSpeed {
  STANDARD("STANDARD", "Standard", 3),
  NEXTDAY("NEXTDAY", "Express", 1);

  private String code;
  private String label;
  private int businessDays;

  @JsonValue
  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public String getLabel() {
    return label;
  }

  public void setLabel(String label) {
    this.label = label;
  }

  public int getBusinessDays() {
    return businessDays;
  }

  public void setBusinessDays(int businessDays) {
    this.businessDays = businessDays;
  }

  @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
  public static CesExternalTransferSpeed of(String value) {
    for (CesExternalTransferSpeed e : values()) {
      if (e.code.equalsIgnoreCase(value)) {
        return e;
      }
    }

    throw new IllegalArgumentException("CesExternalTransferDirection: unknown value: " + value);
  }
}
