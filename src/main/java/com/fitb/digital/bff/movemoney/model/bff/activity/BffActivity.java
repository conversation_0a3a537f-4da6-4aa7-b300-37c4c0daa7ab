/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.bff.activity;

import com.fitb.digital.bff.movemoney.model.ActivityBase;
import java.time.LocalDate;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class BffActivity extends ActivityBase {
  @Deprecated(since = "api v2, client 4.2.0")
  private boolean toAccountInternal;

  @Deprecated(since = "api v2, client 4.2.0")
  public boolean toAccountInternal() {
    return toAccountInternal;
  }

  @Deprecated(since = "api v2, client 4.2.0")
  private boolean fromAccountInternal;

  @Deprecated(since = "api v2, client 4.2.0")
  public boolean fromAccountInternal() {
    return fromAccountInternal;
  }

  @Deprecated(since = "api v2, client 4.2.0")
  private LocalDate earliestAvailableDate;

  @Deprecated(since = "api v2, client 4.2.0")
  public LocalDate getEarliestAvailableDate() {
    return earliestAvailableDate;
  }

  @Deprecated(since = "api v2, client 4.2.0")
  private LocalDate latestAvailableDate;

  @Deprecated(since = "api v2, client 4.2.0")
  public LocalDate getLatestAvailableDate() {
    return latestAvailableDate;
  }

  @Deprecated(since = "api v2, client 4.2.0")
  // Flag to determine if automatic payment is set up for invoiceEnabled accounts
  private boolean automaticPaymentOn;

  @Deprecated(since = "api v2, client 4.2.0")
  public boolean getAutomaticPaymentOn() {
    return automaticPaymentOn;
  }

  // Flag to determine if the activity is the base series template, or a child/single instance of a
  // series.
  private boolean isSeriesTemplate = false;

  @Deprecated(since = "api v2, client 4.2.0")
  private String activityClassification;
}
