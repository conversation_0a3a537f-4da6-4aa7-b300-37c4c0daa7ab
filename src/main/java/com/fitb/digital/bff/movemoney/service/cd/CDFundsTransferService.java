/* Copyright 2024 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.service.cd;

import com.fitb.digital.bff.movemoney.client.CDClient;
import com.fitb.digital.bff.movemoney.model.bff.cd.requests.CDFundTransferRequest;
import com.fitb.digital.bff.movemoney.model.bff.cd.responses.CDFundTransferResponse;
import com.fitb.digital.bff.movemoney.service.BlackoutDateService;
import com.fitb.digital.bff.movemoney.util.CDFundUtil;
import java.io.File;
import java.net.MalformedURLException;
import java.net.URL;
import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class CDFundsTransferService {

  @Value("${fitb.digital.bff.movemoney.blackout-dates-filepath:/blackout_dates.json}")
  private String localBlackoutDatesFile = "default";

  @Value(
      "${fitb.digital.bff.movemoney.blackout-dates-url:https://onlinebanking.53.com/apps/ib/mbl/json/blackoutDates.json}")
  private String blackoutDatesUrl = "default";

  @Autowired private CDClient cdClient;
  @Autowired private CDFundUtil cdfundUtil;
  @Autowired private BlackoutDateService blackoutDateService;

  public CDFundTransferResponse redeemCD(CDFundTransferRequest request) {
    // Determine the cutoff time flag for cd-redemption
    List<LocalDate> blackoutDates;
    try {
      blackoutDates =
          blackoutDateService.getBlackoutDates(
              new URL(blackoutDatesUrl), new File(localBlackoutDatesFile));
    } catch (MalformedURLException e) {
      blackoutDates = blackoutDateService.getBlackoutDates(null, new File(localBlackoutDatesFile));
    }
    ZonedDateTime zdt = cdfundUtil.getCurrentZonedDateTime();
    boolean cutOff = cdfundUtil.isCutoffTime(zdt, blackoutDates);
    request.setCutoff(cutOff);
    CDFundTransferResponse response = cdClient.redeemCD(request);
    response.setCutoff(cutOff);
    return response;
  }
}
