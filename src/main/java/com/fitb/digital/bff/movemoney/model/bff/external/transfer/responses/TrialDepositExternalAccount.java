/* Copyright 2022 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.bff.external.transfer.responses;

import com.fitb.digital.bff.movemoney.service.externaltransfer.ExternalTransferAccountVerificationStatus;

public interface TrialDepositExternalAccount {

  public String getAccountStatus();

  public Integer getTrialDepositRemainingAttempts();

  public void setAccountStatus(String status);

  public default void patchTrialDepositStatus() {
    if (ExternalTransferAccountVerificationStatus.ACCOUNT_REQUIRES_APPROVAL
            .toString()
            .equals(getAccountStatus())
        && getTrialDepositRemainingAttempts() < 1)
      setAccountStatus(ExternalTransferAccountVerificationStatus.TRIAL_DEPOSITS_FAILED.toString());
  }
}
