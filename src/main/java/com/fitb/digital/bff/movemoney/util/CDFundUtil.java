/* Copyright 2025 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.util;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.List;
import org.springframework.stereotype.Service;

@Service
public class CDFundUtil {

  private static final ZoneId EST = ZoneId.of("America/New_York");

  public boolean isCutoffTime(ZonedDateTime currentZonedDateTime, List<LocalDate> holidayList) {

    // Check if time is after 19:30 EST
    if (currentZonedDateTime.getHour() > 19
        || (currentZonedDateTime.getHour() == 19 && currentZonedDateTime.getMinute() >= 30)) {
      return true;
    }
    // Check if it's a weekend
    if (currentZonedDateTime.getDayOfWeek().equals(DayOfWeek.SATURDAY)
        || currentZonedDateTime.getDayOfWeek().equals(DayOfWeek.SUNDAY)) {
      return true;
    }
    // check holiday calendar
    return holidayList.contains(currentZonedDateTime.toLocalDate());
  }

  public ZonedDateTime getCurrentZonedDateTime() {
    return ZonedDateTime.now(EST);
  }
}
