/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.bff.external.transfer.requests;

import com.fitb.digital.bff.movemoney.model.bff.external.transfer.ExternalAccountType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class BffAddAccountRequest {
  private String accountNickName;
  private String accountNumber;
  private ExternalAccountType accountTypeCode;
  private String creditAccountNumber;
  private String creditRoutingNumber;
  private String routingNumber;
}
