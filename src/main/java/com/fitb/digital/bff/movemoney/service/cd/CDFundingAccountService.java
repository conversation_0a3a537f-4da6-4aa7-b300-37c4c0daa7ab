/* Copyright 2024 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.service.cd;

import com.fitb.digital.bff.movemoney.client.CDClient;
import com.fitb.digital.bff.movemoney.model.bff.cd.responses.CDFundingAccountsListResponse;
import com.fitb.digital.bff.movemoney.model.client.account.responses.InternalAccount;
import com.fitb.digital.bff.movemoney.service.account.AccountServiceAsync;
import com.fitb.digital.bff.movemoney.util.ExceptionUtil;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class CDFundingAccountService {

  @Autowired private CDClient cdClient;

  @Autowired private AccountServiceAsync accountServiceAsync;

  public CDFundingAccountsListResponse getCDFundingAccounts() {

    // get the list of funding accounts from transfer-cd-service
    CDFundingAccountsListResponse response = cdClient.getCDFundingAccounts();

    List<InternalAccount> accountList = new ArrayList<>();
    try {
      // get AccountList for getting the accountDisplayName
      var accountListFuture = accountServiceAsync.getAccountListAsync(true).get();
      if (!accountListFuture.getAccounts().isEmpty()) {
        accountList = accountListFuture.getAccounts();
      }
    } catch (Exception e) {
      Thread.currentThread().interrupt();
      ExceptionUtil.handleAsyncExceptions(
          e,
          "FeignClient exception while building account list.",
          "Exception while retrieving account list.");
    }
    setAccountDisplayName(response, accountList);
    return response;
  }

  private void setAccountDisplayName(
      CDFundingAccountsListResponse response, List<InternalAccount> accountList) {
    // clear external accounts
    response.getExternalAccounts().clear();

    response
        .getInternalAccounts()
        .forEach(
            fundingAccount -> {
              // clear account number as it's not used
              fundingAccount.setAccountNumber("");
              /* match internal account by accountId and set the accountDisplayName as transfer cd
              service is not sending display name for UI. */
              accountList.forEach(
                  account -> {
                    if (account.getId().equalsIgnoreCase(fundingAccount.getCloudAccountId())) {
                      fundingAccount.setAccountDisplayName(account.getDisplayName());
                    }
                  });
            });
  }
}
