/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model;

import java.io.Serializable;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class PayeeAddress implements Serializable {
  private String streetLine1;
  private String streetLine2;
  private String city;
  private String stateOrProvince;
  private String postalCode;
  private boolean badAddressIndicator;
  private String country;

  public PayeeAddress(PayeeAddress other) {
    this.streetLine1 = other.streetLine1;
    this.streetLine2 = other.streetLine2;
    this.city = other.city;
    this.stateOrProvince = other.stateOrProvince;
    this.postalCode = other.postalCode;
    this.badAddressIndicator = other.badAddressIndicator;
    this.country = other.country;
  }
}
