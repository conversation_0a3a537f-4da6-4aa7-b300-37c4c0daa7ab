/* Copyright 2024 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.bff.cd.requests;

import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CDFundTransferRequest {
  @NotNull private String fromAccountId;
  @NotNull private String toAccountId;
  @NotNull private BigDecimal amount;
  @NotNull private Boolean cutoff;
  @NotNull private String transferType = "internal"; // defaulting to internal
}
