/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.client.account.responses;

import com.fitb.digital.bff.movemoney.model.client.CesBaseResponse;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class ListResponse extends CesBaseResponse {
  private List<InternalAccount> accounts = new ArrayList<>();
  private List<CesGoal> goals = new ArrayList<>();
}
