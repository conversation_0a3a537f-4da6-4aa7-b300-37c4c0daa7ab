  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
${Ansi.GREEN} :: Spring Boot ::        ${spring-boot.formatted-version}${Ansi.DEFAULT}

${Ansi.BRIGHT_BLUE}
#######                          #######                           ######
#       # ###### ##### #    #       #    #    # # #####  #####     #     #   ##   #    # #    #
#       # #        #   #    #       #    #    # # #    # #    #    #     #  #  #  ##   # #   #
#####   # #####    #   ######       #    ###### # #    # #    #    ######  #    # # #  # ####
#       # #        #   #    #       #    #    # # #####  #    #    #     # ###### #  # # #  #
#       # #        #   #    #       #    #    # # #   #  #    #    #     # #    # #   ## #   #
#       # #        #   #    #       #    #    # # #    # #####     ######  #    # #    # #    #
${Ansi.DEFAULT}
Application : ${spring.application.name}
Version : ${application.version:Development}
========================================================================================================================
