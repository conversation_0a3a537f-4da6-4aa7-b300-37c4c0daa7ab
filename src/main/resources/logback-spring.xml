<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <springProfile name="aws">
        <include resource="org/springframework/boot/logging/logback/defaults.xml" />
        <property name="LOG_FILE" value="${LOG_FILE:-${LOG_PATH:-${LOG_TEMP:-${java.io.tmpdir:-/tmp}}}/spring.log}"/>
        <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
            <encoder class="net.logstash.logback.encoder.LogstashEncoder">
                <jsonGeneratorDecorator class="net.logstash.logback.mask.MaskingJsonGeneratorDecorator">
                    <defaultMask>****</defaultMask>
                    <path>accountNumber</path>
                    <path>creditAccountNumber</path>
                </jsonGeneratorDecorator>
            </encoder>
        </appender>
        <include resource="org/springframework/boot/logging/logback/file-appender.xml" />
        <root level="INFO">
            <appender-ref ref="CONSOLE" />
            <appender-ref ref="FILE" />
        </root>
    </springProfile>

    <springProfile name="testautomation">
        <include resource="org/springframework/boot/logging/logback/defaults.xml" />
        <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
            <encoder class="net.logstash.logback.encoder.LogstashEncoder"/>
        </appender>
        <root level="INFO">
            <appender-ref ref="CONSOLE" />
        </root>
    </springProfile>

    <springProfile name="local-sit">
        <include resource="org/springframework/boot/logging/logback/defaults.xml" />
        <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
            <encoder class="net.logstash.logback.encoder.LogstashEncoder">
                <jsonGeneratorDecorator class="net.logstash.logback.mask.MaskingJsonGeneratorDecorator">
                    <defaultMask>****</defaultMask>
                    <path>accountNumber</path>
                    <path>creditAccountNumber</path>
                </jsonGeneratorDecorator>
            </encoder>
        </appender>
        <root level="INFO">
            <appender-ref ref="CONSOLE" />
        </root>
    </springProfile>


    <springProfile name="!aws &amp; !testautomation &amp; !local-sit">
        <include resource="org/springframework/boot/logging/logback/base.xml"/>
    </springProfile>

</configuration>
