cmdb: ${CMDB_ID:mo3607}
env: ${ENV:local}
OAUTH2_REDIRECT_URL: http://localhost:${server.port:8080}/swagger-ui/oauth2-redirect.html # for swagger-ui oauth2 flow to work in browser
springdoc:
  swagger-ui:
    disable-swagger-default-url: true
    url: /move-money-bff/v3/api-docs
    config-url: /move-money-bff/v3/api-docs/swagger-config
    tryItOutEnabled: true
    use-root-path: true
fitb:
  digital:
    bff:
      movemoney:
        blackout-dates-url: https://onlinebanking.53.com/apps/ib/mbl/json/blackoutDates.json
        blackout-dates-filepath: /blackoutDates.json
        client-context-filter:
          disabledPaths: /swagger, /actuator, /api-docs
        threads:
          core-count: 10
          max-count: 30
          queue-count: 10
        external-transfer-accounts:
          max: 5
    service:
      logging:
        db: false
        log-body: true
        log-request-headers: true

risk-score:
  jwk-set-uri-default: ${RISK_MICROSERVICE_CORE_API_URL:https://edc2kagkrc-vpce-04e3fad85e181e1ea.execute-api.us-east-2.amazonaws.com/api/}risk-microservice/JWKS
  jwk-set-uri-legacy: ${RISK_MICROSERVICE_CORE_API_URL_legacy:https://edc2kagkrc-vpce-04e3fad85e181e1ea.execute-api.us-east-2.amazonaws.com/api/}risk-microservice/JWKS
  jwk-set-uri-scf: ${RISK_MICROSERVICE_CORE_API_URL_scf:https://edc2kagkrc-vpce-04e3fad85e181e1ea.execute-api.us-east-2.amazonaws.com/api}/risk-microservice/JWKS
  algorithms:
    - RS512
  #This specifies the active profile, i.e. if no other profile is set this will be active.
  #profiles can be set by passing
  #JVM Args : -Dspring.profiles.active=dev
  #Environment Variables: export spring_profiles_active=dev
  #As annotations (we do this in tests): @ActiveProfiles("test")
  # and many other ways not valuable to iterate here.
spring:
  profiles:
    active: local
  #Because no active profile is specified, this will be the default, values will be overridden by active profiles
  #notice the line above is the "ACTIVE" profile, not the name of this profile, thus the default
  application:
    name: ${SERVICE_KEY:move-money-bff}
  cloud:
    aws:
      parameterstore:
        enabled: false
      secretsmanager:
        enabled: false
    vault:
      #      https://cloud.spring.io/spring-cloud-vault/reference/html/#vault.config.backends.kv.versioned
      #      Vault looks for secrets in the following locations:
      #      /secret/{application}/{profile}
      #      /secret/{application}
      #      /secret/{default-context}/{profile}
      #      /secret/{default-context}
      #      Secrets can be obtained from other contexts within the key-value backend by adding their paths to the
      #      application name, separated by commas.
      #      For example, given the application name usefulapp,mysql1,projectx/aws, each of these folders will be used:
      #      /secret/usefulapp
      #      /secret/mysql1
      #      /secret/projectx/aws
      application-name: ${cmdb}/launch-darkly/${env},${cmdb}/application,${cmdb}/${spring.application.name},${cmdb}/unbound/public.cloud/app-local-account,${cmdb}/unbound/public.cloud/app-tweak,${cmdb}/unbound/public.cloud/client-temp,${cmdb}/unbound/public.cloud/ccn-key,${cmdb}/unbound/public.cloud/crit-key,${cmdb}/unbound/public.cloud/ncrit-key,${cmdb}/unbound/public.cloud/ssn-key #The name of your application, drives the locations SCV tries to load properties from
      enabled: false
      generic:
        enabled: false
      kv:
        enabled: true
        backend: kv
        profile-separator: '/'
        default-context: ${cmdb}
      connection-timeout: 5000
      read-timeout: 15000
#Define our cmdb tag for later reference.
tokenization:
  type: "ROT13"

resilience4j:
  circuitbreaker:
    configs:
      default:
        slidingWindowSize: #{120*1000}
        minimumNumberOfCalls: 10
        permittedNumberOfCallsInHalfOpenState: 5
        automaticTransitionFromOpenToHalfOpenEnabled: true
        waitDurationInOpenState: #{60*1000}
        failureRateThreshold: 50
        eventConsumerBufferSize: 10
        ignoreExceptions:
          - com.fitb.digital.bff.movemoney.exceptions.CesFeignException
          - com.fitb.digital.bff.movemoney.exceptions.FeignClientException
    instances:
      customer:
        baseConfig: default
  retry:
    configs:
      default:
        maxAttempts: 3
        waitDuration: 100
        ignoreExceptions:
          - com.fitb.digital.bff.movemoney.exceptions.CesFeignException
          - com.fitb.digital.bff.movemoney.exceptions.FeignClientException
    instances:
      account:
        baseConfig: default
      customer:
        baseConfig: default
      transaction:
        baseConfig: default
feign:
  client:
    config:
      CesShortTimeout:
        connectTimeout: 5000
        readTimeout: 10000
      default:
        connectTimeout: 5000
        readTimeout: 30000
        loggerLevel: FULL
      ces-proxy:
        url: ${ces_proxy_uri}
        legacy: ${ces_proxy_uri_legacy}
        scf: ${ces_proxy_uri_scf}
      transfer-cd:
        url: ${TRANSFER_CD_SERVICE_URL}
      core-transfer-tds:
        url: ${CORETRANSFER_TDS_SERVICE_URL}
      xfer-and-pay-orc:
        url: ${xfer_and_pay_orc_uri:https://zl4aiu3rqj-vpce-04e3fad85e181e1ea.execute-api.us-east-2.amazonaws.com/api/xferandpayorc-service}
        legacy: ${xfer_and_pay_orc_uri_legacy:https://zl4aiu3rqj-vpce-04e3fad85e181e1ea.execute-api.us-east-2.amazonaws.com/api/xferandpayorc-service}
        scf: ${xfer_and_pay_orc_uri_scf:https://zl4aiu3rqj-vpce-04e3fad85e181e1ea.execute-api.us-east-2.amazonaws.com/api/xferandpayorc-service}
  httpclient:
    #This is the limit of simultaneous feign calls we can make to the same host:port (default was 50)
    max-connections-per-route: 300
    max-connections: 600
spring-boot-actuator:
  scopes:
    read: ${spring-boot-actuator-scopes-read:consumer-mobile.nube.53.com/actuator/read} # these are example scopes that will need to be created in B2E ping, we generally scope them to the aws account, not the microservice
    write: ${spring-boot-actuator-scopes-write:consumer-mobile.nube.53.com/actuator/write}
  permissions:
    read: ${spring-boot-actuator-permissions-read:g.consumer_mobile_actuator_read} #These are example ed groups that will need to be created in ED, and request for the user or the role., we generally scope them to the aws account, not the microservice
    write: ${spring-boot-actuator-permissions-write:g.consumer_mobile_actuator_admin}
logging:
  level:
    ROOT: WARN
#    org.apache.tomcat: DEBUG
#    org.springframework.boot.web.embedded.tomcat: DEBUG
server:
  shutdown:
    grace-period: 30s
  tomcat:
    accesslog:
      enabled: true
      directory: ${TOMCAT_LOG_DIR:logs}
      request-attributes-enabled: true
    use-relative-redirects: true
    threads:
      max: 600
  max-http-request-header-size: 10KB
management: #Configures actuator
  info:
    env:
      enabled: true
  endpoints:
    enabled-by-default: true
    web:
      exposure:
        include: health, env, sessions, jolokia, caches, prometheus, metrics, threaddump, auditevents, beans, conditions, info, loggers, mappings, scheduledtasks, logfile
        exclude: refresh, heapdump, restart, pause, resume, httptrace, httpstrace, configprops, shutdown, features
  endpoint:
    env:
      keys-to-sanitize:
        - .*password.*
        - .*pass$
        - .*id.*
        - .*secret.*
        - .*key.*
        - .*token.*
        - .*credential.*
        - .*certificate.*
        - .*vcap_services$
        - ${cmdb}-eclient
        - ${cmdb}.kms
        - .*tweak.*
        - .*sun.java.command.*
    metrics:
      enabled: true
    prometheus:
      enabled: true
    loggers:
      enabled: true
    jolokia:
      config:
        debug: true
  health:
    show-details: "ALWAYS"
    vault.enabled: false
featureflag:
  sdk-key: ${ld-digital-banking-sdk-key:test-key}
--- #This is a seperator between profiles, the same can be done with files.
spring:
  config:
    activate:
      on-profile: aws
    import: aws-parameterstore:/config/application/, optional:vault://
  security:
    oauth2:
      resourceserver:
        jwt:
          issuers:
            B2C: ${oauth_b2c_issuer_uri} #property comes from AWS SSM (Parameter Store). Its shared by all the applications in the vpc that need to validate JWT tokens
            B2C_OLD: ${oauth_b2c_old_issuer_uri}
            B2E: ${oauth_b2e_issuer_uri}
            B2E_M2M: ${oauth_b2e_okta_m2m_issuer_uri}
            B2E_OIDC: ${oauth_b2e_okta_oidc_issuer_uri}
            B2E_MULTI: ${oauth_b2e_okta_multi_issuer_uri}
          algorithms:
            - RS256
            - ES256
  cloud:
    aws:
      parameterstore:
        enabled: true
      secretsmanager:
        enabled: true
  boot:
    admin:
      client:
        url: ${spring-boot-admin-url}
        auto-deregistration: true
server:
  ssl-auto-configure:
    enabled: true
featureflag:
  sdk-key: ${ld-digital-banking-sdk-key}
logging:
  level:
    com.fitb: INFO
    org.springframework: INFO
---
spring:
  config:
    activate:
      on-profile: DEV,TRIBE_DEV
  cloud:
    vault:
      authentication: TOKEN # Connecting vault using token for dev, the secret is already added as part of ecs env variable
      token: ${hashi_vault_secret}
feign:
  client:
    config:
      default:
        connectTimeout: 5000
        readTimeout: 30000
        loggerLevel: HEADERS
      ces-proxy:
        url: ${ces_proxy_uri:http://localhost:3001}
        legacy: ${ces_proxy_uri_legacy:http://localhost:3001}
        scf: ${ces_proxy_uri_scf:http://localhost:3001}
      transfer-cd:
        url: ${TRANSFER_CD_SERVICE_URL:http://localhost:3001}
      core-transfer-tds:
        url: ${CORETRANSFER_TDS_SERVICE_URL:http://localhost:3001}
risk-score:
  disabled: true
---
spring:
  main:
    banner-mode: "OFF"
  config:
    activate:
      on-profile: SIT,UAT,PROD
  cloud.vault:
    enabled: true
    uri: ${hashi_vault_uri} #because bootstrap runs early, these must be environment variables (or hard coded), and in our case are set by terraform at Apply time.
    authentication: APPROLE
    app-role:
      role-id: ${hashi_vault_role}
      secret-id: ${hashi_vault_secret}
tokenization:
  type: FORTANIX
#ukc:
#  host: ${unbound_uri}
#  environment: dev
#  applicationName: ${cmdb}
#  partitionName: "public.cloud"
#  clientRegistrationName: ${cmdb}-eclient
#  clientRegistrationCode: ${${cmdb}-eclient}
#  username: ${cmdb}.kms@${ukc.partitionName}
#  password: ${${cmdb}.kms.${ukc.environment}}
#  keySets:
#    http:
#      NCRIT:
#        keyName: ${cmdb}/unbound/${ukc.partitionName}/ncrit-key
#        tweak: ${cmdb}/unbound/${ukc.partitionName}/app-tweak
#        purpose: NCRIT
#      CRIT:
#        keyName: ${cmdb}/unbound/${ukc.partitionName}/crit-key
#        tweak: ${cmdb}/unbound/${ukc.partitionName}/app-tweak
#        purpose: CRIT
#      SSN:
#        keyName: ${cmdb}/unbound/${ukc.partitionName}/ssn-key
#        tweak: ${cmdb}/unbound/${ukc.partitionName}/app-tweak
#        purpose: SSN
#      CCN:
#        keyName: ${cmdb}/unbound/${ukc.partitionName}/ccn-key
#        tweak: ${cmdb}/unbound/${ukc.partitionName}/app-tweak
#        purpose: CCN
#    persistience:
#      NCRIT:
#        keyName: ${cmdb}/unbound/${ukc.partitionName}/ncrit-key
#        tweak: ${cmdb}/unbound/${ukc.partitionName}/app-tweak
#        purpose: NCRIT
#      CRIT:
#        keyName: ${cmdb}/unbound/${ukc.partitionName}/crit-key
#        tweak: ${cmdb}/unbound/${ukc.partitionName}/app-tweak
#        purpose: CRIT
#      SSN:
#        keyName: ${cmdb}/unbound/${ukc.partitionName}/ssn-key
#        tweak: ${cmdb}/unbound/${ukc.partitionName}/app-tweak
#        purpose: SSN
#      CCN:
#        keyName: ${cmdb}/unbound/${ukc.partitionName}/ccn-key
#        tweak: ${cmdb}/unbound/${ukc.partitionName}/app-tweak
#        purpose: CCN
#    CES:
##      NCRIT:
##        keyName: co2009/unbound/${ukc.partitionName}/ncrit-key
##        tweak: co2009/unbound/${ukc.partitionName}/app-tweak
##        purpose: NCRIT
#      CRIT:
#        keyName: co2009/unbound/${ukc.partitionName}/crit-key
#        tweak: co2009/unbound/${ukc.partitionName}/app-tweak
#        purpose: CRIT
##      SSN:
##        keyName: co2009/unbound/${ukc.partitionName}/ssn-key
##        tweak: co2009/unbound/${ukc.partitionName}/app-tweak
##        purpose: SSN
##      CCN:
##        keyName: co2009/unbound/${ukc.partitionName}/ccn-key
##        tweak: co2009/unbound/${ukc.partitionName}/app-tweak
##        purpose: CCN
fortanix:
  applicationName: ${cmdb}
  apiKey: ${cmdb}/fortanix/creds
  keySets:
    CES:
      CRIT:
        keyName: co2009/fortanix/default/crit-key
        tweak: co2009/fortanix/app-tweak
        purpose: CRIT
---
spring:
  config:
    activate:
      on-profile: SIT,UAT

logging:
  level:
    ROOT: WARN
    com.fitb: DEBUG
    org.springframework.security.oauth2: INFO
    org.springframework.web.filter.CommonsRequestLoggingFilter: DEBUG
    org.springframework.boot: INFO
    org.springframework.web.servlet.DispatcherServlet: DEBUG
fitb:
  digital:
    service:
      logging:
        log-body: true
---
spring:
  config:
    activate:
      on-profile: PROD
ukc:
  environment: prd

logging:
  level:
    ROOT: WARN

feign:
  client:
    config:
      xfer-and-pay-orc:
        url: ${xfer_and_pay_orc_uri:https://909786ytfg-vpce-04e3fad85e181e1ea.execute-api.us-east-2.amazonaws.com/api/xferandpayorc-service}
        legacy: ${xfer_and_pay_orc_uri_legacy:https://909786ytfg-vpce-04e3fad85e181e1ea.execute-api.us-east-2.amazonaws.com/api/xferandpayorc-service}
        scf: ${xfer_and_pay_orc_uri_scf:https://909786ytfg-vpce-04e3fad85e181e1ea.execute-api.us-east-2.amazonaws.com/api/xferandpayorc-service}
---
spring:
  config:
    activate:
      on-profile: local
  jackson:
    serialization:
      INDENT_OUTPUT: true #For local development, lets pretty print our outputs.
logging: #Most of your local logging config will be set here.
  level:
    root: WARN
    org.springframework: ERROR
    com.fitb: DEBUG
    #    jdbc.connection: DEBUG
    #    org.postgresql: DEBUG
    org.apache.http.wire: INFO
fitb:
  digital:
    logging:
      log-headers: true
feign:
  client:
    config:
      default:
        loggerLevel: full
      ces-proxy:
        url: ${ces_proxy_uri:http://localhost:3001}
        legacy: ${ces_proxy_uri_legacy:http://localhost:3001}
        scf: ${ces_proxy_uri_scf:http://localhost:3001}
      transfer-cd:
        url: http://localhost:3000
      core-transfer-tds:
        url: http://localhost:3000
server:
  servlet:
    context-path: /move-money-bff
risk-score:
  disabled: true
featureflag:
  sdk-key: ${ld-digital-banking-sdk-key:test-key}
  type: file_based
  client-configuration-files: "./src/main/resources/ld-flags.json"
---
spring:
  security:
    oauth2:
      resourceserver:
        jwt:
          issuers:
            B2C: ${oauth_b2c_issuer_uri:https://b2c-sso-dev.auth2-dev.nube.53.com} #property comes from AWS SSM (Parameter Store). Its shared by all the applications in the vpc that need to validate JWT tokens
            B2C_OLD: ${oauth_b2c_old_issuer_uri:https://np.sso.53.com}
            B2E: ${oauth_b2e_issuer_uri:https://np.b2e.sso.53.com}
            B2E_M2M: ${oauth_b2e_okta_m2m_issuer_uri:https://myapps-stg.53.com/oauth2/ausceiupr9fpMQiGc1d7}
            B2E_OIDC: ${oauth_b2e_okta_oidc_issuer_uri:https://myapps-stg.53.com/oauth2/ausc78dihqepdXghX1d7}
            B2E_MULTI: ${oauth_b2e_okta_multi_issuer_uri:https://myapps-stg.53.com/oauth2/ausc45lkkrjKH04mJ1d7}
          algorithms:
            - RS256
            - ES256
  jackson:
    serialization:
      INDENT_OUTPUT: true #For local development, lets pretty print our outputs.
  config:
    activate:
      on-profile: local-sit
fitb:
  digital:
    logging:
      log-headers: true
logging: #Most of your local logging config will be set here.
  level:
    root: WARN
    org.springframework: ERROR
    org.springframework.security.oauth2: DEBUG
    com.fitb: DEBUG
    #    jdbc.connection: DEBUG
    #    org.postgresql: DEBUG
    org.apache.http.wire: INFO
feign:
  client:
    config:
      ces-proxy:
        url: ${ces_proxy_uri:https://dywal6wff9-vpce-04e3fad85e181e1ea.execute-api.us-east-2.amazonaws.com/api/ces-proxy}
        legacy: ${ces_proxy_uri_legacy:https://dywal6wff9-vpce-04e3fad85e181e1ea.execute-api.us-east-2.amazonaws.com/api/ces-proxy}
        scf: ${ces_proxy_uri_scf:https://dywal6wff9-vpce-04e3fad85e181e1ea.execute-api.us-east-2.amazonaws.com/api/ces-proxy}
      transfer-cd:
        url: ${TRANSFER_CD_SERVICE_URL}
      core-transfer-tds:
        url: ${CORETRANSFER_TDS_SERVICE_URL}
      xfer-and-pay-orc:
        url: ${xfer_and_pay_orc_uri:https://zl4aiu3rqj-vpce-04e3fad85e181e1ea.execute-api.us-east-2.amazonaws.com/api/xferandpayorc-service}
        legacy: ${xfer_and_pay_orc_uri_legacy:https://zl4aiu3rqj-vpce-04e3fad85e181e1ea.execute-api.us-east-2.amazonaws.com/api/xferandpayorc-service}
        scf: ${xfer_and_pay_orc_uri_scf:https://zl4aiu3rqj-vpce-04e3fad85e181e1ea.execute-api.us-east-2.amazonaws.com/api/xferandpayorc-service}
#        url: ${xfer_and_pay_orc_uri:http://localhost:8081/xferandpayorc-service}
http:
  proxyHost: zscaler-wide.info53.com
  proxyPort: 8080
server:
  servlet:
    context-path: /move-money-bff
featureflag:
  sdk-key: ${ld-digital-banking-sdk-key:test-key}
  type: file_based
  client-configuration-files: "./src/main/resources/ld-flags.json"
---
spring:
  jackson:
    serialization:
      INDENT_OUTPUT: true
  security:
    oauth2:
      resourceserver:
        jwt:
          issuers:
            B2C: https://b2c.null.com
            B2C_OLD: https://b2e.null.com
            B2E: https://b2e.null.com
            B2E_M2M: https://b2e.null.com
            B2E_OIDC: https://b2e.null.com
            B2E_MULTI: https://b2e.null.com
          algorithms:
            - RS256
            - ES256
  config:
    activate:
      on-profile: unit-test
feign:
  client:
    config:
      default:
        connectTimeout: 5000
        readTimeout: 30000
        loggerLevel: basic
      ces-proxy:
        url: https://null.com
        legacy: https://null.com
        scf: https://null.com
      transfer-cd:
        url: https://null.com
      core-transfer-tds:
        url: https://null.com
risk-score:
  disabled: true

---
spring:
  jackson:
    serialization:
      INDENT_OUTPUT: true
  security:
    oauth2:
      resourceserver:
        jwt:
          issuers:
            B2C: https://b2c.null.com
            B2C_OLD: https://b2e.null.com
            B2E: https://b2e.null.com
            B2E_M2M: https://b2e.null.com
            B2E_OIDC: https://b2e.null.com
            B2E_MULTI: https://b2e.null.com
          algorithms:
            - RS256
            - ES256
  config:
    activate:
      on-profile: newman-test
feign:
  client:
    config:
      default:
        connectTimeout: 5000
        readTimeout: 30000
        loggerLevel: basic
server:
  ssl-auto-configure:
    enabled: true
risk-score:
  disabled: true
