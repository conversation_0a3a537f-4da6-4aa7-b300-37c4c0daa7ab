{"info": {"_postman_id": "2fc1db29-dfd7-4aa4-826b-11e26dabee77", "name": "MoveMoney Newman", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "hello", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": ["if(! pm.variables.get(\"mockUserID\")){", "    // generate a new, pseudo-random UUID", "    var uuid  = require('uuid');", "    pm.variables.set(\"mockUserID\", uuid.v4()); ", "    console.log(\"mockUserID set to: \"+pm.variables.get(\"mockUserID\"))", "}"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "type": "string"}]}, "method": "GET", "header": [{"key": "scope", "type": "text", "value": "openid"}, {"key": "sub", "type": "text", "value": "{{mockUserID}}"}, {"key": "Authorization", "value": "Bearer ey<PERSON>hbGciOiJSUzI1NiIsImtpZCI6IjM5STRoOVl5anNRWTlpa0hhcWdOREQ3WHY4byIsInBpLmF0bSI6Im51eWoifQ.eyJzY29wZSI6Im9wZW5pZCIsImNsaWVudF9pZCI6Ik1vYmlsZV9JT1MiLCJpc3MiOiJodHRwczovL2IyYy1zc28tZGV2LmF1dGgyLWRldi5udWJlLjUzLmNvbSIsInN1YiI6IjI1MDYwOWRjLTkyZWQtNGI5My1iMmY2LWQ2ZTRhNjgxOTU4NiIsImxhc3RfbmFtZSI6IlRFU1RDVVNUT01FUiIsImZ1bGxfbmFtZSI6IklBTiBURVNUQ1VTVE9NRVIiLCJmaXJzdF9uYW1lIjoiSUFOIiwidXNlcm5hbWUiOiJpYW50ZXN0dXNlciIsImV4cCI6MTYxNDI4NTQyMH0.NLQnQV9wbLOC2lZnKBsc7p5KmC6s8cLEQst917RY4gdC0pmewyjKPA3ANBgSvgb36gFcBhdWsIP-useTEwJn28Q_kxdpdgW9j07OwFZZn8dWJ6yYjvY7sNHu9_Jhq0Wf8d9gEajAIvMsBGmkOk8b1ByCNH4l6xx5KmoDenXeKqW6ioQ3yBqk7Kx4RRJZlmqqim_GIPo6lZKe4V9St4i_X6ZHiuv2kjL_pX2XJT0XZ4kPjsJLPhgknW5S3GdNyofxyKOSYP3UIV3WVs6TQa0FQ8n6iY8gfLTvVmZ4rhIdafOemoCKQOlnbc8uhXlhBcjI9ePmMFMa5AVSQ7zULO9o1A", "type": "text", "disabled": true}], "url": {"raw": "{{protocol}}://{{host}}:{{port}}{{servletcontext}}{{context}}/hello", "protocol": "{{protocol}}", "host": ["{{host}}"], "port": "{{port}}{{servletcontext}}{{context}}", "path": ["hello"]}, "description": "Demonstrates the mock-oauth2 flow for local development.  This allows us to easily define security expectations in integration tests for what scopes the requestor has without using a full-fledged oauth2 provider.  This is controlled from the client via headers.\n\nPlease note the 'scopes' and 'userId' headers"}, "response": []}, {"name": "activity", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": ["if(! pm.variables.get(\"mockUserID\")){", "    // generate a new, pseudo-random UUID", "    var uuid  = require('uuid');", "    pm.variables.set(\"mockUserID\", uuid.v4()); ", "    console.log(\"mockUserID set to: \"+pm.variables.get(\"mockUserID\"))", "}"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJSUzI1NiIsImtpZCI6IlFtNXZNSFRsSGNrQmpLd21sTzUwaVVHczBaYyIsInBpLmF0bSI6Im51eWoifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.mTg3F-O189izQyBP2iJkzdjoDTv9UfSfS96BF2G-3d0RimV3U2UNN5PC1iaIH10zkiEd2gST7G--M6Cd-UxME49D6KU4ojqz0lfXG3hfOsW7-XTUVaw36A1m7tMiOkLWlSEkzxZTgfGNhBEjsT5-P3AC4b6zbsYk4H73WgHLuR2J4kgTQHPaUP3-TQ4cxUdFAg2GyTXfwZiugN_YV_vmx_pfZ6Ny9F4f1zZK3pqQ4eU6jrWIpHRJPQBKzK9-Wou_XoBkVXrqkb_LXtG_LvD4klxYSulma7xJaVLfOgOukAFISJQ3pUrZYP1BjFteCuw5ve0TH7FH4V_MrpW5aajyPA", "type": "string"}]}, "method": "POST", "header": [{"key": "scope", "type": "text", "value": "openid"}, {"key": "sub", "type": "text", "value": "{{mockUserID}}"}], "body": {"mode": "raw", "raw": "{\n    \"requestGuid\": \"********-30ae-45c9-858b-09f83ee033ea\",\n    \"fromAccountId\": \"5F1AB09C-29C7-AF4F-33F9-992C1E7639CB\",\n    \"toAccountId\": \"A97D877A-B8EE-89B4-6664-7C7CE4FBEB42\",\n    \"amount\": 1,\n    \"dueDate\": \"2021-03-01\",\n    \"frequency\": \"ONE_TIME\",\n    \"numberOfTransactions\": null,\n    \"memo\": null,\n    \"additionalPrincipleAmount\": null,\n    \"displayId\": null\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{protocol}}://{{host}}:{{port}}{{servletcontext}}{{context}}/activity", "protocol": "{{protocol}}", "host": ["{{host}}"], "port": "{{port}}{{servletcontext}}{{context}}", "path": ["activity"]}, "description": "Demonstrates the mock-oauth2 flow for local development.  This allows us to easily define security expectations in integration tests for what scopes the requestor has without using a full-fledged oauth2 provider.  This is controlled from the client via headers.\n\nPlease note the 'scopes' and 'userId' headers"}, "response": []}, {"name": "activity delete", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": ["if(! pm.variables.get(\"mockUserID\")){", "    // generate a new, pseudo-random UUID", "    var uuid  = require('uuid');", "    pm.variables.set(\"mockUserID\", uuid.v4()); ", "    console.log(\"mockUserID set to: \"+pm.variables.get(\"mockUserID\"))", "}"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJSUzI1NiIsImtpZCI6IlFtNXZNSFRsSGNrQmpLd21sTzUwaVVHczBaYyIsInBpLmF0bSI6Im51eWoifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.mTg3F-O189izQyBP2iJkzdjoDTv9UfSfS96BF2G-3d0RimV3U2UNN5PC1iaIH10zkiEd2gST7G--M6Cd-UxME49D6KU4ojqz0lfXG3hfOsW7-XTUVaw36A1m7tMiOkLWlSEkzxZTgfGNhBEjsT5-P3AC4b6zbsYk4H73WgHLuR2J4kgTQHPaUP3-TQ4cxUdFAg2GyTXfwZiugN_YV_vmx_pfZ6Ny9F4f1zZK3pqQ4eU6jrWIpHRJPQBKzK9-Wou_XoBkVXrqkb_LXtG_LvD4klxYSulma7xJaVLfOgOukAFISJQ3pUrZYP1BjFteCuw5ve0TH7FH4V_MrpW5aajyPA", "type": "string"}]}, "method": "DELETE", "header": [{"key": "scope", "type": "text", "value": "openid"}, {"key": "sub", "type": "text", "value": "{{mockUserID}}"}], "body": {"mode": "raw", "raw": "{\n    \"requestGuid\": \"********-30ae-45c9-858b-09f83ee033ea\",\n    \"fromAccountId\": \"5F1AB09C-29C7-AF4F-33F9-992C1E7639CB\",\n    \"toAccountId\": \"A97D877A-B8EE-89B4-6664-7C7CE4FBEB42\",\n    \"amount\": 1,\n    \"dueDate\": \"2021-03-01\",\n    \"frequency\": \"ONE_TIME\",\n    \"numberOfTransactions\": null,\n    \"memo\": null,\n    \"additionalPrincipleAmount\": null,\n    \"displayId\": null\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{protocol}}://{{host}}:{{port}}{{servletcontext}}{{context}}/activity?activityId=no-way-man", "protocol": "{{protocol}}", "host": ["{{host}}"], "port": "{{port}}{{servletcontext}}{{context}}", "path": ["activity"], "query": [{"key": "activityId", "value": "no-way-man"}]}, "description": "Demonstrates the mock-oauth2 flow for local development.  This allows us to easily define security expectations in integration tests for what scopes the requestor has without using a full-fledged oauth2 provider.  This is controlled from the client via headers.\n\nPlease note the 'scopes' and 'userId' headers"}, "response": []}, {"name": "activity update", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": ["if(! pm.variables.get(\"mockUserID\")){", "    // generate a new, pseudo-random UUID", "    var uuid  = require('uuid');", "    pm.variables.set(\"mockUserID\", uuid.v4()); ", "    console.log(\"mockUserID set to: \"+pm.variables.get(\"mockUserID\"))", "}"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJSUzI1NiIsImtpZCI6IlFtNXZNSFRsSGNrQmpLd21sTzUwaVVHczBaYyIsInBpLmF0bSI6Im51eWoifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.mTg3F-O189izQyBP2iJkzdjoDTv9UfSfS96BF2G-3d0RimV3U2UNN5PC1iaIH10zkiEd2gST7G--M6Cd-UxME49D6KU4ojqz0lfXG3hfOsW7-XTUVaw36A1m7tMiOkLWlSEkzxZTgfGNhBEjsT5-P3AC4b6zbsYk4H73WgHLuR2J4kgTQHPaUP3-TQ4cxUdFAg2GyTXfwZiugN_YV_vmx_pfZ6Ny9F4f1zZK3pqQ4eU6jrWIpHRJPQBKzK9-Wou_XoBkVXrqkb_LXtG_LvD4klxYSulma7xJaVLfOgOukAFISJQ3pUrZYP1BjFteCuw5ve0TH7FH4V_MrpW5aajyPA", "type": "string"}]}, "method": "PUT", "header": [{"key": "scope", "type": "text", "value": "openid"}, {"key": "sub", "type": "text", "value": "{{mockUserID}}"}], "body": {"mode": "raw", "raw": "{\n    \"requestGuid\": \"********-30ae-45c9-858b-09f83ee033ea\",\n    \"fromAccountId\": \"5F1AB09C-29C7-AF4F-33F9-992C1E7639CB\",\n    \"toAccountId\": \"A97D877A-B8EE-89B4-6664-7C7CE4FBEB42\",\n    \"amount\": 1,\n    \"dueDate\": \"2021-03-01\",\n    \"frequency\": \"ONE_TIME\",\n    \"numberOfTransactions\": null,\n    \"memo\": null,\n    \"additionalPrincipleAmount\": null,\n    \"displayId\": null\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{protocol}}://{{host}}:{{port}}{{servletcontext}}{{context}}/activity", "protocol": "{{protocol}}", "host": ["{{host}}"], "port": "{{port}}{{servletcontext}}{{context}}", "path": ["activity"]}, "description": "Demonstrates the mock-oauth2 flow for local development.  This allows us to easily define security expectations in integration tests for what scopes the requestor has without using a full-fledged oauth2 provider.  This is controlled from the client via headers.\n\nPlease note the 'scopes' and 'userId' headers"}, "response": []}, {"name": "activity", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": ["if(! pm.variables.get(\"mockUserID\")){", "    // generate a new, pseudo-random UUID", "    var uuid  = require('uuid');", "    pm.variables.set(\"mockUserID\", uuid.v4()); ", "    console.log(\"mockUserID set to: \"+pm.variables.get(\"mockUserID\"))", "}"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJSUzI1NiIsImtpZCI6IlFtNXZNSFRsSGNrQmpLd21sTzUwaVVHczBaYyIsInBpLmF0bSI6Im51eWoifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.mTg3F-O189izQyBP2iJkzdjoDTv9UfSfS96BF2G-3d0RimV3U2UNN5PC1iaIH10zkiEd2gST7G--M6Cd-UxME49D6KU4ojqz0lfXG3hfOsW7-XTUVaw36A1m7tMiOkLWlSEkzxZTgfGNhBEjsT5-P3AC4b6zbsYk4H73WgHLuR2J4kgTQHPaUP3-TQ4cxUdFAg2GyTXfwZiugN_YV_vmx_pfZ6Ny9F4f1zZK3pqQ4eU6jrWIpHRJPQBKzK9-Wou_XoBkVXrqkb_LXtG_LvD4klxYSulma7xJaVLfOgOukAFISJQ3pUrZYP1BjFteCuw5ve0TH7FH4V_MrpW5aajyPA", "type": "string"}]}, "method": "GET", "header": [{"key": "scope", "type": "text", "value": "openid"}, {"key": "sub", "type": "text", "value": "{{mockUserID}}"}], "url": {"raw": "{{protocol}}://{{host}}:{{port}}{{servletcontext}}{{context}}/activity", "protocol": "{{protocol}}", "host": ["{{host}}"], "port": "{{port}}{{servletcontext}}{{context}}", "path": ["activity"]}, "description": "Demonstrates the mock-oauth2 flow for local development.  This allows us to easily define security expectations in integration tests for what scopes the requestor has without using a full-fledged oauth2 provider.  This is controlled from the client via headers.\n\nPlease note the 'scopes' and 'userId' headers"}, "response": []}, {"name": "transfer limits", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": ["if(! pm.variables.get(\"mockUserID\")){", "    // generate a new, pseudo-random UUID", "    var uuid  = require('uuid');", "    pm.variables.set(\"mockUserID\", uuid.v4()); ", "    console.log(\"mockUserID set to: \"+pm.variables.get(\"mockUserID\"))", "}"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "type": "string"}]}, "method": "GET", "header": [{"key": "scope", "value": "openid", "type": "text"}, {"key": "sub", "value": "{{mockUserID}}", "type": "text"}], "url": {"raw": "{{protocol}}://{{host}}:{{port}}{{servletcontext}}{{context}}/activity/transferlimits?fromAccountId=11C4779D-B8BB-2124-D0FD-D991197BF092&toAccountId=47A2575A-9053-24EA-9B57-DA4B4D6F9DC2", "protocol": "{{protocol}}", "host": ["{{host}}"], "port": "{{port}}{{servletcontext}}{{context}}", "path": ["activity", "transferlimits"], "query": [{"key": "fromAccountId", "value": "11C4779D-B8BB-2124-D0FD-D991197BF092"}, {"key": "toAccountId", "value": "47A2575A-9053-24EA-9B57-DA4B4D6F9DC2"}]}, "description": "Demonstrates the mock-oauth2 flow for local development.  This allows us to easily define security expectations in integration tests for what scopes the requestor has without using a full-fledged oauth2 provider.  This is controlled from the client via headers.\n\nPlease note the 'scopes' and 'userId' headers"}, "response": []}, {"name": "accounts", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": ["if(! pm.variables.get(\"mockUserID\")){", "    // generate a new, pseudo-random UUID", "    var uuid  = require('uuid');", "    pm.variables.set(\"mockUserID\", uuid.v4()); ", "    console.log(\"mockUserID set to: \"+pm.variables.get(\"mockUserID\"))", "}"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJSUzI1NiIsImtpZCI6IlFtNXZNSFRsSGNrQmpLd21sTzUwaVVHczBaYyIsInBpLmF0bSI6Im51eWoifQ.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************.mTg3F-O189izQyBP2iJkzdjoDTv9UfSfS96BF2G-3d0RimV3U2UNN5PC1iaIH10zkiEd2gST7G--M6Cd-UxME49D6KU4ojqz0lfXG3hfOsW7-XTUVaw36A1m7tMiOkLWlSEkzxZTgfGNhBEjsT5-P3AC4b6zbsYk4H73WgHLuR2J4kgTQHPaUP3-TQ4cxUdFAg2GyTXfwZiugN_YV_vmx_pfZ6Ny9F4f1zZK3pqQ4eU6jrWIpHRJPQBKzK9-Wou_XoBkVXrqkb_LXtG_LvD4klxYSulma7xJaVLfOgOukAFISJQ3pUrZYP1BjFteCuw5ve0TH7FH4V_MrpW5aajyPA", "type": "string"}]}, "method": "GET", "header": [{"key": "scope", "value": "openid", "type": "text"}, {"key": "sub", "value": "{{mockUserID}}", "type": "text"}], "url": {"raw": "{{protocol}}://{{host}}:{{port}}{{servletcontext}}{{context}}/transfer/info", "protocol": "{{protocol}}", "host": ["{{host}}"], "port": "{{port}}{{servletcontext}}{{context}}", "path": ["transfer", "info"]}, "description": "Demonstrates the mock-oauth2 flow for local development.  This allows us to easily define security expectations in integration tests for what scopes the requestor has without using a full-fledged oauth2 provider.  This is controlled from the client via headers.\n\nPlease note the 'scopes' and 'userId' headers"}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}]}