{"status": "SUCCESS", "statusCode": "TRANSFER_ACTIVITY_PARTIAL_LIST", "retrievalErrors": ["RETRIEVAL_ERROR_EXTERNAL_TRANSFER"], "activities": [{"id": "********", "recurringId": "17598", "displayStatus": "In Process", "fromAccountName": "5/3 ENHANCED CHECKING", "dueDate": "2018-04-18", "amount": 1, "toAccountName": "5/3 Savings", "displayId": "********", "fromAccountNumber": "X5534", "toAccountId": "D55D0F37-3BC5-F313-1ED5-B3F86124C896", "expressDelivery": false, "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER", "createTimestamp": "2018-04-03T12:30:07.000Z", "fromAccountId": "********-A64A-3BCD-8E1A-88785C9CDA12", "toAccountNumber": "X0771", "status": "5"}, {"id": "********", "recurringId": "17475", "displayStatus": "In Process", "fromAccountName": "5/3 ENHANCED CHECKING", "dueDate": "2018-04-18", "amount": 1, "toAccountName": "MORTGAGE LOAN", "displayId": "********", "fromAccountNumber": "X5534", "toAccountId": "4423B530-EEF6-4B52-B6A7-3C20F6A1F28F", "expressDelivery": false, "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER", "createTimestamp": "2018-04-11T04:00:13.000Z", "fromAccountId": "********-A64A-3BCD-8E1A-88785C9CDA12", "toAccountNumber": "X6688", "additionalPrincipleAmount": 0, "status": "5"}, {"id": "********", "recurringId": "19377", "displayStatus": "In Process", "fromAccountName": "5/3 ENHANCED CHECKING", "dueDate": "2018-04-18", "amount": 1.11, "toAccountName": "5/3 Savings", "displayId": "********", "fromAccountNumber": "X5534", "toAccountId": "D55D0F37-3BC5-F313-1ED5-B3F86124C896", "expressDelivery": false, "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER", "createTimestamp": "2018-04-11T04:00:13.000Z", "fromAccountId": "********-A64A-3BCD-8E1A-88785C9CDA12", "toAccountNumber": "X0771", "status": "5"}, {"id": "********", "recurringId": "17579", "displayStatus": "In Process", "fromAccountName": "5/3 ENHANCED CHECKING", "dueDate": "2018-04-18", "amount": 480.94, "toAccountName": "MORTGAGE LOAN", "displayId": "********", "fromAccountNumber": "X5534", "toAccountId": "4423B530-EEF6-4B52-B6A7-3C20F6A1F28F", "expressDelivery": false, "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER", "createTimestamp": "2018-04-11T04:00:14.000Z", "fromAccountId": "********-A64A-3BCD-8E1A-88785C9CDA12", "toAccountNumber": "X6688", "additionalPrincipleAmount": 0, "status": "5"}, {"id": "********", "recurringId": "20666", "displayStatus": "In Process", "fromAccountName": "5/3 ENHANCED CHECKING", "dueDate": "2018-04-18", "amount": 0.53, "toAccountName": "5/3 RELATIONSHIP MONEY MARKET", "displayId": "********", "fromAccountNumber": "X5534", "toAccountId": "********-2007-93AD-7CF0-E1D7394985AA", "expressDelivery": false, "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER", "createTimestamp": "2018-04-11T04:00:20.000Z", "fromAccountId": "********-A64A-3BCD-8E1A-88785C9CDA12", "toAccountNumber": "X3950", "status": "5"}, {"id": "********", "recurringId": "17475", "displayStatus": "In Process", "fromAccountName": "5/3 ENHANCED CHECKING", "dueDate": "2018-04-18", "amount": 1, "toAccountName": "MORTGAGE LOAN", "displayId": "********", "fromAccountNumber": "X5534", "toAccountId": "4423B530-EEF6-4B52-B6A7-3C20F6A1F28F", "expressDelivery": false, "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER", "createTimestamp": "2018-04-11T04:00:25.000Z", "fromAccountId": "********-A64A-3BCD-8E1A-88785C9CDA12", "toAccountNumber": "X6688", "additionalPrincipleAmount": 0, "status": "5"}, {"id": "********", "recurringId": "19377", "displayStatus": "In Process", "fromAccountName": "5/3 ENHANCED CHECKING", "dueDate": "2018-04-18", "amount": 1.11, "toAccountName": "5/3 Savings", "displayId": "********", "fromAccountNumber": "X5534", "toAccountId": "D55D0F37-3BC5-F313-1ED5-B3F86124C896", "expressDelivery": false, "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER", "createTimestamp": "2018-04-11T04:00:25.000Z", "fromAccountId": "********-A64A-3BCD-8E1A-88785C9CDA12", "toAccountNumber": "X0771", "status": "5"}, {"id": "********", "recurringId": "17579", "displayStatus": "In Process", "fromAccountName": "5/3 ENHANCED CHECKING", "dueDate": "2018-04-18", "amount": 480.94, "toAccountName": "MORTGAGE LOAN", "displayId": "********", "fromAccountNumber": "X5534", "toAccountId": "4423B530-EEF6-4B52-B6A7-3C20F6A1F28F", "expressDelivery": false, "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER", "createTimestamp": "2018-04-11T04:00:26.000Z", "fromAccountId": "********-A64A-3BCD-8E1A-88785C9CDA12", "toAccountNumber": "X6688", "additionalPrincipleAmount": 0, "status": "5"}, {"id": "********", "recurringId": "20666", "displayStatus": "In Process", "fromAccountName": "5/3 ENHANCED CHECKING", "dueDate": "2018-04-18", "amount": 0.53, "toAccountName": "5/3 RELATIONSHIP MONEY MARKET", "displayId": "********", "fromAccountNumber": "X5534", "toAccountId": "********-2007-93AD-7CF0-E1D7394985AA", "expressDelivery": false, "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER", "createTimestamp": "2018-04-11T04:00:30.000Z", "fromAccountId": "********-A64A-3BCD-8E1A-88785C9CDA12", "toAccountNumber": "X3950", "status": "5"}, {"id": "********", "recurringId": "18250", "displayStatus": "In Process", "fromAccountName": "5/3 ENHANCED CHECKING", "dueDate": "2018-04-19", "amount": 20, "toAccountName": "5/3 Goal Setter Savings", "displayId": "********", "fromAccountNumber": "X5534", "toAccountId": "E0C5748F-60DF-885A-53E9-616DCD656054", "expressDelivery": false, "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER", "createTimestamp": "2018-04-12T04:00:09.000Z", "fromAccountId": "********-A64A-3BCD-8E1A-88785C9CDA12", "toAccountNumber": "X1518", "status": "5"}, {"id": "********", "recurringId": "19508", "displayStatus": "In Process", "fromAccountName": "5/3 ENHANCED CHECKING", "dueDate": "2018-04-22", "amount": 222.22, "toAccountName": "MASTERCARD ACCOUNT", "displayId": "********", "fromAccountNumber": "X5534", "toAccountId": "A78F7A75-B787-986C-2113-1AAAF2A6852F", "expressDelivery": false, "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER", "createTimestamp": "2018-04-06T04:00:12.000Z", "fromAccountId": "********-A64A-3BCD-8E1A-88785C9CDA12", "toAccountNumber": "X6052", "status": "5"}, {"id": "********", "recurringId": "17599", "displayStatus": "In Process", "fromAccountName": "5/3 ENHANCED CHECKING", "dueDate": "2018-04-24", "amount": 1, "toAccountName": "5/3 Savings", "displayId": "********", "fromAccountNumber": "X5534", "toAccountId": "D55D0F37-3BC5-F313-1ED5-B3F86124C896", "expressDelivery": false, "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER", "createTimestamp": "2018-03-23T04:00:07.000Z", "fromAccountId": "********-A64A-3BCD-8E1A-88785C9CDA12", "toAccountNumber": "X0771", "status": "5"}, {"id": "********", "recurringId": "17599", "displayStatus": "In Process", "fromAccountName": "5/3 ENHANCED CHECKING", "dueDate": "2018-04-24", "amount": 1, "toAccountName": "5/3 Savings", "displayId": "********", "fromAccountNumber": "X5534", "toAccountId": "D55D0F37-3BC5-F313-1ED5-B3F86124C896", "expressDelivery": false, "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER", "createTimestamp": "2018-03-23T04:00:17.000Z", "fromAccountId": "********-A64A-3BCD-8E1A-88785C9CDA12", "toAccountNumber": "X0771", "status": "5"}, {"id": "********", "recurringId": "17599", "displayStatus": "In Process", "fromAccountName": "5/3 ENHANCED CHECKING", "dueDate": "2018-04-24", "amount": 1, "toAccountName": "5/3 Savings", "displayId": "********", "fromAccountNumber": "X5534", "toAccountId": "D55D0F37-3BC5-F313-1ED5-B3F86124C896", "expressDelivery": false, "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER", "createTimestamp": "2018-03-23T04:00:25.000Z", "fromAccountId": "********-A64A-3BCD-8E1A-88785C9CDA12", "toAccountNumber": "X0771", "status": "5"}, {"id": "********", "recurringId": "21671", "displayStatus": "Canceled", "fromAccountName": "5/3 ENHANCED CHECKING", "dueDate": "2018-04-24", "amount": 22.22, "toAccountName": "5/3 Goal Setter Savings", "displayId": "********", "fromAccountNumber": "X5534", "toAccountId": "E0C5748F-60DF-885A-53E9-616DCD656054", "expressDelivery": false, "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER", "createTimestamp": "2018-04-10T15:00:00.000Z", "fromAccountId": "********-A64A-3BCD-8E1A-88785C9CDA12", "toAccountNumber": "X1518", "status": "0"}, {"id": "********", "recurringId": "19377", "displayStatus": "In Process", "fromAccountName": "5/3 ENHANCED CHECKING", "dueDate": "2018-04-25", "amount": 1.11, "toAccountName": "5/3 Savings", "displayId": "********", "fromAccountNumber": "X5534", "toAccountId": "D55D0F37-3BC5-F313-1ED5-B3F86124C896", "expressDelivery": false, "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER", "createTimestamp": "2018-04-18T04:00:05.000Z", "fromAccountId": "********-A64A-3BCD-8E1A-88785C9CDA12", "toAccountNumber": "X0771", "status": "5"}, {"id": "********", "recurringId": "17475", "displayStatus": "In Process", "fromAccountName": "5/3 ENHANCED CHECKING", "dueDate": "2018-04-25", "amount": 1, "toAccountName": "MORTGAGE LOAN", "displayId": "********", "fromAccountNumber": "X5534", "toAccountId": "4423B530-EEF6-4B52-B6A7-3C20F6A1F28F", "expressDelivery": false, "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER", "createTimestamp": "2018-04-18T04:00:05.000Z", "fromAccountId": "********-A64A-3BCD-8E1A-88785C9CDA12", "toAccountNumber": "X6688", "additionalPrincipleAmount": 0, "status": "5"}, {"id": "********", "recurringId": "17579", "displayStatus": "In Process", "fromAccountName": "5/3 ENHANCED CHECKING", "dueDate": "2018-04-25", "amount": 480.94, "toAccountName": "MORTGAGE LOAN", "displayId": "********", "fromAccountNumber": "X5534", "toAccountId": "4423B530-EEF6-4B52-B6A7-3C20F6A1F28F", "expressDelivery": false, "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER", "createTimestamp": "2018-04-18T04:00:07.000Z", "fromAccountId": "********-A64A-3BCD-8E1A-88785C9CDA12", "toAccountNumber": "X6688", "additionalPrincipleAmount": 0, "status": "5"}, {"id": "********", "recurringId": "20666", "displayStatus": "In Process", "fromAccountName": "5/3 ENHANCED CHECKING", "dueDate": "2018-04-25", "amount": 0.53, "toAccountName": "5/3 RELATIONSHIP MONEY MARKET", "displayId": "********", "fromAccountNumber": "X5534", "toAccountId": "********-2007-93AD-7CF0-E1D7394985AA", "expressDelivery": false, "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER", "createTimestamp": "2018-04-18T04:00:12.000Z", "fromAccountId": "********-A64A-3BCD-8E1A-88785C9CDA12", "toAccountNumber": "X3950", "status": "5"}, {"id": "********", "recurringId": "18250", "displayStatus": "In Process", "fromAccountName": "5/3 ENHANCED CHECKING", "dueDate": "2018-04-26", "amount": 20, "toAccountName": "5/3 Goal Setter Savings", "displayId": "********", "fromAccountNumber": "X5534", "toAccountId": "E0C5748F-60DF-885A-53E9-616DCD656054", "expressDelivery": false, "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER", "createTimestamp": "2018-04-19T04:00:09.000Z", "fromAccountId": "********-A64A-3BCD-8E1A-88785C9CDA12", "toAccountNumber": "X1518", "status": "5"}, {"id": "********", "displayStatus": "Processed", "fromAccountName": "5/3 ENHANCED CHECKING", "dueDate": "2018-04-30", "amount": 1, "toAccountName": "5/3 Goal Setter Savings", "displayId": "********", "fromAccountNumber": "X5534", "toAccountId": "E0C5748F-60DF-885A-53E9-616DCD656054", "expressDelivery": false, "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER", "createTimestamp": "2018-04-30T19:42:58.000Z", "fromAccountId": "********-A64A-3BCD-8E1A-88785C9CDA12", "toAccountNumber": "X1518", "status": "3"}, {"id": "********", "displayStatus": "Processed", "fromAccountName": "5/3 ENHANCED CHECKING", "dueDate": "2018-04-30", "amount": 1, "toAccountName": "5/3 RELATIONSHIP MONEY MARKET", "displayId": "********", "fromAccountNumber": "X5534", "toAccountId": "********-2007-93AD-7CF0-E1D7394985AA", "expressDelivery": false, "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER", "createTimestamp": "2018-04-30T19:55:32.000Z", "fromAccountId": "********-A64A-3BCD-8E1A-88785C9CDA12", "toAccountNumber": "X3950", "status": "3"}, {"id": "********", "recurringId": "17475", "displayStatus": "In Process", "fromAccountName": "5/3 ENHANCED CHECKING", "dueDate": "2018-05-02", "amount": 1, "toAccountName": "MORTGAGE LOAN", "displayId": "********", "fromAccountNumber": "X5534", "toAccountId": "4423B530-EEF6-4B52-B6A7-3C20F6A1F28F", "expressDelivery": false, "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER", "createTimestamp": "2018-04-25T04:00:05.000Z", "fromAccountId": "********-A64A-3BCD-8E1A-88785C9CDA12", "toAccountNumber": "X6688", "additionalPrincipleAmount": 0, "status": "5"}, {"id": "********", "recurringId": "19377", "displayStatus": "In Process", "fromAccountName": "5/3 ENHANCED CHECKING", "dueDate": "2018-05-02", "amount": 1.11, "toAccountName": "5/3 Savings", "displayId": "********", "fromAccountNumber": "X5534", "toAccountId": "D55D0F37-3BC5-F313-1ED5-B3F86124C896", "expressDelivery": false, "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER", "createTimestamp": "2018-04-25T04:00:05.000Z", "fromAccountId": "********-A64A-3BCD-8E1A-88785C9CDA12", "toAccountNumber": "X0771", "status": "5"}, {"id": "********", "recurringId": "17579", "displayStatus": "In Process", "fromAccountName": "5/3 ENHANCED CHECKING", "dueDate": "2018-05-02", "amount": 480.94, "toAccountName": "MORTGAGE LOAN", "displayId": "********", "fromAccountNumber": "X5534", "toAccountId": "4423B530-EEF6-4B52-B6A7-3C20F6A1F28F", "expressDelivery": false, "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER", "createTimestamp": "2018-04-25T04:00:07.000Z", "fromAccountId": "********-A64A-3BCD-8E1A-88785C9CDA12", "toAccountNumber": "X6688", "additionalPrincipleAmount": 0, "status": "5"}, {"id": "********", "recurringId": "20666", "displayStatus": "In Process", "fromAccountName": "5/3 ENHANCED CHECKING", "dueDate": "2018-05-02", "amount": 0.53, "toAccountName": "5/3 RELATIONSHIP MONEY MARKET", "displayId": "********", "fromAccountNumber": "X5534", "toAccountId": "********-2007-93AD-7CF0-E1D7394985AA", "expressDelivery": false, "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER", "createTimestamp": "2018-04-25T04:00:13.000Z", "fromAccountId": "********-A64A-3BCD-8E1A-88785C9CDA12", "toAccountNumber": "X3950", "status": "5"}, {"id": "********", "recurringId": "17598", "displayStatus": "In Process", "fromAccountName": "5/3 ENHANCED CHECKING", "dueDate": "2018-05-03", "amount": 1, "toAccountName": "5/3 Savings", "displayId": "********", "fromAccountNumber": "X5534", "toAccountId": "D55D0F37-3BC5-F313-1ED5-B3F86124C896", "expressDelivery": false, "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER", "createTimestamp": "2018-04-18T04:00:09.000Z", "fromAccountId": "********-A64A-3BCD-8E1A-88785C9CDA12", "toAccountNumber": "X0771", "status": "5"}, {"id": "********", "recurringId": "18250", "displayStatus": "In Process", "fromAccountName": "5/3 ENHANCED CHECKING", "dueDate": "2018-05-03", "amount": 20, "toAccountName": "5/3 Goal Setter Savings", "displayId": "********", "fromAccountNumber": "X5534", "toAccountId": "E0C5748F-60DF-885A-53E9-616DCD656054", "expressDelivery": false, "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER", "createTimestamp": "2018-04-26T04:00:10.000Z", "fromAccountId": "********-A64A-3BCD-8E1A-88785C9CDA12", "toAccountNumber": "X1518", "status": "5"}, {"id": "********", "recurringId": "19508", "displayStatus": "In Process", "fromAccountName": "5/3 ENHANCED CHECKING", "dueDate": "2018-05-07", "amount": 222.22, "toAccountName": "MASTERCARD ACCOUNT", "displayId": "********", "fromAccountNumber": "X5534", "toAccountId": "A78F7A75-B787-986C-2113-1AAAF2A6852F", "expressDelivery": false, "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER", "createTimestamp": "2018-04-20T04:00:14.000Z", "fromAccountId": "********-A64A-3BCD-8E1A-88785C9CDA12", "toAccountNumber": "X6052", "status": "5"}, {"id": "********", "recurringId": "17475", "displayStatus": "In Process", "fromAccountName": "5/3 ENHANCED CHECKING", "dueDate": "2018-05-09", "amount": 1, "toAccountName": "MORTGAGE LOAN", "displayId": "********", "fromAccountNumber": "X5534", "toAccountId": "4423B530-EEF6-4B52-B6A7-3C20F6A1F28F", "expressDelivery": false, "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER", "createTimestamp": "2018-05-02T04:00:07.000Z", "fromAccountId": "********-A64A-3BCD-8E1A-88785C9CDA12", "toAccountNumber": "X6688", "additionalPrincipleAmount": 0, "status": "5"}, {"id": "********", "recurringId": "19377", "displayStatus": "In Process", "fromAccountName": "5/3 ENHANCED CHECKING", "dueDate": "2018-05-09", "amount": 1.11, "toAccountName": "5/3 Savings", "displayId": "********", "fromAccountNumber": "X5534", "toAccountId": "D55D0F37-3BC5-F313-1ED5-B3F86124C896", "expressDelivery": false, "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER", "createTimestamp": "2018-05-02T04:00:07.000Z", "fromAccountId": "********-A64A-3BCD-8E1A-88785C9CDA12", "toAccountNumber": "X0771", "status": "5"}, {"id": "********", "recurringId": "17579", "displayStatus": "In Process", "fromAccountName": "5/3 ENHANCED CHECKING", "dueDate": "2018-05-09", "amount": 480.94, "toAccountName": "MORTGAGE LOAN", "displayId": "********", "fromAccountNumber": "X5534", "toAccountId": "4423B530-EEF6-4B52-B6A7-3C20F6A1F28F", "expressDelivery": false, "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER", "createTimestamp": "2018-05-02T04:00:09.000Z", "fromAccountId": "********-A64A-3BCD-8E1A-88785C9CDA12", "toAccountNumber": "X6688", "additionalPrincipleAmount": 0, "status": "5"}, {"id": "********", "recurringId": "20666", "displayStatus": "In Process", "fromAccountName": "5/3 ENHANCED CHECKING", "dueDate": "2018-05-09", "amount": 0.53, "toAccountName": "5/3 RELATIONSHIP MONEY MARKET", "displayId": "********", "fromAccountNumber": "X5534", "toAccountId": "********-2007-93AD-7CF0-E1D7394985AA", "expressDelivery": false, "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER", "createTimestamp": "2018-05-02T04:00:14.000Z", "fromAccountId": "********-A64A-3BCD-8E1A-88785C9CDA12", "toAccountNumber": "X3950", "status": "5"}, {"id": "********", "recurringId": "18250", "displayStatus": "In Process", "fromAccountName": "5/3 ENHANCED CHECKING", "dueDate": "2018-05-10", "amount": 20, "toAccountName": "5/3 Goal Setter Savings", "displayId": "********", "fromAccountNumber": "X5534", "toAccountId": "E0C5748F-60DF-885A-53E9-616DCD656054", "expressDelivery": false, "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER", "createTimestamp": "2018-05-03T04:00:15.000Z", "fromAccountId": "********-A64A-3BCD-8E1A-88785C9CDA12", "toAccountNumber": "X1518", "status": "5"}, {"id": "********", "recurringId": "17475", "displayStatus": "Scheduled", "fromAccountName": "5/3 ENHANCED CHECKING", "dueDate": "2018-05-16", "amount": 1, "toAccountName": "MORTGAGE LOAN", "displayId": "********", "fromAccountNumber": "X5534", "toAccountId": "4423B530-EEF6-4B52-B6A7-3C20F6A1F28F", "expressDelivery": false, "editable": true, "cancelable": true, "type": "INTERNAL_TRANSFER", "createTimestamp": "2018-05-09T04:00:11.000Z", "fromAccountId": "********-A64A-3BCD-8E1A-88785C9CDA12", "toAccountNumber": "X6688", "additionalPrincipleAmount": 0, "status": "4"}, {"id": "********", "recurringId": "19377", "displayStatus": "Scheduled", "fromAccountName": "5/3 ENHANCED CHECKING", "dueDate": "2018-05-16", "amount": 1.11, "toAccountName": "5/3 Savings", "displayId": "********", "fromAccountNumber": "X5534", "toAccountId": "D55D0F37-3BC5-F313-1ED5-B3F86124C896", "expressDelivery": false, "editable": false, "cancelable": true, "type": "INTERNAL_TRANSFER", "createTimestamp": "2018-05-09T04:00:11.000Z", "fromAccountId": "********-A64A-3BCD-8E1A-88785C9CDA12", "toAccountNumber": "X0771", "status": "4"}, {"id": "********", "recurringId": "17579", "displayStatus": "Scheduled", "fromAccountName": "5/3 ENHANCED CHECKING", "dueDate": "2018-05-16", "amount": 480.94, "toAccountName": "MORTGAGE LOAN", "displayId": "********", "fromAccountNumber": "X5534", "toAccountId": "4423B530-EEF6-4B52-B6A7-3C20F6A1F28F", "expressDelivery": false, "editable": true, "cancelable": true, "type": "INTERNAL_TRANSFER", "createTimestamp": "2018-05-09T04:00:12.000Z", "fromAccountId": "********-A64A-3BCD-8E1A-88785C9CDA12", "toAccountNumber": "X6688", "additionalPrincipleAmount": 0, "status": "4"}, {"id": "********", "recurringId": "20666", "displayStatus": "Scheduled", "fromAccountName": "5/3 ENHANCED CHECKING", "dueDate": "2018-05-16", "amount": 0.53, "toAccountName": "5/3 RELATIONSHIP MONEY MARKET", "displayId": "********", "fromAccountNumber": "X5534", "toAccountId": "********-2007-93AD-7CF0-E1D7394985AA", "expressDelivery": false, "editable": true, "cancelable": true, "type": "INTERNAL_TRANSFER", "createTimestamp": "2018-05-09T04:00:22.000Z", "fromAccountId": "********-A64A-3BCD-8E1A-88785C9CDA12", "toAccountNumber": "X3950", "status": "4"}, {"id": "********", "recurringId": "17579", "displayStatus": "Scheduled", "fromAccountName": "5/3 ENHANCED CHECKING", "dueDate": "2018-05-16", "amount": 480.94, "toAccountName": "MORTGAGE LOAN", "displayId": "********", "fromAccountNumber": "X5534", "toAccountId": "4423B530-EEF6-4B52-B6A7-3C20F6A1F28F", "expressDelivery": false, "editable": true, "cancelable": true, "type": "INTERNAL_TRANSFER", "createTimestamp": "2018-05-09T04:00:29.000Z", "fromAccountId": "********-A64A-3BCD-8E1A-88785C9CDA12", "toAccountNumber": "X6688", "additionalPrincipleAmount": 0, "status": "4"}, {"id": "********", "recurringId": "20666", "displayStatus": "Scheduled", "fromAccountName": "5/3 ENHANCED CHECKING", "dueDate": "2018-05-16", "amount": 0.53, "toAccountName": "5/3 RELATIONSHIP MONEY MARKET", "displayId": "********", "fromAccountNumber": "X5534", "toAccountId": "********-2007-93AD-7CF0-E1D7394985AA", "expressDelivery": false, "editable": true, "cancelable": true, "type": "INTERNAL_TRANSFER", "createTimestamp": "2018-05-09T04:00:31.000Z", "fromAccountId": "********-A64A-3BCD-8E1A-88785C9CDA12", "toAccountNumber": "X3950", "status": "4"}, {"id": "********", "recurringId": "17475", "displayStatus": "Scheduled", "fromAccountName": "5/3 ENHANCED CHECKING", "dueDate": "2018-05-16", "amount": 1, "toAccountName": "MORTGAGE LOAN", "displayId": "********", "fromAccountNumber": "X5534", "toAccountId": "4423B530-EEF6-4B52-B6A7-3C20F6A1F28F", "expressDelivery": false, "editable": true, "cancelable": true, "type": "INTERNAL_TRANSFER", "createTimestamp": "2018-05-09T04:00:46.000Z", "fromAccountId": "********-A64A-3BCD-8E1A-88785C9CDA12", "toAccountNumber": "X6688", "additionalPrincipleAmount": 0, "status": "4"}, {"id": "********", "recurringId": "19377", "displayStatus": "Scheduled", "fromAccountName": "5/3 ENHANCED CHECKING", "dueDate": "2018-05-16", "amount": 1.11, "toAccountName": "5/3 Savings", "displayId": "********", "fromAccountNumber": "X5534", "toAccountId": "D55D0F37-3BC5-F313-1ED5-B3F86124C896", "expressDelivery": false, "editable": false, "cancelable": true, "type": "INTERNAL_TRANSFER", "createTimestamp": "2018-05-09T04:00:47.000Z", "fromAccountId": "********-A64A-3BCD-8E1A-88785C9CDA12", "toAccountNumber": "X0771", "status": "4"}, {"id": "********", "recurringId": "17579", "displayStatus": "Scheduled", "fromAccountName": "5/3 ENHANCED CHECKING", "dueDate": "2018-05-16", "amount": 480.94, "toAccountName": "MORTGAGE LOAN", "displayId": "********", "fromAccountNumber": "X5534", "toAccountId": "4423B530-EEF6-4B52-B6A7-3C20F6A1F28F", "expressDelivery": false, "editable": true, "cancelable": true, "type": "INTERNAL_TRANSFER", "createTimestamp": "2018-05-09T04:00:47.000Z", "fromAccountId": "********-A64A-3BCD-8E1A-88785C9CDA12", "toAccountNumber": "X6688", "additionalPrincipleAmount": 0, "status": "4"}, {"id": "********", "recurringId": "20666", "displayStatus": "Scheduled", "fromAccountName": "5/3 ENHANCED CHECKING", "dueDate": "2018-05-16", "amount": 0.53, "toAccountName": "5/3 RELATIONSHIP MONEY MARKET", "displayId": "********", "fromAccountNumber": "X5534", "toAccountId": "********-2007-93AD-7CF0-E1D7394985AA", "expressDelivery": false, "editable": true, "cancelable": true, "type": "INTERNAL_TRANSFER", "createTimestamp": "2018-05-09T04:00:50.000Z", "fromAccountId": "********-A64A-3BCD-8E1A-88785C9CDA12", "toAccountNumber": "X3950", "status": "4"}, {"id": "********", "recurringId": "18250", "displayStatus": "Scheduled", "fromAccountName": "5/3 ENHANCED CHECKING", "dueDate": "2018-05-17", "amount": 20, "toAccountName": "5/3 Goal Setter Savings", "displayId": "********", "fromAccountNumber": "X5534", "toAccountId": "E0C5748F-60DF-885A-53E9-616DCD656054", "expressDelivery": false, "editable": true, "cancelable": true, "type": "INTERNAL_TRANSFER", "createTimestamp": "2018-05-10T04:00:12.000Z", "fromAccountId": "********-A64A-3BCD-8E1A-88785C9CDA12", "toAccountNumber": "X1518", "status": "4"}, {"id": "********", "recurringId": "17598", "displayStatus": "Scheduled", "fromAccountName": "5/3 ENHANCED CHECKING", "dueDate": "2018-05-18", "amount": 1, "toAccountName": "5/3 Savings", "displayId": "********", "fromAccountNumber": "X5534", "toAccountId": "D55D0F37-3BC5-F313-1ED5-B3F86124C896", "expressDelivery": false, "editable": false, "cancelable": true, "type": "INTERNAL_TRANSFER", "createTimestamp": "2018-05-03T04:00:14.000Z", "fromAccountId": "********-A64A-3BCD-8E1A-88785C9CDA12", "toAccountNumber": "X0771", "status": "4"}, {"id": "********", "recurringId": "19508", "displayStatus": "Scheduled", "fromAccountName": "5/3 ENHANCED CHECKING", "dueDate": "2018-05-22", "amount": 222.22, "toAccountName": "MASTERCARD ACCOUNT", "displayId": "********", "fromAccountNumber": "X5534", "toAccountId": "A78F7A75-B787-986C-2113-1AAAF2A6852F", "expressDelivery": false, "editable": true, "cancelable": true, "type": "INTERNAL_TRANSFER", "createTimestamp": "2018-05-07T04:00:24.000Z", "fromAccountId": "********-A64A-3BCD-8E1A-88785C9CDA12", "toAccountNumber": "X6052", "status": "4"}, {"id": "********", "recurringId": "19508", "displayStatus": "Scheduled", "fromAccountName": "5/3 ENHANCED CHECKING", "dueDate": "2018-05-22", "amount": 222.22, "toAccountName": "MASTERCARD ACCOUNT", "displayId": "********", "fromAccountNumber": "X5534", "toAccountId": "A78F7A75-B787-986C-2113-1AAAF2A6852F", "expressDelivery": false, "editable": true, "cancelable": true, "type": "INTERNAL_TRANSFER", "createTimestamp": "2018-05-07T04:00:34.000Z", "fromAccountId": "********-A64A-3BCD-8E1A-88785C9CDA12", "toAccountNumber": "X6052", "status": "4"}, {"id": "********", "recurringId": "19508", "displayStatus": "Scheduled", "fromAccountName": "5/3 ENHANCED CHECKING", "dueDate": "2018-05-22", "amount": 222.22, "toAccountName": "MASTERCARD ACCOUNT", "displayId": "********", "fromAccountNumber": "X5534", "toAccountId": "A78F7A75-B787-986C-2113-1AAAF2A6852F", "expressDelivery": false, "editable": true, "cancelable": true, "type": "INTERNAL_TRANSFER", "createTimestamp": "2018-05-07T04:00:43.000Z", "fromAccountId": "********-A64A-3BCD-8E1A-88785C9CDA12", "toAccountNumber": "X6052", "status": "4"}, {"id": "********", "recurringId": "19508", "displayStatus": "Scheduled", "fromAccountName": "5/3 ENHANCED CHECKING", "dueDate": "2018-05-22", "amount": 222.22, "toAccountName": "MASTERCARD ACCOUNT", "displayId": "********", "fromAccountNumber": "X5534", "toAccountId": "A78F7A75-B787-986C-2113-1AAAF2A6852F", "expressDelivery": false, "editable": true, "cancelable": true, "type": "INTERNAL_TRANSFER", "createTimestamp": "2018-05-07T04:00:51.000Z", "fromAccountId": "********-A64A-3BCD-8E1A-88785C9CDA12", "toAccountNumber": "X6052", "status": "4"}, {"id": "********", "recurringId": "17599", "displayStatus": "Scheduled", "fromAccountName": "5/3 ENHANCED CHECKING", "dueDate": "2018-05-24", "amount": 1, "toAccountName": "5/3 Savings", "displayId": "********", "fromAccountNumber": "X5534", "toAccountId": "D55D0F37-3BC5-F313-1ED5-B3F86124C896", "expressDelivery": false, "editable": false, "cancelable": true, "type": "INTERNAL_TRANSFER", "createTimestamp": "2018-04-24T04:00:08.000Z", "fromAccountId": "********-A64A-3BCD-8E1A-88785C9CDA12", "toAccountNumber": "X0771", "status": "4"}, {"id": "********", "recurringId": "19507", "displayStatus": "Scheduled", "fromAccountName": "5/3 Goal Setter Savings", "dueDate": "2018-06-07", "amount": 6.66, "toAccountName": "MASTERCARD ACCOUNT", "displayId": "********", "fromAccountNumber": "X1518", "toAccountId": "A78F7A75-B787-986C-2113-1AAAF2A6852F", "expressDelivery": false, "editable": true, "cancelable": true, "type": "INTERNAL_TRANSFER", "createTimestamp": "2018-03-07T05:00:11.000Z", "fromAccountId": "E0C5748F-60DF-885A-53E9-616DCD656054", "toAccountNumber": "X6052", "status": "4"}, {"id": "********", "recurringId": "19507", "displayStatus": "Scheduled", "fromAccountName": "5/3 Goal Setter Savings", "dueDate": "2018-06-07", "amount": 6.66, "toAccountName": "MASTERCARD ACCOUNT", "displayId": "********", "fromAccountNumber": "X1518", "toAccountId": "A78F7A75-B787-986C-2113-1AAAF2A6852F", "expressDelivery": false, "editable": true, "cancelable": true, "type": "INTERNAL_TRANSFER", "createTimestamp": "2018-03-07T05:00:22.000Z", "fromAccountId": "E0C5748F-60DF-885A-53E9-616DCD656054", "toAccountNumber": "X6052", "status": "4"}, {"id": "********", "recurringId": "19507", "displayStatus": "Scheduled", "fromAccountName": "5/3 Goal Setter Savings", "dueDate": "2018-06-07", "amount": 6.66, "toAccountName": "MASTERCARD ACCOUNT", "displayId": "********", "fromAccountNumber": "X1518", "toAccountId": "A78F7A75-B787-986C-2113-1AAAF2A6852F", "expressDelivery": false, "editable": true, "cancelable": true, "type": "INTERNAL_TRANSFER", "createTimestamp": "2018-03-07T05:00:32.000Z", "fromAccountId": "E0C5748F-60DF-885A-53E9-616DCD656054", "toAccountNumber": "X6052", "status": "4"}, {"id": "********", "displayStatus": "Processed", "fromAccountName": "5/3 ENHANCED CHECKING", "dueDate": "2018-06-08", "amount": 14.14, "toAccountName": "5/3 Savings", "displayId": "********", "fromAccountNumber": "X5534", "toAccountId": "D55D0F37-3BC5-F313-1ED5-B3F86124C896", "expressDelivery": false, "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER", "createTimestamp": "2017-10-13T09:38:06.000Z", "fromAccountId": "********-A64A-3BCD-8E1A-88785C9CDA12", "toAccountNumber": "X0771", "status": "3"}, {"id": "********", "displayStatus": "Scheduled", "fromAccountName": "5/3 ENHANCED CHECKING", "dueDate": "2018-08-23", "amount": 12.34, "cancelable": true, "displayId": "********", "fromAccountNumber": "X5534", "expressDelivery": false, "editable": false, "type": "INTERNAL_TRANSFER", "createTimestamp": "2018-02-09T17:32:31.000Z", "fromAccountId": "********-A64A-3BCD-8E1A-88785C9CDA12", "toAccountNumber": "X1983", "status": "4"}], "recurringActivities": [{"id": "5936CB23-37A4-6D8A-213E-EC682511A584", "recurringId": "5936CB23-37A4-6D8A-213E-EC682511A584", "amount": 10.12, "dueDate": "2018-01-26", "displayId": "3776868", "fromAccountId": "89B62A9C-034D-7D08-68FE-B9CF86CA9357", "fromAccountNumber": "X5155", "fromAccountName": "vamsi", "toAccountId": "28EEEAA3-6CAD-0466-0CC4-D852E68D4D1A", "toAccountNumber": "X6022", "toAccountName": "5/3 ESSENTIAL CHECKING", "expressDelivery": true, "creationDate": "2018-01-25", "deliveryDate": "2018-01-26", "frequency": "ONCE_A_WEEK", "status": "Complete", "displayStatus": "Completed", "editable": false, "cancelable": false, "type": "EXTERNAL_TRANSFER"}, {"id": "FA626BA8-B407-56E7-DA61-604628CD4421", "recurringId": "FA626BA8-B407-56E7-DA61-604628CD4421", "amount": 10.12, "dueDate": "2018-01-26", "displayId": "3776869", "fromAccountId": "89B62A9C-034D-7D08-68FE-B9CF86CA9357", "fromAccountNumber": "X5155", "fromAccountName": "vamsi", "toAccountId": "28EEEAA3-6CAD-0466-0CC4-D852E68D4D1A", "toAccountNumber": "X6022", "toAccountName": "5/3 ESSENTIAL CHECKING", "expressDelivery": true, "creationDate": "2018-01-25", "deliveryDate": "2018-01-26", "frequency": "ONCE_A_WEEK", "status": "Complete", "displayStatus": "Completed", "editable": false, "cancelable": false, "type": "EXTERNAL_TRANSFER"}, {"id": "462AA725-7990-EA11-E3C6-03B387AA128F", "recurringId": "462AA725-7990-EA11-E3C6-03B387AA128F", "amount": 10.12, "dueDate": "2018-02-02", "displayId": "3787861", "fromAccountId": "89B62A9C-034D-7D08-68FE-B9CF86CA9357", "fromAccountNumber": "X5155", "fromAccountName": "vamsi", "toAccountId": "28EEEAA3-6CAD-0466-0CC4-D852E68D4D1A", "toAccountNumber": "X6022", "toAccountName": "5/3 ESSENTIAL CHECKING", "expressDelivery": true, "creationDate": "2018-02-01", "deliveryDate": "2018-02-02", "frequency": "ONCE_A_WEEK", "status": "Complete", "displayStatus": "Completed", "editable": false, "cancelable": false, "type": "EXTERNAL_TRANSFER"}, {"id": "A2C2E7CD-6AA9-A3FD-B3D8-************", "recurringId": "A2C2E7CD-6AA9-A3FD-B3D8-************", "amount": 10.12, "dueDate": "2018-02-02", "displayId": "3787858", "fromAccountId": "89B62A9C-034D-7D08-68FE-B9CF86CA9357", "fromAccountNumber": "X5155", "fromAccountName": "vamsi", "toAccountId": "28EEEAA3-6CAD-0466-0CC4-D852E68D4D1A", "toAccountNumber": "X6022", "toAccountName": "5/3 ESSENTIAL CHECKING", "expressDelivery": true, "creationDate": "2018-02-01", "deliveryDate": "2018-02-02", "frequency": "ONCE_A_WEEK", "status": "Complete", "displayStatus": "Completed", "editable": false, "cancelable": false, "type": "EXTERNAL_TRANSFER"}, {"id": "4D168C0D-A7CE-BC5B-6361-E20DF4520F8E", "recurringId": "4D168C0D-A7CE-BC5B-6361-E20DF4520F8E", "amount": 10.12, "dueDate": "2018-02-08", "displayId": "525672", "fromAccountId": "89B62A9C-034D-7D08-68FE-B9CF86CA9357", "fromAccountNumber": "X5155", "fromAccountName": "vamsi", "toAccountId": "28EEEAA3-6CAD-0466-0CC4-D852E68D4D1A", "toAccountNumber": "X6022", "toAccountName": "5/3 ESSENTIAL CHECKING", "expressDelivery": true, "memo": "Test", "deliveryDate": "2018-02-09", "frequency": "ONCE_A_WEEK", "numberOfActivities": -1, "status": "Cancelled", "displayStatus": "Canceled", "editable": false, "cancelable": false, "type": "EXTERNAL_TRANSFER"}, {"id": "A2B89A59-1521-E74A-6E31-AA2561EA11F1", "recurringId": "A2B89A59-1521-E74A-6E31-AA2561EA11F1", "amount": 10.12, "dueDate": "2018-02-08", "displayId": "525673", "fromAccountId": "89B62A9C-034D-7D08-68FE-B9CF86CA9357", "fromAccountNumber": "X5155", "fromAccountName": "vamsi", "toAccountId": "28EEEAA3-6CAD-0466-0CC4-D852E68D4D1A", "toAccountNumber": "X6022", "toAccountName": "5/3 ESSENTIAL CHECKING", "expressDelivery": true, "memo": "Test", "deliveryDate": "2018-02-09", "frequency": "ONCE_A_WEEK", "numberOfActivities": -1, "status": "Cancelled", "displayStatus": "Canceled", "editable": false, "cancelable": false, "type": "EXTERNAL_TRANSFER"}], "shouldPromptForIncome": false}