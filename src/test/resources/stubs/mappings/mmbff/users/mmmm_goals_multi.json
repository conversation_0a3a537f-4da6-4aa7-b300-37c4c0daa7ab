{"mappings": [{"priority": 101, "request": {"method": "GET", "urlPattern": "/services/account/list.*", "customMatcher": {"name": "jwt-matcher", "parameters": {"payload": {"username": "mmmm_goals_multi"}}}}, "response": {"status": 200, "bodyFileName": "mmbff/users/mmmm_goals_multi/account_list_response.json", "headers": {"Content-Type": "application/json"}}}, {"priority": 101, "request": {"method": "GET", "urlPattern": "/services/transferandpay/profile.*", "customMatcher": {"name": "jwt-matcher", "parameters": {"payload": {"username": "mmmm_goals_multi"}}}}, "response": {"status": 200, "bodyFileName": "mmbff/users/mmmm_goals_multi/profile_response.json", "headers": {"Content-Type": "application/json"}}}]}