{"mappings": [{"priority": 103, "request": {"method": "GET", "urlPattern": "/services/account/list.*", "customMatcher": {"name": "jwt-matcher", "parameters": {"payload": {"username": "mmmm_billpayee_no_external"}}}}, "response": {"status": 200, "bodyFileName": "mmbff/users/mmmm_billpayee_no_external/account_list_response.json", "headers": {"Content-Type": "application/json"}}}, {"priority": 103, "request": {"method": "GET", "urlPattern": "/services/transferandpay/profile.*", "customMatcher": {"name": "jwt-matcher", "parameters": {"payload": {"username": "mmmm_billpayee_no_external"}}}}, "response": {"status": 200, "bodyFileName": "mmbff/users/mmmm_billpayee_no_external/profile_response.json", "headers": {"Content-Type": "application/json"}}}, {"priority": 103, "request": {"method": "GET", "urlPattern": "/services/billpay/profile.*", "customMatcher": {"name": "jwt-matcher", "parameters": {"payload": {"username": "mmmm_billpayee_no_external"}}}}, "response": {"status": 200, "bodyFileName": "mmbff/users/mmmm_billpayee_no_external/billpay_profile_response.json", "headers": {"Content-Type": "application/json"}}}]}