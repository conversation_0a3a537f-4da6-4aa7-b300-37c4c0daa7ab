{"mappings": [{"priority": 100, "request": {"method": "GET", "urlPattern": "/services/account/list.*", "customMatcher": {"name": "jwt-matcher", "parameters": {"payload": {"username": "mmmm_ddaonly"}}}}, "response": {"status": 200, "bodyFileName": "mmbff/users/mmmm_ddaonly/account_list_response.json", "headers": {"Content-Type": "application/json"}}}, {"priority": 100, "request": {"method": "GET", "urlPattern": "/services/transferandpay/profile.*", "customMatcher": {"name": "jwt-matcher", "parameters": {"payload": {"username": "mmmm_ddaonly"}}}}, "response": {"status": 200, "bodyFileName": "mmbff/users/mmmm_ddaonly/profile_response.json", "headers": {"Content-Type": "application/json"}}}]}