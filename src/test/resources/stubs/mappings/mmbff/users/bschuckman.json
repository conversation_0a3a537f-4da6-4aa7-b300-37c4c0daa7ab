{"mappings": [{"priority": 103, "request": {"method": "GET", "urlPattern": "/services/account/list.*", "customMatcher": {"name": "jwt-matcher", "parameters": {"payload": {"username": "b<PERSON><PERSON><PERSON>"}}}}, "response": {"status": 200, "bodyFileName": "mmbff/users/bschuckman/account_list_response.json", "headers": {"Content-Type": "application/json"}}}, {"priority": 103, "request": {"method": "GET", "urlPattern": "/services/transferandpay/profile.*", "customMatcher": {"name": "jwt-matcher", "parameters": {"payload": {"username": "b<PERSON><PERSON><PERSON>"}}}}, "response": {"status": 200, "bodyFileName": "mmbff/users/bschuckman/profile_response.json", "headers": {"Content-Type": "application/json"}}}, {"priority": 101, "request": {"method": "GET", "urlPathPattern": "/services/transferandpay/activity", "customMatcher": {"name": "jwt-matcher", "parameters": {"payload": {"username": "b<PERSON><PERSON><PERSON>"}}}}, "response": {"status": 200, "bodyFileName": "mmbff/users/bschuckman/xferpay_activity_response.json", "headers": {"Content-Type": "application/json"}}}, {"priority": 101, "request": {"method": "GET", "urlPathPattern": "/services/account/detail", "customMatcher": {"name": "jwt-matcher", "parameters": {"payload": {"username": "b<PERSON><PERSON><PERSON>"}}}, "queryParameters": {"id": {"equalTo": "1693217e-cb05-43fe-9d05-bce8d79d679f"}}}, "response": {"status": 200, "bodyFileName": "mmbff/users/bschuckman/account_detail_il.json", "headers": {"Content-Type": "application/json"}}}, {"priority": 101, "request": {"method": "GET", "urlPathPattern": "/services/account/transaction/history", "customMatcher": {"name": "jwt-matcher", "parameters": {"payload": {"username": "b<PERSON><PERSON><PERSON>"}}}, "queryParameters": {"accountId": {"equalTo": "1693217e-cb05-43fe-9d05-bce8d79d679f"}}}, "response": {"status": 200, "bodyFileName": "mmbff/users/bschuckman/account_history_sav_9014.json", "headers": {"Content-Type": "application/json"}}}, {"priority": 100, "request": {"method": "GET", "urlPathPattern": "/services/account/detail", "customMatcher": {"name": "jwt-matcher", "parameters": {"payload": {"username": "b<PERSON><PERSON><PERSON>"}}}, "queryParameters": {"id": {"equalTo": "6d85c192-b6c9-4dae-8df3-2aa266364e01"}}}, "response": {"status": 200, "bodyFileName": "mmbff/users/bschuckman/account_detail_el.json", "headers": {"Content-Type": "application/json"}}}, {"priority": 100, "request": {"method": "GET", "urlPathPattern": "/services/account/transaction/history", "customMatcher": {"name": "jwt-matcher", "parameters": {"payload": {"username": "b<PERSON><PERSON><PERSON>"}}}, "queryParameters": {"accountId": {"equalTo": "6d85c192-b6c9-4dae-8df3-2aa266364e01"}}}, "response": {"status": 200, "bodyFileName": "mmbff/users/bschuckman/account_history_el.json", "headers": {"Content-Type": "application/json"}}}, {"priority": 100, "request": {"method": "GET", "urlPathPattern": "/services/account/detail", "customMatcher": {"name": "jwt-matcher", "parameters": {"payload": {"username": "b<PERSON><PERSON><PERSON>"}}}, "queryParameters": {"id": {"equalTo": "********-14ad-4d6a-b0de-5f16a0413a02"}}}, "response": {"status": 200, "bodyFileName": "mmbff/users/bschuckman/account_detail_dda_3426.json", "headers": {"Content-Type": "application/json"}}}, {"priority": 101, "request": {"method": "GET", "urlPathPattern": "/services/account/detail", "customMatcher": {"name": "jwt-matcher", "parameters": {"payload": {"username": "b<PERSON><PERSON><PERSON>"}}}, "queryParameters": {"id": {"equalTo": "3be8332-bd2b-4d42-a96a-310a7d52d4f9"}}}, "response": {"status": 200, "bodyFileName": "mmbff/users/bschuckman/account_detail_sav_9014.json", "headers": {"Content-Type": "application/json"}}}, {"priority": 101, "request": {"method": "GET", "urlPathPattern": "/services/account/transaction/history", "customMatcher": {"name": "jwt-matcher", "parameters": {"payload": {"username": "b<PERSON><PERSON><PERSON>"}}}, "queryParameters": {"accountId": {"equalTo": "3be8332-bd2b-4d42-a96a-310a7d52d4f9"}}}, "response": {"status": 200, "bodyFileName": "mmbff/users/bschuckman/account_history_sav_9014.json", "headers": {"Content-Type": "application/json"}}}, {"priority": 101, "request": {"method": "GET", "urlPathPattern": "/services/account/transaction/history", "customMatcher": {"name": "jwt-matcher", "parameters": {"payload": {"username": "b<PERSON><PERSON><PERSON>"}}}, "queryParameters": {"accountId": {"equalTo": "********-14ad-4d6a-b0de-5f16a0413a02"}}}, "response": {"status": 200, "bodyFileName": "mmbff/users/bschuckman/account_history_dda_3426.json", "headers": {"Content-Type": "application/json"}}}, {"priority": 101, "request": {"method": "GET", "urlPathPattern": "/services/account/transaction/history/statement", "customMatcher": {"name": "jwt-matcher", "parameters": {"payload": {"username": "b<PERSON><PERSON><PERSON>"}}}, "queryParameters": {"accountId": {"equalTo": "********-14ad-4d6a-b0de-5f16a0413a02"}, "statementCycleId": {"equalTo": "C"}}}, "response": {"status": 200, "bodyFileName": "mmbff/users/bschuckman/account_statement_dda_3426.json", "headers": {"Content-Type": "application/json"}}}, {"priority": 101, "request": {"method": "GET", "urlPathPattern": "/services/account/transaction/history/statementCycles", "customMatcher": {"name": "jwt-matcher", "parameters": {"payload": {"username": "b<PERSON><PERSON><PERSON>"}}}, "queryParameters": {"accountId": {"equalTo": "********-14ad-4d6a-b0de-5f16a0413a02"}}}, "response": {"status": 200, "bodyFileName": "mmbff/users/bschuckman/account_statement_cycles_dda_3426.json", "headers": {"Content-Type": "application/json"}}}, {"priority": 101, "request": {"method": "GET", "urlPathPattern": "/services/account/transaction/history/statement", "customMatcher": {"name": "jwt-matcher", "parameters": {"payload": {"username": "b<PERSON><PERSON><PERSON>"}}}, "queryParameters": {"accountId": {"equalTo": "********-14ad-4d6a-b0de-5f16a0413a02"}, "statementCycleId": {"equalTo": "1"}}}, "response": {"status": 200, "bodyFileName": "mmbff/users/bschuckman/account_statement_dda_3426_c1.json", "headers": {"Content-Type": "application/json"}}}, {"priority": 101, "request": {"method": "GET", "urlPathPattern": "/services/billpay/profile", "customMatcher": {"name": "jwt-matcher", "parameters": {"payload": {"username": "b<PERSON><PERSON><PERSON>"}}}}, "response": {"status": 200, "bodyFileName": "mmbff/users/bschuckman/billpay_profile_response.json", "headers": {"Content-Type": "application/json"}}}, {"priority": 101, "request": {"method": "GET", "urlPathPattern": "/services/externaltransfer/userAccounts", "customMatcher": {"name": "jwt-matcher", "parameters": {"payload": {"username": "b<PERSON><PERSON><PERSON>"}}}}, "response": {"status": 200, "bodyFileName": "mmbff/users/bschuckman/userAccounts_response.json", "headers": {"Content-Type": "application/json"}}}, {"priority": 101, "request": {"method": "GET", "urlPathPattern": "/services/webpayment/profile", "customMatcher": {"name": "jwt-matcher", "parameters": {"payload": {"username": "b<PERSON><PERSON><PERSON>"}}}}, "response": {"status": 200, "bodyFileName": "mmbff/users/bschuckman/webpayment_profile_response.json", "headers": {"Content-Type": "application/json"}}}]}