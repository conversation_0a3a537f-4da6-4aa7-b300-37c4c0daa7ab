{"mappings": [{"priority": 102, "request": {"method": "GET", "urlPattern": "/services/account/list.*", "customMatcher": {"name": "jwt-matcher", "parameters": {"payload": {"username": "mmmm_loanonly"}}}}, "response": {"status": 200, "bodyFileName": "mmbff/users/mmmm_loanonly/account_list_response.json", "headers": {"Content-Type": "application/json"}}}, {"priority": 102, "request": {"method": "GET", "urlPattern": "/services/transferandpay/profile.*", "customMatcher": {"name": "jwt-matcher", "parameters": {"payload": {"username": "mmmm_loanonly"}}}}, "response": {"status": 200, "bodyFileName": "mmbff/users/mmmm_loanonly/profile_response.json", "headers": {"Content-Type": "application/json"}}}]}