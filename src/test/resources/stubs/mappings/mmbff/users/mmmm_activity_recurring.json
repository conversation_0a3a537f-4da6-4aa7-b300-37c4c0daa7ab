{"mappings": [{"priority": 101, "request": {"method": "GET", "urlPattern": "/services/account/list.*", "customMatcher": {"name": "jwt-matcher", "parameters": {"payload": {"username": "mmmm_activity_recurring"}}}}, "response": {"status": 200, "bodyFileName": "mmbff/users/mmmm_activity_recurring/account_list_response.json", "headers": {"Content-Type": "application/json"}}}, {"priority": 101, "request": {"method": "GET", "urlPattern": "/services/transferandpay/profile.*", "customMatcher": {"name": "jwt-matcher", "parameters": {"payload": {"username": "mmmm_activity_recurring"}}}}, "response": {"status": 200, "bodyFileName": "mmbff/users/mmmm_activity_recurring/profile_response.json", "headers": {"Content-Type": "application/json"}}}, {"priority": 101, "request": {"method": "GET", "urlPathPattern": "/services/transferandpay/activity", "queryParameters": {"dateRange": {"matches": ".*"}}, "customMatcher": {"name": "jwt-matcher", "parameters": {"payload": {"username": "mmmm_activity_recurring"}}}}, "response": {"status": 200, "bodyFileName": "mmbff/users/mmmm_activity_recurring/activity_response.json", "headers": {"Content-Type": "application/json"}}}]}