{"mappings": [{"priority": 100, "request": {"method": "GET", "urlPattern": "/services/externaltransfer/userAccounts", "customMatcher": {"name": "jwt-matcher", "parameters": {"payload": {"username": "mmmm_external_xfer_2"}}}}, "response": {"status": 200, "bodyFileName": "mmbff/users/mmmm_external_xfer_2/userAccounts.json", "headers": {"Content-Type": "application/json"}}}, {"priority": 100, "request": {"method": "GET", "urlPattern": "/services/webpayment/profile", "customMatcher": {"name": "jwt-matcher", "parameters": {"payload": {"username": "mmmm_external_xfer_2"}}}}, "response": {"status": 200, "bodyFileName": "mmbff/users/mmmm_external_xfer_2/webpayment_profile.json", "headers": {"Content-Type": "application/json"}}}, {"priority": 100, "request": {"method": "GET", "urlPattern": "/services/account/list.*", "customMatcher": {"name": "jwt-matcher", "parameters": {"payload": {"username": "mmmm_external_xfer_2"}}}}, "response": {"status": 200, "bodyFileName": "mmbff/users/mmmm_external_xfer_2/account_list.json", "headers": {"Content-Type": "application/json"}}}, {"priority": 100, "request": {"method": "GET", "urlPattern": "/services/transferandpay/activity", "customMatcher": {"name": "jwt-matcher", "parameters": {"payload": {"username": "mmmm_external_xfer_2"}}}}, "response": {"status": 200, "bodyFileName": "mmbff/users/mmmm_external_xfer_2/activity.json", "headers": {"Content-Type": "application/json"}}}, {"priority": 100, "request": {"method": "GET", "urlPattern": "/services/invoicedPayment/paymentAccount", "customMatcher": {"name": "jwt-matcher", "parameters": {"payload": {"username": "mmmm_external_xfer_2"}}}}, "response": {"status": 200, "bodyFileName": "mmbff/users/mmmm_external_xfer_2/invoice_payment_account.json", "headers": {"Content-Type": "application/json"}}}, {"priority": 100, "request": {"method": "GET", "urlPattern": "/services/billpay/profile", "customMatcher": {"name": "jwt-matcher", "parameters": {"payload": {"username": "mmmm_external_xfer_2"}}}}, "response": {"status": 200, "bodyFileName": "mmbff/users/mmmm_external_xfer_2/billpay_profile.json", "headers": {"Content-Type": "application/json"}}}, {"priority": 100, "request": {"method": "GET", "urlPattern": "/services/transferandpay/profile.*", "customMatcher": {"name": "jwt-matcher", "parameters": {"payload": {"username": "mmmm_external_xfer_2"}}}}, "response": {"status": 200, "bodyFileName": "mmbff/users/mmmm_external_xfer_2/profile.json", "headers": {"Content-Type": "application/json"}}}]}