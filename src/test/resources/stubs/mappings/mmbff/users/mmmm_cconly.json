{"mappings": [{"priority": 103, "request": {"method": "GET", "urlPattern": "/services/account/list.*", "customMatcher": {"name": "jwt-matcher", "parameters": {"payload": {"username": "mmmm_cconly"}}}}, "response": {"status": 200, "bodyFileName": "mmbff/users/mmmm_billpayee_no_external/account_list_response.json", "headers": {"Content-Type": "application/json"}}}, {"priority": 103, "request": {"method": "GET", "urlPattern": "/services/transferandpay/profile.*", "customMatcher": {"name": "jwt-matcher", "parameters": {"payload": {"username": "mmmm_cconly"}}}}, "response": {"status": 200, "bodyFileName": "mmbff/users/mmmm_billpayee_no_external/profile_response.json", "headers": {"Content-Type": "application/json"}}}, {"priority": 103, "request": {"method": "GET", "urlPattern": "/services/externaltransfer/userAccounts", "customMatcher": {"name": "jwt-matcher", "parameters": {"payload": {"username": "mmmm_cconly"}}}}, "response": {"status": 400, "bodyFileName": "mmbff/users/mmmm_cconly/externaltransfer_useraccounts.json", "headers": {"Content-Type": "application/json"}}}, {"priority": 101, "request": {"method": "GET", "urlPathPattern": "/services/webpayment/profile", "customMatcher": {"name": "jwt-matcher", "parameters": {"payload": {"username": "mmmm_cconly"}}}}, "response": {"status": 200, "bodyFileName": "mmbff/users/bschuckman/webpayment_profile_response.json", "headers": {"Content-Type": "application/json"}}}, {"priority": 100, "request": {"method": "GET", "urlPattern": "/services/invoicedPayment/paymentAccount", "customMatcher": {"name": "jwt-matcher", "parameters": {"payload": {"username": "mmmm_cconly"}}}}, "response": {"status": 200, "bodyFileName": "mmbff/users/mmmm_external_xfer_2/invoice_payment_account.json", "headers": {"Content-Type": "application/json"}}}]}