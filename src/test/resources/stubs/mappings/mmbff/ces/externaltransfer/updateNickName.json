{"mappings": [{"priority": 101, "request": {"method": "POST", "urlPathPattern": "/services/externaltransfer/updateNickName", "bodyPatterns": [{"matchesJsonPath": {"expression": "$.accountNickName", "contains": ""}}]}, "response": {"status": 400, "headers": {"Content-Type": "application/json"}, "jsonBody": {"status": "INVALID_REQUEST", "statusCode": "FIELD_VALIDATION_ERROR", "statusReason": "[{errorMessage='Account nickname is required'}, ]"}}}, {"priority": 100, "request": {"method": "POST", "urlPathPattern": "/services/externaltransfer/updateNickName", "bodyPatterns": [{"matchesJsonPath": {"expression": "$.accountNickName", "contains": "duplicate"}}]}, "response": {"status": 400, "headers": {"Content-Type": "application/json"}, "jsonBody": {"status": "CASHEDGE_ERROR", "statusCode": "UNKNOWN_ERROR_CODE", "statusReason": "The nickname you entered already has been added to your online profile."}}}, {"priority": 102, "request": {"method": "POST", "urlPathPattern": "/services/externaltransfer/updateNickName", "bodyPatterns": [{"matchesJsonPath": {"expression": "$.accountId", "contains": "field-validation-error"}}]}, "response": {"status": 400, "headers": {"Content-Type": "application/json"}, "jsonBody": {"status": "INVALID_REQUEST", "statusCode": "FIELD_VALIDATION_ERROR", "statusReason": "[{errorMessage='Account nickname is required'}, ]"}}}, {"priority": 110, "request": {"method": "POST", "urlPathPattern": "/services/externaltransfer/updateNickName"}, "response": {"status": 200, "bodyFileName": "mmbff/ces/services/externaltransfer/updateNickName_success.json", "headers": {"Content-Type": "application/json"}}}]}