{"mappings": [{"priority": 105, "request": {"method": "GET", "urlPathPattern": "/services/externaltransfer/userAccounts"}, "response": {"status": 200, "bodyFileName": "mmbff/ces/services/externaltransfer/userAccounts_approvalDenied.json", "headers": {"Content-Type": "application/json", "sub": "denied"}}}, {"priority": 999, "request": {"method": "GET", "urlPathPattern": "/services/externaltransfer/userAccounts"}, "response": {"status": 200, "bodyFileName": "mmbff/ces/services/externaltransfer/userAccounts_success.json", "headers": {"Content-Type": "application/json"}}}, {"priority": 102, "request": {"method": "DELETE", "urlPathPattern": "/services/externaltransfer/deleteExternalAccount"}, "response": {"status": 200, "bodyFileName": "mmbff/ces/services/externaltransfer/deleteAccountResponse.json", "headers": {"Content-Type": "application/json"}}}]}