{"mappings": [{"priority": 100, "request": {"method": "GET", "urlPathPattern": "/services/externaltransfer/limits", "queryParameters": {"fromAcctId": {"matches": ".*"}, "toAcctId": {"matches": ".*"}}}, "response": {"status": 200, "bodyFileName": "mmbff/ces/services/externaltransfer/limits.json", "headers": {"Content-Type": "application/json"}}}, {"priority": 101, "request": {"method": "GET", "urlPathPattern": "/services/externaltransfer/limits"}, "response": {"status": 200, "bodyFileName": "mmbff/ces/services/externaltransfer/limits.json", "headers": {"Content-Type": "application/json"}}}]}