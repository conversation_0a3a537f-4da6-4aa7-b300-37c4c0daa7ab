{"mappings": [{"priority": 100, "request": {"method": "GET", "urlPathPattern": "/services/externaltransfer/fiBrokerageRoutingInfo", "queryParameters": {"routingName": {"matches": "*********"}}}, "response": {"status": 404, "bodyFileName": "mmbff/ces/services/externaltransfer/financialInstitutionInfo_notFound.json", "headers": {"Content-Type": "application/json"}}}, {"priority": 101, "request": {"method": "GET", "urlPathPattern": "/services/externaltransfer/fiBrokerageRoutingInfo", "queryParameters": {"routingName": {"matches": "*********"}}}, "response": {"status": 400, "bodyFileName": "mmbff/ces/services/externaltransfer/financialInstitutionInfo_cashEdgeError.json", "headers": {"Content-Type": "application/json"}}}, {"priority": 102, "request": {"method": "GET", "urlPathPattern": "/services/externaltransfer/fiBrokerageRoutingInfo", "queryParameters": {"routingName": {"matches": "*********"}}}, "response": {"status": 500, "bodyFileName": "mmbff/ces/services/externaltransfer/financialInstitutionInfo_cashEdgeError.json", "headers": {"Content-Type": "application/json"}}}, {"priority": 110, "request": {"method": "GET", "urlPathPattern": "/services/externaltransfer/fiBrokerageRoutingInfo", "queryParameters": {"routingName": {"matches": ".*"}}}, "response": {"status": 200, "bodyFileName": "mmbff/ces/services/externaltransfer/financialInstitutionInfo.json", "headers": {"Content-Type": "application/json"}}}]}