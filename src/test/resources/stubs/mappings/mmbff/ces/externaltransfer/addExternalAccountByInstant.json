{"mappings": [{"priority": 100, "request": {"method": "POST", "urlPathPattern": "/tokenized/services/externaltransfer/addExternalAccountByInstant", "bodyPatterns": [{"matchesJsonPath": {"expression": "accountNickName", "contains": "VEB"}}]}, "response": {"status": 200, "bodyFileName": "mmbff/ces/services/externaltransfer/addExternalAccountByInstant_successNoVerification.json", "headers": {"Content-Type": "application/json"}}}, {"priority": 101, "request": {"method": "POST", "urlPathPattern": "/tokenized/services/externaltransfer/addExternalAccountByInstant", "bodyPatterns": [{"matchesJsonPath": {"expression": "accountNickName", "contains": "USA"}}]}, "response": {"status": 200, "bodyFileName": "mmbff/ces/services/externaltransfer/addExternalAccountByInstant_successAndVerification.json", "headers": {"Content-Type": "application/json"}}}, {"priority": 200, "request": {"method": "POST", "urlPathPattern": "/tokenized/services/externaltransfer/addExternalAccountByInstant"}, "response": {"status": 200, "bodyFileName": "mmbff/ces/services/externaltransfer/addExternalAccountByInstant_successAndVerification.json", "headers": {"Content-Type": "application/json"}}}]}