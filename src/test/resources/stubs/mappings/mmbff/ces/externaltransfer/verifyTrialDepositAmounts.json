{"mappings": [{"priority": 100, "request": {"method": "POST", "urlPathPattern": "/services/externaltransfer/verifyTrialDepositAmounts", "bodyPatterns": [{"matchesJsonPath": {"expression": "$.accountId", "contains": "cashEdgeError"}}]}, "response": {"status": 400, "bodyFileName": "mmbff/ces/services/externaltransfer/verifyTrialDepositResponse_badRequest.json", "headers": {"Content-Type": "application/json"}}}, {"priority": 101, "request": {"method": "POST", "urlPathPattern": "/services/externaltransfer/verifyTrialDepositAmounts", "bodyPatterns": [{"matchesJsonPath": "$.amounts[?(@ == '0.950')]"}]}, "response": {"status": 400, "bodyFileName": "mmbff/ces/services/externaltransfer/verifyTrialDepositResponse_badRequest.json", "headers": {"Content-Type": "application/json"}}}, {"priority": 999, "request": {"method": "POST", "urlPathPattern": "/services/externaltransfer/verifyTrialDepositAmounts"}, "response": {"status": 200, "bodyFileName": "mmbff/ces/services/externaltransfer/verifyTrialDepositResponse_success.json", "headers": {"Content-Type": "application/json"}}}]}