{"mappings": [{"priority": 192, "request": {"method": "POST", "urlPathPattern": "/tokenized/services/externaltransfer/addExternalAccountByRealTime", "bodyPatterns": [{"jsonPath": "$.cesAuthenticationParams[0].authId", "contains": "35562"}]}, "response": {"status": 200, "bodyFileName": "mmbff/ces/services/externaltransfer/addAccountRealTime_step1.json", "headers": {"Content-Type": "application/json"}}}, {"priority": 193, "request": {"method": "POST", "urlPathPattern": "/tokenized/services/externaltransfer/addExternalAccountByRealTime", "bodyPatterns": [{"jsonPath": "$.cesAuthenticationParams[0].authId", "contains": "mChosen"}]}, "response": {"status": 200, "bodyFileName": "mmbff/ces/services/externaltransfer/addAccountRealTime_step2.json", "headers": {"Content-Type": "application/json"}}}, {"priority": 194, "request": {"method": "POST", "urlPathPattern": "/tokenized/services/externaltransfer/addExternalAccountByRealTime", "bodyPatterns": [{"jsonPath": "$.cesAuthenticationParams[0].authId", "contains": "mAuthCode"}]}, "response": {"status": 200, "bodyFileName": "mmbff/ces/services/externaltransfer/addAccountRealTime_step3.json", "headers": {"Content-Type": "application/json"}}}, {"priority": 1000, "request": {"method": "POST", "urlPathPattern": "/tokenized/services/externaltransfer/addExternalAccountByRealTime", "bodyPatterns": [{"matchesJsonPath": {"expression": "$.accountId", "contains": ".*"}}]}, "response": {"status": 200, "bodyFileName": "mmbff/ces/services/externaltransfer/addExternalAccountRealTime_2fa.json", "headers": {"Content-Type": "application/json"}}}, {"priority": 101, "request": {"method": "POST", "urlPathPattern": "/tokenized/services/externaltransfer/addExternalAccountByRealTime", "bodyPatterns": [{"jsonPath": "$.accountNickName", "contains": "USA"}]}, "response": {"status": 200, "bodyFileName": "mmbff/ces/services/externaltransfer/addExternalAccountRealTime_confirm.json", "headers": {"Content-Type": "application/json"}}}]}