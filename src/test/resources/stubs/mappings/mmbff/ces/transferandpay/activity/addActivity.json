{"mappings": [{"priority": 100, "request": {"method": "POST", "urlPattern": "/services/transferandpay/activity", "bodyPatterns": [{"matchesJsonPath": {"expression": "$.amount", "contains": "428"}}]}, "response": {"status": 428, "bodyFileName": "mmbff/ces/services/transferandpay/activity/addActivity/add_activity_428.json", "headers": {"Content-Type": "application/json"}}}, {"priority": 105, "request": {"method": "POST", "urlPattern": "/services/transferandpay/activity"}, "response": {"status": 200, "bodyFileName": "mmbff/ces/services/transferandpay/activity/addActivity/add_activity.json", "headers": {"Content-Type": "application/json"}}}]}