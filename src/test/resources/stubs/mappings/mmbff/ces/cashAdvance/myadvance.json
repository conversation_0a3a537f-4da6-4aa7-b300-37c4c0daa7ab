{"mappings": [{"priority": 100, "request": {"method": "POST", "urlPattern": "/services/cashAdvance", "bodyPatterns": [{"matchesJsonPath": {"expression": "$.amount", "contains": "5.15"}}]}, "response": {"status": 200, "bodyFileName": "mmbff/ces/services/cashAdvance/pay_advance.json", "headers": {"Content-Type": "application/json"}}}, {"priority": 105, "request": {"method": "POST", "urlPattern": "/services/cashAdvance"}, "response": {"status": 200, "bodyFileName": "mmbff/ces/services/cashAdvance/add_advance.json", "headers": {"Content-Type": "application/json"}}}]}