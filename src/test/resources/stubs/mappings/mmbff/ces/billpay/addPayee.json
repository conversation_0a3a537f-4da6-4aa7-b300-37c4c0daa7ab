{"mappings": [{"priority": 105, "request": {"method": "POST", "urlPattern": "/tokenized/services/billpay/addPayee", "bodyPatterns": [{"matchesJsonPath": {"expression": "$.cesPayee.cesAddress.streetLine1", "contains": "bad"}}]}, "response": {"status": 422, "bodyFileName": "mmbff/ces/services/billpay/addPayee/add_payee_response_address_fixed.json", "headers": {"Content-Type": "application/json"}}}, {"priority": 104, "request": {"method": "POST", "urlPattern": "/tokenized/services/billpay/addPayee", "bodyPatterns": [{"matchesJsonPath": {"expression": "$.cesPayee.cesAddress.postalCode", "contains": "0"}}]}, "response": {"status": 400, "bodyFileName": "mmbff/ces/services/billpay/addPayee/add_payee_response_needs_custom.json", "headers": {"Content-Type": "application/json"}}}, {"priority": 103, "request": {"method": "POST", "urlPattern": "/tokenized/services/billpay/addPayee", "bodyPatterns": [{"matchesJsonPath": {"expression": "$.cesPayee.nickname", "contains": "zip2"}}]}, "response": {"status": 400, "bodyFileName": "mmbff/ces/services/billpay/addPayee/add_payee_response_needs_zip2.json", "headers": {"Content-Type": "application/json"}}}, {"priority": 102, "request": {"method": "POST", "urlPattern": "/tokenized/services/billpay/addPayee", "bodyPatterns": [{"matchesJsonPath": {"expression": "$.cesPayee.nickname", "contains": "zip1"}}]}, "response": {"status": 400, "bodyFileName": "mmbff/ces/services/billpay/addPayee/add_payee_response_needs_zip1.json", "headers": {"Content-Type": "application/json"}}}, {"priority": 1001, "request": {"method": "POST", "urlPattern": "/tokenized/services/billpay/addPayee", "bodyPatterns": [{"matchesJsonPath": {"expression": "$.cesPayee.nickname", "contains": "work"}}]}, "response": {"status": 200, "bodyFileName": "mmbff/ces/services/billpay/addPayee/add_payee_response.json", "headers": {"Content-Type": "application/json"}}}, {"priority": 1002, "request": {"method": "POST", "urlPattern": "/tokenized/services/billpay/addPayee", "bodyPatterns": [{"matchesJsonPath": {"expression": "$.cesPayee.nickname", "contains": "acctid"}}]}, "response": {"status": 422, "bodyFileName": "mmbff/ces/services/billpay/addPayee/add_cust_payee_response_acctid.json", "headers": {"Content-Type": "application/json"}}}]}