{"mappings": [{"priority": 1000, "request": {"method": "GET", "urlPattern": "/services/billpay/profile", "headers": {"X-Device-UID": {"contains": "billpay-disabled"}}}, "response": {"status": 401, "bodyFileName": "mmbff/ces/services/billpay/profile/profile_billpay_not_enabled.json", "headers": {"Content-Type": "application/json"}}}, {"priority": 1001, "request": {"method": "GET", "urlPattern": "/services/billpay/profile"}, "response": {"status": 200, "bodyFileName": "mmbff/ces/services/billpay/profile/profile.json", "headers": {"Content-Type": "application/json"}}}]}