{"status": "SUCCESS", "accounts": [{"id": "7985b1c3-81eb-47df-8579-f7d8eccfb11c", "sortOrder": 0, "hidden": false, "cashAdvanceEligible": false, "displayTransactionOk": true, "displayAccountDetailsOk": true, "remoteAccountDetailsTemplate": false, "noBalanceDisplay": false, "statementEligible": true, "accountDetailsExternal": false, "displayCardManagementOk": true, "authUserMultiCardAccount": false, "familyCardAccount": false, "hasParentAccountNumber": false, "displayName": "5/3 ESSENTIAL CHECKING", "shortName": "CK1", "displayAccountNumber": "X6102", "accountType": "DDA", "affiliate": "5/3", "revolvingLineOfCreditAccount": false, "depositAccount": true, "checksAccount": true, "checksReorder": true, "expressChecking": false, "basicChecking": false, "ableChecking": false, "reloadablePrepaidCard": false, "cashAdvanceAccount": false, "creditCardAccount": false, "trustAccount": false, "blockedForCredit": false, "accountDescription": "5/3 ESSENTIAL CHECKING", "bankEmployeeAccount": false, "openDate": "2021-09-13", "availableBalance": 8971.45, "ledgerBalance": 8977.45, "searchableOk": true, "activitySettingManageAlertsOk": true, "activitySettingChangePinOk": true, "activitySettingActivateCardOk": true, "fromTransferOk": true, "transferToOk": true, "goodFunds": true, "billPayOk": true, "closed": false, "business": false, "sweepAccount": false, "linkedPca": false, "brokerageAccount": false, "liability": false, "supportsStatementCycles": true, "nonPlasticCard": false, "transactionExportEligible": true, "displayFullAccountAndRoutingOk": true, "cardReplacementCode": "CARD_ACTIVE", "customerDataVendorManaged": false, "externalTransferEligible": true, "changeAddressEligible": true, "smartSavingsAccount": true, "momentumSavings": false, "momentumChecking": false}, {"id": "1aacb823-e12e-48ff-9e56-9db280dc7b6c", "sortOrder": 1, "hidden": false, "cashAdvanceEligible": false, "displayTransactionOk": true, "displayAccountDetailsOk": true, "remoteAccountDetailsTemplate": false, "noBalanceDisplay": false, "statementEligible": true, "accountDetailsExternal": false, "displayCardManagementOk": true, "authUserMultiCardAccount": false, "familyCardAccount": false, "hasParentAccountNumber": false, "displayName": "FIFTH THIRD MOMENTUM SAVINGS", "shortName": "SV", "displayAccountNumber": "X0854", "accountType": "SAV", "affiliate": "5/3", "revolvingLineOfCreditAccount": false, "depositAccount": true, "checksAccount": true, "checksReorder": false, "expressChecking": false, "basicChecking": false, "ableChecking": false, "reloadablePrepaidCard": false, "cashAdvanceAccount": false, "creditCardAccount": false, "trustAccount": false, "blockedForCredit": false, "accountDescription": "FIFTH THIRD MOMENTUM SAVINGS", "bankEmployeeAccount": false, "openDate": "2021-09-13", "availableBalance": 8983.45, "ledgerBalance": 8977.45, "searchableOk": true, "activitySettingManageAlertsOk": true, "activitySettingChangePinOk": true, "activitySettingActivateCardOk": true, "fromTransferOk": true, "transferToOk": true, "goodFunds": true, "billPayOk": true, "closed": false, "business": false, "sweepAccount": false, "linkedPca": false, "brokerageAccount": false, "liability": false, "supportsStatementCycles": true, "nonPlasticCard": false, "transactionExportEligible": true, "displayFullAccountAndRoutingOk": true, "cardReplacementCode": "CARD_ACTIVE", "customerDataVendorManaged": false, "externalTransferEligible": true, "changeAddressEligible": true, "smartSavingsAccount": false, "momentumSavings": true, "momentumChecking": false}], "goals": [{"id": "32224C79-AC8D-0843-FA57-01773189621E", "accountId": "1aacb823-e12e-48ff-9e56-9db280dc7b6c", "displayAccountNumber": "X0854", "name": "MyTestGOalJ", "category": "Vacation", "imageLocation": "vacation", "targetAmount": 100, "progressAmount": 149, "targetDate": "2022-06-01", "created": "2021-12-01T19:36:03.650Z", "editable": true, "generalSavings": false}, {"id": "AB99E17B-5AB2-A3B2-6792-96C53E6A9F37", "accountId": "1aacb823-e12e-48ff-9e56-9db280dc7b6c", "displayAccountNumber": "X0854", "name": "Tiny house", "category": "Home", "imageLocation": "home", "targetAmount": 6150, "progressAmount": 28.55, "targetDate": "2022-11-29", "created": "2021-11-29T17:02:22.318Z", "updated": "2021-11-29T17:05:23.370Z", "editable": true, "generalSavings": false}, {"id": "A0C95895-A46A-1543-A307-39D5F0392D5D", "accountId": "1aacb823-e12e-48ff-9e56-9db280dc7b6c", "displayAccountNumber": "X0854", "name": "Mustang Car", "category": "Car", "imageLocation": "car", "targetAmount": 11111, "progressAmount": 56, "targetDate": "2033-11-18", "created": "2021-11-17T20:04:06.078Z", "updated": "2021-11-22T23:36:32.088Z", "editable": true, "generalSavings": false}, {"id": "155DAC68-8624-AEE8-0A6A-C56E427E387A", "accountId": "1aacb823-e12e-48ff-9e56-9db280dc7b6c", "displayAccountNumber": "X0854", "name": "General Savings", "category": "General", "targetAmount": 8983.45, "progressAmount": 8749.9, "created": "2022-04-04T18:35:04.521Z", "editable": false, "generalSavings": true}], "quickActions": [{"displayOrder": 0, "displayType": "QUICK_DEPOSIT", "displayText": "<PERSON> De<PERSON>t"}, {"displayOrder": 1, "displayType": "ZELLE", "displayText": "Send Money with <PERSON><PERSON>®"}, {"displayOrder": 2, "displayType": "OPEN_SAVINGS", "displayText": "Open Savings Account"}, {"displayOrder": 3, "displayType": "MAKE_PAYMENT", "displayText": "Make Payment"}, {"displayOrder": 4, "displayType": "TRANSFER_FUNDS", "displayText": "Transfer Funds"}, {"displayOrder": 5, "displayType": "DOCUMENT_CENTER", "displayText": "View Documents"}]}