{"status": "SUCCESS", "cesRealTimeAccountVerificationStatus": {"statusCode": "5092", "statusDesc": "Two FA Online Account Verification", "accountNumber": "X0698", "verificationMode": "online", "verificationStatus": "Requires Approval", "failureReasonCode": "303", "failureReasonDesc": "303", "twoFactorAccountVerificationParam": [{"authId": "mChosen", "authValue": "To verify your identity, we need to send you an authorization code. Please choose the device by clicking on the corresponding radio button Or Enter the Device Option (E.g text_1 or voice_1 or email_1) for delivering the authorization code to the user preferred device<br>text_1 for XXX-XXX-9977 : Text message<br>voice_1 for XXX-XXX-9977 : Phone call<br>text_2 for XXX-XXX-5961 : Text message<br>voice_2 for XXX-XXX-5961 : Phone call<br>text_3 for XXX-XXX-8663 : Text message<br>voice_3 for XXX-XXX-8663 : Phone call"}]}}