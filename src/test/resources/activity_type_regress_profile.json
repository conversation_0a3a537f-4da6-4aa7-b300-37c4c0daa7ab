{"status": "SUCCESS", "retrievalErrors": [], "activities": [{"id": "********", "amount": 5, "dueDate": "2021-09-15", "displayId": "********", "fromAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "fromAccountNumber": "X4500", "fromAccountName": "5/3 ESSENTIAL CHECKING", "toAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "toAccountNumber": "X1821", "toAccountName": "MASTERCARD ACCOUNT", "expressDelivery": false, "status": "5", "displayStatus": "In Process", "createTimestamp": "2021-09-14T18:39:04.000Z", "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER"}, {"id": "********", "amount": 0.05, "dueDate": "2021-09-15", "displayId": "********", "fromAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "fromAccountNumber": "X4500", "fromAccountName": "5/3 ESSENTIAL CHECKING", "toAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "toAccountNumber": "X1821", "toAccountName": "MASTERCARD ACCOUNT", "expressDelivery": false, "status": "6", "displayStatus": "Unsuccessful", "createTimestamp": "2021-09-15T18:21:21.000Z", "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER"}, {"id": "********", "recurringId": "35938", "amount": 132, "dueDate": "2021-09-16", "displayId": "********", "fromAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "fromAccountNumber": "X4500", "fromAccountName": "5/3 ESSENTIAL CHECKING", "toAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "toAccountNumber": "X1821", "toAccountName": "MASTERCARD ACCOUNT", "expressDelivery": false, "frequency": "ONCE_A_MONTH", "numberOfActivities": 3, "numberOfRemainingActivities": 1, "status": "5", "displayStatus": "In Process", "createTimestamp": "2021-07-15T13:18:32.000Z", "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER"}, {"id": "********", "recurringId": "35852", "amount": 0.87, "dueDate": "2021-09-16", "displayId": "********", "fromAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "fromAccountNumber": "X4500", "fromAccountName": "5/3 ESSENTIAL CHECKING", "toAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "toAccountNumber": "X1821", "toAccountName": "MASTERCARD ACCOUNT", "expressDelivery": false, "status": "5", "displayStatus": "In Process", "createTimestamp": "2021-09-09T04:05:20.000Z", "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER"}, {"id": "********", "recurringId": "35850", "amount": 0.05, "dueDate": "2021-09-16", "displayId": "********", "fromAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "fromAccountNumber": "X1821", "fromAccountName": "MASTERCARD ACCOUNT", "toAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "toAccountNumber": "X4500", "toAccountName": "5/3 ESSENTIAL CHECKING", "expressDelivery": false, "additionalPrincipleAmount": 0, "status": "5", "displayStatus": "In Process", "createTimestamp": "2021-09-09T04:05:20.000Z", "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER"}, {"id": "********", "recurringId": "35851", "amount": 0.88, "dueDate": "2021-09-16", "displayId": "********", "fromAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "fromAccountNumber": "X1821", "fromAccountName": "MASTERCARD ACCOUNT", "toAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "toAccountNumber": "X4500", "toAccountName": "5/3 ESSENTIAL CHECKING", "expressDelivery": false, "status": "5", "displayStatus": "In Process", "createTimestamp": "2021-09-09T04:05:20.000Z", "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER"}, {"id": "********", "recurringId": "36064", "amount": 0.23, "dueDate": "2021-09-16", "displayId": "********", "fromAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "fromAccountNumber": "X1821", "fromAccountName": "MASTERCARD ACCOUNT", "toAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "toAccountNumber": "X4500", "toAccountName": "5/3 ESSENTIAL CHECKING", "expressDelivery": false, "status": "5", "displayStatus": "In Process", "createTimestamp": "2021-09-09T04:05:22.000Z", "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER"}, {"id": "********", "amount": 3, "dueDate": "2021-09-16", "displayId": "********", "fromAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "fromAccountNumber": "X4500", "fromAccountName": "5/3 ESSENTIAL CHECKING", "toAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "toAccountNumber": "X1821", "toAccountName": "MASTERCARD ACCOUNT", "expressDelivery": false, "status": "5", "displayStatus": "In Process", "createTimestamp": "2021-09-15T18:12:52.000Z", "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER"}, {"id": "********", "amount": 0.05, "dueDate": "2021-09-16", "displayId": "********", "fromAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "fromAccountNumber": "X4500", "fromAccountName": "5/3 ESSENTIAL CHECKING", "toAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "toAccountNumber": "X1821", "toAccountName": "MASTERCARD ACCOUNT", "expressDelivery": false, "status": "5", "displayStatus": "In Process", "createTimestamp": "2021-09-15T18:24:46.000Z", "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER"}, {"id": "********", "amount": 0.07, "dueDate": "2021-09-16", "displayId": "********", "fromAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "fromAccountNumber": "X4500", "fromAccountName": "5/3 ESSENTIAL CHECKING", "toAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "toAccountNumber": "X1821", "toAccountName": "MASTERCARD ACCOUNT", "expressDelivery": false, "status": "5", "displayStatus": "In Process", "createTimestamp": "2021-09-15T18:25:36.000Z", "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER"}, {"id": "********", "amount": 0.17, "dueDate": "2021-09-16", "displayId": "********", "fromAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "fromAccountNumber": "X4500", "fromAccountName": "5/3 ESSENTIAL CHECKING", "toAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "toAccountNumber": "X1821", "toAccountName": "MASTERCARD ACCOUNT", "expressDelivery": false, "status": "5", "displayStatus": "In Process", "createTimestamp": "2021-09-15T18:26:37.000Z", "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER"}, {"id": "********", "recurringId": "36105", "amount": 0.09, "dueDate": "2021-09-16", "displayId": "********", "fromAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "fromAccountNumber": "X4500", "fromAccountName": "5/3 ESSENTIAL CHECKING", "toAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "toAccountNumber": "X1821", "toAccountName": "MASTERCARD ACCOUNT", "expressDelivery": false, "frequency": "ONCE_A_WEEK", "numberOfActivities": 0, "status": "5", "displayStatus": "In Process", "createTimestamp": "2021-09-15T18:47:25.000Z", "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER"}, {"id": "********", "amount": 4, "dueDate": "2021-09-17", "displayId": "********", "fromAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "fromAccountNumber": "X4500", "fromAccountName": "5/3 ESSENTIAL CHECKING", "toAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "toAccountNumber": "X1821", "toAccountName": "MASTERCARD ACCOUNT", "expressDelivery": false, "status": "5", "displayStatus": "In Process", "createTimestamp": "2021-09-14T18:39:44.000Z", "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER"}, {"id": "********", "amount": 278, "dueDate": "2021-09-17", "displayId": "********", "fromAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "fromAccountNumber": "X4500", "fromAccountName": "5/3 ESSENTIAL CHECKING", "toAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "toAccountNumber": "X1821", "toAccountName": "MASTERCARD ACCOUNT", "expressDelivery": false, "status": "6", "displayStatus": "Unsuccessful", "createTimestamp": "2021-09-17T16:22:17.000Z", "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER"}, {"id": "********", "amount": 278, "dueDate": "2021-09-17", "displayId": "********", "fromAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "fromAccountNumber": "X4500", "fromAccountName": "5/3 ESSENTIAL CHECKING", "toAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "toAccountNumber": "X1821", "toAccountName": "MASTERCARD ACCOUNT", "expressDelivery": false, "status": "6", "displayStatus": "Unsuccessful", "createTimestamp": "2021-09-17T16:22:20.000Z", "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER"}, {"id": "********", "amount": 278, "dueDate": "2021-09-17", "displayId": "********", "fromAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "fromAccountNumber": "X4500", "fromAccountName": "5/3 ESSENTIAL CHECKING", "toAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "toAccountNumber": "X1821", "toAccountName": "MASTERCARD ACCOUNT", "expressDelivery": false, "status": "6", "displayStatus": "Unsuccessful", "createTimestamp": "2021-09-17T16:22:22.000Z", "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER"}, {"id": "********", "recurringId": "35954", "amount": 0.11, "dueDate": "2021-09-21", "displayId": "********", "fromAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "fromAccountNumber": "X4500", "fromAccountName": "5/3 ESSENTIAL CHECKING", "toAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "toAccountNumber": "X1821", "toAccountName": "MASTERCARD ACCOUNT", "expressDelivery": false, "status": "5", "displayStatus": "In Process", "createTimestamp": "2021-09-14T04:05:34.000Z", "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER"}, {"id": "********", "recurringId": "36106", "amount": 0.14, "dueDate": "2021-09-21", "displayId": "********", "fromAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "fromAccountNumber": "X4500", "fromAccountName": "5/3 ESSENTIAL CHECKING", "toAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "toAccountNumber": "X1821", "toAccountName": "MASTERCARD ACCOUNT", "expressDelivery": false, "status": "5", "displayStatus": "In Process", "createTimestamp": "2021-09-15T22:01:24.000Z", "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER"}, {"id": "********", "recurringId": "35851", "amount": 0.88, "dueDate": "2021-09-23", "displayId": "********", "fromAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "fromAccountNumber": "X1821", "fromAccountName": "MASTERCARD ACCOUNT", "toAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "toAccountNumber": "X4500", "toAccountName": "5/3 ESSENTIAL CHECKING", "expressDelivery": false, "status": "5", "displayStatus": "In Process", "createTimestamp": "2021-09-16T04:05:27.000Z", "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER"}, {"id": "********", "recurringId": "35850", "amount": 0.05, "dueDate": "2021-09-23", "displayId": "********", "fromAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "fromAccountNumber": "X1821", "fromAccountName": "MASTERCARD ACCOUNT", "toAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "toAccountNumber": "X4500", "toAccountName": "5/3 ESSENTIAL CHECKING", "expressDelivery": false, "additionalPrincipleAmount": 0, "status": "5", "displayStatus": "In Process", "createTimestamp": "2021-09-16T04:05:27.000Z", "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER"}, {"id": "********", "recurringId": "35852", "amount": 0.87, "dueDate": "2021-09-23", "displayId": "********", "fromAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "fromAccountNumber": "X4500", "fromAccountName": "5/3 ESSENTIAL CHECKING", "toAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "toAccountNumber": "X1821", "toAccountName": "MASTERCARD ACCOUNT", "expressDelivery": false, "status": "0", "displayStatus": "Canceled", "createTimestamp": "2021-09-16T04:05:27.000Z", "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER"}, {"id": "********", "recurringId": "36064", "amount": 0.23, "dueDate": "2021-09-23", "displayId": "********", "fromAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "fromAccountNumber": "X1821", "fromAccountName": "MASTERCARD ACCOUNT", "toAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "toAccountNumber": "X4500", "toAccountName": "5/3 ESSENTIAL CHECKING", "expressDelivery": false, "status": "5", "displayStatus": "In Process", "createTimestamp": "2021-09-16T04:05:29.000Z", "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER"}, {"id": "********", "recurringId": "36105", "amount": 0.09, "dueDate": "2021-09-23", "displayId": "********", "fromAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "fromAccountNumber": "X4500", "fromAccountName": "5/3 ESSENTIAL CHECKING", "toAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "toAccountNumber": "X1821", "toAccountName": "MASTERCARD ACCOUNT", "expressDelivery": false, "status": "5", "displayStatus": "In Process", "createTimestamp": "2021-09-16T04:05:31.000Z", "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER"}, {"id": "********", "recurringId": "36114", "amount": 200, "dueDate": "2021-09-24", "displayId": "********", "fromAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "fromAccountNumber": "X4500", "fromAccountName": "5/3 ESSENTIAL CHECKING", "toAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "toAccountNumber": "X1821", "toAccountName": "MASTERCARD ACCOUNT", "expressDelivery": false, "status": "6", "displayStatus": "Unsuccessful", "createTimestamp": "2021-09-25T00:37:34.000Z", "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER"}, {"id": "********", "recurringId": "35954", "amount": 0.11, "dueDate": "2021-09-28", "displayId": "********", "fromAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "fromAccountNumber": "X4500", "fromAccountName": "5/3 ESSENTIAL CHECKING", "toAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "toAccountNumber": "X1821", "toAccountName": "MASTERCARD ACCOUNT", "expressDelivery": false, "status": "5", "displayStatus": "In Process", "createTimestamp": "2021-09-21T04:05:31.000Z", "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER"}, {"id": "********", "recurringId": "36106", "amount": 0.14, "dueDate": "2021-09-28", "displayId": "********", "fromAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "fromAccountNumber": "X4500", "fromAccountName": "5/3 ESSENTIAL CHECKING", "toAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "toAccountNumber": "X1821", "toAccountName": "MASTERCARD ACCOUNT", "expressDelivery": false, "status": "5", "displayStatus": "In Process", "createTimestamp": "2021-09-21T04:05:39.000Z", "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER"}, {"id": "********", "recurringId": "35925", "amount": 0.13, "dueDate": "2021-09-29", "displayId": "********", "fromAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "fromAccountNumber": "X4500", "fromAccountName": "5/3 ESSENTIAL CHECKING", "toAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "toAccountNumber": "X1821", "toAccountName": "MASTERCARD ACCOUNT", "expressDelivery": false, "status": "5", "displayStatus": "In Process", "createTimestamp": "2021-09-15T04:05:22.000Z", "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER"}, {"id": "********", "recurringId": "35925", "amount": 0.13, "dueDate": "2021-09-29", "displayId": "********", "fromAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "fromAccountNumber": "X4500", "fromAccountName": "5/3 ESSENTIAL CHECKING", "toAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "toAccountNumber": "X1821", "toAccountName": "MASTERCARD ACCOUNT", "expressDelivery": false, "status": "5", "displayStatus": "In Process", "createTimestamp": "2021-09-15T04:05:44.000Z", "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER"}, {"id": "********", "recurringId": "36113", "amount": 5, "dueDate": "2021-09-29", "displayId": "********", "fromAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "fromAccountNumber": "X4500", "fromAccountName": "5/3 ESSENTIAL CHECKING", "toAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "toAccountNumber": "X1821", "toAccountName": "MASTERCARD ACCOUNT", "expressDelivery": false, "frequency": "ONCE_A_WEEK", "numberOfActivities": 6, "numberOfRemainingActivities": 3, "status": "5", "displayStatus": "In Process", "createTimestamp": "2021-09-25T00:11:40.000Z", "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER"}, {"id": "********", "amount": 100, "dueDate": "2021-09-30", "displayId": "********", "fromAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "fromAccountNumber": "X4500", "fromAccountName": "5/3 ESSENTIAL CHECKING", "toAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "toAccountNumber": "X1821", "toAccountName": "MASTERCARD ACCOUNT", "expressDelivery": false, "status": "5", "displayStatus": "In Process", "createTimestamp": "2021-09-14T21:43:21.000Z", "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER"}, {"id": "********", "recurringId": "35850", "amount": 0.05, "dueDate": "2021-09-30", "displayId": "********", "fromAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "fromAccountNumber": "X1821", "fromAccountName": "MASTERCARD ACCOUNT", "toAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "toAccountNumber": "X4500", "toAccountName": "5/3 ESSENTIAL CHECKING", "expressDelivery": false, "additionalPrincipleAmount": 0, "status": "5", "displayStatus": "In Process", "createTimestamp": "2021-09-23T04:05:36.000Z", "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER"}, {"id": "********", "recurringId": "35851", "amount": 0.88, "dueDate": "2021-09-30", "displayId": "********", "fromAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "fromAccountNumber": "X1821", "fromAccountName": "MASTERCARD ACCOUNT", "toAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "toAccountNumber": "X4500", "toAccountName": "5/3 ESSENTIAL CHECKING", "expressDelivery": false, "status": "5", "displayStatus": "In Process", "createTimestamp": "2021-09-23T04:05:36.000Z", "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER"}, {"id": "********", "recurringId": "35852", "amount": 0.87, "dueDate": "2021-09-30", "displayId": "********", "fromAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "fromAccountNumber": "X4500", "fromAccountName": "5/3 ESSENTIAL CHECKING", "toAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "toAccountNumber": "X1821", "toAccountName": "MASTERCARD ACCOUNT", "expressDelivery": false, "status": "5", "displayStatus": "In Process", "createTimestamp": "2021-09-23T04:05:36.000Z", "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER"}, {"id": "********", "recurringId": "36064", "amount": 0.23, "dueDate": "2021-09-30", "displayId": "********", "fromAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "fromAccountNumber": "X1821", "fromAccountName": "MASTERCARD ACCOUNT", "toAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "toAccountNumber": "X4500", "toAccountName": "5/3 ESSENTIAL CHECKING", "expressDelivery": false, "status": "5", "displayStatus": "In Process", "createTimestamp": "2021-09-23T04:05:38.000Z", "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER"}, {"id": "********", "recurringId": "36105", "amount": 0.09, "dueDate": "2021-09-30", "displayId": "********", "fromAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "fromAccountNumber": "X4500", "fromAccountName": "5/3 ESSENTIAL CHECKING", "toAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "toAccountNumber": "X1821", "toAccountName": "MASTERCARD ACCOUNT", "expressDelivery": false, "status": "5", "displayStatus": "In Process", "createTimestamp": "2021-09-23T04:05:42.000Z", "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER"}, {"id": "********", "recurringId": "35852", "amount": 0.87, "dueDate": "2021-09-30", "displayId": "********", "fromAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "fromAccountNumber": "X4500", "fromAccountName": "5/3 ESSENTIAL CHECKING", "toAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "toAccountNumber": "X1821", "toAccountName": "MASTERCARD ACCOUNT", "expressDelivery": false, "status": "5", "displayStatus": "In Process", "createTimestamp": "2021-09-23T04:06:01.000Z", "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER"}, {"id": "********", "recurringId": "35850", "amount": 0.05, "dueDate": "2021-09-30", "displayId": "********", "fromAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "fromAccountNumber": "X1821", "fromAccountName": "MASTERCARD ACCOUNT", "toAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "toAccountNumber": "X4500", "toAccountName": "5/3 ESSENTIAL CHECKING", "expressDelivery": false, "additionalPrincipleAmount": 0, "status": "5", "displayStatus": "In Process", "createTimestamp": "2021-09-23T04:06:01.000Z", "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER"}, {"id": "********", "recurringId": "35851", "amount": 0.88, "dueDate": "2021-09-30", "displayId": "********", "fromAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "fromAccountNumber": "X1821", "fromAccountName": "MASTERCARD ACCOUNT", "toAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "toAccountNumber": "X4500", "toAccountName": "5/3 ESSENTIAL CHECKING", "expressDelivery": false, "status": "5", "displayStatus": "In Process", "createTimestamp": "2021-09-23T04:06:01.000Z", "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER"}, {"id": "********", "recurringId": "36064", "amount": 0.23, "dueDate": "2021-09-30", "displayId": "********", "fromAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "fromAccountNumber": "X1821", "fromAccountName": "MASTERCARD ACCOUNT", "toAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "toAccountNumber": "X4500", "toAccountName": "5/3 ESSENTIAL CHECKING", "expressDelivery": false, "status": "5", "displayStatus": "In Process", "createTimestamp": "2021-09-23T04:06:02.000Z", "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER"}, {"id": "********", "recurringId": "36105", "amount": 0.09, "dueDate": "2021-09-30", "displayId": "********", "fromAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "fromAccountNumber": "X4500", "fromAccountName": "5/3 ESSENTIAL CHECKING", "toAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "toAccountNumber": "X1821", "toAccountName": "MASTERCARD ACCOUNT", "expressDelivery": false, "status": "5", "displayStatus": "In Process", "createTimestamp": "2021-09-23T04:06:05.000Z", "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER"}, {"id": "********", "recurringId": "36114", "amount": 200, "dueDate": "2021-10-01", "displayId": "********", "fromAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "fromAccountNumber": "X4500", "fromAccountName": "5/3 ESSENTIAL CHECKING", "toAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "toAccountNumber": "X1821", "toAccountName": "MASTERCARD ACCOUNT", "expressDelivery": false, "status": "5", "displayStatus": "In Process", "createTimestamp": "2021-09-25T01:05:00.000Z", "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER"}, {"id": "********", "recurringId": "35954", "amount": 0.11, "dueDate": "2021-10-05", "displayId": "********", "fromAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "fromAccountNumber": "X4500", "fromAccountName": "5/3 ESSENTIAL CHECKING", "toAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "toAccountNumber": "X1821", "toAccountName": "MASTERCARD ACCOUNT", "expressDelivery": false, "status": "5", "displayStatus": "In Process", "createTimestamp": "2021-09-28T04:05:33.000Z", "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER"}, {"id": "********", "recurringId": "36106", "amount": 0.14, "dueDate": "2021-10-05", "displayId": "********", "fromAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "fromAccountNumber": "X4500", "fromAccountName": "5/3 ESSENTIAL CHECKING", "toAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "toAccountNumber": "X1821", "toAccountName": "MASTERCARD ACCOUNT", "expressDelivery": false, "status": "5", "displayStatus": "In Process", "createTimestamp": "2021-09-28T04:05:42.000Z", "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER"}, {"id": "********", "recurringId": "36113", "amount": 5, "dueDate": "2021-10-06", "displayId": "********", "fromAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "fromAccountNumber": "X4500", "fromAccountName": "5/3 ESSENTIAL CHECKING", "toAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "toAccountNumber": "X1821", "toAccountName": "MASTERCARD ACCOUNT", "expressDelivery": false, "status": "5", "displayStatus": "In Process", "createTimestamp": "2021-09-29T04:05:39.000Z", "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER"}, {"id": "********", "recurringId": "35850", "amount": 0.05, "dueDate": "2021-10-07", "displayId": "********", "fromAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "fromAccountNumber": "X1821", "fromAccountName": "MASTERCARD ACCOUNT", "toAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "toAccountNumber": "X4500", "toAccountName": "5/3 ESSENTIAL CHECKING", "expressDelivery": false, "additionalPrincipleAmount": 0, "status": "5", "displayStatus": "In Process", "createTimestamp": "2021-09-30T04:05:39.000Z", "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER"}, {"id": "********", "recurringId": "35852", "amount": 0.87, "dueDate": "2021-10-07", "displayId": "********", "fromAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "fromAccountNumber": "X4500", "fromAccountName": "5/3 ESSENTIAL CHECKING", "toAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "toAccountNumber": "X1821", "toAccountName": "MASTERCARD ACCOUNT", "expressDelivery": false, "status": "5", "displayStatus": "In Process", "createTimestamp": "2021-09-30T04:05:39.000Z", "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER"}, {"id": "********", "recurringId": "35851", "amount": 0.88, "dueDate": "2021-10-07", "displayId": "********", "fromAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "fromAccountNumber": "X1821", "fromAccountName": "MASTERCARD ACCOUNT", "toAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "toAccountNumber": "X4500", "toAccountName": "5/3 ESSENTIAL CHECKING", "expressDelivery": false, "status": "5", "displayStatus": "In Process", "createTimestamp": "2021-09-30T04:05:39.000Z", "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER"}, {"id": "********", "recurringId": "36064", "amount": 0.23, "dueDate": "2021-10-07", "displayId": "********", "fromAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "fromAccountNumber": "X1821", "fromAccountName": "MASTERCARD ACCOUNT", "toAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "toAccountNumber": "X4500", "toAccountName": "5/3 ESSENTIAL CHECKING", "expressDelivery": false, "status": "5", "displayStatus": "In Process", "createTimestamp": "2021-09-30T04:05:40.000Z", "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER"}, {"id": "********", "recurringId": "36105", "amount": 0.09, "dueDate": "2021-10-07", "displayId": "********", "fromAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "fromAccountNumber": "X4500", "fromAccountName": "5/3 ESSENTIAL CHECKING", "toAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "toAccountNumber": "X1821", "toAccountName": "MASTERCARD ACCOUNT", "expressDelivery": false, "status": "5", "displayStatus": "In Process", "createTimestamp": "2021-09-30T04:05:43.000Z", "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER"}, {"id": "********", "recurringId": "35954", "amount": 0.11, "dueDate": "2021-10-12", "displayId": "********", "fromAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "fromAccountNumber": "X4500", "fromAccountName": "5/3 ESSENTIAL CHECKING", "toAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "toAccountNumber": "X1821", "toAccountName": "MASTERCARD ACCOUNT", "expressDelivery": false, "status": "5", "displayStatus": "In Process", "createTimestamp": "2021-10-06T20:36:10.000Z", "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER"}, {"id": "********", "amount": 2297.48, "dueDate": "2021-10-12", "displayId": "********", "fromAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "fromAccountNumber": "X4500", "fromAccountName": "5/3 ESSENTIAL CHECKING", "toAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "toAccountNumber": "X1821", "toAccountName": "MASTERCARD ACCOUNT", "expressDelivery": false, "status": "8", "displayStatus": "Unsuccessful", "createTimestamp": "2021-10-12T18:02:08.000Z", "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER"}, {"id": "********", "amount": 300, "dueDate": "2021-10-12", "displayId": "********", "fromAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "fromAccountNumber": "X4500", "fromAccountName": "5/3 ESSENTIAL CHECKING", "toAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "toAccountNumber": "X1821", "toAccountName": "MASTERCARD ACCOUNT", "expressDelivery": false, "status": "6", "displayStatus": "Unsuccessful", "createTimestamp": "2021-10-12T18:03:11.000Z", "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER"}, {"id": "********", "amount": 2297.48, "dueDate": "2021-10-12", "displayId": "********", "fromAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "fromAccountNumber": "X4500", "fromAccountName": "5/3 ESSENTIAL CHECKING", "toAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "toAccountNumber": "X1821", "toAccountName": "MASTERCARD ACCOUNT", "expressDelivery": false, "status": "8", "displayStatus": "Unsuccessful", "createTimestamp": "2021-10-12T22:28:20.000Z", "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER"}, {"id": "********", "recurringId": "35925", "amount": 0.13, "dueDate": "2021-10-13", "displayId": "********", "fromAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "fromAccountNumber": "X4500", "fromAccountName": "5/3 ESSENTIAL CHECKING", "toAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "toAccountNumber": "X1821", "toAccountName": "MASTERCARD ACCOUNT", "expressDelivery": false, "status": "5", "displayStatus": "In Process", "createTimestamp": "2021-09-29T04:05:21.000Z", "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER"}, {"id": "********", "recurringId": "36113", "amount": 5, "dueDate": "2021-10-13", "displayId": "********", "fromAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "fromAccountNumber": "X4500", "fromAccountName": "5/3 ESSENTIAL CHECKING", "toAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "toAccountNumber": "X1821", "toAccountName": "MASTERCARD ACCOUNT", "expressDelivery": false, "status": "5", "displayStatus": "In Process", "createTimestamp": "2021-10-06T20:36:39.000Z", "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER"}, {"id": "********", "amount": 10, "dueDate": "2021-10-13", "displayId": "********", "fromAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "fromAccountNumber": "X4500", "fromAccountName": "5/3 ESSENTIAL CHECKING", "toAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "toAccountNumber": "X1821", "toAccountName": "MASTERCARD ACCOUNT", "expressDelivery": false, "status": "6", "displayStatus": "Unsuccessful", "createTimestamp": "2021-10-13T21:06:34.000Z", "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER"}, {"id": "********", "amount": 10, "dueDate": "2021-10-13", "displayId": "********", "fromAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "fromAccountNumber": "X4500", "fromAccountName": "5/3 ESSENTIAL CHECKING", "toAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "toAccountNumber": "X1821", "toAccountName": "MASTERCARD ACCOUNT", "expressDelivery": false, "status": "6", "displayStatus": "Unsuccessful", "createTimestamp": "2021-10-13T21:06:41.000Z", "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER"}, {"id": "********", "amount": 10, "dueDate": "2021-10-13", "displayId": "********", "fromAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "fromAccountNumber": "X4500", "fromAccountName": "5/3 ESSENTIAL CHECKING", "toAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "toAccountNumber": "X1821", "toAccountName": "MASTERCARD ACCOUNT", "expressDelivery": false, "status": "6", "displayStatus": "Unsuccessful", "createTimestamp": "2021-10-13T21:08:58.000Z", "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER"}, {"id": "********", "recurringId": "35852", "amount": 0.87, "dueDate": "2021-10-14", "displayId": "********", "fromAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "fromAccountNumber": "X4500", "fromAccountName": "5/3 ESSENTIAL CHECKING", "toAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "toAccountNumber": "X1821", "toAccountName": "MASTERCARD ACCOUNT", "expressDelivery": false, "status": "5", "displayStatus": "In Process", "createTimestamp": "2021-10-07T04:05:37.000Z", "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER"}, {"id": "********", "recurringId": "35850", "amount": 0.05, "dueDate": "2021-10-14", "displayId": "********", "fromAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "fromAccountNumber": "X1821", "fromAccountName": "MASTERCARD ACCOUNT", "toAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "toAccountNumber": "X4500", "toAccountName": "5/3 ESSENTIAL CHECKING", "expressDelivery": false, "additionalPrincipleAmount": 0, "status": "5", "displayStatus": "In Process", "createTimestamp": "2021-10-07T04:05:37.000Z", "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER"}, {"id": "********", "recurringId": "35851", "amount": 0.88, "dueDate": "2021-10-14", "displayId": "********", "fromAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "fromAccountNumber": "X1821", "fromAccountName": "MASTERCARD ACCOUNT", "toAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "toAccountNumber": "X4500", "toAccountName": "5/3 ESSENTIAL CHECKING", "expressDelivery": false, "status": "5", "displayStatus": "In Process", "createTimestamp": "2021-10-07T04:05:37.000Z", "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER"}, {"id": "********", "recurringId": "36064", "amount": 0.23, "dueDate": "2021-10-14", "displayId": "********", "fromAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "fromAccountNumber": "X1821", "fromAccountName": "MASTERCARD ACCOUNT", "toAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "toAccountNumber": "X4500", "toAccountName": "5/3 ESSENTIAL CHECKING", "expressDelivery": false, "status": "5", "displayStatus": "In Process", "createTimestamp": "2021-10-07T04:05:39.000Z", "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER"}, {"id": "********", "recurringId": "36105", "amount": 0.09, "dueDate": "2021-10-14", "displayId": "********", "fromAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "fromAccountNumber": "X4500", "fromAccountName": "5/3 ESSENTIAL CHECKING", "toAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "toAccountNumber": "X1821", "toAccountName": "MASTERCARD ACCOUNT", "expressDelivery": false, "status": "5", "displayStatus": "In Process", "createTimestamp": "2021-10-07T04:05:42.000Z", "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER"}, {"id": "********", "recurringId": "35938", "amount": 132, "dueDate": "2021-10-16", "displayId": "********", "fromAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "fromAccountNumber": "X4500", "fromAccountName": "5/3 ESSENTIAL CHECKING", "toAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "toAccountNumber": "X1821", "toAccountName": "MASTERCARD ACCOUNT", "expressDelivery": false, "status": "5", "displayStatus": "In Process", "createTimestamp": "2021-09-16T04:05:33.000Z", "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER"}, {"id": "********", "recurringId": "35954", "amount": 0.11, "dueDate": "2021-10-19", "displayId": "********", "fromAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "fromAccountNumber": "X4500", "fromAccountName": "5/3 ESSENTIAL CHECKING", "toAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "toAccountNumber": "X1821", "toAccountName": "MASTERCARD ACCOUNT", "expressDelivery": false, "status": "4", "displayStatus": "Scheduled", "createTimestamp": "2021-10-12T04:05:49.000Z", "editable": true, "cancelable": true, "type": "INTERNAL_TRANSFER"}, {"id": "********", "recurringId": "35954", "amount": 0.11, "dueDate": "2021-10-19", "displayId": "********", "fromAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "fromAccountNumber": "X4500", "fromAccountName": "5/3 ESSENTIAL CHECKING", "toAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "toAccountNumber": "X1821", "toAccountName": "MASTERCARD ACCOUNT", "expressDelivery": false, "status": "4", "displayStatus": "Scheduled", "createTimestamp": "2021-10-12T04:06:19.000Z", "editable": true, "cancelable": true, "type": "INTERNAL_TRANSFER"}, {"id": "********", "recurringId": "36121", "amount": 30, "dueDate": "2021-10-20", "displayId": "********", "fromAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "fromAccountNumber": "X4500", "fromAccountName": "5/3 ESSENTIAL CHECKING", "toAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "toAccountNumber": "X1821", "toAccountName": "MASTERCARD ACCOUNT", "expressDelivery": false, "frequency": "ONCE_A_MONTH", "numberOfActivities": 5, "numberOfRemainingActivities": 5, "status": "4", "displayStatus": "Scheduled", "createTimestamp": "2021-10-08T21:18:16.000Z", "editable": true, "cancelable": true, "type": "INTERNAL_TRANSFER"}, {"id": "********", "recurringId": "36113", "amount": 5, "dueDate": "2021-10-20", "displayId": "********", "fromAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "fromAccountNumber": "X4500", "fromAccountName": "5/3 ESSENTIAL CHECKING", "toAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "toAccountNumber": "X1821", "toAccountName": "MASTERCARD ACCOUNT", "expressDelivery": false, "status": "4", "displayStatus": "Scheduled", "createTimestamp": "2021-10-13T04:05:39.000Z", "editable": true, "cancelable": true, "type": "INTERNAL_TRANSFER"}, {"id": "********", "recurringId": "36120", "amount": 352, "dueDate": "2021-10-21", "displayId": "********", "fromAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "fromAccountNumber": "X4500", "fromAccountName": "5/3 ESSENTIAL CHECKING", "toAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "toAccountNumber": "X1821", "toAccountName": "MASTERCARD ACCOUNT", "expressDelivery": false, "frequency": "ONCE_A_MONTH", "numberOfActivities": 5, "numberOfRemainingActivities": 5, "status": "4", "displayStatus": "Scheduled", "createTimestamp": "2021-10-07T19:55:27.000Z", "editable": true, "cancelable": true, "type": "INTERNAL_TRANSFER"}, {"id": "********", "recurringId": "35851", "amount": 0.88, "dueDate": "2021-10-21", "displayId": "********", "fromAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "fromAccountNumber": "X1821", "fromAccountName": "MASTERCARD ACCOUNT", "toAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "toAccountNumber": "X4500", "toAccountName": "5/3 ESSENTIAL CHECKING", "expressDelivery": false, "status": "4", "displayStatus": "Scheduled", "createTimestamp": "2021-10-14T04:05:45.000Z", "editable": true, "cancelable": true, "type": "INTERNAL_TRANSFER"}, {"id": "********", "recurringId": "35850", "amount": 0.05, "dueDate": "2021-10-21", "displayId": "********", "fromAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "fromAccountNumber": "X1821", "fromAccountName": "MASTERCARD ACCOUNT", "toAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "toAccountNumber": "X4500", "toAccountName": "5/3 ESSENTIAL CHECKING", "expressDelivery": false, "additionalPrincipleAmount": 0, "status": "4", "displayStatus": "Scheduled", "createTimestamp": "2021-10-14T04:05:45.000Z", "editable": true, "cancelable": true, "type": "INTERNAL_TRANSFER"}, {"id": "********", "recurringId": "35852", "amount": 0.87, "dueDate": "2021-10-21", "displayId": "********", "fromAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "fromAccountNumber": "X4500", "fromAccountName": "5/3 ESSENTIAL CHECKING", "toAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "toAccountNumber": "X1821", "toAccountName": "MASTERCARD ACCOUNT", "expressDelivery": false, "status": "4", "displayStatus": "Scheduled", "createTimestamp": "2021-10-14T04:05:46.000Z", "editable": true, "cancelable": true, "type": "INTERNAL_TRANSFER"}, {"id": "********", "recurringId": "36064", "amount": 0.23, "dueDate": "2021-10-21", "displayId": "********", "fromAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "fromAccountNumber": "X1821", "fromAccountName": "MASTERCARD ACCOUNT", "toAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "toAccountNumber": "X4500", "toAccountName": "5/3 ESSENTIAL CHECKING", "expressDelivery": false, "status": "4", "displayStatus": "Scheduled", "createTimestamp": "2021-10-14T04:05:48.000Z", "editable": true, "cancelable": true, "type": "INTERNAL_TRANSFER"}, {"id": "********", "recurringId": "36105", "amount": 0.09, "dueDate": "2021-10-21", "displayId": "********", "fromAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "fromAccountNumber": "X4500", "fromAccountName": "5/3 ESSENTIAL CHECKING", "toAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "toAccountNumber": "X1821", "toAccountName": "MASTERCARD ACCOUNT", "expressDelivery": false, "status": "4", "displayStatus": "Scheduled", "createTimestamp": "2021-10-14T04:05:52.000Z", "editable": true, "cancelable": true, "type": "INTERNAL_TRANSFER"}, {"id": "********", "recurringId": "36123", "amount": 2297.48, "dueDate": "2021-10-22", "displayId": "********", "fromAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "fromAccountNumber": "X4500", "fromAccountName": "5/3 ESSENTIAL CHECKING", "toAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "toAccountNumber": "X1821", "toAccountName": "MASTERCARD ACCOUNT", "expressDelivery": false, "frequency": "ONCE_A_MONTH", "numberOfActivities": 5, "numberOfRemainingActivities": 5, "status": "4", "displayStatus": "Scheduled", "createTimestamp": "2021-10-12T15:56:22.000Z", "editable": true, "cancelable": true, "type": "INTERNAL_TRANSFER"}, {"id": "********", "recurringId": "36122", "amount": 2297.48, "dueDate": "2021-10-26", "displayId": "********", "fromAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "fromAccountNumber": "X4500", "fromAccountName": "5/3 ESSENTIAL CHECKING", "toAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "toAccountNumber": "X1821", "toAccountName": "MASTERCARD ACCOUNT", "expressDelivery": false, "frequency": "ONCE_A_MONTH", "numberOfActivities": 5, "numberOfRemainingActivities": 5, "status": "4", "displayStatus": "Scheduled", "createTimestamp": "2021-10-12T14:59:40.000Z", "editable": true, "cancelable": true, "type": "INTERNAL_TRANSFER"}, {"id": "********", "recurringId": "35925", "amount": 0.13, "dueDate": "2021-10-27", "displayId": "********", "fromAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "fromAccountNumber": "X4500", "fromAccountName": "5/3 ESSENTIAL CHECKING", "toAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "toAccountNumber": "X1821", "toAccountName": "MASTERCARD ACCOUNT", "expressDelivery": false, "status": "4", "displayStatus": "Scheduled", "createTimestamp": "2021-10-13T04:05:20.000Z", "editable": true, "cancelable": true, "type": "INTERNAL_TRANSFER"}, {"id": "********", "recurringId": "36119", "amount": 46.46, "dueDate": "2021-10-28", "displayId": "********", "fromAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "fromAccountNumber": "X4500", "fromAccountName": "5/3 ESSENTIAL CHECKING", "toAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "toAccountNumber": "X1821", "toAccountName": "MASTERCARD ACCOUNT", "expressDelivery": false, "status": "0", "displayStatus": "Canceled", "createTimestamp": "2021-10-07T19:31:49.000Z", "editable": false, "cancelable": false, "type": "INTERNAL_TRANSFER"}, {"id": "********", "recurringId": "35938", "amount": 132, "dueDate": "2021-11-16", "displayId": "********", "fromAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "fromAccountNumber": "X4500", "fromAccountName": "5/3 ESSENTIAL CHECKING", "toAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "toAccountNumber": "X1821", "toAccountName": "MASTERCARD ACCOUNT", "expressDelivery": false, "status": "4", "displayStatus": "Scheduled", "createTimestamp": "2021-10-15T04:05:45.000Z", "editable": true, "cancelable": true, "type": "INTERNAL_TRANSFER"}], "recurringActivities": [{"id": "********", "recurringId": "36105", "amount": 0.09, "dueDate": "2021-09-16", "displayId": "36105", "fromAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "fromAccountNumber": "X4500", "fromAccountName": "5/3 ESSENTIAL CHECKING", "toAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "toAccountNumber": "X1821", "toAccountName": "MASTERCARD ACCOUNT", "expressDelivery": false, "frequency": "ONCE_A_WEEK", "numberOfActivities": 0, "status": "4", "displayStatus": "Scheduled", "createTimestamp": "2021-09-15T18:47:25.000Z", "editable": false, "cancelable": true, "type": "INTERNAL_TRANSFER"}, {"id": "********", "recurringId": "35938", "amount": 132, "dueDate": "2021-09-16", "displayId": "35938", "fromAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "fromAccountNumber": "X4500", "fromAccountName": "5/3 ESSENTIAL CHECKING", "toAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "toAccountNumber": "X1821", "toAccountName": "MASTERCARD ACCOUNT", "expressDelivery": false, "frequency": "ONCE_A_MONTH", "numberOfActivities": 3, "numberOfRemainingActivities": 1, "status": "4", "displayStatus": "Scheduled", "createTimestamp": "2021-07-15T13:18:32.000Z", "editable": false, "cancelable": true, "type": "INTERNAL_TRANSFER"}, {"id": "********", "recurringId": "36113", "amount": 5, "dueDate": "2021-09-29", "displayId": "36113", "fromAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "fromAccountNumber": "X4500", "fromAccountName": "5/3 ESSENTIAL CHECKING", "toAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "toAccountNumber": "X1821", "toAccountName": "MASTERCARD ACCOUNT", "expressDelivery": false, "frequency": "ONCE_A_WEEK", "numberOfActivities": 6, "numberOfRemainingActivities": 3, "status": "4", "displayStatus": "Scheduled", "createTimestamp": "2021-09-25T00:11:40.000Z", "editable": false, "cancelable": true, "type": "INTERNAL_TRANSFER"}, {"id": "********", "recurringId": "36121", "amount": 30, "dueDate": "2021-10-20", "displayId": "36121", "fromAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "fromAccountNumber": "X4500", "fromAccountName": "5/3 ESSENTIAL CHECKING", "toAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "toAccountNumber": "X1821", "toAccountName": "MASTERCARD ACCOUNT", "expressDelivery": false, "frequency": "ONCE_A_MONTH", "numberOfActivities": 5, "numberOfRemainingActivities": 5, "status": "4", "displayStatus": "Scheduled", "createTimestamp": "2021-10-08T21:18:16.000Z", "editable": true, "cancelable": true, "type": "INTERNAL_TRANSFER"}, {"id": "********", "recurringId": "36120", "amount": 352, "dueDate": "2021-10-21", "displayId": "36120", "fromAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "fromAccountNumber": "X4500", "fromAccountName": "5/3 ESSENTIAL CHECKING", "toAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "toAccountNumber": "X1821", "toAccountName": "MASTERCARD ACCOUNT", "expressDelivery": false, "frequency": "ONCE_A_MONTH", "numberOfActivities": 5, "numberOfRemainingActivities": 5, "status": "4", "displayStatus": "Scheduled", "createTimestamp": "2021-10-07T19:55:27.000Z", "editable": true, "cancelable": true, "type": "INTERNAL_TRANSFER"}, {"id": "********", "recurringId": "36123", "amount": 2297.48, "dueDate": "2021-10-22", "displayId": "36123", "fromAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "fromAccountNumber": "X4500", "fromAccountName": "5/3 ESSENTIAL CHECKING", "toAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "toAccountNumber": "X1821", "toAccountName": "MASTERCARD ACCOUNT", "expressDelivery": false, "frequency": "ONCE_A_MONTH", "numberOfActivities": 5, "numberOfRemainingActivities": 5, "status": "4", "displayStatus": "Scheduled", "createTimestamp": "2021-10-12T15:56:22.000Z", "editable": true, "cancelable": true, "type": "INTERNAL_TRANSFER"}, {"id": "********", "recurringId": "36122", "amount": 2297.48, "dueDate": "2021-10-26", "displayId": "36122", "fromAccountId": "cb041b1a-663c-4fb2-b00d-92ccacfe0bab", "fromAccountNumber": "X4500", "fromAccountName": "5/3 ESSENTIAL CHECKING", "toAccountId": "03fb7fe2-0780-4ffd-94de-a1b9dd5361d2", "toAccountNumber": "X1821", "toAccountName": "MASTERCARD ACCOUNT", "expressDelivery": false, "frequency": "ONCE_A_MONTH", "numberOfActivities": 5, "numberOfRemainingActivities": 5, "status": "4", "displayStatus": "Scheduled", "createTimestamp": "2021-10-12T14:59:40.000Z", "editable": true, "cancelable": true, "type": "INTERNAL_TRANSFER"}], "shouldPromptForIncome": false}