/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.controller;

import static com.fitb.digital.bff.movemoney.featureflags.FeatureFlagNames.ORCHESTRATOR_ENABLED;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.fitb.digital.bff.movemoney.model.RetrievalErrors;
import com.fitb.digital.bff.movemoney.model.bff.account.responses.AccountsResponse;
import com.fitb.digital.bff.movemoney.model.client.CesResponse;
import com.fitb.digital.bff.movemoney.service.transferinfo.TransferInfoService;
import com.fitb.digital.bff.movemoney.service.transferinfo.TransferInfoServiceV1;
import com.fitb.digital.lib.featureflag.service.FeatureFlagService;
import java.util.ArrayList;
import java.util.Arrays;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class MmTransferInfoControllerTest {
  @Mock private TransferInfoServiceV1 transferInfoServiceV1;
  @Mock private TransferInfoService transferInfoService;
  @Mock private FeatureFlagService featureFlagService;

  @InjectMocks private MmTransferInfoController controller;

  @Test
  public void getTransferInfoUsesCesProxyWhenUseOrchestratorFlagIsFalse() {
    when(transferInfoServiceV1.getTransferAccounts(true, true)).thenReturn(new AccountsResponse());
    when(featureFlagService.isFeatureEnabled(ORCHESTRATOR_ENABLED)).thenReturn(false);
    AccountsResponse result = controller.getTransferAccounts(true, true);
    assertNotNull(result);
  }

  @Test
  public void getTransferInfoUsesOrchestratorWhenFlagIsTrue() {
    when(transferInfoService.getTransferAccounts()).thenReturn(new AccountsResponse());
    when(featureFlagService.isFeatureEnabled(ORCHESTRATOR_ENABLED)).thenReturn(true);
    AccountsResponse result = controller.getTransferAccounts(true, true);
    assertNotNull(result);
  }

  @Test
  void getAccountsReceivesProfileRetrievalError() {
    var response = new AccountsResponse();
    response.setStatus(CesResponse.SUCCESS);
    response.setStatusCode("TRANSFER_ACTIVITY_PARTIAL_LIST");
    response.setRetrievalErrors(
        new ArrayList<>(Arrays.asList(RetrievalErrors.RETRIEVAL_ERROR_EXTERNAL_TRANSFER)));

    when(transferInfoServiceV1.getTransferAccounts(true, true)).thenReturn(response);
    when(featureFlagService.isFeatureEnabled(anyString())).thenReturn(false);

    var controllerResponse = controller.getTransferAccounts(true, true);

    assertNotNull(controllerResponse);
    assertEquals(controllerResponse.getStatusCode(), "TRANSFER_ACTIVITY_PARTIAL_LIST");
    assertTrue(
        controllerResponse
            .getRetrievalErrors()
            .contains(RetrievalErrors.RETRIEVAL_ERROR_EXTERNAL_TRANSFER));
  }
}
