/* Copyright 2024 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.service.cd;

import static com.fitb.digital.bff.movemoney.utils.TestUtils.mockFromFile;
import static org.junit.jupiter.api.Assertions.*;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

import com.fitb.digital.bff.movemoney.client.CDClient;
import com.fitb.digital.bff.movemoney.model.bff.cd.requests.CDFundTransferRequest;
import com.fitb.digital.bff.movemoney.model.bff.cd.responses.CDFundTransferResponse;
import com.fitb.digital.bff.movemoney.service.BlackoutDateService;
import com.fitb.digital.bff.movemoney.util.CDFundUtil;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@Slf4j
@ExtendWith(MockitoExtension.class)
public class CDFundsTransferServiceTest {

  @InjectMocks private CDFundsTransferService cdfundsTransferService;

  @Mock private CDClient cdClient;

  @Mock CDFundUtil cdfundUtil;

  @Mock(strictness = Mock.Strictness.LENIENT)
  private BlackoutDateService blackoutDateService;

  private static final ZoneId EST = ZoneId.of("America/New_York");

  @BeforeEach
  void setUp() {
    when(blackoutDateService.getBlackoutDates(any(), any())).thenReturn(new ArrayList<>());
  }

  /**
   * Test to verify that the redeemCD method processes requests correctly when the current time is
   * before the cutoff time
   */
  @Test
  public void redeemCDWithoutCutOffTime() {
    CDFundTransferRequest request = new CDFundTransferRequest();
    CDFundUtil realCdfundUtil = new CDFundUtil();
    when(cdClient.redeemCD(request))
        .thenReturn(mockFromFile("stubs/cd-redeem.json", CDFundTransferResponse.class));

    ZonedDateTime zonedDateTime =
        ZonedDateTime.of(2025, 1, 13, 18, 30, 0, 0, ZoneId.of("America/New_York"));

    when(cdfundUtil.getCurrentZonedDateTime()).thenReturn(zonedDateTime);

    // This ensures the real business logic of isCutoffTime is executed
    when(cdfundUtil.isCutoffTime(Mockito.any(), Mockito.any()))
        .thenAnswer(
            invocationOnMock -> {
              ZonedDateTime zonedDateTime2 = invocationOnMock.getArgument(0, ZonedDateTime.class);
              List zonedDateTimeList = invocationOnMock.getArgument(1, List.class);
              return realCdfundUtil.isCutoffTime(zonedDateTime2, zonedDateTimeList);
            });

    // Call the service method
    var response = cdfundsTransferService.redeemCD(request);

    // Assertions
    assertNotNull(response);
    assertEquals(CDFundTransferResponse.class, response.getClass());
    assertEquals("fse43-uye2423fsdf-fsff2342v-3242g", response.getTransactionReferenceNumber());
    assertEquals(false, response.getCutoff());
  }

  /**
   * Test to verify that the redeemCD method processes requests correctly when the current time is
   * after or equal to the cutoff time
   */
  @Test
  public void redeemCDWithCutOffTime() {
    CDFundTransferRequest request = new CDFundTransferRequest();
    CDFundUtil realCdfundUtil = new CDFundUtil();
    when(cdClient.redeemCD(request))
        .thenReturn(mockFromFile("stubs/cd-redeem.json", CDFundTransferResponse.class));

    ZonedDateTime zonedDateTime =
        ZonedDateTime.of(2025, 1, 13, 20, 35, 0, 0, ZoneId.of("America/New_York"));

    when(cdfundUtil.getCurrentZonedDateTime()).thenReturn(zonedDateTime);

    // This ensures the real business logic of isCutoffTime is executed
    when(cdfundUtil.isCutoffTime(Mockito.any(), Mockito.any()))
        .thenAnswer(
            invocationOnMock -> {
              ZonedDateTime zonedDateTime2 = invocationOnMock.getArgument(0, ZonedDateTime.class);
              List zonedDateTimeList = invocationOnMock.getArgument(1, List.class);
              return realCdfundUtil.isCutoffTime(zonedDateTime2, zonedDateTimeList);
            });

    // Call the service method
    var response = cdfundsTransferService.redeemCD(request);

    // Assertions
    assertNotNull(response);
    assertEquals(CDFundTransferResponse.class, response.getClass());
    assertEquals("fse43-uye2423fsdf-fsff2342v-3242g", response.getTransactionReferenceNumber());
    assertEquals(true, response.getCutoff());
  }
}
