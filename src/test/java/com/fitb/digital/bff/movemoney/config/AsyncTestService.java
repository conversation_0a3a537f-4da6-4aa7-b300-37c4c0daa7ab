/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.config;

import java.util.concurrent.CompletableFuture;
import org.springframework.scheduling.annotation.Async;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

@Service
public class AsyncTestService {
  @Async
  public CompletableFuture<Authentication> getAuthentication() {
    return CompletableFuture.completedFuture(
        SecurityContextHolder.getContext().getAuthentication());
  }
}
