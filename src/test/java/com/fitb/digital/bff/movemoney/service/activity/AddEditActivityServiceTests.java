/* Copyright 2023 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.service.activity;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.fitb.digital.bff.movemoney.client.xferandpayorc.XferAndPayOrcClient;
import com.fitb.digital.bff.movemoney.client.xferandpayorc.model.XferAndPayActivity;
import com.fitb.digital.bff.movemoney.model.bff.activity.requests.BffActivityRequest;
import com.fitb.digital.bff.movemoney.utils.TestUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;

@ExtendWith(MockitoExtension.class)
@SpringBootTest
class AddEditActivityServiceTests {
  @MockBean private XferAndPayOrcClient xferAndPayOrcClient;

  @Autowired private ActivityService activityService;

  @Test
  void addActivityMapsInternalTransfersBetweenDDAsOneTime() {
    var orcResponse =
        TestUtils.mockFromFile(
            "stubs/__files/mmbff/xferandpayorc/addactivity/internal_dda_one_time.json",
            XferAndPayActivity.class);
    when(xferAndPayOrcClient.postTransferAndPayActivity(any())).thenReturn(orcResponse);

    var result = activityService.addActivity(new BffActivityRequest());

    assertEquals(orcResponse.getAmount().doubleValue(), result.getActivity().getAmount());
    assertEquals(
        orcResponse.getFrequency().toString(), result.getActivity().getFrequency().toString());
    assertEquals(orcResponse.getDisplayStatus(), result.getActivity().getDisplayStatus());
    assertEquals(orcResponse.getDisplayStatus(), result.getActivity().getNormalizedDisplayStatus());
    assertEquals("INTERNAL_TRANSFER", result.getActivity().getActivityType());
    assertEquals(orcResponse.getDueDate(), result.getActivity().getDueDate());
    assertEquals(orcResponse.getDisplayId(), result.getActivity().getDisplayId());
    assertEquals(orcResponse.getId(), result.getActivity().getId());
    assertEquals(orcResponse.getToAccountNumber(), result.getActivity().getToAccountNumber());
    assertEquals(orcResponse.getFromAccountNumber(), result.getActivity().getFromAccountNumber());
    assertEquals(
        orcResponse.getFromAccountName().toLowerCase(),
        result.getActivity().getFromAccountName().toLowerCase());
    assertEquals(
        orcResponse.getToAccountName().toLowerCase(),
        result.getActivity().getToAccountName().toLowerCase());
    assertNull(result.getActivity().getRecurringId());
  }

  @Test
  void editActivityMapsInternalTransfersBetweenDDAsOneTime() {
    var orcResponse =
        TestUtils.mockFromFile(
            "stubs/__files/mmbff/xferandpayorc/addactivity/internal_dda_one_time.json",
            XferAndPayActivity.class);
    when(xferAndPayOrcClient.editTransferAndPayActivity(any())).thenReturn(orcResponse);

    var result = activityService.editActivity(new BffActivityRequest());

    assertEquals(orcResponse.getAmount().doubleValue(), result.getActivity().getAmount());
    assertEquals(
        orcResponse.getFrequency().toString(), result.getActivity().getFrequency().toString());
    assertEquals(orcResponse.getDisplayStatus(), result.getActivity().getDisplayStatus());
    assertEquals(orcResponse.getDisplayStatus(), result.getActivity().getNormalizedDisplayStatus());
    assertEquals("INTERNAL_TRANSFER", result.getActivity().getActivityType());
    assertEquals(orcResponse.getDueDate(), result.getActivity().getDueDate());
    assertEquals(orcResponse.getDisplayId(), result.getActivity().getDisplayId());
    assertEquals(orcResponse.getId(), result.getActivity().getId());
    assertEquals(orcResponse.getToAccountNumber(), result.getActivity().getToAccountNumber());
    assertEquals(orcResponse.getFromAccountNumber(), result.getActivity().getFromAccountNumber());
    assertEquals(
        orcResponse.getFromAccountName().toLowerCase(),
        result.getActivity().getFromAccountName().toLowerCase());
    assertEquals(
        orcResponse.getToAccountName().toLowerCase(),
        result.getActivity().getToAccountName().toLowerCase());
    assertNull(result.getActivity().getRecurringId());
  }

  @Test
  void addActivityMapsInternalTransfersBetweenDDAsRecurringUntilICancel() {
    var orcResponse =
        TestUtils.mockFromFile(
            "stubs/__files/mmbff/xferandpayorc/addactivity/internal_dda_recurring_until_i_cancel.json",
            XferAndPayActivity.class);
    when(xferAndPayOrcClient.postTransferAndPayActivity(any())).thenReturn(orcResponse);

    var result = activityService.addActivity(new BffActivityRequest());

    assertEquals("36735", result.getActivity().getRecurringId());
  }

  @Test
  void editActivityMapsInternalTransfersBetweenDDAsRecurringUntilICancel() {
    var orcResponse =
        TestUtils.mockFromFile(
            "stubs/__files/mmbff/xferandpayorc/addactivity/internal_dda_recurring_until_i_cancel.json",
            XferAndPayActivity.class);

    when(xferAndPayOrcClient.editTransferAndPayActivity(any())).thenReturn(orcResponse);
    var editResult = activityService.editActivity(new BffActivityRequest());

    assertEquals("36735", editResult.getActivity().getRecurringId());
  }

  @Test
  void addActivityMapsInternalTransfersBetweenDDAsRecurringNumberOfTimes() {
    var orcResponse =
        TestUtils.mockFromFile(
            "stubs/__files/mmbff/xferandpayorc/addactivity/internal_dda_recurring_number_of_times.json",
            XferAndPayActivity.class);
    when(xferAndPayOrcClient.postTransferAndPayActivity(any())).thenReturn(orcResponse);

    var result = activityService.addActivity(new BffActivityRequest());

    assertEquals("36738", result.getActivity().getRecurringId());
    assertEquals(10, result.getActivity().getNumberOfActivities());
    assertEquals(10, result.getActivity().getNumberOfRemainingActivities());
  }

  @Test
  void editActivityMapsInternalTransfersBetweenDDAsRecurringNumberOfTimes() {
    var orcResponse =
        TestUtils.mockFromFile(
            "stubs/__files/mmbff/xferandpayorc/addactivity/internal_dda_recurring_number_of_times.json",
            XferAndPayActivity.class);
    when(xferAndPayOrcClient.editTransferAndPayActivity(any())).thenReturn(orcResponse);

    var editResult = activityService.editActivity(new BffActivityRequest());

    assertEquals("36738", editResult.getActivity().getRecurringId());
    assertEquals(10, editResult.getActivity().getNumberOfActivities());
    assertEquals(10, editResult.getActivity().getNumberOfRemainingActivities());
  }

  @Test
  void addActivityMapsBillPaymentOneTime() { // Recurring is not allowed
    var orcResponse =
        TestUtils.mockFromFile(
            "stubs/__files/mmbff/xferandpayorc/addactivity/billpay_one_time.json",
            XferAndPayActivity.class);
    when(xferAndPayOrcClient.postTransferAndPayActivity(any())).thenReturn(orcResponse);

    var result = activityService.addActivity(new BffActivityRequest());
    assertEquals("BILLPAY", result.getActivity().getActivityType());
  }

  @Test
  void editActivityMapsBillPaymentOneTime() { // Recurring is not allowed
    var orcResponse =
        TestUtils.mockFromFile(
            "stubs/__files/mmbff/xferandpayorc/addactivity/billpay_one_time.json",
            XferAndPayActivity.class);
    when(xferAndPayOrcClient.editTransferAndPayActivity(any())).thenReturn(orcResponse);
    var editResult = activityService.editActivity(new BffActivityRequest());

    assertEquals("BILLPAY", editResult.getActivity().getActivityType());
  }

  @Test
  void addActivityMapsExternalTransferOneTime() {
    var orcResponse =
        TestUtils.mockFromFile(
            "stubs/__files/mmbff/xferandpayorc/addactivity/external_one_time_with_memo.json",
            XferAndPayActivity.class);
    when(xferAndPayOrcClient.postTransferAndPayActivity(any())).thenReturn(orcResponse);

    var result = activityService.addActivity(new BffActivityRequest());
    assertEquals("EXTERNAL_TRANSFER", result.getActivity().getActivityType());
    assertEquals("test a memo", result.getActivity().getMemo());
  }

  @Test
  void editActivityMapsExternalTransferOneTime() {
    var orcResponse =
        TestUtils.mockFromFile(
            "stubs/__files/mmbff/xferandpayorc/addactivity/external_one_time_with_memo.json",
            XferAndPayActivity.class);
    when(xferAndPayOrcClient.editTransferAndPayActivity(any())).thenReturn(orcResponse);

    var editResult = activityService.editActivity(new BffActivityRequest());
    assertEquals("EXTERNAL_TRANSFER", editResult.getActivity().getActivityType());
    assertEquals("test a memo", editResult.getActivity().getMemo());
  }

  @Test
  void addActivityMapsExternalTransferRecurringUntilICancel() {
    var orcResponse =
        TestUtils.mockFromFile(
            "stubs/__files/mmbff/xferandpayorc/addactivity/external_recurring_until_i_cancel.json",
            XferAndPayActivity.class);
    when(xferAndPayOrcClient.postTransferAndPayActivity(any())).thenReturn(orcResponse);

    var result = activityService.addActivity(new BffActivityRequest());
    assertEquals("EXTERNAL_TRANSFER", result.getActivity().getActivityType());
    assertEquals(-1, result.getActivity().getNumberOfActivities());
  }

  @Test
  void editActivityMapsExternalTransferRecurringUntilICancel() {
    var orcResponse =
        TestUtils.mockFromFile(
            "stubs/__files/mmbff/xferandpayorc/addactivity/external_recurring_until_i_cancel.json",
            XferAndPayActivity.class);
    when(xferAndPayOrcClient.editTransferAndPayActivity(any())).thenReturn(orcResponse);

    var editResult = activityService.editActivity(new BffActivityRequest());
    assertEquals("EXTERNAL_TRANSFER", editResult.getActivity().getActivityType());
    assertEquals(-1, editResult.getActivity().getNumberOfActivities());
  }

  @Test
  void addActivityMapsExternalTransferRecurringNumberOfTimes() {
    var orcResponse =
        TestUtils.mockFromFile(
            "stubs/__files/mmbff/xferandpayorc/addactivity/external_recurring_number_of_times.json",
            XferAndPayActivity.class);
    when(xferAndPayOrcClient.postTransferAndPayActivity(any())).thenReturn(orcResponse);

    var result = activityService.addActivity(new BffActivityRequest());
    assertEquals("EXTERNAL_TRANSFER", result.getActivity().getActivityType());
    assertEquals(10, result.getActivity().getNumberOfActivities());
  }

  @Test
  void editActivityMapsExternalTransferRecurringNumberOfTimes() {
    var orcResponse =
        TestUtils.mockFromFile(
            "stubs/__files/mmbff/xferandpayorc/addactivity/external_recurring_number_of_times.json",
            XferAndPayActivity.class);
    when(xferAndPayOrcClient.editTransferAndPayActivity(any())).thenReturn(orcResponse);

    var editResult = activityService.editActivity(new BffActivityRequest());
    assertEquals("EXTERNAL_TRANSFER", editResult.getActivity().getActivityType());
    assertEquals(10, editResult.getActivity().getNumberOfActivities());
  }

  @Test
  void addActivityMapsGoalsTransferOneTime() {
    var orcResponse =
        TestUtils.mockFromFile(
            "stubs/__files/mmbff/xferandpayorc/addactivity/goals_add_money_one_time.json",
            XferAndPayActivity.class);
    when(xferAndPayOrcClient.postTransferAndPayActivity(any())).thenReturn(orcResponse);

    var result = activityService.addActivity(new BffActivityRequest());

    // Orchestrator returns internal transfer for type, I don't think clients use this so not fixing
    // for now
    assertEquals("GOALS_ADDMNY", result.getActivity().getActivityType());
  }
}
