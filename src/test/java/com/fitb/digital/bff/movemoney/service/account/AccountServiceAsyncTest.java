/* Copyright 2022 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.service.account;

import static com.fitb.digital.bff.movemoney.utils.TestUtils.mockCESFullAccountList;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.fitb.digital.bff.movemoney.client.CesClient;
import com.fitb.digital.bff.movemoney.model.client.account.responses.ListResponse;
import com.fitb.digital.bff.movemoney.model.client.profile.responses.ProfileResponse;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class AccountServiceAsyncTest {
  @InjectMocks private AccountServiceAsync accountService;
  @Mock private CesClient cesClient;

  @Test
  void profileAsyncWorks() throws ExecutionException, InterruptedException {
    when(cesClient.getTransferAndPayProfile(any())).thenReturn(mockProfileResponse());
    CompletableFuture<ProfileResponse> profileFork = accountService.getProfileAsync(true);
    ProfileResponse response = profileFork.get();
    assertNotNull(response);
  }

  @Test
  void asyncAccountListWorks() throws ExecutionException, InterruptedException {
    when(cesClient.getAccountsList(true)).thenReturn(mockCESFullAccountList());
    CompletableFuture<ListResponse> listFork = accountService.getAccountListAsync(true);
    ListResponse response = listFork.get();
    assertNotNull(response);
  }

  private ProfileResponse mockProfileResponse() {
    return new ProfileResponse();
  }
}
