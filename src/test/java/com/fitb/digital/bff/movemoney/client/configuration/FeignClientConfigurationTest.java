/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.client.configuration;

import static org.springframework.test.util.AssertionErrors.*;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import java.net.UnknownHostException;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

class FeignClientConfigurationTest {

  private FeignClientConfiguration feignClientConfiguration = new FeignClientConfiguration();

  @Test
  void testFeignRequestAllowAnon() {
    SecurityContextHolder.getContext().setAuthentication(null);
    RequestInterceptor interceptor = feignClientConfiguration.requestTokenBearerInterceptor();
    RequestTemplate mockTemplate = new RequestTemplate();
    interceptor.apply(mockTemplate);
    assertEquals("It should not add a header if no JWT exists", 0, mockTemplate.headers().size());
  }

  @Test
  void testFeignRequestHaveAuthorizationHeader() {
    Jwt mockJwt = Mockito.mock(Jwt.class);
    Mockito.doReturn("mockValue").when(mockJwt).getTokenValue();
    JwtAuthenticationToken mockToken = new JwtAuthenticationToken(mockJwt);
    SecurityContextHolder.getContext().setAuthentication(mockToken);
    RequestInterceptor interceptor = feignClientConfiguration.requestTokenBearerInterceptor();
    RequestTemplate requestTemplate = new RequestTemplate();
    interceptor.apply(requestTemplate);
    assertEquals(
        "It should add an Authorization header if a JWT exists in the security context",
        1,
        requestTemplate.headers().size());
    assertEquals(
        "It should return a Bearer token with the mockValue",
        "Bearer mockValue",
        requestTemplate.headers().get("Authorization").toArray()[0]);
    SecurityContextHolder.clearContext(); // clean up after ourselves
  }

  @Test
  void testFeignHeaderForwarderNoRequest() {
    RequestInterceptor interceptor = feignClientConfiguration.requestHeaderInterceptor();
    RequestTemplate requestTemplate = new RequestTemplate();
    interceptor.apply(requestTemplate);
    // nothing to test, just making sure the null request doesnt throw an npe.
    assertNotNull("Be quiet Sonar.", interceptor);
  }

  @Test
  void testFeignHeaderForwarder() {
    MockHttpServletRequest mockRequest = new MockHttpServletRequest();
    ServletRequestAttributes mockRequestAttributes = new ServletRequestAttributes(mockRequest);
    RequestContextHolder.setRequestAttributes(mockRequestAttributes);
    mockRequest.addHeader("x-mock-header", "mock value");

    RequestInterceptor interceptor = feignClientConfiguration.requestHeaderInterceptor();
    RequestTemplate requestTemplate = new RequestTemplate();
    interceptor.apply(requestTemplate);

    // 2 only because of the x-forwarded-for that gets added
    assertEquals("It should forward the x-mock-header header", 3, requestTemplate.headers().size());
    assertEquals(
        "It should return the value",
        "mock value",
        requestTemplate.headers().get("x-mock-header").toArray()[0]);

    RequestContextHolder.resetRequestAttributes();
  }

  @Test
  void testFeignShouldForwardApprovedHeaders() {
    MockHttpServletRequest mockRequest = new MockHttpServletRequest();
    ServletRequestAttributes mockRequestAttributes = new ServletRequestAttributes(mockRequest);
    RequestContextHolder.setRequestAttributes(mockRequestAttributes);

    mockRequest.addHeader("true-client-ip", "mock value");
    mockRequest.addHeader("user-agent", "mock value");

    RequestInterceptor interceptor = feignClientConfiguration.requestHeaderInterceptor();
    RequestTemplate requestTemplate = new RequestTemplate();
    interceptor.apply(requestTemplate);

    assertTrue(
        "Contains true-client-ip header", requestTemplate.headers().containsKey("true-client-ip"));
    assertTrue("Contains user-agent header", requestTemplate.headers().containsKey("user-agent"));

    RequestContextHolder.resetRequestAttributes();
  }

  @Test
  void testFeignHeaderXForwardedForFirstHop() throws UnknownHostException {
    MockHttpServletRequest mockRequest = new MockHttpServletRequest();
    ServletRequestAttributes mockRequestAttributes = new ServletRequestAttributes(mockRequest);
    RequestContextHolder.setRequestAttributes(mockRequestAttributes);
    mockRequest.setRemoteAddr("*******");
    mockRequest.setLocalAddr("*******");

    RequestInterceptor interceptor = feignClientConfiguration.requestHeaderInterceptor();
    RequestTemplate requestTemplate = new RequestTemplate();
    interceptor.apply(requestTemplate);

    // 2 only because of the x-forwarded-for that gets added
    assertEquals(
        "It should create the x-forwarded-for header", 2, requestTemplate.headers().size());
    assertEquals(
        "It should return the value",
        "*******, *******",
        requestTemplate.headers().get("x-forwarded-for").toArray()[0]);

    RequestContextHolder.resetRequestAttributes();
  }

  @Test
  void testFeignHeaderXForwardedForAppender() throws UnknownHostException {
    MockHttpServletRequest mockRequest = new MockHttpServletRequest();
    ServletRequestAttributes mockRequestAttributes = new ServletRequestAttributes(mockRequest);
    RequestContextHolder.setRequestAttributes(mockRequestAttributes);
    mockRequest.addHeader("x-forwarded-for", "127.0.0.1");
    mockRequest.setLocalAddr("*******");

    RequestInterceptor interceptor = feignClientConfiguration.requestHeaderInterceptor();
    RequestTemplate requestTemplate = new RequestTemplate();
    interceptor.apply(requestTemplate);

    // 2 only because of the x-forwarded-for that gets added
    assertEquals(
        "It should create the x-forwarded-for header", 2, requestTemplate.headers().size());
    assertEquals(
        "It should return the value",
        "127.0.0.1, *******",
        requestTemplate.headers().get("x-forwarded-for").toArray()[0]);

    RequestContextHolder.resetRequestAttributes();
  }
}
