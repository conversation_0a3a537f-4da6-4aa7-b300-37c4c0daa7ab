/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.controller;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.fitb.digital.bff.movemoney.model.bff.BffSimpleResponse;
import com.fitb.digital.bff.movemoney.model.bff.billpay.requests.BffAddPayeeRequest;
import com.fitb.digital.bff.movemoney.model.bff.billpay.responses.*;
import com.fitb.digital.bff.movemoney.service.BillpayService;
import com.fitb.digital.lib.tokenization.tokenizer.Tokenizer;
import java.util.ArrayList;
import java.util.Objects;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;

@ExtendWith(MockitoExtension.class)
public class MmBillpayControllerTest {
  @Mock private BillpayService billpayService;
  @Mock private Tokenizer tokenizer;
  @InjectMocks private MmBillpayController mmBillpayController;

  @Test
  public void globalPayeeHappyPath() {
    var payeeResponse = new BffGlobalPayeeResponse();
    var globalPayees = new ArrayList<BffGlobalPayee>();
    globalPayees.add(new BffGlobalPayee());
    payeeResponse.setGlobalPayees(globalPayees);

    when(billpayService.getGlobalPayees()).thenReturn(payeeResponse);
    var payeeControllerResponse = mmBillpayController.globalPayees();
    assertNotNull(payeeControllerResponse);
    assertFalse(payeeControllerResponse.getGlobalPayees().isEmpty());
  }

  @Test
  public void getPayeeAccountHappyPath() {
    var billPayeeAccountResponse = new BffBillPayeeAccountResponse();
    billPayeeAccountResponse.setAccountNumber("12345");

    when(billpayService.getTokenizedPayeeAccount(anyString())).thenReturn(billPayeeAccountResponse);
    var payeeAccountControllerResponse = mmBillpayController.getPayeeAccountNumber("payee_id");
    assertNotNull(payeeAccountControllerResponse);
    assertEquals(
        billPayeeAccountResponse.getAccountNumber(),
        Objects.requireNonNull(payeeAccountControllerResponse).getAccountNumber());
  }

  @Test
  public void addPayeeHappyPathTwoFactor() {
    var bffPayeeResponse = new BffPayeeResponse();
    bffPayeeResponse.setCesHttpStatus(HttpStatus.OK);
    when(billpayService.createPayee(any())).thenReturn(bffPayeeResponse);

    var request = new BffAddPayeeRequest();
    request.setPersonalPayee(true);
    var addPayeeResponse = mmBillpayController.addPayee(request);

    assertNotNull(addPayeeResponse);
    assertEquals(HttpStatus.OK, addPayeeResponse.getStatusCode());
  }

  @Test
  public void addPayeeNullAccountNumber_isSetEmptyString() {
    var bffPayeeResponse = new BffPayeeResponse();
    bffPayeeResponse.setCesHttpStatus(HttpStatus.OK);
    when(billpayService.createPayee(any())).thenReturn(bffPayeeResponse);

    var request = new BffAddPayeeRequest();
    request.setPersonalPayee(true);
    mmBillpayController.addPayee(request);

    var addPayeeRequest = new BffAddPayeeRequest();
    addPayeeRequest.setAccountNumber("");
    addPayeeRequest.setPersonalPayee(true);
    verify(billpayService).createPayee(addPayeeRequest);
  }

  @Test
  public void deletePayeeHappyPath() {
    when(billpayService.deletePayee(any())).thenReturn(new BffSimpleResponse());
    var deletePayeeResponse = mmBillpayController.deletePayee("anything");
    assertNotNull(deletePayeeResponse);
  }

  @Test
  public void getProfileHappyPath() {
    when(billpayService.getProfile()).thenReturn(new BffBillpayProfile());
    var bffProfile = mmBillpayController.getProfile();
    assertNotNull(bffProfile);
  }
}
