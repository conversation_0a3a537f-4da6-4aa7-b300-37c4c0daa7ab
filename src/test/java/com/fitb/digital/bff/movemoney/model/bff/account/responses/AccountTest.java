/* Copyright 2024 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.bff.account.responses;

import static org.junit.jupiter.api.Assertions.*;

import com.fitb.digital.bff.movemoney.client.xferandpayorc.model.XferAndPayActor;
import org.junit.jupiter.api.Test;

class AccountTest {

  @Test
  public void fromXferAndPayActor_mapsInvoiceEnabledAsTrue() {
    XferAndPayActor xferAndPayActor = new XferAndPayActor();
    xferAndPayActor.setInvoiceEnabled(true);
    Account account = Account.fromXferAndPayActor(xferAndPayActor);
    assertEquals(true, account.isInvoiceEnabled());
  }

  @Test
  public void fromXferAndPayActor_mapsInvoiceEnabledAsFalse() {
    XferAndPayActor xferAndPayActor = new XferAndPayActor();
    xferAndPayActor.setInvoiceEnabled(false);
    Account account = Account.fromXferAndPayActor(xferAndPayActor);
    assertEquals(false, account.isInvoiceEnabled());
  }
}
