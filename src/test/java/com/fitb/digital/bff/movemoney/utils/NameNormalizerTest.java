/* Copyright 2022 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.utils;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

import com.fitb.digital.bff.movemoney.util.NameNormalizer;
import org.junit.jupiter.api.Test;

public class NameNormalizerTest {
  @Test
  void emptyTest() {
    assertEquals("", NameNormalizer.normalizeAccountName(""));
  }

  @Test
  void nullTest() {
    assertNull(NameNormalizer.normalizeAccountName(null));
  }

  @Test
  void noopConverion() {
    assertEquals(
        "5/3 Essential Checking", NameNormalizer.normalizeAccountName("5/3 Essential Checking"));
  }

  @Test
  void upperToTitleCase() {
    assertEquals(
        "5/3 Essential Checking", NameNormalizer.normalizeAccountName("5/3 ESSENTIAL CHECKING"));
  }

  @Test
  void lowerNoopForNickname() {
    assertEquals(
        "5/3 essential checking", NameNormalizer.normalizeNickname("5/3 essential checking"));
  }

  @Test
  void upperToTitleCaseWithException() {
    assertEquals("5/3 Super DDA", NameNormalizer.normalizeAccountName("5/3 SUPER DDA"));
  }

  @Test
  void removeExtraWhitespace() {
    assertEquals("5/3 Super DDA", NameNormalizer.normalizeAccountName("5/3      SUPER     DDA"));
  }

  @Test
  void sarcasticNicknameNoop() {
    assertEquals(
        "5/3 eSsEnTiAl cHeCkInG", NameNormalizer.normalizeNickname("5/3 eSsEnTiAl cHeCkInG"));
  }

  @Test
  void regressionPncBank() {
    assertEquals(
        "Pnc Bank, Ohio Checking", NameNormalizer.normalizeAccountName("PNC BANK, OHIO CHECKING"));
  }

  @Test
  void regressionBankOfAmerica() {
    assertEquals(
        "Bank of America Checking",
        NameNormalizer.normalizeAccountName("BANK OF AMERICA CHECKING"));
  }

  @Test
  void regressionMyAdvance() {
    assertEquals("MyAdvance", NameNormalizer.normalizeAccountName("MyAdvance"));
  }

  @Test
  void northAmericaAbreviationWorks() {
    assertEquals("N.A.", NameNormalizer.normalizeAccountName("N.A."));
  }
}
