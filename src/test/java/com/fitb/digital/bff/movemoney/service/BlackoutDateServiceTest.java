/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.service;

import static org.junit.jupiter.api.Assertions.*;

import java.io.File;
import java.net.MalformedURLException;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

@Slf4j
@ExtendWith(MockitoExtension.class)
public class BlackoutDateServiceTest {
  @InjectMocks private BlackoutDateService blackoutDateService;

  @Test
  void getBlackoutDatesSucceedsWithValidUrl() throws MalformedURLException {
    var dates =
        blackoutDateService.getBlackoutDates(
            this.getClass().getResource("/blackout_dates.json"), null);

    assertNotNull(dates);
  }

  @Test
  void getBlackoutDatesSucceedsWithInvalidUrl() throws MalformedURLException {
    var dates =
        blackoutDateService.getBlackoutDates(
            this.getClass().getResource("/blackout_dates_does_not_exist.json"),
            new File(this.getClass().getResource("/blackout_dates.json").getFile()));

    assertNotNull(dates);
  }

  @Test
  void getBlackoutDatesReturnsNullWithInvalidUrlAndFile() {
    var dates =
        blackoutDateService.getBlackoutDates(
            this.getClass().getResource("/blackout_dates_does_not_exist.json"),
            new File("/blackout_dates_does_not_exist.json"));

    assertNull(dates);
  }

  @Test
  void getBlackoutDatesSucceedsWithInvalidDateFormat() throws MalformedURLException {
    var dates =
        blackoutDateService.getBlackoutDates(
            this.getClass().getResource("/blackout_dates_invalid_format.json"), null);

    assertNotNull(dates);
  }

  @Test
  void getBlackoutDatesSucceedsTwiceInARow() throws MalformedURLException {
    var dates =
        blackoutDateService.getBlackoutDates(
            this.getClass().getResource("/blackout_dates.json"), null);

    assertNotNull(dates);

    dates =
        blackoutDateService.getBlackoutDates(
            this.getClass().getResource("/blackout_dates.json"), null);

    assertNotNull(dates);
  }

  //  @Test
  //  void getLocalBlackoutDatesReturnsBlackoutDates() {
  //    assertEquals(100, blackoutDateService.getBlackoutDates().size());
  //  }
}
