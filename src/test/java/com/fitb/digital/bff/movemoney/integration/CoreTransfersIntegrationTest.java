/* Copyright 2025 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.integration;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fitb.digital.bff.movemoney.featureflags.FeatureFlagNames;
import com.fitb.digital.bff.movemoney.featureflags.FeatureFlagVariants;
import com.fitb.digital.bff.movemoney.model.bff.CoreTransfers.responses.TDSCoreTransferActivity;
import com.fitb.digital.bff.movemoney.model.bff.CoreTransfers.responses.TDSCoreTransferActivityResponse;
import com.fitb.digital.bff.movemoney.model.bff.activity.BffActivity;
import com.fitb.digital.bff.movemoney.model.bff.activity.RecurringActivityFrequency;
import com.fitb.digital.bff.movemoney.model.bff.activity.requests.BffActivityRequest;
import com.fitb.digital.bff.movemoney.model.bff.activity.responses.BffAddActivityResponse;
import com.fitb.digital.bff.movemoney.model.bff.activity.responses.BffGetActivityResponse;
import com.fitb.digital.bff.movemoney.model.client.account.responses.InternalAccount;
import com.fitb.digital.bff.movemoney.model.client.account.responses.ListResponse;
import com.fitb.digital.bff.movemoney.model.client.activity.responses.ClientActivity;
import com.fitb.digital.bff.movemoney.model.client.activity.responses.ClientActivityResponse;
import com.fitb.digital.bff.movemoney.service.account.AccountServiceAsync;
import com.fitb.digital.bff.movemoney.service.activity.ActivityServiceAsync;
import com.fitb.digital.bff.movemoney.service.coretransfers.CoreTransfersActivityService;
import com.fitb.digital.bff.movemoney.service.coretransfers.CoreTransfersService;
import com.fitb.digital.lib.featureflag.service.FeatureFlagService;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.concurrent.CompletableFuture;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;

@Slf4j
@SpringBootTest
@AutoConfigureMockMvc
public class CoreTransfersIntegrationTest {

  @Autowired private MockMvc mockMvc;

  @Autowired private ObjectMapper objectMapper;

  @MockBean private FeatureFlagService featureFlagService;
  @MockBean private ActivityServiceAsync activityServiceAsync;
  @MockBean private CoreTransfersActivityService coreTransfersActivityService;
  @MockBean private CoreTransfersService coreTransfersService;
  @MockBean private AccountServiceAsync accountServiceAsync;

  private static final String BEARER_TOKEN =
      "Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6IlFac0xydXlkWEdPQ01qaGk4OXdldGlKa25aVSIsInBpLmF0bSI6Im51eWoifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NsXi0ciImrNR9gsrfQNAO34RKbqMcCPIZjjt8KU3i0xLe86vM_Q4FP8JF8lLdgCJAOUc3lZOTMxD72MPh7TiDJWs7uDZBE7TROIUfSMen4l8mGFRsSF9n4xqThTt93CkdXEoBFh4AuV8SXFYc8vTrDK0cVRWgos918AbpknfYwWxNZZnFzaXsbL_F7wXxx1cQc_GjYb1_ETVmOwKMkIwI_wd-xDbrOC2cOtX0mtxX3Cp3xsYf22324nl3kp8-ObAfcQ4XwzptQ9ReW3XHl3bkbDjPw7ZtA47SNflCgndNXinzEBvL71pwCAlDTdEzcnYHbLUwXSxvaJW-Meri4maSA";
  private static final String FROM_ACCOUNT_ID = "from-account-123";
  private static final String TO_ACCOUNT_ID = "to-account-456";
  private static final BigDecimal TRANSFER_AMOUNT = new BigDecimal("100.50");

  @BeforeEach
  void setUp() {
    // Reset all mocks before each test
    reset(
        featureFlagService,
        activityServiceAsync,
        coreTransfersActivityService,
        coreTransfersService,
        accountServiceAsync);
  }

  @Test
  @DisplayName(
      "Scenario 1: CES Success + Core Transfers Enabled + Core Transfers Success = Full Success")
  void testScenario1_FullSuccess() throws Exception {
    // Given
    setupFeatureFlags(true, false); // Core Transfers enabled, Orchestrator disabled
    setupSuccessfulCESResponse();
    setupSuccessfulCoreTransfersActivityResponse();
    setupSuccessfulAccountListResponse();

    // When - Get activities
    MvcResult result =
        mockMvc
            .perform(
                get("/v2/activity")
                    .header("scope", "openid")
                    .header("sub", "mock-mvc-test")
                    .header("Authorization", BEARER_TOKEN)
                    .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                    .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andReturn();

    // Then
    BffGetActivityResponse response =
        objectMapper.readValue(
            result.getResponse().getContentAsString(), BffGetActivityResponse.class);

    assertNotNull(response);
    assertEquals("SUCCESS", response.getStatus());
    assertTrue(response.getRetrievalErrors().isEmpty());

    // Should have activities from both CES and Core Transfers
    assertFalse(response.getRecentActivities().isEmpty());

    // Verify Core Transfers activities are included
    boolean hasCoreTransferActivity =
        response.getRecentActivities().stream()
            .anyMatch(activity -> "CT-REF-123".equals(activity.getId()));
    assertTrue(hasCoreTransferActivity, "Should contain Core Transfers activity");
  }

  @Test
  @DisplayName(
      "Scenario 2: CES Success + Core Transfers Enabled + Core Transfers Failed = Partial Success")
  void testScenario2_PartialSuccess_CoreTransfersFails() throws Exception {
    // Given
    setupFeatureFlags(true, false);
    setupSuccessfulCESResponse();
    setupFailedCoreTransfersActivityResponse();
    setupSuccessfulAccountListResponse();

    // When
    MvcResult result =
        mockMvc
            .perform(
                get("/v2/activity")
                    .header("scope", "openid")
                    .header("sub", "mock-mvc-test")
                    .header("Authorization", BEARER_TOKEN)
                    .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                    .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andReturn();

    // Then
    BffGetActivityResponse response =
        objectMapper.readValue(
            result.getResponse().getContentAsString(), BffGetActivityResponse.class);

    assertNotNull(response);
    assertEquals("PARTIAL_SUCCESS", response.getStatus());
    assertTrue(response.getRetrievalErrors().contains("RETRIEVAL_ERROR_CORE_TRANSFERS"));

    // Should have only CES activities
    assertFalse(response.getRecentActivities().isEmpty());
  }

  @Test
  @DisplayName(
      "Scenario 3: CES Failed + Core Transfers Enabled + Core Transfers Success = Partial Success")
  void testScenario3_PartialSuccess_CESFails() throws Exception {
    // Given
    setupFeatureFlags(true, false);
    setupFailedCESResponse();
    setupSuccessfulCoreTransfersActivityResponse();
    setupSuccessfulAccountListResponse();

    // When
    MvcResult result =
        mockMvc
            .perform(
                get("/v2/activity")
                    .header("scope", "openid")
                    .header("sub", "mock-mvc-test")
                    .header("Authorization", BEARER_TOKEN)
                    .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                    .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andReturn();

    // Then
    BffGetActivityResponse response =
        objectMapper.readValue(
            result.getResponse().getContentAsString(), BffGetActivityResponse.class);

    assertNotNull(response);
    assertEquals("PARTIAL_SUCCESS", response.getStatus());
    assertTrue(response.getRetrievalErrors().contains("UNABLE_TO_GET_ACTIVITY"));

    // Should have only Core Transfers activities
    assertFalse(response.getRecentActivities().isEmpty());

    // Verify only Core Transfers activities are present
    boolean allAreCoreTransferActivities =
        response.getRecentActivities().stream()
            .allMatch(activity -> activity.getId().startsWith("CT-"));
    assertTrue(allAreCoreTransferActivities, "Should contain only Core Transfers activities");
  }

  @Test
  @DisplayName(
      "Scenario 4: CES Failed + Core Transfers Enabled + Core Transfers Failed = Exception")
  void testScenario4_BothServicesFail() throws Exception {
    // Given
    setupFeatureFlags(true, false);
    setupFailedCESResponse();
    setupFailedCoreTransfersActivityResponse();
    setupSuccessfulAccountListResponse();

    // When & Then
    mockMvc
        .perform(
            get("/v2/activity")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isServiceUnavailable());
  }

  @Test
  @DisplayName("Scenario 5: CES Success + Core Transfers Disabled = Full Success (CES only)")
  void testScenario5_CoreTransfersDisabled_CESSuccess() throws Exception {
    // Given
    setupFeatureFlags(false, false); // Core Transfers disabled
    setupSuccessfulCESResponse();

    // When
    MvcResult result =
        mockMvc
            .perform(
                get("/v2/activity")
                    .header("scope", "openid")
                    .header("sub", "mock-mvc-test")
                    .header("Authorization", BEARER_TOKEN)
                    .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                    .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andReturn();

    // Then
    BffGetActivityResponse response =
        objectMapper.readValue(
            result.getResponse().getContentAsString(), BffGetActivityResponse.class);

    assertNotNull(response);
    assertEquals("SUCCESS", response.getStatus());
    assertTrue(response.getRetrievalErrors().isEmpty());

    // Should have only CES activities
    assertFalse(response.getRecentActivities().isEmpty());

    // Verify no Core Transfers activities
    boolean hasNoCoreTransferActivities =
        response.getRecentActivities().stream()
            .noneMatch(activity -> activity.getId().startsWith("CT-"));
    assertTrue(hasNoCoreTransferActivities, "Should not contain Core Transfers activities");
  }

  @Test
  @DisplayName("Scenario 6: CES Failed + Core Transfers Disabled = Exception")
  void testScenario6_CoreTransfersDisabled_CESFails() throws Exception {
    // Given
    setupFeatureFlags(false, false); // Core Transfers disabled
    setupFailedCESResponse();

    // When & Then
    mockMvc
        .perform(
            get("/v2/activity")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isServiceUnavailable());
  }

  @Test
  @DisplayName("Scenario 7: Core Transfer Creation + Activity Retrieval Integration")
  void testScenario7_CoreTransferCreationAndRetrieval() throws Exception {
    // Given
    setupFeatureFlags(true, false);
    setupSuccessfulCoreTransfersCreateResponse();
    setupSuccessfulCoreTransfersActivityResponseWithCreatedTransfer();
    setupSuccessfulCESResponse();
    setupSuccessfulAccountListResponse();

    // Step 1: Create a Core Transfer
    BffActivityRequest createRequest = createCoreTransferRequest();

    MvcResult createResult =
        mockMvc
            .perform(
                post("/activity")
                    .header("scope", "openid")
                    .header("sub", "mock-mvc-test")
                    .header("Authorization", BEARER_TOKEN)
                    .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(createRequest)))
            .andExpect(status().isOk())
            .andReturn();

    BffAddActivityResponse createResponse =
        objectMapper.readValue(
            createResult.getResponse().getContentAsString(), BffAddActivityResponse.class);

    assertNotNull(createResponse);
    assertEquals("SUCCESS", createResponse.getStatus());
    assertNotNull(createResponse.getActivity());

    // Step 2: Retrieve activities and verify the created transfer is included
    MvcResult getResult =
        mockMvc
            .perform(
                get("/v2/activity")
                    .header("scope", "openid")
                    .header("sub", "mock-mvc-test")
                    .header("Authorization", BEARER_TOKEN)
                    .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                    .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andReturn();

    BffGetActivityResponse getResponse =
        objectMapper.readValue(
            getResult.getResponse().getContentAsString(), BffGetActivityResponse.class);

    assertNotNull(getResponse);
    assertEquals("SUCCESS", getResponse.getStatus());

    // Verify the created transfer appears in activities
    boolean hasCreatedTransfer =
        getResponse.getRecentActivities().stream()
            .anyMatch(activity -> "CT-CREATED-456".equals(activity.getId()));
    assertTrue(hasCreatedTransfer, "Should contain the created Core Transfer activity");
  }

  // Helper methods for setting up feature flags
  private void setupFeatureFlags(boolean coreTransfersEnabled, boolean orchestratorEnabled) {
    when(featureFlagService.isFeatureEnabled(FeatureFlagNames.CORE_TRANSFERS_TDS_ENABLED))
        .thenReturn(coreTransfersEnabled);
    when(featureFlagService.isFeatureEnabled(FeatureFlagNames.ORCHESTRATOR_ENABLED))
        .thenReturn(orchestratorEnabled);
    when(featureFlagService.getFeatureVariant(anyString(), anyString()))
        .thenReturn(FeatureFlagVariants.ENABLED);
  }

  // Helper methods for setting up client mocks
  private void setupSuccessfulCESResponse() {
    ClientActivityResponse cesResponse = new ClientActivityResponse();
    cesResponse.setStatus("SUCCESS");
    cesResponse.setStatusCode("200");
    cesResponse.setStatusReason("OK");

    // Add a CES activity
    ClientActivity cesActivity = new ClientActivity();
    cesActivity.setId("ces-activity-1");
    cesActivity.setDisplayId("ces-activity-1");
    cesActivity.setFromAccountId("ces-from-1");
    cesActivity.setToAccountId("ces-to-1");
    cesActivity.setAmount(50.00);
    cesActivity.setDisplayStatus("Completed");
    cesActivity.setStatus("3");
    cesActivity.setActivityType("INTERNAL_TRANSFER");
    cesActivity.setDueDate(LocalDate.now().minusDays(1));
    cesActivity.setCreateTimestamp(LocalDate.now().minusDays(1).atStartOfDay());

    cesResponse.setActivities(Arrays.asList(cesActivity));

    when(activityServiceAsync.getActivityAsync())
        .thenReturn(CompletableFuture.completedFuture(cesResponse));
  }

  private void setupFailedCESResponse() {
    when(activityServiceAsync.getActivityAsync())
        .thenThrow(new RuntimeException("CES service unavailable"));
  }

  private void setupSuccessfulCoreTransfersActivityResponse() {
    TDSCoreTransferActivityResponse response = new TDSCoreTransferActivityResponse();

    TDSCoreTransferActivity activity1 = new TDSCoreTransferActivity();
    activity1.setReferenceId("CT-REF-123");
    activity1.setFromAccountId(FROM_ACCOUNT_ID);
    activity1.setFromAccountType("DDA");
    activity1.setToAccountId(TO_ACCOUNT_ID);
    activity1.setToAccountType("SAV");
    activity1.setAmount(TRANSFER_AMOUNT);
    activity1.setTransferStatus("SUCCESS");
    activity1.setCreatedDate(LocalDate.now().minusDays(1));
    activity1.setExpectedPostingDate(LocalDate.now().minusDays(1));

    TDSCoreTransferActivity activity2 = new TDSCoreTransferActivity();
    activity2.setReferenceId("CT-REF-456");
    activity2.setFromAccountId("from-account-789");
    activity2.setFromAccountType("SAV");
    activity2.setToAccountId("to-account-012");
    activity2.setToAccountType("DDA");
    activity2.setAmount(new BigDecimal("250.75"));
    activity2.setTransferStatus("PENDING");
    activity2.setCreatedDate(LocalDate.now().minusDays(2));
    activity2.setExpectedPostingDate(LocalDate.now().minusDays(2));

    response.setTransferActivities(Arrays.asList(activity1, activity2));

    when(coreTransfersActivityService.getCoreTransfersActivitiesAsync())
        .thenReturn(CompletableFuture.completedFuture(response));

    // Mock the mapping method to return BffActivity objects
    BffActivity bffActivity1 = new BffActivity();
    bffActivity1.setId("CT-REF-123");
    bffActivity1.setFromAccountId(FROM_ACCOUNT_ID);
    bffActivity1.setToAccountId(TO_ACCOUNT_ID);
    bffActivity1.setAmount(TRANSFER_AMOUNT.doubleValue());
    bffActivity1.setDisplayStatus("Completed");
    bffActivity1.setActivityType("INTERNAL_TRANSFER");

    BffActivity bffActivity2 = new BffActivity();
    bffActivity2.setId("CT-REF-456");
    bffActivity2.setFromAccountId("from-account-789");
    bffActivity2.setToAccountId("to-account-012");
    bffActivity2.setAmount(250.75);
    bffActivity2.setDisplayStatus("In Process");
    bffActivity2.setActivityType("INTERNAL_TRANSFER");

    when(coreTransfersActivityService.mapCoreTransfersActivities(any(), anyList()))
        .thenReturn(Arrays.asList(bffActivity1, bffActivity2));
  }

  private void setupFailedCoreTransfersActivityResponse() {
    when(coreTransfersActivityService.getCoreTransfersActivitiesAsync())
        .thenThrow(new RuntimeException("Core Transfers service unavailable"));

    // No need to mock mapping method since the async call fails
  }

  private void setupSuccessfulAccountListResponse() {
    ListResponse accountResponse = new ListResponse();
    accountResponse.setStatus("SUCCESS");
    accountResponse.setStatusCode("200");
    accountResponse.setStatusReason("OK");

    InternalAccount account1 = new InternalAccount();
    account1.setId(FROM_ACCOUNT_ID);
    account1.setDisplayAccountNumber("**********");
    account1.setDisplayName("Test Checking Account");
    account1.setAccountType("DDA");
    account1.setAvailableBalance(new BigDecimal("1000.00"));
    account1.setLedgerBalance(new BigDecimal("1000.00"));

    InternalAccount account2 = new InternalAccount();
    account2.setId(TO_ACCOUNT_ID);
    account2.setDisplayAccountNumber("**********");
    account2.setDisplayName("Test Savings Account");
    account2.setAccountType("SAV");
    account2.setAvailableBalance(new BigDecimal("2000.00"));
    account2.setLedgerBalance(new BigDecimal("2000.00"));

    accountResponse.setAccounts(Arrays.asList(account1, account2));

    when(accountServiceAsync.getAccountListAsync(true))
        .thenReturn(CompletableFuture.completedFuture(accountResponse));
  }

  private void setupSuccessfulCoreTransfersCreateResponse() {
    BffAddActivityResponse response = new BffAddActivityResponse();
    response.setStatus("SUCCESS");

    BffActivity activity = new BffActivity();
    activity.setId("CT-CREATED-456");
    activity.setFromAccountId(FROM_ACCOUNT_ID);
    activity.setToAccountId(TO_ACCOUNT_ID);
    activity.setAmount(TRANSFER_AMOUNT.doubleValue());
    activity.setDisplayStatus("Processed");
    activity.setActivityType("INTERNAL_TRANSFER");
    activity.setStatus("3");

    response.setActivity(activity);

    when(coreTransfersService.addActivityTds(any())).thenReturn(response);
  }

  private void setupSuccessfulCoreTransfersActivityResponseWithCreatedTransfer() {
    TDSCoreTransferActivityResponse response = new TDSCoreTransferActivityResponse();

    // Include the created transfer in the activity response
    TDSCoreTransferActivity createdActivity = new TDSCoreTransferActivity();
    createdActivity.setReferenceId("CT-CREATED-456");
    createdActivity.setFromAccountId(FROM_ACCOUNT_ID);
    createdActivity.setFromAccountType("DDA");
    createdActivity.setToAccountId(TO_ACCOUNT_ID);
    createdActivity.setToAccountType("SAV");
    createdActivity.setAmount(TRANSFER_AMOUNT);
    createdActivity.setTransferStatus("SUCCESS");
    createdActivity.setCreatedDate(LocalDate.now());
    createdActivity.setExpectedPostingDate(LocalDate.now());

    // Include existing activities as well
    TDSCoreTransferActivity existingActivity = new TDSCoreTransferActivity();
    existingActivity.setReferenceId("CT-REF-123");
    existingActivity.setFromAccountId("existing-from-123");
    existingActivity.setFromAccountType("SAV");
    existingActivity.setToAccountId("existing-to-456");
    existingActivity.setToAccountType("DDA");
    existingActivity.setAmount(new BigDecimal("75.25"));
    existingActivity.setTransferStatus("PENDING");
    existingActivity.setCreatedDate(LocalDate.now().minusDays(1));
    existingActivity.setExpectedPostingDate(LocalDate.now().minusDays(1));

    response.setTransferActivities(Arrays.asList(createdActivity, existingActivity));

    when(coreTransfersActivityService.getCoreTransfersActivitiesAsync())
        .thenReturn(CompletableFuture.completedFuture(response));

    // Mock the mapping method to return BffActivity objects
    BffActivity bffCreatedActivity = new BffActivity();
    bffCreatedActivity.setId("CT-CREATED-456");
    bffCreatedActivity.setFromAccountId(FROM_ACCOUNT_ID);
    bffCreatedActivity.setToAccountId(TO_ACCOUNT_ID);
    bffCreatedActivity.setAmount(TRANSFER_AMOUNT.doubleValue());
    bffCreatedActivity.setDisplayStatus("Completed");
    bffCreatedActivity.setActivityType("INTERNAL_TRANSFER");

    BffActivity bffExistingActivity = new BffActivity();
    bffExistingActivity.setId("CT-EXISTING-789");
    bffExistingActivity.setFromAccountId("existing-from-123");
    bffExistingActivity.setToAccountId("existing-to-456");
    bffExistingActivity.setAmount(75.25);
    bffExistingActivity.setDisplayStatus("Completed");
    bffExistingActivity.setActivityType("INTERNAL_TRANSFER");

    when(coreTransfersActivityService.mapCoreTransfersActivities(any(), anyList()))
        .thenReturn(Arrays.asList(bffCreatedActivity, bffExistingActivity));
  }

  private BffActivityRequest createCoreTransferRequest() {
    BffActivityRequest request = new BffActivityRequest();
    request.setActivityType("INTERNAL_TRANSFER");
    request.setFromAccountId(FROM_ACCOUNT_ID);
    request.setFromAccountType("DDA");
    request.setToAccountId(TO_ACCOUNT_ID);
    request.setToAccountType("SAV");
    request.setAmount(TRANSFER_AMOUNT);
    request.setFrequency(RecurringActivityFrequency.ONE_TIME);
    request.setDueDate(LocalDate.now());
    request.setMemo("Integration test transfer");
    request.setScheduleImmediately(false);
    return request;
  }
}
