/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.config;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Locale;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class BufferedCesAccountListResponseWrapperTest {

  @Mock HttpServletResponse response;
  @Mock ByteArrayOutputStream bos;

  private BufferedResponseWrapper fixture;

  @BeforeEach
  public void onSetUp() {
    MockitoAnnotations.initMocks(this);
    fixture = new BufferedResponseWrapper(response);
  }

  @Test
  public void testLocale() {
    Locale expected = Locale.CANADA;
    when(response.getLocale()).thenReturn(expected);
    fixture.setLocale(expected);
    assertEquals(expected, fixture.getLocale(), "original locale must be returned");
  }

  @Test
  public void testAddCookie() {
    fixture.addCookie(new Cookie("Blah", "Value"));
    verify(response, times(1)).addCookie(any(Cookie.class));
  }

  @Test
  public void testEncodeURL() {
    String input = "original";
    String expected = "encoded";
    when(response.encodeURL(eq(input))).thenReturn(expected);
    assertEquals(expected, fixture.encodeURL(input), "URL encoded value must be returned");
  }

  @Test
  public void testEncodeUrl() {
    String input = "original";
    String expected = "encoded";
    when(response.encodeURL(eq(input))).thenReturn(expected);
    assertEquals(expected, fixture.encodeURL(input), "URL encoded value must be returned");
  }

  @Test
  public void testEncodeRedirectURL() {
    String input = "original";
    String expected = "encoded";
    when(response.encodeRedirectURL(eq(input))).thenReturn(expected);
    assertEquals(expected, fixture.encodeRedirectURL(input), "URL encoded value must be returned");
  }

  @Test
  public void testEncodeRedirectUrl() {
    String input = "original";
    String expected = "encoded";
    when(response.encodeRedirectURL(eq(input))).thenReturn(expected);
    assertEquals(expected, fixture.encodeRedirectURL(input), "URL encoded value must be returned");
  }

  @Test
  public void testSendRedirect() throws IOException {
    String input = "input";
    fixture.sendRedirect(input);
    verify(response, times(1)).sendRedirect(eq(input));
  }

  @Test
  public void testGetPrintWriter() throws IOException {
    fixture.getWriter();
    verify(response, times(1)).getWriter();
  }

  @Test
  public void testSetCharacterEncoding() throws IOException {
    fixture.setCharacterEncoding("UTF-8");
    verify(response, times(1)).setCharacterEncoding(any(String.class));
  }

  @Test
  public void testSetContentLength() {
    fixture.setContentLength(1024);
    verify(response, times(1)).setContentLength(anyInt());
  }

  @Test
  public void testSetBufferSize() {
    fixture.setBufferSize(1024);
    verify(response, times(1)).setBufferSize(anyInt());
  }

  @Test
  public void testIsCommited() {
    fixture.isCommitted();
    verify(response, times(1)).isCommitted();
  }

  @Test
  public void testSetDateHeader() {
    fixture.setDateHeader("header1", 20210219);
    verify(response, times(1)).setDateHeader(any(String.class), anyLong());
  }

  @Test
  public void testSetIntHeader() {
    fixture.setIntHeader("header2", 200);
    verify(response, times(1)).setIntHeader(any(String.class), anyInt());
  }

  @Test
  public void testGetHeaderNames() {
    fixture.getHeaderNames();
    verify(response, times(1)).getHeaderNames();
  }

  @Test
  public void testSetContentType() {
    fixture.setContentType("application/json");
    verify(response, times(1)).setContentType(any(String.class));
  }

  @Test
  public void testResetBuffer() {
    fixture.resetBuffer();
    verify(response, times(1)).resetBuffer();
  }

  @Test
  public void testReset() {
    fixture.reset();
    verify(response, times(1)).reset();
  }

  @Test
  public void testSendError() throws IOException {
    fixture.sendError(400);
    verify(response, times(1)).sendError(anyInt());
  }

  @Test
  public void testSendError2() throws IOException {
    fixture.sendError(400, "Bad Request");
    verify(response, times(1)).sendError(anyInt(), any(String.class));
  }

  @Test
  public void testAddDateHeader() {
    fixture.addDateHeader("header1", 20210219);
    verify(response, times(1)).addDateHeader(any(String.class), anyLong());
  }

  @Test
  public void testAddIntHeader() {
    fixture.addIntHeader("header1", 200);
    verify(response, times(1)).addIntHeader(any(String.class), anyInt());
  }

  @Test
  public void testSetStatus() {
    fixture.setStatus(200);
    verify(response, times(1)).setStatus(anyInt());
  }

  @Test
  public void testSetStatus2() {
    fixture.setStatus(200);
    verify(response, times(1)).setStatus(anyInt());
  }
}
