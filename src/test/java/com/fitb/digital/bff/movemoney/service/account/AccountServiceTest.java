/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.service.account;

import static com.fitb.digital.bff.movemoney.utils.TestUtils.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.fitb.digital.bff.movemoney.client.CesClient;
import com.fitb.digital.bff.movemoney.model.client.account.responses.ClientAccountDetail;
import com.fitb.digital.bff.movemoney.model.client.account.responses.ClientAccountDetailResponse;
import com.fitb.digital.bff.movemoney.model.local.TransferDetails;
import java.time.LocalDate;
import java.util.Optional;
import java.util.concurrent.ExecutionException;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@Slf4j
@ExtendWith(MockitoExtension.class)
public class AccountServiceTest {
  @InjectMocks private AccountService accountService;
  @Mock private CesClient cesClient;

  @Test
  public void getTransferDetailsLogic() throws ExecutionException, InterruptedException {
    Optional<TransferDetails> details =
        accountService.getTransferDetails(
            "********-A64A-3BCD-8E1A-88785C9CDA12",
            "E0C5748F-60DF-885A-53E9-616DCD656054",
            mockCESProfileResponse());
    assertNotNull(details);
    assertTrue(details.isPresent());
    assertNotNull(details.get().getEarliestAvailableDate());
    assertNotNull(details.get().getLatestAvailableDate());
  }

  @Test
  public void getAccountDetailReturnsAccountDetail() {
    ClientAccountDetailResponse detail = new ClientAccountDetailResponse();
    var accountDetail = new ClientAccountDetail();
    accountDetail.setAccountType("SAV");
    accountDetail.setAccountDescription("Savings");
    detail.setAccountDetail(accountDetail);
    when(cesClient.getAccountDetail(any())).thenReturn(detail);

    var response = accountService.getAccountDetails("yea buddy");

    assertNotNull(response);
    assertEquals("SAV", response.getAccountDetail().getAccountType());
  }

  @Test
  public void accountDetailsMappingAllFields() {
    when(cesClient.getAccountDetail(anyString())).thenReturn(mockAccountDetails());
    var detailResponse = accountService.getAccountDetails("can-you-spare-a-dime");
    assertNotNull(detailResponse);
    assertEquals("SUCCESS", detailResponse.getStatusCode());
    var details = detailResponse.getAccountDetail();
    assertEquals(LocalDate.parse("2015-10-02"), details.getLastStatementDate());
  }

  @Test
  public void creditCardDetailsAreMapped() {
    when(cesClient.getAccountDetail(anyString())).thenReturn(mockCreditCardDetails());
    var detailResponse = accountService.getAccountDetails("credit-card");
    var response = detailResponse.getAccountDetail();
    assertNotNull(response.getNextPaymentAmount());
    assertEquals(response.getLastStatementBalance(), response.getNextPaymentAmount());
  }

  @Test
  public void installmentLoanDetailsAreMapped() {
    var cesDetails = mockInstallmentLoanDetails();
    when(cesClient.getAccountDetail(anyString())).thenReturn(cesDetails);
    var detailResponse = accountService.getAccountDetails("my-loan");
    var response = detailResponse.getAccountDetail();
    assertNotNull(response.getInitialPrincipal());
    assertEquals(cesDetails.getAccountDetail().getNoteAmount(), response.getInitialPrincipal());
  }
}
