/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.utils;

import static com.fitb.digital.bff.movemoney.service.activity.ActivityServiceV1.NO_ACTIVITY_FOUND;

import com.fitb.digital.bff.movemoney.model.bff.activity.RecurringActivityFrequency;
import com.fitb.digital.bff.movemoney.model.client.activity.responses.*;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class ActivityUtils {
  public static final String ACCOUNT_ID_1 = "account-1";
  public static final String ACCOUNT_ID_2 = "account-2";
  public static final String ACTIVITY_1 = "activity-1";
  public static final String ACTIVITY_2 = "activity-2";
  public static final String ACTIVITY_3 = "activity-3";

  public static ClientActivity mockActivity(String id, String toAccount, String fromAccount) {
    ClientActivity a = new ClientActivity();
    a.setId(id);
    a.setDisplayId(id);
    a.setToAccountId(toAccount);
    a.setFromAccountId(fromAccount);
    return a;
  }

  public static ClientActivity mockRecurringActivity(
      String id, String toAccount, String fromAccount) {
    ClientActivity a = new ClientActivity();
    a.setRecurringId(id);
    a.setDisplayId(id);
    a.setToAccountId(toAccount);
    a.setFromAccountId(fromAccount);
    a.setFrequency(RecurringActivityFrequency.ONCE_A_MONTH);
    return a;
  }

  public static ClientActivityResponse mockActivityResponseClient() {
    ClientActivityResponse c = new ClientActivityResponse();
    c.setStatus("SUCCESS");
    c.setStatusCode("OK");
    List<ClientActivity> activities = List.of(mockActivity(ACTIVITY_1, ACCOUNT_ID_1, ACCOUNT_ID_2));
    c.setActivities(activities);
    return c;
  }

  public static TransferLimitsResponse mockTransferLimitsResponseClient() {
    TransferLimitsResponse t = new TransferLimitsResponse();
    t.setStatus("SUCCESS");
    t.setStatusCode("OK");
    List<Limit> limits = new ArrayList();

    Limit l1 = new Limit();
    l1.setHostAccountId("5740834B-C621-D166-5E4A-96FC49CFBF6B");
    l1.setNonHostAccountId("23E13F71-9BD0-BD5B-3AC9-4902688DAF6F");
    l1.setDirection(CesExternalTransferDirection.OUTBOUND);
    l1.setSpeed(CesExternalTransferSpeed.STANDARD);
    l1.setTransactionLimit(BigDecimal.valueOf(10000.00));
    l1.setDailyLimit(BigDecimal.valueOf(10000.00));
    l1.setRemainingToday(BigDecimal.valueOf(10000.00));
    l1.setMonthlyLimit(BigDecimal.valueOf(15000.00));
    l1.setRemainingThisMonth(BigDecimal.valueOf(15000.00));
    l1.setOutstandingLimit(BigDecimal.valueOf(15000.00));

    Limit l2 = new Limit();
    l2.setHostAccountId("5740834B-C621-D166-5E4A-96FC49CFBF6B");
    l2.setNonHostAccountId("23E13F71-9BD0-BD5B-3AC9-4902688DAF6F");
    l2.setDirection(CesExternalTransferDirection.OUTBOUND);
    l2.setSpeed(CesExternalTransferSpeed.NEXTDAY);
    l2.setTransactionLimit(BigDecimal.valueOf(2000.00));
    l2.setDailyLimit(BigDecimal.valueOf(2000.00));
    l2.setRemainingToday(BigDecimal.valueOf(2000.00));
    l2.setMonthlyLimit(BigDecimal.valueOf(5000.00));
    l2.setRemainingThisMonth(BigDecimal.valueOf(5000.00));
    l2.setOutstandingLimit(BigDecimal.valueOf(2000.00));

    limits.add(l1);
    limits.add(l2);
    t.setLimits(limits);
    return t;
  }

  public static ClientActivityResponse mockRecurringActivityResponseClient() {
    ClientActivityResponse c = new ClientActivityResponse();
    c.setStatus("SUCCESS");
    c.setStatusCode("OK");
    List<ClientActivity> recurringActivities =
        List.of(
            mockRecurringActivity(ACTIVITY_2, ACCOUNT_ID_1, ACCOUNT_ID_2),
            mockRecurringActivity(ACTIVITY_3, ACCOUNT_ID_1, ACCOUNT_ID_2));
    c.setRecurringActivities(recurringActivities);
    return c;
  }

  public static ClientActivityResponse mockEmptyActivityResponseClient() {
    ClientActivityResponse c = new ClientActivityResponse();
    c.setStatus("SUCCESS");
    c.setStatusCode(NO_ACTIVITY_FOUND);
    c.setStatusReason("No activity found.");
    c.setRecurringActivities(new ArrayList<>());
    c.setActivities(new ArrayList<>());
    return c;
  }
}
