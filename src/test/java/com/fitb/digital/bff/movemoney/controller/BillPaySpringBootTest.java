/* Copyright 2023 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.controller;

import static com.fitb.digital.bff.movemoney.utils.TestUtils.mockFromFile;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.fitb.digital.bff.movemoney.client.CesClient;
import com.fitb.digital.bff.movemoney.exceptions.CesFeignException;
import com.fitb.digital.bff.movemoney.model.client.CesBaseResponse;
import com.fitb.digital.bff.movemoney.model.client.CesResponse;
import com.fitb.digital.bff.movemoney.model.client.billpay.responses.CesPayeeResponse;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

@SpringBootTest
@AutoConfigureMockMvc
public class BillPaySpringBootTest {
  @Autowired private MockMvc mockMvc;
  @MockBean CesClient cesClient;

  static final String BEARER_TOKEN =
      "Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6IlFac0xydXlkWEdPQ01qaGk4OXdldGlKa25aVSIsInBpLmF0bSI6Im51eWoifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NsXi0ciImrNR9gsrfQNAO34RKbqMcCPIZjjt8KU3i0xLe86vM_Q4FP8JF8lLdgCJAOUc3lZOTMxD72MPh7TiDJWs7uDZBE7TROIUfSMen4l8mGFRsSF9n4xqThTt93CkdXEoBFh4AuV8SXFYc8vTrDK0cVRWgos918AbpknfYwWxNZZnFzaXsbL_F7wXxx1cQc_GjYb1_ETVmOwKMkIwI_wd-xDbrOC2cOtX0mtxX3Cp3xsYf22324nl3kp8-ObAfcQ4XwzptQ9ReW3XHl3bkbDjPw7ZtA47SNflCgndNXinzEBvL71pwCAlDTdEzcnYHbLUwXSxvaJW-Meri4maSA";

  @Test
  @DisplayName(
      """
                        Given a user tries to add a payee
                        When the account number is null
                        And the request is not a personalPayee
                        Then the request is denied with INVALID_DUE_DATE status code.
                        """)
  void addPayeeHappyPath() throws Exception {
    var content =
        """
                            {
                                "name": "newbillname123",
                                "accountNumber": 1234,
                                "nickname": "Bill",
                                "address": {
                                    "streetLine1": "3267 BRAMPTON ST",
                                    "streetLine2": " ",
                                    "city": "DUBLIN",
                                    "stateOrProvince": "OH",
                                    "postalCode": "43017",
                                    "badAddressIndicator": false,
                                    "country": null
                                },
                                "personalPayee": true
                            }
                                        """;

    CesPayeeResponse cesPayeeResponse =
        mockFromFile("ces_addPayee_success.json", CesPayeeResponse.class);

    when(cesClient.addPayee(any())).thenReturn(cesPayeeResponse);
    this.mockMvc
        .perform(
            MockMvcRequestBuilders.post("/billpay/addpayee")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BillPaySpringBootTest.BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .content(content)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(MockMvcResultMatchers.status().isOk());
  }

  @Test
  @DisplayName(
      """
                        Given a user tries to add a payee
                        When the payee is a custom payee
                        And the account number is null
                        And the Personal Payee flag is true
                        Then the request is successful.
                        """)
  void addPayeeHappyPath_whenTheAccountNumberIsNullAndPersonalPayeeTrue() throws Exception {
    var content =
        """
                            {
                                "name": "newbillname123",
                                "accountNumber": null,
                                "nickname": "Bill",
                                "address": {
                                    "streetLine1": "3267 BRAMPTON ST",
                                    "streetLine2": " ",
                                    "city": "DUBLIN",
                                    "stateOrProvince": "OH",
                                    "postalCode": "43017",
                                    "badAddressIndicator": false,
                                    "country": null
                                },
                                "personalPayee": true
                            }
                                        """;

    CesPayeeResponse cesPayeeResponse =
        mockFromFile("ces_addPayee_success.json", CesPayeeResponse.class);

    when(cesClient.addPayee(any())).thenReturn(cesPayeeResponse);
    this.mockMvc
        .perform(
            MockMvcRequestBuilders.post("/billpay/addpayee")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BillPaySpringBootTest.BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .content(content)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(MockMvcResultMatchers.status().isOk());
  }

  @Test
  @DisplayName(
      """
                            Given a user tries to add a payee
                            When the
                            And the request is
                            Then the request is .
                            """)
  void addPayee_non200() throws Exception {
    var content =
        """
                                {
                                    "name": "newbillname123",
                                    "accountNumber": 1234,
                                    "nickname": "Bill",
                                    "address": {
                                        "streetLine1": "3267 BRAMPTON ST",
                                        "streetLine2": " ",
                                        "city": "DUBLIN",
                                        "stateOrProvince": "OH",
                                        "postalCode": "43017",
                                        "badAddressIndicator": false,
                                        "country": null
                                    },
                                    "personalPayee": true
                                }
                                            """;

    CesPayeeResponse cesPayeeResponse =
        mockFromFile("ces_addPayee_success.json", CesPayeeResponse.class);

    when(cesClient.addPayee(any())).thenReturn(cesPayeeResponse);
    this.mockMvc
        .perform(
            MockMvcRequestBuilders.post("/billpay/addpayee")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BillPaySpringBootTest.BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .content(content)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(MockMvcResultMatchers.status().isOk());
  }

  @Test
  @DisplayName(
      """
                          Given a user tries to add a payee
                          When the payee is a custom payee
                          And the account number is null
                          And the Personal Payee flag is false
                          Then the request is denied with BAD_REQUEST status code.
                          """)
  void addPayee_shouldReturnBadRequest_whenTheAccountNumberIsNullAndPersonalPayeFalse()
      throws Exception {
    var content =
        """
                              {
                                  "name": "newbillname123",
                                  "accountNumber": null,
                                  "nickname": "Bill",
                                  "address": {
                                      "streetLine1": "3267 BRAMPTON ST",
                                      "streetLine2": " ",
                                      "city": "DUBLIN",
                                      "stateOrProvince": "OH",
                                      "postalCode": "43017",
                                      "badAddressIndicator": false,
                                      "country": null
                                  },
                                  "personalPayee": false
                              }
                                          """;

    CesPayeeResponse cesPayeeResponse =
        mockFromFile("ces_addPayee_success.json", CesPayeeResponse.class);

    when(cesClient.addPayee(any())).thenReturn(cesPayeeResponse);
    this.mockMvc
        .perform(
            MockMvcRequestBuilders.post("/billpay/addpayee")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BillPaySpringBootTest.BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .content(content)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(MockMvcResultMatchers.status().isBadRequest())
        .andExpect(MockMvcResultMatchers.jsonPath("$.status").value("BILL_PAY_ERROR"))
        .andExpect(
            MockMvcResultMatchers.jsonPath("$.statusCode").value("PERSONAL_PAYEE_FLAG_NOT_TRUE"))
        .andExpect(
            MockMvcResultMatchers.jsonPath("$.statusReason")
                .value("Custom payees with no account number must set personalPayee flag to true."))
        .andExpect(MockMvcResultMatchers.jsonPath("$.error.code").value("BAD_REQUEST"))
        .andExpect(
            MockMvcResultMatchers.jsonPath("$.error.message")
                .value("Custom payees with no account number must set personalPayee flag to true."))
        .andExpect(MockMvcResultMatchers.jsonPath("$.error.target").value("/billpay/addpayee"))
        .andExpect(
            MockMvcResultMatchers.jsonPath("$.error.innerError.code")
                .value("BILL_PAY_ERROR.PERSONAL_PAYEE_FLAG_NOT_TRUE"));
  }

  @Test
  @DisplayName(
      """
                              Given a user tries to add a payee
                              When the payee is a custom payee
                              And the account number is null
                              And the Personal Payee flag is false
                              Then the request is denied with BAD_REQUEST status code.
                              """)
  void addPayee_shouldReturnBadRequest_whenTheAccountNumberIsEmptyAndPersonalPayeeFalse()
      throws Exception {
    var content =
        """
                                  {
                                      "name": "newbillname123",
                                      "accountNumber": "",
                                      "nickname": "Bill",
                                      "address": {
                                          "streetLine1": "3267 BRAMPTON ST",
                                          "streetLine2": " ",
                                          "city": "DUBLIN",
                                          "stateOrProvince": "OH",
                                          "postalCode": "43017",
                                          "badAddressIndicator": false,
                                          "country": null
                                      },
                                      "personalPayee": false
                                  }
                                              """;

    CesPayeeResponse cesPayeeResponse =
        mockFromFile("ces_addPayee_success.json", CesPayeeResponse.class);

    when(cesClient.addPayee(any())).thenReturn(cesPayeeResponse);
    this.mockMvc
        .perform(
            MockMvcRequestBuilders.post("/billpay/addpayee")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BillPaySpringBootTest.BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .content(content)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(MockMvcResultMatchers.status().isBadRequest())
        .andExpect(MockMvcResultMatchers.jsonPath("$.status").value("BILL_PAY_ERROR"))
        .andExpect(
            MockMvcResultMatchers.jsonPath("$.statusCode").value("PERSONAL_PAYEE_FLAG_NOT_TRUE"))
        .andExpect(
            MockMvcResultMatchers.jsonPath("$.statusReason")
                .value("Custom payees with no account number must set personalPayee flag to true."))
        .andExpect(MockMvcResultMatchers.jsonPath("$.error.code").value("BAD_REQUEST"))
        .andExpect(
            MockMvcResultMatchers.jsonPath("$.error.message")
                .value("Custom payees with no account number must set personalPayee flag to true."))
        .andExpect(MockMvcResultMatchers.jsonPath("$.error.target").value("/billpay/addpayee"))
        .andExpect(
            MockMvcResultMatchers.jsonPath("$.error.innerError.code")
                .value("BILL_PAY_ERROR.PERSONAL_PAYEE_FLAG_NOT_TRUE"));
  }

  @Test
  @DisplayName(
      """
                            Given a a Ces response with HttpStatus.UNPROCESSABLE_ENTITY
                            When creating a payee
                            Then the request is successful.
                            """)
  void addPayee_throwsException_whenTheCesResponseIsUnmappableBody() throws Exception {
    var content =
        """
                                {
                                    "name": "newbillname123",
                                    "accountNumber": null,
                                    "nickname": "Bill",
                                    "address": {
                                        "streetLine1": "3267 BRAMPTON ST",
                                        "streetLine2": " ",
                                        "city": "DUBLIN",
                                        "stateOrProvince": "OH",
                                        "postalCode": "43017",
                                        "badAddressIndicator": false,
                                        "country": null
                                    },
                                    "personalPayee": true
                                }
                                            """;

    CesResponse cesResponse = new CesBaseResponse();
    cesResponse.setStatus("some status");
    cesResponse.setStatusCode("some status code");
    cesResponse.setStatusReason("some status reason");

    CesFeignException cesFeignException =
        new CesFeignException(HttpStatus.UNPROCESSABLE_ENTITY, "some reason");
    cesFeignException.setCesResponse(cesResponse);

    when(cesClient.addPayee(any())).thenThrow(cesFeignException);
    this.mockMvc
        .perform(
            MockMvcRequestBuilders.post("/billpay/addpayee")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BillPaySpringBootTest.BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .content(content)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(MockMvcResultMatchers.status().isUnprocessableEntity())
        .andExpect(MockMvcResultMatchers.jsonPath("$.status").value("some status"))
        .andExpect(MockMvcResultMatchers.jsonPath("$.statusCode").value("some status code"))
        .andExpect(MockMvcResultMatchers.jsonPath("$.statusReason").value("some status reason"));
  }

  @Test
  @DisplayName(
      """
                            Given a user tries to update a payee
                            When the request is correctly formed
                            Then the response is OK status code.
                            """)
  void editPayeeHappyPath() throws Exception {
    var content =
        """
                                {
                                    "id": "8DFD67D4-9612-CD6D-62AF-716D589FF7B4",
                                    "name": "newbillname123",
                                    "accountNumber": 1234,
                                    "nickname": "Bill",
                                    "address": {
                                        "streetLine1": "3267 BRAMPTON ST",
                                        "streetLine2": " ",
                                        "city": "DUBLIN",
                                        "stateOrProvince": "OH",
                                        "postalCode": "43017",
                                        "badAddressIndicator": false,
                                        "country": null
                                    },
                                    "personalPayee": true
                                }
                                            """;

    CesPayeeResponse cesPayeeResponse =
        mockFromFile("ces_updatePayee_success.json", CesPayeeResponse.class);

    when(cesClient.updatePayee(any())).thenReturn(cesPayeeResponse);
    this.mockMvc
        .perform(
            MockMvcRequestBuilders.put("/billpay/editpayee")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BillPaySpringBootTest.BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .content(content)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(MockMvcResultMatchers.status().isOk());
  }

  @Test
  @DisplayName(
      """
                                Given a user tries to update a payee
                                When Ces returns an unprocessible entity
                                Then the request is denied with UNPROCESSABLE_ENTITY status code.
                                """)
  void editPayee_returns_UNPROCESSABLE_ENTITY_when_Ces_returns_UNPROCESSABLE_ENTITY()
      throws Exception {
    var content =
        """
                                    {
                                        "id": "8DFD67D4-9612-CD6D-62AF-716D589FF7B4",
                                        "name": "newbillname123",
                                        "accountNumber": 1234,
                                        "nickname": "Bill",
                                        "address": {
                                            "streetLine1": "3267 BRAMPTON ST",
                                            "streetLine2": " ",
                                            "city": "DUBLIN",
                                            "stateOrProvince": "OH",
                                            "postalCode": "43017",
                                            "badAddressIndicator": false,
                                            "country": null
                                        },
                                        "personalPayee": true
                                    }
                                                """;

    CesResponse cesResponse = new CesBaseResponse();
    cesResponse.setStatus("some status");
    cesResponse.setStatusCode("some status code");
    cesResponse.setStatusReason("some status reason");

    CesFeignException cesFeignException =
        new CesFeignException(HttpStatus.UNPROCESSABLE_ENTITY, "some reason");
    cesFeignException.setCesResponse(cesResponse);
    when(cesClient.updatePayee(any())).thenThrow(cesFeignException);
    this.mockMvc
        .perform(
            MockMvcRequestBuilders.put("/billpay/editpayee")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BillPaySpringBootTest.BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .content(content)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(MockMvcResultMatchers.status().isUnprocessableEntity())
        .andExpect(MockMvcResultMatchers.jsonPath("$.status").value("some status"))
        .andExpect(MockMvcResultMatchers.jsonPath("$.statusCode").value("some status code"))
        .andExpect(MockMvcResultMatchers.jsonPath("$.statusReason").value("some status reason"));
  }

  // TODO
  @Test
  @DisplayName(
      """
                                  Given a user tries to update a payee with an invalid address
                                  When Ces returns an exception
                                  Then an ADDRESS_NOT_FOUND VALIDATION_ERROR is returned to the client.
                                  """)
  void editPayee_passCesStatusCode_invalidAddress() throws Exception {
    var content = "{}";

    CesPayeeResponse cesPayeeResponse =
        mockFromFile("ces_addPayee_invalid_address.json", CesPayeeResponse.class);

    when(cesClient.updatePayee(any()))
        .thenThrow(
            new CesFeignException(
                HttpStatus.UNPROCESSABLE_ENTITY, cesPayeeResponse.getStatusReason()));
    this.mockMvc
        .perform(
            MockMvcRequestBuilders.put("/billpay/editpayee")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BillPaySpringBootTest.BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .content(content)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(MockMvcResultMatchers.status().isUnprocessableEntity())
        .andExpect(MockMvcResultMatchers.jsonPath("$.status").value("UNKNOWN_ERROR"))
        .andExpect(
            MockMvcResultMatchers.jsonPath("$.statusCode").value("UNPROCESSABLE_CES_RESPONSE"));
  }

  @DisplayName(
      """
                                      Given a user tries to update a payee
                                      When Ces returns a BAD_GATEWAY exception
                                      Then a BAD_GATEWAY is returned to the client.
                                      """)
  @Test
  public void editPayeeSetsHttpStatusToCesHttpStatus() throws Exception {
    var content = "{}";

    CesPayeeResponse cesPayeeResponse =
        mockFromFile("ces_bad_gateway.json", CesPayeeResponse.class);

    when(cesClient.updatePayee(any()))
        .thenThrow(
            new CesFeignException(
                HttpStatus.BAD_GATEWAY, cesPayeeResponse.getStatusReason(), cesPayeeResponse));
    this.mockMvc
        .perform(
            MockMvcRequestBuilders.put("/billpay/editpayee")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BillPaySpringBootTest.BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .content(content)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(MockMvcResultMatchers.status().isBadGateway())
        .andExpect(MockMvcResultMatchers.jsonPath("$.status").value("BAD_GATEWAY"));
  }

  @DisplayName(
      """
                                          Given a user tries to update a payee
                                          When Ces returns an ADDRESS_CHANGED exception
                                          Then a ADDRESS_CHANGED is returned to the client.
                                          """)
  @Test
  public void editPayeeReturnsADDRESS_CHANGED() throws Exception {
    var content = "{}";

    CesPayeeResponse cesPayeeResponse =
        mockFromFile(
            "service/billpay/add_payee_response_address_fixed.json", CesPayeeResponse.class);

    when(cesClient.updatePayee(any()))
        .thenThrow(
            new CesFeignException(
                HttpStatus.UNPROCESSABLE_ENTITY,
                cesPayeeResponse.getStatusReason(),
                cesPayeeResponse));
    this.mockMvc
        .perform(
            MockMvcRequestBuilders.put("/billpay/editpayee")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BillPaySpringBootTest.BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .content(content)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(MockMvcResultMatchers.status().isUnprocessableEntity())
        .andExpect(MockMvcResultMatchers.jsonPath("$.status").value("VALIDATION_ERROR"))
        .andExpect(MockMvcResultMatchers.jsonPath("$.statusCode").value("ADDRESS_CHANGED"));
  }
}
