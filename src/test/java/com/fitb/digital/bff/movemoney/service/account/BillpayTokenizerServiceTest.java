/* Copyright 2022 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.service.account;

import static com.fitb.digital.bff.movemoney.utils.TestUtils.mockFromFile;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.fitb.digital.bff.movemoney.client.CesClient;
import com.fitb.digital.bff.movemoney.model.bff.billpay.BffPayee;
import com.fitb.digital.bff.movemoney.model.bff.billpay.requests.BffAddPayeeRequest;
import com.fitb.digital.bff.movemoney.model.client.billpay.requests.CesPayeeRequest;
import com.fitb.digital.bff.movemoney.model.client.billpay.responses.CesPayeeAccountResponse;
import com.fitb.digital.bff.movemoney.model.client.billpay.responses.CesPayeeResponse;
import com.fitb.digital.bff.movemoney.service.BillpayService;
import com.fitb.digital.lib.tokenization.tokenizer.Tokenizer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class BillpayTokenizerServiceTest {
  @Mock private CesClient cesClient;
  @Mock Tokenizer simpleTokenizer;

  @Test
  void verifyAddPayeeUsesTokenization() {
    var billpayService = new BillpayService(simpleTokenizer, cesClient);

    when(cesClient.addPayee(any()))
        .thenReturn(mockFromFile("service/add_payee_response.json", CesPayeeResponse.class));
    BffAddPayeeRequest addPayeeRequest = new BffAddPayeeRequest();
    addPayeeRequest.setName("Cincinnati Bell");
    addPayeeRequest.setAccountNumber("************");

    when(simpleTokenizer.tokenize(anyString(), any(CesPayeeRequest.class)))
        .thenReturn(new CesPayeeRequest());

    billpayService.createPayee(addPayeeRequest);

    verify(simpleTokenizer).tokenize(anyString(), any(CesPayeeRequest.class));
  }

  @Test
  void verifyGetPayeeUsesTokenization() {
    var billpayService = new BillpayService(simpleTokenizer, cesClient);

    when(cesClient.getTokenizedPayeeAccount(anyString()))
        .thenReturn(mockFromFile("service/add_payee_response.json", CesPayeeAccountResponse.class));
    BffAddPayeeRequest addPayeeRequest = new BffAddPayeeRequest();
    addPayeeRequest.setName("Cincinnati Bell");
    addPayeeRequest.setAccountNumber("************");

    var detokenizedResponse = new CesPayeeAccountResponse();
    detokenizedResponse.setAccountNumber("12345");
    when(simpleTokenizer.detokenize(any(CesPayeeAccountResponse.class)))
        .thenReturn(detokenizedResponse);

    billpayService.getTokenizedPayeeAccount("this-is-my-id");

    verify(simpleTokenizer).detokenize(any(CesPayeeAccountResponse.class));
  }

  @Test
  void verifyUpdatePayeeUsesTokenization() {
    var billpayService = new BillpayService(simpleTokenizer, cesClient);

    when(cesClient.updatePayee(any()))
        .thenReturn(mockFromFile("service/add_payee_response.json", CesPayeeResponse.class));
    BffAddPayeeRequest addPayeeRequest = new BffAddPayeeRequest();
    addPayeeRequest.setName("Cincinnati Bell");
    addPayeeRequest.setAccountNumber("************");

    when(simpleTokenizer.tokenize(anyString(), any(CesPayeeRequest.class)))
        .thenReturn(new CesPayeeRequest());

    var request = new BffPayee();
    billpayService.updatePayee(request);
    verify(simpleTokenizer).tokenize(any(), any(CesPayeeRequest.class));
  }
}
