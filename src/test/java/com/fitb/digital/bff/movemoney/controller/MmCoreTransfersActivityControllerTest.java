/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.controller;

import static com.fitb.digital.bff.movemoney.featureflags.FeatureFlagNames.*;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.fitb.digital.bff.movemoney.featureflags.ActivityFeatureFlagChecker;
import com.fitb.digital.bff.movemoney.model.bff.activity.RecurringActivityFrequency;
import com.fitb.digital.bff.movemoney.model.bff.activity.requests.BffActivityRequest;
import com.fitb.digital.bff.movemoney.model.bff.activity.responses.BffAddActivityResponse;
import com.fitb.digital.bff.movemoney.service.activity.ActivityServiceV1;
import com.fitb.digital.bff.movemoney.service.coretransfers.CoreTransfersService;
import com.fitb.digital.lib.featureflag.service.FeatureFlagService;
import java.time.LocalDate;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;

@ExtendWith(MockitoExtension.class)
class MmCoreTransfersActivityControllerTest {
  @Mock private CoreTransfersService coreTransfersService;

  @Mock private ActivityServiceV1 activityServiceV1;

  @Mock private FeatureFlagService featureFlagService;

  @Spy @InjectMocks private MmActivityController controller;

  @Mock private ActivityFeatureFlagChecker activityFeatureFlagChecker;

  @Test
  void addActivityUsesCesProxyWhenCoreTransfersEnabledIsTrue() {
    when(coreTransfersService.addActivityTds(any())).thenReturn(new BffAddActivityResponse());

    when(featureFlagService.isFeatureEnabled(ORCHESTRATOR_ENABLED)).thenReturn(false);
    when(featureFlagService.isFeatureEnabled(CORE_TRANSFERS_TDS_ENABLED)).thenReturn(true);
    BffActivityRequest request = new BffActivityRequest();
    request.setActivityType("INTERNAL_TRANSFER");
    request.setFrequency(RecurringActivityFrequency.ONE_TIME);
    request.setDueDate(LocalDate.now());
    request.setFromAccountType("DDA");
    request.setToAccountType("SAV");

    var result = controller.addActivity(request);
    assertEquals(HttpStatus.OK, result.getStatusCode());
  }

  @Test
  void addActivityUsesCesProxyWhenCoreTransfersEnabledIsFalse() {
    when(activityServiceV1.addActivity(any())).thenReturn(new BffAddActivityResponse());

    when(featureFlagService.isFeatureEnabled(ORCHESTRATOR_ENABLED)).thenReturn(false);
    when(featureFlagService.isFeatureEnabled(CORE_TRANSFERS_TDS_ENABLED)).thenReturn(false);
    BffActivityRequest request = new BffActivityRequest();
    request.setActivityType("INTERNAL_TRANSFER");
    request.setFrequency(RecurringActivityFrequency.ONE_TIME);
    request.setDueDate(LocalDate.now());
    request.setFromAccountType("DDA");
    request.setToAccountType("SAV");

    var result = controller.addActivity(request);
    assertEquals(HttpStatus.OK, result.getStatusCode());
  }
}
