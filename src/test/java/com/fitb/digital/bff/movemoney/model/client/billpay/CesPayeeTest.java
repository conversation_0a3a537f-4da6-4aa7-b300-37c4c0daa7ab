/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.client.billpay;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.fitb.digital.bff.movemoney.model.CesPayeeType;
import com.fitb.digital.bff.movemoney.model.PayeeAddress;
import com.fitb.digital.bff.movemoney.model.bff.billpay.requests.BffAddPayeeRequest;
import org.junit.jupiter.api.Test;

public class CesPayeeTest {

  public static final String NAME = "Name";
  public static final String NICKNAME = "Nickname";
  public static final String ACCOUNT_NUMBER = "account-number";
  public static final String POSTAL_CODE = "45000";
  public static final String STREET_LINE_1 = "1 Line";
  public static final String CITY = "Somewhere";
  public static final String STATE_OR_PROVINCE = "OH";

  @Test
  public void mapTwoFactorBff() {
    BffAddPayeeRequest bff = new BffAddPayeeRequest();
    bff.setName(NAME);
    bff.setNickname(NICKNAME);
    bff.setAccountNumber(ACCOUNT_NUMBER);
    CesPayee ces = CesPayee.from(bff);
    assertEquals(CesPayeeType.TWOFACTOR, ces.getCesPayeeType());
    assertEquals(NAME, ces.getName());
    assertEquals(NICKNAME, ces.getNickname());
    assertEquals(ACCOUNT_NUMBER, ces.getAccountNumber());
  }

  @Test
  public void mapThreeFactorBff() {
    BffAddPayeeRequest bff = new BffAddPayeeRequest();
    bff.setName(NAME);
    bff.setNickname(NICKNAME);
    bff.setAccountNumber(ACCOUNT_NUMBER);
    bff.setAddress(new PayeeAddress());
    bff.getAddress().setPostalCode(POSTAL_CODE);
    CesPayee ces = CesPayee.from(bff);
    assertEquals(CesPayeeType.THREEFACTOR, ces.getCesPayeeType());
    assertEquals(NAME, ces.getName());
    assertEquals(NICKNAME, ces.getNickname());
    assertEquals(ACCOUNT_NUMBER, ces.getAccountNumber());
    assertEquals(POSTAL_CODE, ces.getCesAddress().getPostalCode());
  }

  @Test
  public void mapSixFactorBff() {
    BffAddPayeeRequest bff = new BffAddPayeeRequest();
    bff.setName(NAME);
    bff.setNickname(NICKNAME);
    bff.setAccountNumber(ACCOUNT_NUMBER);
    bff.setAddress(new PayeeAddress());
    bff.getAddress().setStreetLine1(STREET_LINE_1);
    bff.getAddress().setCity(CITY);
    bff.getAddress().setStateOrProvince(STATE_OR_PROVINCE);
    bff.getAddress().setPostalCode(POSTAL_CODE);
    CesPayee ces = CesPayee.from(bff);
    assertEquals(CesPayeeType.SIXFACTOR, ces.getCesPayeeType());
    assertEquals(NAME, ces.getName());
    assertEquals(NICKNAME, ces.getNickname());
    assertEquals(ACCOUNT_NUMBER, ces.getAccountNumber());
    assertEquals(STREET_LINE_1, ces.getCesAddress().getStreetLine1());
    assertEquals(CITY, ces.getCesAddress().getCity());
    assertEquals(STATE_OR_PROVINCE, ces.getCesAddress().getStateOrProvince());
    assertEquals(POSTAL_CODE, ces.getCesAddress().getPostalCode());
  }

  @Test
  public void mapPersonalPayeeBff() {
    BffAddPayeeRequest bff = new BffAddPayeeRequest();
    bff.setName(NAME);
    bff.setNickname(NICKNAME);
    bff.setAccountNumber(ACCOUNT_NUMBER);
    bff.setAddress(new PayeeAddress());
    bff.getAddress().setStreetLine1(STREET_LINE_1);
    bff.getAddress().setCity(CITY);
    bff.getAddress().setStateOrProvince(STATE_OR_PROVINCE);
    bff.getAddress().setPostalCode(POSTAL_CODE);
    bff.setPersonalPayee(true);
    CesPayee ces = CesPayee.from(bff);
    assertEquals(CesPayeeType.PERSONAL_PAYEE, ces.getCesPayeeType());
    assertEquals(NAME, ces.getName());
    assertEquals(NICKNAME, ces.getNickname());
    assertEquals(ACCOUNT_NUMBER, ces.getAccountNumber());
    assertEquals(STREET_LINE_1, ces.getCesAddress().getStreetLine1());
    assertEquals(CITY, ces.getCesAddress().getCity());
    assertEquals(STATE_OR_PROVINCE, ces.getCesAddress().getStateOrProvince());
    assertEquals(POSTAL_CODE, ces.getCesAddress().getPostalCode());
  }
}
