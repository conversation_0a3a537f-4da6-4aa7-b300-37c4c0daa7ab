/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.controller;

import static com.fitb.digital.bff.movemoney.featureflags.FeatureFlagNames.ORCHESTRATOR_ENABLED;
import static com.fitb.digital.bff.movemoney.utils.TestUtils.mockFromFile;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

import com.fitb.digital.bff.movemoney.client.xferandpayorc.model.XferAndPayActivityRequest;
import com.fitb.digital.bff.movemoney.client.xferandpayorc.model.XferAndPayRecurringFrequencyType;
import com.fitb.digital.bff.movemoney.constants.ExceptionStatus;
import com.fitb.digital.bff.movemoney.exceptions.BffException;
import com.fitb.digital.bff.movemoney.featureflags.ActivityFeatureFlagChecker;
import com.fitb.digital.bff.movemoney.model.ActivityType;
import com.fitb.digital.bff.movemoney.model.bff.BffResponse;
import com.fitb.digital.bff.movemoney.model.bff.BffSimpleResponse;
import com.fitb.digital.bff.movemoney.model.bff.activity.BffActivity;
import com.fitb.digital.bff.movemoney.model.bff.activity.BffLimit;
import com.fitb.digital.bff.movemoney.model.bff.activity.requests.BffActivityRequest;
import com.fitb.digital.bff.movemoney.model.bff.activity.responses.BffAddActivityResponse;
import com.fitb.digital.bff.movemoney.model.bff.activity.responses.BffGetTransferLimitsResponse;
import com.fitb.digital.bff.movemoney.model.client.activity.responses.ClientActivity;
import com.fitb.digital.bff.movemoney.service.activity.ActivityService;
import com.fitb.digital.bff.movemoney.service.activity.ActivityServiceV1;
import com.fitb.digital.lib.featureflag.service.FeatureFlagService;
import java.math.BigDecimal;
import java.time.*;
import java.util.Arrays;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.modelmapper.ModelMapper;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

@ExtendWith(MockitoExtension.class)
class MmActivityControllerTest {
  @Mock private ActivityServiceV1 activityServiceV1;

  @Mock private ActivityService activityService;

  @Mock private FeatureFlagService featureFlagService;

  @Mock private ActivityFeatureFlagChecker activityFeatureFlagChecker;

  @Spy @InjectMocks private MmActivityController controller;

  @Test
  void addActivityUsesCesProxyWhenUseOrchestratorFlagIsFalse() {
    when(activityServiceV1.addActivity(any())).thenReturn(new BffAddActivityResponse());

    when(featureFlagService.isFeatureEnabled(ORCHESTRATOR_ENABLED)).thenReturn(false);

    var result = controller.addActivity(new BffActivityRequest());
    assertEquals(HttpStatus.OK, result.getStatusCode());
  }

  @Test
  void addActivityUsesOrchestratorWhenFlagIsTrue() {
    when(activityService.addActivity(any())).thenReturn(new BffAddActivityResponse());

    when(featureFlagService.isFeatureEnabled(ORCHESTRATOR_ENABLED)).thenReturn(true);

    var result = controller.addActivity(new BffActivityRequest());
    assertEquals(HttpStatus.OK, result.getStatusCode());
  }

  @Test
  void postActivityWithNoDueDateUsesEasternTimeZoneActivityTypeBillPay() {

    Instant instant = Instant.parse("2022-09-07T02:00:00Z");
    ZonedDateTime mockZDt = instant.atZone(ZoneId.of("America/New_York"));
    when(controller.getCurrentEasternZonedDateTime()).thenReturn(mockZDt);

    ArgumentCaptor<BffActivityRequest> argumentCaptor =
        ArgumentCaptor.forClass(BffActivityRequest.class);

    BffActivityRequest bffActivityRequest = new BffActivityRequest();
    bffActivityRequest.setActivityType(ActivityType.BILLPAY);
    controller.addActivity(bffActivityRequest);

    verify(activityServiceV1).addActivity(argumentCaptor.capture());

    var arg = argumentCaptor.getValue();

    assertEquals(LocalDate.of(2022, 9, 6), arg.getDueDate());
  }

  @Test
  void postActivityScheduleImmediatelyUsesEasternTimeZoneActivityTypeBillPay() {
    Instant instant = Instant.parse("2022-09-07T02:00:00Z");
    ZonedDateTime mockZDt = instant.atZone(ZoneId.of("America/New_York"));
    when(controller.getCurrentEasternZonedDateTime()).thenReturn(mockZDt);

    ArgumentCaptor<BffActivityRequest> argumentCaptor =
        ArgumentCaptor.forClass(BffActivityRequest.class);

    var request = new BffActivityRequest();
    request.setScheduleImmediately(true);
    request.setDueDate(LocalDate.of(2022, 9, 7));
    request.setActivityType(ActivityType.BILLPAY);
    controller.addActivity(request);

    verify(activityServiceV1).addActivity(argumentCaptor.capture());

    var arg = argumentCaptor.getValue();

    assertEquals(LocalDate.of(2022, 9, 6), arg.getDueDate());
  }

  @Test
  void postActivityWithScheduleImmediatelyFlagAndDateOneDayInFuturePassesActivityTypeBillPay() {
    LocalDate dueDate = controller.getCurrentEasternZonedDateTime().toLocalDate().plusDays(1);
    BffActivityRequest bffActivityRequest = new BffActivityRequest();
    bffActivityRequest.setScheduleImmediately(true);
    bffActivityRequest.setDueDate(dueDate);
    ArgumentCaptor<BffActivityRequest> argumentCaptor =
        ArgumentCaptor.forClass(BffActivityRequest.class);
    bffActivityRequest.setActivityType(ActivityType.BILLPAY);
    var response = controller.addActivity(bffActivityRequest);

    verify(activityServiceV1).addActivity(argumentCaptor.capture());

    var arg = argumentCaptor.getValue();

    assertEquals(HttpStatus.OK, response.getStatusCode());
    assertEquals(controller.getCurrentEasternZonedDateTime().toLocalDate(), arg.getDueDate());
  }

  @Test
  void postActivityWithScheduleImmediatelyFlagAndDateOneDayInPastPassesActivityTypeBillPay() {
    LocalDate dueDate = controller.getCurrentEasternZonedDateTime().toLocalDate().minusDays(1);
    BffActivityRequest bffActivityRequest = new BffActivityRequest();
    bffActivityRequest.setScheduleImmediately(true);
    bffActivityRequest.setDueDate(dueDate);
    bffActivityRequest.setActivityType(ActivityType.BILLPAY);

    ArgumentCaptor<BffActivityRequest> argumentCaptor =
        ArgumentCaptor.forClass(BffActivityRequest.class);

    var response = controller.addActivity(bffActivityRequest);

    verify(activityServiceV1).addActivity(argumentCaptor.capture());

    var arg = argumentCaptor.getValue();

    assertEquals(HttpStatus.OK, response.getStatusCode());
    assertEquals(controller.getCurrentEasternZonedDateTime().toLocalDate(), arg.getDueDate());

    assertEquals(HttpStatus.OK, response.getStatusCode());
  }

  @Test
  void getTransferLimitsReturnsTransferLimits() throws ExecutionException, InterruptedException {
    var bffTransferLimits = new BffGetTransferLimitsResponse();
    bffTransferLimits.setStatus("SUCCESS");
    var limit = new BffLimit();
    limit.setHostAccountId("5740834B-C621-D166-5E4A-96FC49CFBF6B");
    limit.setDailyLimit(new BigDecimal(200.00));
    limit.setRemainingToday(new BigDecimal(200.00));
    limit.setRemainingThisMonth(new BigDecimal(200.00));
    bffTransferLimits.setLimits(Arrays.asList(new BffLimit[] {limit}));
    when(activityServiceV1.getTransferLimits(
            "5740834B-C621-D166-5E4A-96FC49CFBF6B", "23E13F71-9BD0-BD5B-3AC9-4902688DAF6F"))
        .thenReturn(bffTransferLimits);
    var result =
        controller.getTransferLimits(
            "5740834B-C621-D166-5E4A-96FC49CFBF6B", "23E13F71-9BD0-BD5B-3AC9-4902688DAF6F");
    assertNotNull(result);
    assertNotNull(result.getLimits());
  }

  @Test
  void cancelActivityV1ReturnsOk() {
    when(activityServiceV1.cancelActivity(any(String.class)))
        .thenReturn(new BffSimpleResponse().status(BffResponse.SUCCESS));
    var result = controller.cancelActivity("12345", ActivityType.INTERNAL_TRANSFER);

    assertEquals(BffResponse.SUCCESS, result.getStatus());
  }

  @Test
  void cancelActivityReturnsOk() {
    when(activityService.cancelActivity(any(String.class), any()))
        .thenReturn(new BffSimpleResponse().status(BffResponse.SUCCESS));
    when(featureFlagService.isFeatureEnabled(ORCHESTRATOR_ENABLED)).thenReturn(true);
    var result = controller.cancelActivity("12345", ActivityType.INTERNAL_TRANSFER);

    assertEquals(BffResponse.SUCCESS, result.getStatus());
  }

  @Test
  void addActivityReturnsOkIfActivityExistsActivityTypeBillPay() {
    var addActivity = new BffActivityRequest();
    var response = new BffAddActivityResponse();
    response.setActivity(new BffActivity());
    addActivity.setActivityType(ActivityType.BILLPAY);
    when(activityServiceV1.addActivity(addActivity)).thenReturn(response);

    ResponseEntity<BffAddActivityResponse> result = controller.addActivity(addActivity);
    assertEquals(HttpStatus.OK, result.getStatusCode());
  }

  @Test
  void addActivityReturnsOkIfActivityListIsEmptyActivityTypeBillPay() {
    var addActivity = new BffActivityRequest();
    var response = new BffAddActivityResponse();
    addActivity.setActivityType(ActivityType.BILLPAY);
    when(activityServiceV1.addActivity(addActivity)).thenReturn(response);

    ResponseEntity<BffAddActivityResponse> result = controller.addActivity(addActivity);
    assertEquals(HttpStatus.OK, result.getStatusCode());
  }

  @Test
  void editActivityReturnsOkActivityTypeBillPay() {
    BffActivityRequest editActivity = mockBffActivity();
    editActivity.setActivityType(ActivityType.BILLPAY);
    when(activityServiceV1.editActivity(editActivity)).thenReturn(new BffAddActivityResponse());

    ResponseEntity<BffAddActivityResponse> result = controller.editActivity(editActivity);
    assertEquals(HttpStatus.OK, result.getStatusCode());
  }

  @Test
  void editActivityMapsActivityResponseActivityTypeBillPay() {
    BffActivityRequest editActivity = mockBffActivity();
    var response = new BffAddActivityResponse();
    var activity = new BffActivity();
    activity.setEditable(true);
    activity.setCancelable(true);
    activity.setDisplayId("testId");
    activity.setId("id");
    response.setActivity(activity);
    editActivity.setActivityType(ActivityType.BILLPAY);

    when(activityServiceV1.editActivity(editActivity)).thenReturn(response);

    ResponseEntity<BffAddActivityResponse> result = controller.editActivity(editActivity);
    assertEquals(HttpStatus.OK, result.getStatusCode());

    assertTrue(Objects.requireNonNull(result.getBody()).getActivity().isEditable());
    assertTrue(Objects.requireNonNull(result.getBody()).getActivity().isCancelable());
    assertEquals("testId", Objects.requireNonNull(result.getBody()).getActivity().getDisplayId());
    assertEquals("id", Objects.requireNonNull(result.getBody()).getActivity().getId());
  }

  @Test
  void editActivityWithScheduleImmediatelyFlagAndDateOneDayInFutureActivityTypeBillPayPasses() {
    LocalDate dueDate = controller.getCurrentEasternZonedDateTime().toLocalDate().plusDays(1);
    BffActivityRequest bffActivityRequest = new BffActivityRequest();
    bffActivityRequest.setScheduleImmediately(true);
    bffActivityRequest.setDueDate(dueDate);
    bffActivityRequest.setActivityType(ActivityType.BILLPAY);

    ArgumentCaptor<BffActivityRequest> argumentCaptor =
        ArgumentCaptor.forClass(BffActivityRequest.class);

    var response = controller.editActivity(bffActivityRequest);

    verify(activityServiceV1).editActivity(argumentCaptor.capture());

    var arg = argumentCaptor.getValue();

    assertEquals(HttpStatus.OK, response.getStatusCode());
    assertEquals(controller.getCurrentEasternZonedDateTime().toLocalDate(), arg.getDueDate());
  }

  @Test
  void editActivityWithScheduleImmediatelyFlagAndDateOneDayInPastActivityTypeBillPayPasses() {
    LocalDate dueDate = controller.getCurrentEasternZonedDateTime().toLocalDate().minusDays(1);
    BffActivityRequest bffActivityRequest = new BffActivityRequest();
    bffActivityRequest.setScheduleImmediately(true);
    bffActivityRequest.setDueDate(dueDate);
    bffActivityRequest.setActivityType(ActivityType.BILLPAY);

    ArgumentCaptor<BffActivityRequest> argumentCaptor =
        ArgumentCaptor.forClass(BffActivityRequest.class);

    var response = controller.editActivity(bffActivityRequest);

    verify(activityServiceV1).editActivity(argumentCaptor.capture());

    var arg = argumentCaptor.getValue();

    assertEquals(HttpStatus.OK, response.getStatusCode());
    assertEquals(controller.getCurrentEasternZonedDateTime().toLocalDate(), arg.getDueDate());

    assertEquals(HttpStatus.OK, response.getStatusCode());
  }

  @Test
  @DisplayName(
      """
          Given a user tries to make a payment
          When the move money activity feature is not enabled
          Then the request is denied.
          """)
  void addActivityDenyAndReturn503WhenFeatureFlagIsNotEnabled() {
    BffActivityRequest bffActivityRequest = new BffActivityRequest();
    bffActivityRequest.setActivityType(ActivityType.BILLPAY);

    final var expected = BffException.serviceUnavailable(ExceptionStatus.FEATURE_OLB_REDIRECT);
    doThrow(expected)
        .when(activityFeatureFlagChecker)
        .checkFeatureEnabled(bffActivityRequest.getActivityType());

    var actual = assertThrows(BffException.class, () -> controller.addActivity(bffActivityRequest));
    assertEquals(expected, actual);
  }

  @Test
  @DisplayName(
      """
                  Given a user tries to edit a payment
                  When the move money activity feature is not enabled
                  Then the request is denied.
                  """)
  void editActivityDenyAndReturn503WhenFeatureFlagStatusIsOLBRedirect() {
    BffActivityRequest bffActivityRequest = new BffActivityRequest();
    bffActivityRequest.setActivityType(ActivityType.BILLPAY);

    final var expected = BffException.serviceUnavailable(ExceptionStatus.FEATURE_OLB_REDIRECT);
    doThrow(expected)
        .when(activityFeatureFlagChecker)
        .checkFeatureEnabled(bffActivityRequest.getActivityType());

    var actual = assertThrows(BffException.class, () -> controller.addActivity(bffActivityRequest));
    assertEquals(expected, actual);
  }

  private BffActivityRequest mockBffActivity() {
    ClientActivity clientActivity = mockFromFile("activity_request.json", ClientActivity.class);
    BffActivityRequest addActivity = new BffActivityRequest();
    addActivity.setRequestGuid("xyzzy");
    ModelMapper mapper = new ModelMapper();
    mapper.map(clientActivity, addActivity);
    return addActivity;
  }

  @Test
  void testFrequencyGetterAndSetter() {
    XferAndPayActivityRequest request = new XferAndPayActivityRequest();
    assertEquals(XferAndPayRecurringFrequencyType.ONE_TIME, request.getFrequency());

    request.setFrequency(XferAndPayRecurringFrequencyType.ONCE_A_MONTH);
    assertEquals(XferAndPayRecurringFrequencyType.ONCE_A_MONTH, request.getFrequency());

    request.setFrequency(null);
    assertEquals(XferAndPayRecurringFrequencyType.ONE_TIME, request.getFrequency());
  }
}
