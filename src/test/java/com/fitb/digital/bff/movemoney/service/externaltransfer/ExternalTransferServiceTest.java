/* Copyright 2022 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.service.externaltransfer;

import static com.fitb.digital.bff.movemoney.utils.TestUtils.mockFromFile;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.fitb.digital.bff.movemoney.client.CesClient;
import com.fitb.digital.bff.movemoney.constants.ExceptionStatus;
import com.fitb.digital.bff.movemoney.exceptions.BffException;
import com.fitb.digital.bff.movemoney.exceptions.CesFeignException;
import com.fitb.digital.bff.movemoney.model.bff.external.transfer.ExternalAccountType;
import com.fitb.digital.bff.movemoney.model.bff.external.transfer.requests.BffAddAccountRequest;
import com.fitb.digital.bff.movemoney.model.bff.external.transfer.requests.BffUpdateExtAccountNicknameRequest;
import com.fitb.digital.bff.movemoney.model.bff.external.transfer.requests.BffVerifyTrialDepositsRequest;
import com.fitb.digital.bff.movemoney.model.bff.external.transfer.responses.*;
import com.fitb.digital.bff.movemoney.model.client.CesBaseResponse;
import com.fitb.digital.bff.movemoney.model.client.CesResponse;
import com.fitb.digital.bff.movemoney.model.client.external.transfer.requests.CesAddExternalAccountByInstantRequest;
import com.fitb.digital.bff.movemoney.model.client.external.transfer.requests.CesAddExternalAccountRealTimeRequest;
import com.fitb.digital.bff.movemoney.model.client.external.transfer.requests.CesVerifyTrialDepositsRequest;
import com.fitb.digital.bff.movemoney.model.client.external.transfer.responses.*;
import com.fitb.digital.bff.movemoney.service.external.ExternalAccountManagementAsync;
import com.fitb.digital.bff.movemoney.utils.TestUtils;
import com.fitb.digital.lib.tokenization.tokenizer.Tokenizer;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentMatcher;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;

@ExtendWith(MockitoExtension.class)
class ExternalTransferServiceTest {
  @InjectMocks private ExternalTransferService transferService;
  @Mock private CesClient cesClient;
  @Mock private ExternalAccountManagementAsync acctManagementAsync;
  @Mock private Tokenizer tokenizer;

  @Test
  void brokerageListIsReadable() {
    when(cesClient.getBrokerages())
        .thenReturn(
            mockFromFile(
                "service/externaltransfer/brokerage_list.json",
                AuthorizedBrokerageListResponse.class));
    final BffBrokerageListResponse response = transferService.getBrokerages();
    assertNotNull(response);
    assertEquals(CesResponse.SUCCESS, response.getStatus());
  }

  @Test
  void updateExternalAccountNickname_mapsCesResponseOnSuccess() {
    var cesResponse = new CesBaseResponse();
    cesResponse.setStatus("SUCCESS");
    when(cesClient.updateExternalTranfserAccountNickname(any())).thenReturn(cesResponse);

    var result =
        transferService.updateExternalAccountNickname(new BffUpdateExtAccountNicknameRequest());

    assertEquals("SUCCESS", result.getStatus());
  }

  @Test
  void
      getAccountVerificationInfo_throwsFeignClientException_whenGetExternalTransferAccountsFails() {
    final var externalTransferAccountsFork = new CompletableFuture<List<BffExternalAccount>>();
    final var expected = new CesFeignException(HttpStatus.SERVICE_UNAVAILABLE, "some reason");
    externalTransferAccountsFork.completeExceptionally(expected);

    when(acctManagementAsync.getExternalTransferAccounts())
        .thenReturn(externalTransferAccountsFork);

    final CesFeignException actual =
        assertThrows(
            CesFeignException.class, () -> transferService.getAccountVerificationInfo("any"));
    assertEquals(expected.getStatusCode(), actual.getStatusCode());
    assertEquals(expected.getMessage(), actual.getMessage());
  }

  @Test
  void getAccountVerificationInfo_throwsNotFoundException_whenNoMatchingAccountId() {
    final var cesUserAccounts = getCesUserAccounts(false);
    final var externalTransferAccountsFork = new CompletableFuture<List<BffExternalAccount>>();
    externalTransferAccountsFork.complete(cesUserAccounts);

    when(acctManagementAsync.getExternalTransferAccounts())
        .thenReturn(externalTransferAccountsFork);

    final BffException actual =
        assertThrows(BffException.class, () -> transferService.getAccountVerificationInfo("any"));
    assertEquals(HttpStatus.NOT_FOUND, actual.getHttpStatus());
    assertEquals(ExceptionStatus.UNABLE_TO_FIND_ACCOUNT.getStatus(), actual.getStatus());
    assertEquals(ExceptionStatus.UNABLE_TO_FIND_ACCOUNT.getCode(), actual.getStatusCode());
    assertEquals(ExceptionStatus.UNABLE_TO_FIND_ACCOUNT.getReason(), actual.getStatusReason());
  }

  @Test
  void getAccountVerificationInfo_throwsServiceNotAvailable() {
    final var externalTransferAccountsFork = new CompletableFuture<List<BffExternalAccount>>();
    externalTransferAccountsFork.completeExceptionally(new RuntimeException("Some reason"));

    when(acctManagementAsync.getExternalTransferAccounts())
        .thenReturn(externalTransferAccountsFork);

    final BffException actual =
        assertThrows(BffException.class, () -> transferService.getAccountVerificationInfo("any"));
    assertEquals(HttpStatus.SERVICE_UNAVAILABLE, actual.getHttpStatus());
    assertEquals(ExceptionStatus.SERVICE_UNAVAILABLE.getStatus(), actual.getStatus());
    assertEquals(ExceptionStatus.SERVICE_UNAVAILABLE.getCode(), actual.getStatusCode());
    assertEquals(ExceptionStatus.SERVICE_UNAVAILABLE.getReason(), actual.getStatusReason());
  }

  @Test
  void getAccountVerificationInfo_throwsServiceNotAvailable_ThreadInterrupted() {
    final var externalTransferAccountsFork = new CompletableFuture<List<BffExternalAccount>>();
    externalTransferAccountsFork.completeExceptionally(new InterruptedException("Some reason"));

    when(acctManagementAsync.getExternalTransferAccounts())
        .thenReturn(externalTransferAccountsFork);

    final BffException actual =
        assertThrows(BffException.class, () -> transferService.getAccountVerificationInfo("any"));
    assertEquals(HttpStatus.SERVICE_UNAVAILABLE, actual.getHttpStatus());
    assertEquals(ExceptionStatus.SERVICE_UNAVAILABLE.getStatus(), actual.getStatus());
    assertEquals(ExceptionStatus.SERVICE_UNAVAILABLE.getCode(), actual.getStatusCode());
    assertEquals(ExceptionStatus.SERVICE_UNAVAILABLE.getReason(), actual.getStatusReason());
  }

  @Test
  void getAccountVerificationInfo_success_whenMatchRoutingNumber() {
    var cesUserAccounts = getCesUserAccounts(true);
    var externalTransferAccountsFork = new CompletableFuture<List<BffExternalAccount>>();
    externalTransferAccountsFork.complete(cesUserAccounts);

    when(acctManagementAsync.getExternalTransferAccounts())
        .thenReturn(externalTransferAccountsFork);

    when(cesClient.getFinancialInstitutionInfo(anyString()))
        .thenReturn(
            mockFromFile(
                "service/externaltransfer/financialInstitutionInfo_loginInfo.json",
                CesFinancialInstitutionInfoResponse.class));
    var result = transferService.getAccountVerificationInfo("F0B22788-4E02-6ACA-4CA7-A24B0198A25D");
    assertEquals("F0B22788-4E02-6ACA-4CA7-A24B0198A25D", result.getId());
    assertNotNull(result.getTrialDepositStartDate());
  }

  @Test
  void getAccountVerificationInfo_success_mapsTrailDepositFailure() {
    var cesUserAccounts = getCesUserAccounts(true);
    var externalTransferAccountsFork = new CompletableFuture<List<BffExternalAccount>>();
    externalTransferAccountsFork.complete(cesUserAccounts);

    when(acctManagementAsync.getExternalTransferAccounts())
        .thenReturn(externalTransferAccountsFork);

    when(cesClient.getFinancialInstitutionInfo(anyString()))
        .thenReturn(
            mockFromFile(
                "service/externaltransfer/financialInstitutionInfo_loginInfo.json",
                CesFinancialInstitutionInfoResponse.class));
    var result = transferService.getAccountVerificationInfo("507F7855-0FBB-C79F-5BE7-0636CF2EC3A6");
    assertEquals("507F7855-0FBB-C79F-5BE7-0636CF2EC3A6", result.getId());
    assertEquals("ACCOUNT_APPROVAL_DENIED", result.getAccountStatus());
  }

  @Test
  void getAccountVerificationInfo_success_brokerageName() {
    var cesUserAccounts = getCesUserAccounts(false);
    var externalTransferAccountsFork = new CompletableFuture<List<BffExternalAccount>>();
    externalTransferAccountsFork.complete(cesUserAccounts);

    when(acctManagementAsync.getExternalTransferAccounts())
        .thenReturn(externalTransferAccountsFork);

    when(cesClient.getBrokerageInfo(anyString()))
        .thenReturn(
            mockFromFile(
                "service/externaltransfer/financialInstitutionInfo_loginInfo.json",
                CesFinancialInstitutionInfoResponse.class));
    var result = transferService.getAccountVerificationInfo("507F7855-0FBB-C79F-5BE7-0636CF2EC3A6");

    assertEquals("507F7855-0FBB-C79F-5BE7-0636CF2EC3A6", result.getId());
    assertEquals("Investment", result.getAccountGroup());
    assertFalse(result.isRealTimeVerification());
  }

  @Test
  void getAccountVerificationInfo_success_routingNumber() {
    var cesUserAccounts = getCesUserAccounts(true);
    var externalTransferAccountsFork = new CompletableFuture<List<BffExternalAccount>>();
    externalTransferAccountsFork.complete(cesUserAccounts);

    when(acctManagementAsync.getExternalTransferAccounts())
        .thenReturn(externalTransferAccountsFork);

    when(cesClient.getFinancialInstitutionInfo(anyString()))
        .thenReturn(
            mockFromFile(
                "service/externaltransfer/financialInstitutionInfo.json",
                CesFinancialInstitutionInfoResponse.class));
    var result = transferService.getAccountVerificationInfo("507F7855-0FBB-C79F-5BE7-0636CF2EC3A6");

    assertEquals("507F7855-0FBB-C79F-5BE7-0636CF2EC3A6", result.getId());
    assertEquals("Cash", result.getAccountGroup());
    assertFalse(result.isRealTimeVerification());
  }

  @Test
  void getFinancialInstitutionInfoForRoutingNumber_mapsSuccessfulResponse_whenLoginInfoReturned() {
    when(cesClient.getFinancialInstitutionInfo(anyString()))
        .thenReturn(
            mockFromFile(
                "service/externaltransfer/financialInstitutionInfo_loginInfo.json",
                CesFinancialInstitutionInfoResponse.class));
    BffFinancialInstitutionInfo response =
        transferService.getFinancialInstitutionInfoForRoutingNumber("*********");
    assertEquals(CesResponse.SUCCESS, response.getStatus());
    assertEquals(2, response.getFinInsLoginInfoList().size());
    assertEquals("10796", response.getFinInsLoginInfoList().get(0).getParamId());
    assertEquals("User ID", response.getFinInsLoginInfoList().get(0).getParamCaption());
    assertEquals(30, response.getFinInsLoginInfoList().get(0).getParamMaxLength());
    assertEquals("CashEdge Bank (Test 2FA)", response.getInstitutionName());
    assertFalse(response.isRealTimeVerification());
  }

  @Test
  void
      getFinancialInstitutionInfoForRoutingNumber_mapsSuccessfulResponse_whenLoginInfoNotReturned() {
    when(cesClient.getFinancialInstitutionInfo(anyString()))
        .thenReturn(
            mockFromFile(
                "service/externaltransfer/financialInstitutionInfo.json",
                CesFinancialInstitutionInfoResponse.class));
    BffFinancialInstitutionInfo response =
        transferService.getFinancialInstitutionInfoForRoutingNumber("*********");
    assertEquals(CesResponse.SUCCESS, response.getStatus());
    assertTrue(response.getFinInsLoginInfoList().isEmpty());
    assertEquals("PNC Bank", response.getInstitutionName());
    assertFalse(response.isRealTimeVerification());
  }

  @Test
  void getBrokerageInfoForRoutingNumber_mapsSuccessfulResponse_whenLoginInfoReturned() {
    when(cesClient.getBrokerageInfo(anyString()))
        .thenReturn(
            mockFromFile(
                "service/externaltransfer/financialInstitutionInfo_loginInfo.json",
                CesFinancialInstitutionInfoResponse.class));
    BffFinancialInstitutionInfo response =
        transferService.getFinancialInstitutionInfoForBrokerageName("Cool Brokerage");
    assertEquals(CesResponse.SUCCESS, response.getStatus());
    assertEquals(2, response.getFinInsLoginInfoList().size());
    assertEquals("CashEdge Bank (Test 2FA)", response.getInstitutionName());
    assertFalse(response.isRealTimeVerification());
  }

  @Test
  void getBrokerageInfoForRoutingNumber_mapsSuccessfulResponse_whenLoginInfoNotReturned() {
    when(cesClient.getBrokerageInfo(anyString()))
        .thenReturn(
            mockFromFile(
                "service/externaltransfer/financialInstitutionInfo.json",
                CesFinancialInstitutionInfoResponse.class));
    BffFinancialInstitutionInfo response =
        transferService.getFinancialInstitutionInfoForBrokerageName("Some Brokerage");
    assertEquals(CesResponse.SUCCESS, response.getStatus());
    assertTrue(response.getFinInsLoginInfoList().isEmpty());
    assertEquals("PNC Bank", response.getInstitutionName());
  }

  @Test
  void addExternalAccount_mapsSuccessfulResponse_whenAccountAddedButNotVerified() {
    when(cesClient.addExternalAccountByInstant(any()))
        .thenReturn(
            mockFromFile(
                "service/externaltransfer/addExternalAccountByInstant_successNoVerification.json",
                CesAddExternalAccountByInstantResponse.class));

    BffAddAccountRequest request = new BffAddAccountRequest();
    request.setAccountTypeCode(ExternalAccountType.CHECKING);

    BffAddAccountResponse response = transferService.addExternalAccount(request);

    assertEquals(CesResponse.SUCCESS, response.getStatus());
    assertEquals(
        "NotAvailable", response.getExternalAccountVerificationStatus().getVerificationStatus());
    assertEquals(
        "ACCOUNT_REQUIRES_APPROVAL",
        response.getExternalAccountVerificationStatus().getAccount().getAccountStatus());
  }

  public static class CesAddExternalAccountByInstantRequestMatcher
      implements ArgumentMatcher<CesAddExternalAccountByInstantRequest> {
    @Override
    public boolean matches(CesAddExternalAccountByInstantRequest request) {
      return true;
    }
  }

  @Test
  void addExternalAccount_throwsFeignClientError_whenCesReturns500() {
    when(cesClient.addExternalAccountByInstant(any()))
        .thenThrow(
            new CesFeignException(HttpStatus.INTERNAL_SERVER_ERROR, "Internal Server Error"));

    BffAddAccountRequest request = new BffAddAccountRequest();
    request.setAccountTypeCode(ExternalAccountType.CHECKING);

    assertThrows(CesFeignException.class, () -> transferService.addExternalAccount(request));
    verify(tokenizer)
        .tokenize(anyString(), argThat(new CesAddExternalAccountByInstantRequestMatcher()));
  }

  @Test
  void mapAuthorizedBrokerage_setsRequiredFields() {
    AuthorizedBrokerage authorizedBrokerage = new AuthorizedBrokerage();
    authorizedBrokerage.setName("brokerageName");
    authorizedBrokerage.setRoutingNumber("*********");
    authorizedBrokerage.setCreditRoutingNumber("*********");
    authorizedBrokerage.setFields(new ArrayList<>());

    BrokerageEntry entry = transferService.mapAuthorizedBrokerage(authorizedBrokerage);

    assertEquals("brokerageName", entry.getName());
    assertEquals("*********", entry.getRoutingNumber());
    assertEquals("*********", entry.getCreditRoutingNumber());
  }

  @Test
  void mapAuthorizedBrokerage_setsBrokerageAndToolTip_whenOnlyBrokerageAndSingleToolTipAvailable() {
    AuthorizedBrokerage authorizedBrokerage = new AuthorizedBrokerage();

    var brokerageFields = new ArrayList<String>();
    brokerageFields.add("brokerageAccount");

    var toolTips = new ArrayList<String>();
    toolTips.add("This is a tool tip");

    authorizedBrokerage.setFields(brokerageFields);
    authorizedBrokerage.setFieldTips(toolTips);

    BrokerageEntry entry = transferService.mapAuthorizedBrokerage(authorizedBrokerage);

    assertEquals("This is a tool tip", entry.getBrokerageAccount().getToolTip());
    assertNull(entry.getBrokerageAccount().getMessage());
    assertFalse(entry.getBrokerageAccount().isOptional());

    assertNull(entry.getCheckingAccount());
  }

  @Test
  void mapAuthorizedBrokerage_setsBrokerageAndToolTipToNull_whenOnlyBrokerageAndToolTipIsEmpty() {
    AuthorizedBrokerage authorizedBrokerage = new AuthorizedBrokerage();

    var brokerageFields = new ArrayList<String>();
    brokerageFields.add("brokerageAccount");

    var toolTips = new ArrayList<String>();
    toolTips.add("");

    authorizedBrokerage.setFields(brokerageFields);
    authorizedBrokerage.setFieldTips(toolTips);

    BrokerageEntry entry = transferService.mapAuthorizedBrokerage(authorizedBrokerage);

    assertNull(entry.getBrokerageAccount().getToolTip());
    assertNull(entry.getBrokerageAccount().getMessage());
    assertFalse(entry.getBrokerageAccount().isOptional());

    assertNull(entry.getCheckingAccount());
  }

  @Test
  void mapAuthorizedBrokerage_setsCheckingAndToolTip_whenCheckingAndToolTipIsAvailable() {
    AuthorizedBrokerage authorizedBrokerage = new AuthorizedBrokerage();
    authorizedBrokerage.setName("Funny Brokerage");

    var brokerageFields = new ArrayList<String>();
    brokerageFields.add("brokerageAccount");
    brokerageFields.add("checkingAccount");

    var toolTips = new ArrayList<String>();
    toolTips.add("cool tool tip");
    toolTips.add("cooler tool tip");

    authorizedBrokerage.setFields(brokerageFields);
    authorizedBrokerage.setFieldTips(toolTips);

    BrokerageEntry entry = transferService.mapAuthorizedBrokerage(authorizedBrokerage);

    assertEquals("cool tool tip", entry.getBrokerageAccount().getToolTip());
    assertNull(entry.getBrokerageAccount().getMessage());
    assertFalse(entry.getBrokerageAccount().isOptional());

    assertEquals("cooler tool tip", entry.getCheckingAccount().getToolTip());
    assertNull(entry.getCheckingAccount().getMessage());
    assertFalse(entry.getCheckingAccount().isOptional());
  }

  @Test
  void
      mapAuthorizedBrokerage_setsCheckingAndSetsToolTipToNull_whenCheckingAndToolTipIsNotAvailable() {
    AuthorizedBrokerage authorizedBrokerage = new AuthorizedBrokerage();
    authorizedBrokerage.setName("Funny Brokerage");

    var brokerageFields = new ArrayList<String>();
    brokerageFields.add("brokerageAccount");
    brokerageFields.add("checkingAccount");

    var toolTips = new ArrayList<String>();
    toolTips.add("cool tool tip");

    authorizedBrokerage.setFields(brokerageFields);
    authorizedBrokerage.setFieldTips(toolTips);

    BrokerageEntry entry = transferService.mapAuthorizedBrokerage(authorizedBrokerage);

    assertEquals("cool tool tip", entry.getBrokerageAccount().getToolTip());
    assertNull(entry.getBrokerageAccount().getMessage());
    assertFalse(entry.getBrokerageAccount().isOptional());

    assertNull(entry.getCheckingAccount().getToolTip());
    assertNull(entry.getCheckingAccount().getMessage());
    assertFalse(entry.getCheckingAccount().isOptional());
  }

  @Test
  void
      mapAuthorizedBrokerage_setsCheckingAndSetsToolTipToNull_whenCheckingAndToolTipIsEmptyString() {
    AuthorizedBrokerage authorizedBrokerage = new AuthorizedBrokerage();
    authorizedBrokerage.setName("Funny Brokerage");

    var brokerageFields = new ArrayList<String>();
    brokerageFields.add("brokerageAccount");
    brokerageFields.add("checkingAccount");

    var toolTips = new ArrayList<String>();
    toolTips.add("cool tool tip");
    toolTips.add("");

    authorizedBrokerage.setFields(brokerageFields);
    authorizedBrokerage.setFieldTips(toolTips);

    BrokerageEntry entry = transferService.mapAuthorizedBrokerage(authorizedBrokerage);

    assertEquals("cool tool tip", entry.getBrokerageAccount().getToolTip());
    assertNull(entry.getBrokerageAccount().getMessage());
    assertFalse(entry.getBrokerageAccount().isOptional());

    assertNull(entry.getCheckingAccount().getToolTip());
    assertNull(entry.getCheckingAccount().getMessage());
    assertFalse(entry.getCheckingAccount().isOptional());
  }

  @Test
  void mapAuthorizedBrokerage_setsCheckingToOptionalAndSetMessage_whenCheckingisCharlesSchwab() {
    AuthorizedBrokerage authorizedBrokerage = new AuthorizedBrokerage();
    authorizedBrokerage.setName("Charles Schwab Brokerage");

    var brokerageFields = new ArrayList<String>();
    brokerageFields.add("brokerageAccount");
    brokerageFields.add("checkingAccount");

    var toolTips = new ArrayList<String>();
    toolTips.add("cool tool tip");
    toolTips.add("this tool tip is actually a message");

    authorizedBrokerage.setFields(brokerageFields);
    authorizedBrokerage.setFieldTips(toolTips);

    BrokerageEntry entry = transferService.mapAuthorizedBrokerage(authorizedBrokerage);

    assertEquals("cool tool tip", entry.getBrokerageAccount().getToolTip());
    assertNull(entry.getBrokerageAccount().getMessage());
    assertFalse(entry.getBrokerageAccount().isOptional());

    assertNull(entry.getCheckingAccount().getToolTip());
    assertEquals("this tool tip is actually a message", entry.getCheckingAccount().getMessage());
    assertTrue(entry.getCheckingAccount().isOptional());
  }

  public static class CesAddExternalAccountRealTimeRequesttMatcher
      implements ArgumentMatcher<CesAddExternalAccountRealTimeRequest> {
    @Override
    public boolean matches(CesAddExternalAccountRealTimeRequest request) {
      return true;
    }
  }

  @Test
  void addExternalAccountWithRealTime_cesRequestMappingStep1() {
    CesAddExternalAccountRealTimeRequest request =
        mockFromFile(
            "service/add_extern_acct_real_time_rq1.json",
            CesAddExternalAccountRealTimeRequest.class);
    when(cesClient.addExternalAccountByRealTime(any()))
        .thenReturn(
            mockFromFile(
                "service/add_extern_acct_real_time_rs1.json",
                CesAddExternalAccountRealTimeResponse.class));
    var realTimeResponse =
        transferService.cesAddExternalAccountRealTime(new CesAddExternalAccountRealTimeRequest());
    assertEquals("SUCCESS", realTimeResponse.getStatus());
    assertEquals(
        "5092", realTimeResponse.getCesRealTimeAccountVerificationStatus().getStatusCode());
    verify(tokenizer)
        .tokenize(anyString(), argThat(new CesAddExternalAccountRealTimeRequesttMatcher()));
  }

  @Test
  void getCesVerifyTrialDepositsRequest_mapsValuesAppropriately() {
    BffVerifyTrialDepositsRequest bffRequest = new BffVerifyTrialDepositsRequest();
    bffRequest.setAccountId("some_id");
    bffRequest.setAmountOne(new BigDecimal(".5"));
    bffRequest.setAmountTwo(new BigDecimal(".3"));

    CesVerifyTrialDepositsRequest cesRequest =
        transferService.getCesVerifyTrialDepositsRequest(bffRequest);

    assertEquals("some_id", cesRequest.getAccountId());
    assertEquals(new BigDecimal(".5"), cesRequest.getAmounts().get(0));
    assertEquals(new BigDecimal(".3"), cesRequest.getAmounts().get(1));
  }

  @Test
  void deleteExternalAccount_happyPath() {
    when(cesClient.deleteExternalAccount(anyString()))
        .thenReturn(new CesBaseResponse("SUCCESS", "", ""));
    var responseEntity = transferService.deleteExternalAccount("eat-pray-love");

    assertEquals("SUCCESS", responseEntity.getStatus());
  }

  private List<BffExternalAccount> getCesUserAccounts(final boolean cash) {
    final String suffix =
        cash ? "userAccounts_success.json" : "userAccounts_success_investment.json";
    return TestUtils.cesExternalAccountsToBffExternalAccounts(
        mockFromFile("service/externaltransfer/" + suffix, CesGetUserAccountsResponse.class)
            .getAccountDataList());
  }
}
