/* Copyright 2024 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.controller.cd;

import static com.fitb.digital.bff.movemoney.utils.TestUtils.mockFromFile;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.when;

import com.fitb.digital.bff.movemoney.model.bff.cd.responses.CDFundingAccountsListResponse;
import com.fitb.digital.bff.movemoney.service.cd.CDFundingAccountService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
@Slf4j
public class MmCDFundingAccountControllerTest {

  @InjectMocks private MmCDFundingAccountController controller;
  @Mock private CDFundingAccountService service;

  @Test
  void getCDFindingAccounts() {
    when(service.getCDFundingAccounts())
        .thenReturn(
            mockFromFile("stubs/cd-funding-accounts.json", CDFundingAccountsListResponse.class));
    var response = controller.getCDFundingAccounts();
    assertNotNull(response);
    assertEquals(CDFundingAccountsListResponse.class, response.getClass());
    assertEquals(3, response.getInternalAccounts().size());
    assertEquals(2, response.getExternalAccounts().size());
  }
}
