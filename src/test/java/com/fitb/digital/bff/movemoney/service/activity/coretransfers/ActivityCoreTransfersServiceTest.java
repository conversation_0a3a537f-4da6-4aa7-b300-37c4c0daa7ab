/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.service.activity.coretransfers;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;

import com.fitb.digital.bff.movemoney.client.CoreTransfersClient;
import com.fitb.digital.bff.movemoney.model.bff.CoreTransfers.requests.TDSCoreTransferRequest;
import com.fitb.digital.bff.movemoney.model.bff.CoreTransfers.responses.TDSCoreTransferResponse;
import com.fitb.digital.bff.movemoney.model.bff.activity.requests.BffActivityRequest;
import com.fitb.digital.bff.movemoney.model.client.CesResponse;
import com.fitb.digital.bff.movemoney.service.coretransfers.CoreTransfersService;
import java.math.BigDecimal;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@Slf4j
@ExtendWith(MockitoExtension.class)
class ActivityCoreTransfersServiceTest {

  @InjectMocks private CoreTransfersService coreTransfersService;

  @Mock private CoreTransfersClient coreTransfersClient;

  @Test
  void postActivityReturnsErrorIfTdsCoreTransferFails() {
    TDSCoreTransferResponse tdsCoreTransferResponse = new TDSCoreTransferResponse();
    TDSCoreTransferResponse.ErrorResponse errorResponse =
        new TDSCoreTransferResponse.ErrorResponse();
    errorResponse.setCode("ERROR");
    errorResponse.setMessage("BADBOY");
    errorResponse.setTarget("NO");
    tdsCoreTransferResponse.setError(errorResponse);
    TDSCoreTransferRequest request = new TDSCoreTransferRequest();
    when(coreTransfersClient.tdsCoreTransfer(any(TDSCoreTransferRequest.class)))
        .thenReturn(tdsCoreTransferResponse);
    var response = coreTransfersClient.tdsCoreTransfer(request);
    assertNotEquals(CesResponse.SUCCESS, response.getError().getCode());
  }

  @Test
  void postActivityReturnsErrorIfTdsCoreTransferNotFound() {
    TDSCoreTransferResponse tdsCoreTransferResponse = new TDSCoreTransferResponse();
    TDSCoreTransferResponse.ErrorResponse errorResponse =
        new TDSCoreTransferResponse.ErrorResponse();
    errorResponse.setCode("ERROR");
    errorResponse.setMessage("BADBOY");
    errorResponse.setTarget("NO");
    tdsCoreTransferResponse.setError(errorResponse);
    TDSCoreTransferRequest request = new TDSCoreTransferRequest();
    when(coreTransfersClient.tdsCoreTransfer(any(TDSCoreTransferRequest.class)))
        .thenReturn(tdsCoreTransferResponse);
    var response = coreTransfersClient.tdsCoreTransfer(request);
    assertEquals("ERROR", response.getError().getCode());
  }

  @Test
  void verifyAddCoreTransferTDSActivitySUCCESS() {
    TDSCoreTransferResponse tdsCoreTransferResponse = new TDSCoreTransferResponse();
    when(coreTransfersClient.tdsCoreTransfer(any(TDSCoreTransferRequest.class)))
        .thenReturn(tdsCoreTransferResponse);
    BffActivityRequest addActivity = new BffActivityRequest();
    addActivity.setAmount(BigDecimal.TEN);
    var response = coreTransfersService.addActivityTds(addActivity);
    assertEquals("SUCCESS", response.getStatus());
  }

  @Test
  void verifyAddCoreTransferTDSActivity() {
    TDSCoreTransferResponse tdsCoreTransferResponse = new TDSCoreTransferResponse();
    when(coreTransfersClient.tdsCoreTransfer(any(TDSCoreTransferRequest.class)))
        .thenReturn(tdsCoreTransferResponse);
    BffActivityRequest addActivity = new BffActivityRequest();
    addActivity.setAmount(BigDecimal.TEN);
    var responseClient = coreTransfersService.addActivityTds(addActivity);
    assertNotNull(responseClient);
    assertNotNull(addActivity.getRequestGuid());
  }
}
