/* Copyright 2024 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.controller.externaltransfer;

import static org.junit.jupiter.api.Assertions.*;

import com.fitb.digital.bff.movemoney.exceptions.BffException;
import com.fitb.digital.bff.movemoney.model.bff.external.transfer.requests.BffAddAccountRequest;
import java.util.stream.Stream;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.RandomUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

public class ExternalTransferInputValidatorTest {
  private ExternalTransferInputValidator validator;

  @BeforeEach
  void setUp() {
    validator = new ExternalTransferInputValidator();
  }

  @ParameterizedTest
  @MethodSource("validateGetBankInfoInput_RoutingNumber_And_BrokerageName_Blank_Args")
  void validateGetBankInfoInput_RoutingNumber_And_BrokerageName_Blank(
      final String routingNumber, final String brokerageName) {
    final BffException actual =
        assertThrows(
            BffException.class,
            () -> validator.validateGetBankInfoInput(routingNumber, brokerageName));

    final BffException expected =
        BffException.badRequest(ExternalTransferInputValidator.BOTH_BLANK_MESSAGE);
    assertEquals(expected, actual);
  }

  private static Stream<Arguments>
      validateGetBankInfoInput_RoutingNumber_And_BrokerageName_Blank_Args() {
    return Stream.of(
        Arguments.of(null, null),
        Arguments.of("", ""),
        Arguments.of(" ", " "),
        Arguments.of("    ", "    "));
  }

  @Test
  void validateGetBankInfoInput_RoutingNumber_And_BrokerageName_NotBlank() {
    final String routingNumber = "routingNumber";
    final String brokerageName = "brokerageName";
    final BffException actual =
        assertThrows(
            BffException.class,
            () -> validator.validateGetBankInfoInput(routingNumber, brokerageName));

    final BffException expected =
        BffException.badRequest(ExternalTransferInputValidator.BOTH_NOT_BLANK_MESSAGE);
    assertEquals(expected, actual);
  }

  @ParameterizedTest
  @MethodSource("invalidRoutingNumbers")
  void validateGetBankInfoInput_RoutingNumber_NotMatchPattern(final String routingNumber) {
    final String brokerageName = null;
    final BffException actual =
        assertThrows(
            BffException.class,
            () -> validator.validateGetBankInfoInput(routingNumber, brokerageName));

    final BffException expected =
        BffException.badRequest(ExternalTransferInputValidator.ROUTING_NUMBER_INVALID_MESSAGE);
    assertEquals(expected, actual);
  }

  @ParameterizedTest
  @MethodSource("validRoutingNumbers")
  void validateGetBankInfoInput_RoutingNumber_MatchPattern(final String routingNumber) {
    final String brokerageName = null;
    assertDoesNotThrow(() -> validator.validateGetBankInfoInput(routingNumber, brokerageName));
  }

  @Test
  void validateGetBankInfoInput_BrokerageName() {
    final String routingNumber = null;
    final String brokerageName = "brokerageName";
    assertDoesNotThrow(() -> validator.validateGetBankInfoInput(routingNumber, brokerageName));
  }

  @ParameterizedTest
  @MethodSource("invalidRoutingNumbers")
  void validateAddAccountInput_RoutingNumber_NotMatch(final String routingNumber) {
    final BffAddAccountRequest request = new BffAddAccountRequest();
    request.setRoutingNumber(routingNumber);

    final BffException actual =
        assertThrows(BffException.class, () -> validator.validateAddAccountInput(request));

    final BffException expected =
        BffException.badRequest(ExternalTransferInputValidator.ROUTING_NUMBER_INVALID_MESSAGE);
    assertEquals(expected, actual);
  }

  @ParameterizedTest
  @MethodSource("invalidRoutingNumbers")
  void validateAddAccountInput_CreditRoutingNumber_NotMatch(final String creditRoutingNumber) {
    final BffAddAccountRequest request = new BffAddAccountRequest();
    request.setRoutingNumber("*********");
    request.setCreditRoutingNumber(creditRoutingNumber);

    final BffException actual =
        assertThrows(BffException.class, () -> validator.validateAddAccountInput(request));

    final BffException expected =
        BffException.badRequest(ExternalTransferInputValidator.ROUTING_NUMBER_INVALID_MESSAGE);
    assertEquals(expected, actual);
  }

  @ParameterizedTest
  @MethodSource("invalidAccountNumbers")
  void validateAddAccountInput_AccountNumber_NotMatch(final String accountNumber) {
    final BffAddAccountRequest request = new BffAddAccountRequest();
    request.setRoutingNumber("*********");
    request.setAccountNumber(accountNumber);

    final BffException actual =
        assertThrows(BffException.class, () -> validator.validateAddAccountInput(request));

    final BffException expected =
        BffException.badRequest(ExternalTransferInputValidator.ACCOUNT_NUMBER_INVALID_MESSAGE);
    assertEquals(expected, actual);
  }

  @ParameterizedTest
  @MethodSource("invalidAccountNumbers")
  void validateAddAccountInput_CreditAccountNumber_NotMatch(final String creditAccountNumber) {
    final BffAddAccountRequest request = new BffAddAccountRequest();
    request.setRoutingNumber("*********");
    request.setAccountNumber("01234");
    request.setCreditAccountNumber(creditAccountNumber);

    final BffException actual =
        assertThrows(BffException.class, () -> validator.validateAddAccountInput(request));

    final BffException expected =
        BffException.badRequest(ExternalTransferInputValidator.ACCOUNT_NUMBER_INVALID_MESSAGE);
    assertEquals(expected, actual);
  }

  @ParameterizedTest
  @MethodSource("validRoutingNumbers")
  void validateAddAccountInput_CreditRoutingNumber_Match(final String creditRoutingNumber) {
    final BffAddAccountRequest request = new BffAddAccountRequest();
    request.setRoutingNumber("*********");
    request.setAccountNumber("01234");
    request.setCreditRoutingNumber(creditRoutingNumber);

    assertDoesNotThrow(() -> validator.validateAddAccountInput(request));
  }

  @ParameterizedTest
  @MethodSource("validAccountNumbers")
  void validateAddAccountInput_CreditAccountNumber_Match(final String creditAccountNumber) {
    final BffAddAccountRequest request = new BffAddAccountRequest();
    request.setRoutingNumber("*********");
    request.setAccountNumber("01234");
    request.setCreditAccountNumber(creditAccountNumber);

    assertDoesNotThrow(() -> validator.validateAddAccountInput(request));
  }

  private static Stream<Arguments> invalidRoutingNumbers() {
    final Stream<Arguments> tooShort =
        Stream.generate(
                () -> {
                  final int length = RandomUtils.nextInt(1, 8);
                  return Arguments.of(RandomStringUtils.randomNumeric(length));
                })
            .limit(10);
    final Stream<Arguments> tooLong =
        Stream.generate(
                () -> {
                  final int length = RandomUtils.nextInt(10, 20);
                  return Arguments.of(RandomStringUtils.randomNumeric(length));
                })
            .limit(10);
    final Stream<Arguments> alpha =
        Stream.generate(() -> Arguments.of(RandomStringUtils.randomAlphabetic(9))).limit(10);
    final Stream<Arguments> alphaNumeric =
        Stream.generate(() -> Arguments.of(RandomStringUtils.randomAlphanumeric(9))).limit(10);
    return Stream.of(tooShort, tooLong, alpha, alphaNumeric).flatMap(a -> a);
  }

  private static Stream<Arguments> validRoutingNumbers() {
    return Stream.generate(() -> Arguments.of(RandomStringUtils.randomNumeric(9))).limit(10);
  }

  private static Stream<Arguments> invalidAccountNumbers() {
    final Stream<Arguments> tooShort =
        Stream.generate(
                () -> {
                  final int length = RandomUtils.nextInt(0, 4);
                  return Arguments.of(RandomStringUtils.randomNumeric(length));
                })
            .limit(10);
    final Stream<Arguments> tooLong =
        Stream.generate(
                () -> {
                  final int length = RandomUtils.nextInt(18, 25);
                  return Arguments.of(RandomStringUtils.randomNumeric(length));
                })
            .limit(10);
    final Stream<Arguments> alpha =
        Stream.generate(
                () -> {
                  final int length = RandomUtils.nextInt(5, 17);
                  return Arguments.of(RandomStringUtils.randomAlphabetic(length));
                })
            .limit(10);
    final Stream<Arguments> alphaNumeric =
        Stream.generate(
                () -> {
                  final int length = RandomUtils.nextInt(5, 17);
                  return Arguments.of(RandomStringUtils.randomAlphanumeric(length));
                })
            .limit(10);
    return Stream.of(tooShort, tooLong, alpha, alphaNumeric).flatMap(a -> a);
  }

  private static Stream<Arguments> validAccountNumbers() {
    return Stream.generate(
            () -> {
              final int length = RandomUtils.nextInt(5, 17);
              return Arguments.of(RandomStringUtils.randomNumeric(length));
            })
        .limit(10);
  }
}
