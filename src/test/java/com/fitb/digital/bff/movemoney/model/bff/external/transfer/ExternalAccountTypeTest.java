/* Copyright 2022 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.bff.external.transfer;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

import org.junit.jupiter.api.Test;

public class ExternalAccountTypeTest {
  @Test
  void fromStringIgnoreCast_WorksCorrectly() {
    assertEquals(
        ExternalAccountType.CHECKING, ExternalAccountType.fromStringIgnoreCase("Checking"));
    assertEquals(ExternalAccountType.SAVINGS, ExternalAccountType.fromStringIgnoreCase("savings"));
    assertEquals(
        ExternalAccountType.MMA_CHECKING, ExternalAccountType.fromStringIgnoreCase("mma checking"));
    assertEquals(
        ExternalAccountType.MMA_SAVINGS, ExternalAccountType.fromStringIgnoreCase("MMA Savings"));
    assertEquals(ExternalAccountType.CASH, ExternalAccountType.fromStringIgnoreCase("CASH"));
    assertNull(ExternalAccountType.fromStringIgnoreCase(null));
  }

  @Test
  void getCode_worksCorrectly() {
    assertEquals("Checking", ExternalAccountType.CHECKING.getCode());
    assertEquals("Savings", ExternalAccountType.SAVINGS.getCode());
    assertEquals("MMA Checking", ExternalAccountType.MMA_CHECKING.getCode());
    assertEquals("MMA Savings", ExternalAccountType.MMA_SAVINGS.getCode());
    assertEquals("Cash", ExternalAccountType.CASH.getCode());
  }
}
