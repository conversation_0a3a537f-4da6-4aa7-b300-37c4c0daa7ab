/* Copyright 2022 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.service.external;

import static com.fitb.digital.bff.movemoney.utils.TestUtils.mockFromFile;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

import com.fitb.digital.bff.movemoney.exceptions.BffException;
import com.fitb.digital.bff.movemoney.exceptions.CesFeignException;
import com.fitb.digital.bff.movemoney.model.bff.external.transfer.responses.BffExternalAccount;
import com.fitb.digital.bff.movemoney.model.client.CesBaseResponse;
import com.fitb.digital.bff.movemoney.model.client.external.transfer.responses.CesGetUserAccountsResponse;
import com.fitb.digital.bff.movemoney.model.client.invoicedpayment.responses.CesInvoicedPaymentAccountResponse;
import com.fitb.digital.bff.movemoney.model.client.webpayment.responses.CesWebpaymentProfileResponse;
import com.fitb.digital.bff.movemoney.utils.TestUtils;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;

@Slf4j
@ExtendWith(MockitoExtension.class)
public class ExternalAccountManagementServiceTest {

  @Mock private ExternalAccountManagementAsync mockAsync;

  private ExternalAccountManagementService externalAccountManagementService;

  @BeforeEach
  void init() {
    externalAccountManagementService = new ExternalAccountManagementService(mockAsync, 5);
  }

  @Test
  void getAccounts_combinesResults_whenSuccessReturned() {
    var cesUserAccounts = getCesUserAccounts();
    var externalTransferAccountsFork = new CompletableFuture<List<BffExternalAccount>>();
    externalTransferAccountsFork.complete(cesUserAccounts);

    var cesWebpaymentProfile = getWebPaymentProfile();
    var webPaymentProfileFork = new CompletableFuture<CesWebpaymentProfileResponse>();
    webPaymentProfileFork.complete(cesWebpaymentProfile);

    var invoicedPaymentProfile = getFundingAccount();
    var invoicedPaymentProfileFork = new CompletableFuture<CesInvoicedPaymentAccountResponse>();
    invoicedPaymentProfileFork.complete(invoicedPaymentProfile);

    when(mockAsync.getExternalTransferAccounts()).thenReturn(externalTransferAccountsFork);
    when(mockAsync.getWebPaymentProfile()).thenReturn(webPaymentProfileFork);
    when(mockAsync.getInvoicedPaymentProfile()).thenReturn(invoicedPaymentProfileFork);

    var externalUserAccounts = externalAccountManagementService.getAccounts();

    assertEquals(
        ExternalAccountResponseStatus.SUCCESS.toString(), externalUserAccounts.getStatus());
    assertTrue(externalUserAccounts.getTransferAccounts().size() > 0);
    assertTrue(externalUserAccounts.getPaymentAccounts().size() > 0);
    assertTrue(externalUserAccounts.getInvoicedPaymentAccounts().size() > 0);
    assertEquals(5, externalUserAccounts.getMaxExternalTransferAccounts());
  }

  @Test
  void getAccounts_mapsPaymentAccountsCorrectly_whenSuccessReturned() {
    var cesUserAccounts = getCesUserAccounts();
    var externalTransferAccountsFork = new CompletableFuture<List<BffExternalAccount>>();
    externalTransferAccountsFork.complete(cesUserAccounts);

    var cesWebpaymentProfile = getWebPaymentProfile();
    var webPaymentProfileFork = new CompletableFuture<CesWebpaymentProfileResponse>();
    webPaymentProfileFork.complete(cesWebpaymentProfile);

    var invoicedPaymentProfile = getFundingAccount();
    var invoicedPaymentProfileFork = new CompletableFuture<CesInvoicedPaymentAccountResponse>();
    invoicedPaymentProfileFork.complete(invoicedPaymentProfile);

    when(mockAsync.getExternalTransferAccounts()).thenReturn(externalTransferAccountsFork);
    when(mockAsync.getWebPaymentProfile()).thenReturn(webPaymentProfileFork);
    when(mockAsync.getInvoicedPaymentProfile()).thenReturn(invoicedPaymentProfileFork);

    var paymentAccount = externalAccountManagementService.getAccounts().getPaymentAccounts().get(0);
    var externalAccount =
        externalAccountManagementService.getAccounts().getTransferAccounts().get(0);
    var invoicedPaymentAccount =
        externalAccountManagementService.getAccounts().getInvoicedPaymentAccounts().get(0);
    assertEquals("296FE786-87BB-2390-9362-3BD933A6EA6B", paymentAccount.getId());
    assertEquals("DDA", paymentAccount.getAccountType());
    assertEquals("X4321", paymentAccount.getDisplayAccountNumber());
    assertEquals("CAPITAL ONE BANK (USA), N.A.", paymentAccount.getInstitutionName());
    assertEquals("*********", paymentAccount.getRoutingNumber());
    assertNotNull(externalAccount);
    assertNotNull(invoicedPaymentAccount);
  }

  @Test
  void getAccounts_mapsExternalAccountsCorrectly_whenSuccessReturned() {
    var cesUserAccounts = getCesUserAccounts();
    var externalTransferAccountsFork = new CompletableFuture<List<BffExternalAccount>>();
    externalTransferAccountsFork.complete(cesUserAccounts);

    var cesWebpaymentProfile = getWebPaymentProfile();
    var webPaymentProfileFork = new CompletableFuture<CesWebpaymentProfileResponse>();
    webPaymentProfileFork.complete(cesWebpaymentProfile);

    var invoicedPaymentProfile = getFundingAccount();
    var invoicedPaymentProfileFork = new CompletableFuture<CesInvoicedPaymentAccountResponse>();
    invoicedPaymentProfileFork.complete(invoicedPaymentProfile);

    when(mockAsync.getExternalTransferAccounts()).thenReturn(externalTransferAccountsFork);
    when(mockAsync.getWebPaymentProfile()).thenReturn(webPaymentProfileFork);
    when(mockAsync.getInvoicedPaymentProfile()).thenReturn(invoicedPaymentProfileFork);

    var externalAccount =
        externalAccountManagementService.getAccounts().getTransferAccounts().get(0);
    var invoicedPaymentAccount =
        externalAccountManagementService.getAccounts().getInvoicedPaymentAccounts().get(0);
    assertNotNull(externalAccount);
    assertNotNull(invoicedPaymentAccount);
    assertEquals("Capital One", externalAccount.getInstitutionName());
    assertEquals("Cap 1 checking", externalAccount.getAccountNickName());
  }

  @Test
  void getAccounts_mapsInvoicedPaymentAccountsCorrectly_whenSuccessReturned() {
    var cesUserAccounts = getCesUserAccounts();
    var externalTransferAccountsFork = new CompletableFuture<List<BffExternalAccount>>();
    externalTransferAccountsFork.complete(cesUserAccounts);

    var cesWebpaymentProfile = getWebPaymentProfile();
    var webPaymentProfileFork = new CompletableFuture<CesWebpaymentProfileResponse>();
    webPaymentProfileFork.complete(cesWebpaymentProfile);

    var invoicedPaymentProfile = getFundingAccount();
    var invoicedPaymentProfileFork = new CompletableFuture<CesInvoicedPaymentAccountResponse>();
    invoicedPaymentProfileFork.complete(invoicedPaymentProfile);

    when(mockAsync.getExternalTransferAccounts()).thenReturn(externalTransferAccountsFork);
    when(mockAsync.getWebPaymentProfile()).thenReturn(webPaymentProfileFork);
    when(mockAsync.getInvoicedPaymentProfile()).thenReturn(invoicedPaymentProfileFork);

    var paymentAccount = externalAccountManagementService.getAccounts().getPaymentAccounts().get(0);
    var invoicedPaymentAccount =
        externalAccountManagementService.getAccounts().getInvoicedPaymentAccounts().get(0);
    var externalAccount =
        externalAccountManagementService.getAccounts().getTransferAccounts().get(0);
    assertEquals("BA25B7F6-D90A-A9F8-AE1A-89F2658B421D", invoicedPaymentAccount.getId());
    assertEquals("DDA", invoicedPaymentAccount.getAccountType());
    assertEquals("********", invoicedPaymentAccount.getDisplayAccountNumber());
    assertEquals("Acme Acme", invoicedPaymentAccount.getAccountNickName());
    assertEquals("SunTrust", invoicedPaymentAccount.getInstitutionName());
    assertEquals("*********", invoicedPaymentAccount.getRoutingNumber());
    assertNotNull(paymentAccount);
    assertNotNull(externalAccount);
  }

  @Test
  void getAccounts_trailDepositsMapped_whenSuccessReturned() {
    var cesUserAccounts = getCesUserAccounts();
    var externalTransferAccountsFork = new CompletableFuture<List<BffExternalAccount>>();
    externalTransferAccountsFork.complete(cesUserAccounts);

    var cesWebpaymentProfile = getWebPaymentProfile();
    var webPaymentProfileFork = new CompletableFuture<CesWebpaymentProfileResponse>();
    webPaymentProfileFork.complete(cesWebpaymentProfile);

    var invoicedPaymentProfile = getFundingAccount();
    var invoicedPaymentProfileFork = new CompletableFuture<CesInvoicedPaymentAccountResponse>();
    invoicedPaymentProfileFork.complete(invoicedPaymentProfile);

    when(mockAsync.getExternalTransferAccounts()).thenReturn(externalTransferAccountsFork);
    when(mockAsync.getWebPaymentProfile()).thenReturn(webPaymentProfileFork);
    when(mockAsync.getInvoicedPaymentProfile()).thenReturn(invoicedPaymentProfileFork);

    var externalUserAccounts = externalAccountManagementService.getAccounts();

    assertEquals(
        ExternalAccountResponseStatus.SUCCESS.toString(), externalUserAccounts.getStatus());
    assertTrue(externalUserAccounts.getTransferAccounts().size() > 0);
    for (var account : externalUserAccounts.getTransferAccounts()) {
      if (account.getTrialDepositRemainingAttempts() < 0)
        assertEquals("ACCOUNT_APPROVAL_DENIED", account.getAccountStatus());
    }
    assertTrue(externalUserAccounts.getInvoicedPaymentAccounts().size() > 0);
  }

  @Test
  void getAccounts_combinesResults_filtersHostAccounts() {
    var cesUserAccounts = getCesUserAccounts();
    var externalTransferAccountsFork = new CompletableFuture<List<BffExternalAccount>>();
    externalTransferAccountsFork.complete(cesUserAccounts);

    var cesWebpaymentProfile = getWebPaymentProfile();
    var webPaymentProfileFork = new CompletableFuture<CesWebpaymentProfileResponse>();
    webPaymentProfileFork.complete(cesWebpaymentProfile);

    var invoicedPaymentProfile = getFundingAccount();
    var invoicedPaymentProfileFork = new CompletableFuture<CesInvoicedPaymentAccountResponse>();
    invoicedPaymentProfileFork.complete(invoicedPaymentProfile);

    when(mockAsync.getExternalTransferAccounts()).thenReturn(externalTransferAccountsFork);
    when(mockAsync.getWebPaymentProfile()).thenReturn(webPaymentProfileFork);
    when(mockAsync.getInvoicedPaymentProfile()).thenReturn(invoicedPaymentProfileFork);

    var externalUserAccounts = externalAccountManagementService.getAccounts();

    assertEquals(
        ExternalAccountResponseStatus.SUCCESS.toString(), externalUserAccounts.getStatus());
    assertEquals(5, externalUserAccounts.getTransferAccounts().size());
    assertTrue(externalUserAccounts.getPaymentAccounts().size() > 0);
    assertTrue(externalUserAccounts.getInvoicedPaymentAccounts().size() > 0);
  }

  @Test
  void getAccounts_returnsExternalTransferFailureStatus_whenExternalTransferFails() {
    var externalTransferAccountsFork = new CompletableFuture<List<BffExternalAccount>>();
    externalTransferAccountsFork.completeExceptionally(
        BffException.serviceUnavailable("some exception"));

    var cesWebpaymentProfile = getWebPaymentProfile();
    var webPaymentProfileFork = new CompletableFuture<CesWebpaymentProfileResponse>();
    webPaymentProfileFork.complete(cesWebpaymentProfile);

    var invoicedPaymentProfile = getFundingAccount();
    var invoicedPaymentProfileFork = new CompletableFuture<CesInvoicedPaymentAccountResponse>();
    invoicedPaymentProfileFork.complete(invoicedPaymentProfile);

    when(mockAsync.getExternalTransferAccounts()).thenReturn(externalTransferAccountsFork);
    when(mockAsync.getWebPaymentProfile()).thenReturn(webPaymentProfileFork);
    when(mockAsync.getInvoicedPaymentProfile()).thenReturn(invoicedPaymentProfileFork);

    var externalUserAccounts = externalAccountManagementService.getAccounts();

    assertEquals(
        ExternalAccountResponseStatus.EXTERNAL_TRANSFER_FAILURE.toString(),
        externalUserAccounts.getStatus());
    assertEquals(0, externalUserAccounts.getTransferAccounts().size());
    assertTrue(externalUserAccounts.getPaymentAccounts().size() > 0);
    assertTrue(externalUserAccounts.getInvoicedPaymentAccounts().size() > 0);
  }

  @Test
  void getAccounts_returnsWebPaymentFailureStatus_whenWebPaymentProfileFails() {
    var externalTransferAccountsFork = new CompletableFuture<List<BffExternalAccount>>();
    externalTransferAccountsFork.complete(getCesUserAccounts());

    var webPaymentProfileFork = new CompletableFuture<CesWebpaymentProfileResponse>();
    webPaymentProfileFork.completeExceptionally(BffException.serviceUnavailable("some exception"));

    var invoicedPaymentProfile = getFundingAccount();
    var invoicedPaymentProfileFork = new CompletableFuture<CesInvoicedPaymentAccountResponse>();
    invoicedPaymentProfileFork.complete(invoicedPaymentProfile);

    when(mockAsync.getExternalTransferAccounts()).thenReturn(externalTransferAccountsFork);
    when(mockAsync.getWebPaymentProfile()).thenReturn(webPaymentProfileFork);
    when(mockAsync.getInvoicedPaymentProfile()).thenReturn(invoicedPaymentProfileFork);

    var externalUserAccounts = externalAccountManagementService.getAccounts();

    assertEquals(
        ExternalAccountResponseStatus.WEB_PAYMENT_FAILURE.toString(),
        externalUserAccounts.getStatus());
    assertEquals(0, externalUserAccounts.getPaymentAccounts().size());
    assertTrue(externalUserAccounts.getTransferAccounts().size() > 0);
    assertTrue(externalUserAccounts.getInvoicedPaymentAccounts().size() > 0);
  }

  @Test
  void getAccounts_returnsInvoicedPaymentFailureStatus_whenInvoicedPaymentProfileFails() {
    var externalTransferAccountsFork = new CompletableFuture<List<BffExternalAccount>>();
    externalTransferAccountsFork.complete(getCesUserAccounts());

    var cesWebpaymentProfile = getWebPaymentProfile();
    var webPaymentProfileFork = new CompletableFuture<CesWebpaymentProfileResponse>();
    webPaymentProfileFork.complete(cesWebpaymentProfile);

    var invoicedPaymentProfileFork = new CompletableFuture<CesInvoicedPaymentAccountResponse>();
    invoicedPaymentProfileFork.completeExceptionally(
        BffException.serviceUnavailable("some exception"));

    when(mockAsync.getExternalTransferAccounts()).thenReturn(externalTransferAccountsFork);
    when(mockAsync.getWebPaymentProfile()).thenReturn(webPaymentProfileFork);
    when(mockAsync.getInvoicedPaymentProfile()).thenReturn(invoicedPaymentProfileFork);

    var externalUserAccounts = externalAccountManagementService.getAccounts();

    assertEquals(
        ExternalAccountResponseStatus.INVOICED_PAYMENT_FAILURE.toString(),
        externalUserAccounts.getStatus());
    assertEquals(0, externalUserAccounts.getInvoicedPaymentAccounts().size());
    assertTrue(externalUserAccounts.getTransferAccounts().size() > 0);
    assertTrue(externalUserAccounts.getPaymentAccounts().size() > 0);
  }

  @Test
  void getAccounts_returnsInvoicedPaymentFailureStatus_whenInvoicedPaymentAndWebPaymentFails() {
    var externalTransferAccountsFork = new CompletableFuture<List<BffExternalAccount>>();
    externalTransferAccountsFork.complete(getCesUserAccounts());

    var webPaymentProfileFork = new CompletableFuture<CesWebpaymentProfileResponse>();
    webPaymentProfileFork.completeExceptionally(BffException.serviceUnavailable("some exception"));

    var invoicedPaymentProfileFork = new CompletableFuture<CesInvoicedPaymentAccountResponse>();
    invoicedPaymentProfileFork.completeExceptionally(
        BffException.serviceUnavailable("some exception"));

    when(mockAsync.getExternalTransferAccounts()).thenReturn(externalTransferAccountsFork);
    when(mockAsync.getWebPaymentProfile()).thenReturn(webPaymentProfileFork);
    when(mockAsync.getInvoicedPaymentProfile()).thenReturn(invoicedPaymentProfileFork);

    var externalUserAccounts = externalAccountManagementService.getAccounts();

    assertEquals(
        ExternalAccountResponseStatus.WEB_PAYMENT_FAILURE
            + ","
            + ExternalAccountResponseStatus.INVOICED_PAYMENT_FAILURE,
        externalUserAccounts.getStatus());
    assertEquals(0, externalUserAccounts.getInvoicedPaymentAccounts().size());
    assertTrue(externalUserAccounts.getTransferAccounts().size() > 0);
    assertEquals(0, externalUserAccounts.getPaymentAccounts().size());
  }

  @Test
  void
      getAccounts_returnsInvoicedPaymentFailureStatus_whenInvoicedPaymentAndExternalTransferFails() {
    var externalTransferAccountsFork = new CompletableFuture<List<BffExternalAccount>>();
    externalTransferAccountsFork.completeExceptionally(
        BffException.serviceUnavailable("some exception"));

    var cesWebpaymentProfile = getWebPaymentProfile();
    var webPaymentProfileFork = new CompletableFuture<CesWebpaymentProfileResponse>();
    webPaymentProfileFork.complete(cesWebpaymentProfile);

    var invoicedPaymentProfileFork = new CompletableFuture<CesInvoicedPaymentAccountResponse>();
    invoicedPaymentProfileFork.completeExceptionally(
        BffException.serviceUnavailable("some exception"));

    when(mockAsync.getExternalTransferAccounts()).thenReturn(externalTransferAccountsFork);
    when(mockAsync.getWebPaymentProfile()).thenReturn(webPaymentProfileFork);
    when(mockAsync.getInvoicedPaymentProfile()).thenReturn(invoicedPaymentProfileFork);

    var externalUserAccounts = externalAccountManagementService.getAccounts();

    assertEquals(
        ExternalAccountResponseStatus.EXTERNAL_TRANSFER_FAILURE
            + ","
            + ExternalAccountResponseStatus.INVOICED_PAYMENT_FAILURE,
        externalUserAccounts.getStatus());
    assertEquals(0, externalUserAccounts.getInvoicedPaymentAccounts().size());
    assertEquals(0, externalUserAccounts.getTransferAccounts().size());
    assertTrue(externalUserAccounts.getPaymentAccounts().size() > 0);
  }

  @Test
  void getAccounts_returnsInvoicedPaymentFailureStatus_whenWebPaymentAndExternalTransferFails() {
    var externalTransferAccountsFork = new CompletableFuture<List<BffExternalAccount>>();
    externalTransferAccountsFork.completeExceptionally(
        BffException.serviceUnavailable("some exception"));

    var webPaymentProfileFork = new CompletableFuture<CesWebpaymentProfileResponse>();
    webPaymentProfileFork.completeExceptionally(BffException.serviceUnavailable("some exception"));

    var invoicedPaymentProfile = getFundingAccount();
    var invoicedPaymentProfileFork = new CompletableFuture<CesInvoicedPaymentAccountResponse>();
    invoicedPaymentProfileFork.complete(invoicedPaymentProfile);

    when(mockAsync.getExternalTransferAccounts()).thenReturn(externalTransferAccountsFork);
    when(mockAsync.getWebPaymentProfile()).thenReturn(webPaymentProfileFork);
    when(mockAsync.getInvoicedPaymentProfile()).thenReturn(invoicedPaymentProfileFork);

    var externalUserAccounts = externalAccountManagementService.getAccounts();

    assertEquals(
        ExternalAccountResponseStatus.EXTERNAL_TRANSFER_FAILURE
            + ","
            + ExternalAccountResponseStatus.WEB_PAYMENT_FAILURE,
        externalUserAccounts.getStatus());
    assertTrue(externalUserAccounts.getInvoicedPaymentAccounts().size() > 0);
    assertEquals(0, externalUserAccounts.getTransferAccounts().size());
    assertEquals(0, externalUserAccounts.getPaymentAccounts().size());
  }

  @Test
  void getAccounts_throwsServiceUnavailableException_whenAllThreeRequestsFail() {
    var externalTransferAccountsFork = new CompletableFuture<List<BffExternalAccount>>();
    externalTransferAccountsFork.completeExceptionally(new Exception("some exception"));

    var webPaymentProfileFork = new CompletableFuture<CesWebpaymentProfileResponse>();
    webPaymentProfileFork.completeExceptionally(new Exception("some exception"));

    var invoicedPaymentProfileFork = new CompletableFuture<CesInvoicedPaymentAccountResponse>();
    invoicedPaymentProfileFork.completeExceptionally(
        BffException.serviceUnavailable("some exception"));

    when(mockAsync.getExternalTransferAccounts()).thenReturn(externalTransferAccountsFork);
    when(mockAsync.getWebPaymentProfile()).thenReturn(webPaymentProfileFork);
    when(mockAsync.getInvoicedPaymentProfile()).thenReturn(invoicedPaymentProfileFork);

    assertThrows(BffException.class, () -> externalAccountManagementService.getAccounts());
  }

  @Test
  public void getAccounts_shouldCatchInvalidStatusFromCESAndReturnEmptyList() {
    var exception = new CesFeignException(HttpStatus.BAD_REQUEST, "User designated as 'loan only'");
    // Should I make INVALID_REQUEST a constant?
    var cesResponse = new CesBaseResponse("INVALID_REQUEST", "NOT_ELIGIBLE", "INVALID_REQUEST");
    exception.setCesResponse(cesResponse);
    var externalTransferAccountsFork = new CompletableFuture<List<BffExternalAccount>>();
    externalTransferAccountsFork.completeExceptionally(exception);

    var cesWebpaymentProfile = getWebPaymentProfile();
    var webPaymentProfileFork = new CompletableFuture<CesWebpaymentProfileResponse>();
    webPaymentProfileFork.complete(cesWebpaymentProfile);

    var invoicedPaymentProfile = getFundingAccount();
    var invoicedPaymentProfileFork = new CompletableFuture<CesInvoicedPaymentAccountResponse>();
    invoicedPaymentProfileFork.complete(invoicedPaymentProfile);

    when(mockAsync.getExternalTransferAccounts()).thenReturn(externalTransferAccountsFork);
    when(mockAsync.getWebPaymentProfile()).thenReturn(webPaymentProfileFork);
    when(mockAsync.getInvoicedPaymentProfile()).thenReturn(invoicedPaymentProfileFork);

    var externalUserAccounts = externalAccountManagementService.getAccounts();

    assertEquals(
        ExternalAccountResponseStatus.SUCCESS.toString(), externalUserAccounts.getStatus());
    assertEquals(0, externalUserAccounts.getTransferAccounts().size());
    assertTrue(externalUserAccounts.getPaymentAccounts().size() > 0);
    assertTrue(externalUserAccounts.getInvoicedPaymentAccounts().size() > 0);
    assertEquals(5, externalUserAccounts.getMaxExternalTransferAccounts());
  }

  private List<BffExternalAccount> getCesUserAccounts() {
    return TestUtils.cesExternalAccountsToBffExternalAccounts(
        mockFromFile(
                "service/externaltransfer/userAccounts_success.json",
                CesGetUserAccountsResponse.class)
            .getAccountDataList());
  }

  private CesWebpaymentProfileResponse getWebPaymentProfile() {
    return mockFromFile(
        "service/webpayment/profile_success.json", CesWebpaymentProfileResponse.class);
  }

  private CesInvoicedPaymentAccountResponse getFundingAccount() {
    return mockFromFile(
        "service/invoicedpayment/invoiced_payment_accounts_response.json",
        CesInvoicedPaymentAccountResponse.class);
  }
}
