/* Copyright 2025 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.utils;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.fitb.digital.bff.movemoney.util.CDFundUtil;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@Slf4j
@ExtendWith(MockitoExtension.class)
public class CDFundUtilTest {

  CDFundUtil cdfundUtil = new CDFundUtil();

  @Test
  public void testValidCutoffTime() {
    ZonedDateTime zonedDateTime =
        ZonedDateTime.of(2025, 1, 13, 10, 30, 0, 0, ZoneId.of("America/New_York"));

    boolean result = cdfundUtil.isCutoffTime(zonedDateTime, new ArrayList<>());
    assertFalse(result);
  }

  @Test
  public void testAfterCutoffTimeHour() {
    ZonedDateTime zonedDateTime =
        ZonedDateTime.of(2025, 1, 13, 20, 30, 0, 0, ZoneId.of("America/New_York"));
    boolean result = cdfundUtil.isCutoffTime(zonedDateTime, new ArrayList<>());
    assertTrue(result);
  }

  @Test
  public void testAfterCutoffTimeMinute() {
    ZonedDateTime zonedDateTime =
        ZonedDateTime.of(2025, 1, 13, 19, 31, 0, 0, ZoneId.of("America/New_York"));
    boolean result = cdfundUtil.isCutoffTime(zonedDateTime, new ArrayList<>());
    assertTrue(result);
  }

  @Test
  public void testWeekendSATCutoffTime() {
    ZonedDateTime zonedDateTime =
        ZonedDateTime.of(2025, 1, 11, 10, 30, 0, 0, ZoneId.of("America/New_York"));
    boolean result = cdfundUtil.isCutoffTime(zonedDateTime, new ArrayList<>());
    assertTrue(result);
  }

  @Test
  public void testWeekendSUNCutoffTime() {
    ZonedDateTime zonedDateTime =
        ZonedDateTime.of(2025, 1, 12, 10, 30, 0, 0, ZoneId.of("America/New_York"));
    boolean result = cdfundUtil.isCutoffTime(zonedDateTime, new ArrayList<>());
    assertTrue(result);
  }

  @Test
  public void testHolidayCutoffTime() {
    ZonedDateTime zdt = ZonedDateTime.now(ZoneId.of("America/New_York"));
    List<LocalDate> holidayList = new ArrayList<>();
    holidayList.add(zdt.toLocalDate());

    boolean result = cdfundUtil.isCutoffTime(zdt, holidayList);
    assertTrue(result);
  }
}
