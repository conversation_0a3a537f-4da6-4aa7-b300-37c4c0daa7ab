/* Copyright 2022 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.service.activity;

import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.when;

import com.fitb.digital.bff.movemoney.client.CesClient;
import com.fitb.digital.bff.movemoney.model.client.CesResponse;
import com.fitb.digital.bff.movemoney.model.client.activity.responses.ClientActivityResponse;
import com.fitb.digital.bff.movemoney.utils.TestUtils;
import java.util.concurrent.ExecutionException;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@Slf4j
@ExtendWith(MockitoExtension.class)
class ActivityServiceV1AsyncTest {
  @InjectMocks private ActivityServiceAsync activityService;

  @Mock private CesClient activityClient;

  @Test
  void verifyGetActivity() throws ExecutionException, InterruptedException {
    ClientActivityResponse getClientActivityResponse = new ClientActivityResponse();
    when(activityClient.getTransferAndPayActivity()).thenReturn(getClientActivityResponse);
    ClientActivityResponse responseClient = activityService.getActivityAsync().get();
    assertNotNull(responseClient);
  }

  @Test
  void getActivityWhenProfileResponseIsValid() throws ExecutionException, InterruptedException {
    ClientActivityResponse getClientActivityResponse = TestUtils.mockCESActivityResponse();
    when(activityClient.getTransferAndPayActivity()).thenReturn(getClientActivityResponse);
    ClientActivityResponse responseClient = activityService.getActivityAsync().get();
    assertNotNull(responseClient);
  }

  @Test
  void getActivityReturnsErrorIfGetTransferAndPayActivityFails()
      throws ExecutionException, InterruptedException {
    when(activityClient.getTransferAndPayActivity())
        .thenReturn(new ClientActivityResponse(CesResponse.ERROR, "", ""));
    var response = activityService.getActivityAsync();
    assertNotEquals(CesResponse.SUCCESS, response.get().getStatus());
  }
}
