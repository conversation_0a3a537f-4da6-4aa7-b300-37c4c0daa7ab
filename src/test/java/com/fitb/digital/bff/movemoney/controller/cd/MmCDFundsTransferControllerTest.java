/* Copyright 2024 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.controller.cd;

import static com.fitb.digital.bff.movemoney.utils.TestUtils.mockFromFile;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.when;

import com.fitb.digital.bff.movemoney.model.bff.cd.requests.CDFundTransferRequest;
import com.fitb.digital.bff.movemoney.model.bff.cd.responses.CDFundTransferResponse;
import com.fitb.digital.bff.movemoney.service.cd.CDFundsTransferService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
@Slf4j
public class MmCDFundsTransferControllerTest {

  @InjectMocks private MmCDFundsTransferController controller;
  @Mock private CDFundsTransferService service;

  @Test
  void redeemCD() {
    CDFundTransferRequest request = new CDFundTransferRequest();
    when(service.redeemCD(request))
        .thenReturn(mockFromFile("stubs/cd-redeem.json", CDFundTransferResponse.class));
    var response = controller.redeemCD(request);
    assertNotNull(response);
    assertEquals(CDFundTransferResponse.class, response.getClass());
    assertEquals("fse43-uye2423fsdf-fsff2342v-3242g", response.getTransactionReferenceNumber());
  }
}
