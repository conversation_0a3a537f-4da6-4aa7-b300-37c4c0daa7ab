/* Copyright 2022 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.controller.externaltransfer;

import static com.fitb.digital.bff.movemoney.utils.TestUtils.mockFromFile;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.when;

import com.fitb.digital.bff.movemoney.exceptions.BffException;
import com.fitb.digital.bff.movemoney.model.bff.BffResponse;
import com.fitb.digital.bff.movemoney.model.bff.BffSimpleResponse;
import com.fitb.digital.bff.movemoney.model.bff.external.transfer.ExternalAccountType;
import com.fitb.digital.bff.movemoney.model.bff.external.transfer.requests.*;
import com.fitb.digital.bff.movemoney.model.bff.external.transfer.responses.*;
import com.fitb.digital.bff.movemoney.service.externaltransfer.ExternalTransferService;
import com.fitb.digital.bff.movemoney.service.externaltransfer.VerificationService;
import java.util.ArrayList;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

@ExtendWith(MockitoExtension.class)
public class MmExternalTransferControllerTest {
  @Mock private ExternalTransferService transferService;
  @Mock private VerificationService verificationService;
  @Mock private ExternalTransferInputValidator validator;

  @InjectMocks private MmExternalTransferController controller;

  BffBrokerageListResponse getBrokerageListResponse() {
    BffBrokerageListResponse brokerageListResponse = new BffBrokerageListResponse();
    brokerageListResponse.setStatus("200");
    brokerageListResponse.setStatusCode("OK");

    var brokerageList = new ArrayList<BrokerageEntry>();
    var entry = new BrokerageEntry();
    entry.setName("brokerage name");
    entry.setRoutingNumber("*********");
    entry.setCreditRoutingNumber("*********");
    brokerageList.add(entry);

    brokerageListResponse.setBrokerages(brokerageList);

    return brokerageListResponse;
  }

  @Test
  void accountVerificationInfo_mapsResponseAndReturnsOk() {
    var verificationInfo = new BffExternalTransferAccountVerificationInfo();
    verificationInfo.setTrialDepositVerification(true);

    when(transferService.getAccountVerificationInfo(any())).thenReturn(verificationInfo);
    var response = controller.getAccountVerificationInfo("account Id");
    assertTrue(response.isTrialDepositVerification());
  }

  @Test
  void controllerMapsCesBrokerageResponse() {
    when(transferService.getBrokerages()).thenReturn(getBrokerageListResponse());
    var response = controller.getBrokerageList();
    assertFalse(response.getBrokerages().isEmpty());
  }

  @Test
  void getBankInfo_returnsOk_whenValidRoutingSupplied() {
    when(transferService.getFinancialInstitutionInfoForRoutingNumber(anyString()))
        .thenReturn(
            mockFromFile(
                "controller/external/transfer/financialInstitutionInfo_loginInfo.json",
                BffFinancialInstitutionInfo.class));
    ResponseEntity<BffFinancialInstitutionInfo> responseEntity =
        controller.getBankInfo("*********", null);
    assertEquals(HttpStatus.OK, responseEntity.getStatusCode());
    assertEquals(2, responseEntity.getBody().getFinInsLoginInfoList().size());
  }

  @Test
  void getBankInfo_returnsOk_whenValidBrokerageNameSupplied() {
    when(transferService.getFinancialInstitutionInfoForBrokerageName(anyString()))
        .thenReturn(
            mockFromFile(
                "controller/external/transfer/financialInstitutionInfo_loginInfo.json",
                BffFinancialInstitutionInfo.class));
    ResponseEntity<BffFinancialInstitutionInfo> responseEntity =
        controller.getBankInfo(null, "Some Brokerage");
    assertEquals(HttpStatus.OK, responseEntity.getStatusCode());
    assertEquals(2, responseEntity.getBody().getFinInsLoginInfoList().size());
    assertEquals("10796", responseEntity.getBody().getFinInsLoginInfoList().get(0).getParamId());
    assertEquals(
        "User ID", responseEntity.getBody().getFinInsLoginInfoList().get(0).getParamCaption());
    assertEquals(30, responseEntity.getBody().getFinInsLoginInfoList().get(0).getParamMaxLength());
  }

  @Test
  void getBankInfo_returnsNotFound_whenValidBrokerageNameSupplied() {
    when(transferService.getFinancialInstitutionInfoForBrokerageName(anyString()))
        .thenReturn(
            mockFromFile(
                "controller/external/transfer/financialInstitutionInfo_notFound.json",
                BffFinancialInstitutionInfo.class));
    final BffException actual =
        assertThrows(BffException.class, () -> controller.getBankInfo(null, "Some Brokerage"));
    final BffException expected = BffException.badRequest("Institution name not found");
    assertEquals(expected, actual);
  }

  @Test
  void getBankInfo_invalidRequest() {
    final String routingNumber = "*********";
    final String brokerageName = "brokerage name";
    final BffException expected = BffException.badRequest("it's a bad request");
    doThrow(expected).when(validator).validateGetBankInfoInput(routingNumber, brokerageName);
    final BffException actual =
        assertThrows(
            BffException.class, () -> controller.getBankInfo(routingNumber, brokerageName));
    assertEquals(expected, actual);
  }

  @Test
  void addAccount_happyPath_returnsOK() {
    var bffAddAccountResponse = new BffAddAccountResponse();
    var externalAccountVerificationStatus = new ExternalAccountVerificationStatus();
    externalAccountVerificationStatus.setVerificationStatus("NotAvailable");
    bffAddAccountResponse.setExternalAccountVerificationStatus(externalAccountVerificationStatus);
    when(transferService.addExternalAccount(any())).thenReturn(bffAddAccountResponse);
    ResponseEntity<BffAddAccountResponse> responseEntity =
        controller.addAccount(getAddAccountRequest());
    assertEquals(HttpStatus.OK, responseEntity.getStatusCode());
    assertEquals(
        "NotAvailable",
        responseEntity.getBody().getExternalAccountVerificationStatus().getVerificationStatus());
  }

  @Test
  void addAccount_invalidRequest() {
    final BffAddAccountRequest request = getAddAccountRequest();
    final BffException expected = BffException.badRequest("it's a bad request");
    doThrow(expected).when(validator).validateAddAccountInput(request);
    final BffException actual =
        assertThrows(BffException.class, () -> controller.addAccount(request));
    assertEquals(expected, actual);
  }

  @Test
  void realTimeVerificationHappyPath() {
    when(verificationService.verifyExternalAccountRealTime(any()))
        .thenReturn(
            mockFromFile(
                "bff_verify_extern_real_time_rsp.json", BffRealTimeVerificationResponse.class));
    var response =
        controller.verifyAccount(
            mockFromFile("verify_external_real_time.json", BffRealTimeVerificationRequest.class));

    assertEquals("SUCCESS", response.getStatus());
    assertEquals("5092", response.getVerificationStatus().getStatusCode());
    assertEquals("X0000", response.getVerificationStatus().getAccountNumber());
    assertEquals(
        "Two FA Online Account Verification", response.getVerificationStatus().getStatusDesc());
    assertEquals("online", response.getVerificationStatus().getVerificationMode());
    assertEquals("Requires Approval", response.getVerificationStatus().getVerificationStatus());
    assertEquals("303", response.getVerificationStatus().getFailureReasonCode());
    assertEquals("303", response.getVerificationStatus().getFailureReasonDesc());

    assertEquals("answer", response.getParameters().get(0).getAuthId());
    assertEquals("What is your favorite color?", response.getParameters().get(0).getAuthValue());
  }

  private BffAddAccountRequest getAddAccountRequest() {
    var bffAddAccountRequest = new BffAddAccountRequest();
    bffAddAccountRequest.setAccountNickName("Nickname");
    bffAddAccountRequest.setAccountTypeCode(ExternalAccountType.CHECKING);
    bffAddAccountRequest.setRoutingNumber("*********");
    bffAddAccountRequest.setAccountNumber("0*********");
    bffAddAccountRequest.setCreditAccountNumber("0*********");
    bffAddAccountRequest.setCreditRoutingNumber("*********");

    return bffAddAccountRequest;
  }

  @Test
  void verifyTrialDeposit_happyPath_returnsOK() {
    when(verificationService.verifyTrialDeposits(any())).thenReturn(new BffSimpleResponse());
    var response = controller.verifyTrialDeposits(new BffVerifyTrialDepositsRequest());
    assertNotNull(response);
  }

  @Test
  void verifyAddExternalAccountWithTrialDepositHappyPath() {
    var status = new ExternalAccountVerificationStatus();
    status.setStatusCode("SUCCESS");
    status.setVerificationMode("trial-deposit");
    status.setVerificationStatus("Requires Approval");
    var bffResponse = new BffAddAccountResponse();
    bffResponse.setStatus("SUCCESS");
    bffResponse.setExternalAccountVerificationStatus(status);
    when(verificationService.initiateTrialDeposits(any(), anyString(), any()))
        .thenReturn(bffResponse);
    var request = new BffInitiateTrialDepositsRequest();
    request.setAccountId("dont-tell-the-boss");
    request.setBrokerageName("some brokerage");
    var response = controller.initiateTrialDeposits(request);

    assertNotNull(response);
  }

  @Test
  void updateAccountNickname_returnsOK() {
    when(transferService.updateExternalAccountNickname(any()))
        .thenReturn(new BffSimpleResponse().status(BffResponse.SUCCESS));
    ResponseEntity<BffSimpleResponse> responseEntity =
        controller.updateAccountNickname(
            new BffUpdateExtAccountNicknameRequest("account-id", "new nickname"));
    assertEquals(HttpStatus.OK, responseEntity.getStatusCode());
  }

  @Test
  void deleteAccount_returnsOk() {
    when(transferService.deleteExternalAccount(any())).thenReturn(new BffSimpleResponse());
    var response = controller.deleteAccount("eat-pray-love");
    assertNotNull(response);
  }

  // TODO: Uncomment when we put this back in
  //  @Test
  //  void initiateTrialDeposits_returns400_whenNoBrokerageOrRoutingNumberProvided() {
  //    var request = new BffInitiateTrialDepositsRequest();
  //    request.setAccountId("1234");
  //
  //    var response = controller.initiateTrialDeposits(request);
  //    assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
  //  }
}
