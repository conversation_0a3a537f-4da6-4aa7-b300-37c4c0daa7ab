/* Copyright 2024 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.service.cd;

import static com.fitb.digital.bff.movemoney.utils.TestUtils.mockFromFile;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.when;

import com.fitb.digital.bff.movemoney.client.CDClient;
import com.fitb.digital.bff.movemoney.model.bff.cd.responses.CDFundingAccountsListResponse;
import com.fitb.digital.bff.movemoney.model.client.account.responses.InternalAccount;
import com.fitb.digital.bff.movemoney.model.client.account.responses.ListResponse;
import com.fitb.digital.bff.movemoney.service.account.AccountServiceAsync;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@Slf4j
@ExtendWith(MockitoExtension.class)
public class CDFundingAccountServiceTest {

  @InjectMocks private CDFundingAccountService cdfundingAccountService;

  @Mock private CDClient cdClient;

  @Mock private AccountServiceAsync accountServiceAsync;

  @Mock private CompletableFuture<ListResponse> completableFuture;

  @Test
  void getCDFundingAccounts() {
    when(cdClient.getCDFundingAccounts())
        .thenReturn(
            mockFromFile("stubs/cd-funding-accounts.json", CDFundingAccountsListResponse.class));

    when(accountServiceAsync.getAccountListAsync(true)).thenReturn(getAccountList());

    var response = cdfundingAccountService.getCDFundingAccounts();
    assertNotNull(response);
    assertEquals(CDFundingAccountsListResponse.class, response.getClass());
    assertEquals(3, response.getInternalAccounts().size());

    // validates external list is empty
    assertEquals(0, response.getExternalAccounts().size());

    // validates if the accountNumbers are removed as they are not used
    assertEquals("", response.getInternalAccounts().get(0).getAccountNumber());
    assertEquals("", response.getInternalAccounts().get(1).getAccountNumber());
    assertEquals("", response.getInternalAccounts().get(2).getAccountNumber());

    // validates id the display names are set for internal accounts
    assertEquals(
        "FIFTH THIRD MOMENTUM CHECKING",
        response.getInternalAccounts().get(0).getAccountDisplayName());
    assertEquals(
        "FIFTH THIRD MOMENTUM SAVING",
        response.getInternalAccounts().get(1).getAccountDisplayName());
    assertEquals(
        "5/3 RELATIONSHIP MONEY MARKET",
        response.getInternalAccounts().get(2).getAccountDisplayName());
  }

  private CompletableFuture<ListResponse> getAccountList() {
    ListResponse response = new ListResponse();
    return CompletableFuture.supplyAsync(
        () -> {
          try {
            List<InternalAccount> internalAccounts = new ArrayList<>();
            InternalAccount account = null;

            account = new InternalAccount();
            account.setId("d85f4f2d-4de1-43c1-9a6d-96b9af54f2fb");
            account.setDisplayName("FIFTH THIRD MOMENTUM CHECKING");
            internalAccounts.add(account);

            account = new InternalAccount();
            account.setId("u5j7492n-h576-g3c6-b5h7-4w89bf23fb4u");
            account.setDisplayName("FIFTH THIRD MOMENTUM SAVING");
            internalAccounts.add(account);

            account = new InternalAccount();
            account.setId("rfsf542d-45w1-u7c4-fs3e-43fsafsd52am");
            account.setDisplayName("5/3 EXPRESS BANKING");
            internalAccounts.add(account);

            account = new InternalAccount();
            account.setId("qk5f4e5g-3dq7-yrc5-345s-fsde43d745fs");
            account.setDisplayName("5/3 RELATIONSHIP MONEY MARKET");
            internalAccounts.add(account);

            response.setAccounts(internalAccounts);
          } catch (Exception e) {
          }
          return response;
        });
  }
}
