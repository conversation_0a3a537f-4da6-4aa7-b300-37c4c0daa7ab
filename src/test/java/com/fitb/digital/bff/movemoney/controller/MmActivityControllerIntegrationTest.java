/* Copyright 2022 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.controller;

import static com.fitb.digital.bff.movemoney.featureflags.FeatureFlagNames.ORCHESTRATOR_ENABLED;
import static com.fitb.digital.bff.movemoney.utils.TestUtils.mockFromFile;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.fitb.digital.bff.movemoney.client.CesClient;
import com.fitb.digital.bff.movemoney.client.xferandpayorc.XferAndPayOrcClient;
import com.fitb.digital.bff.movemoney.client.xferandpayorc.model.XferAndPayActivity;
import com.fitb.digital.bff.movemoney.exceptions.CesFeignException;
import com.fitb.digital.bff.movemoney.featureflags.FeatureFlagNames;
import com.fitb.digital.bff.movemoney.featureflags.FeatureFlagVariants;
import com.fitb.digital.bff.movemoney.model.client.CesBaseResponse;
import com.fitb.digital.lib.featureflag.service.FeatureFlagService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

@SpringBootTest
@AutoConfigureMockMvc
public class MmActivityControllerIntegrationTest {
  @Autowired private MockMvc mockMvc;

  @MockBean FeatureFlagService featureFlagService;

  @MockBean XferAndPayOrcClient xferAndPayOrcClient;

  @MockBean CesClient cesClient;

  static final String BEARER_TOKEN =
      "Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6IlFac0xydXlkWEdPQ01qaGk4OXdldGlKa25aVSIsInBpLmF0bSI6Im51eWoifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NsXi0ciImrNR9gsrfQNAO34RKbqMcCPIZjjt8KU3i0xLe86vM_Q4FP8JF8lLdgCJAOUc3lZOTMxD72MPh7TiDJWs7uDZBE7TROIUfSMen4l8mGFRsSF9n4xqThTt93CkdXEoBFh4AuV8SXFYc8vTrDK0cVRWgos918AbpknfYwWxNZZnFzaXsbL_F7wXxx1cQc_GjYb1_ETVmOwKMkIwI_wd-xDbrOC2cOtX0mtxX3Cp3xsYf22324nl3kp8-ObAfcQ4XwzptQ9ReW3XHl3bkbDjPw7ZtA47SNflCgndNXinzEBvL71pwCAlDTdEzcnYHbLUwXSxvaJW-Meri4maSA";

  @Test
  void addActivityHappyPath() throws Exception {
    var content =
        """
                    {
                                "id": "52FD8076-2D32-A656-C39C-22DA61D720A6",
                                "displayId": "YBABJZZF",
                                "fromAccountId": "abf3228b-5066-4729-8e15-b7d44afc0a8a",
                                "fromAccountNumber": "X1579",
                                "fromAccountName": "Fifth Third Momentum Checking",
                                "toAccountId": "4E82015B-4DF8-ECB1-5FD7-2ECAE0C276E2",
                                "toAccountNumber": null,
                                "toAccountType": null,
                                "toAccountName": "Bill",
                                "amount": 0.55,
                                "expressDelivery": false,
                                "additionalPrincipleAmount": null,
                                "memo": "Test",
                                "creationDate": null,
                                "deliveryDate": null,
                                "endDate": null,
                                "dueDate": "2024-03-27",
                                "frequency": null,
                                "numberOfActivities": null,
                                "numberOfRemainingActivities": null,
                                "status": "SCHEDULED",
                                "displayStatus": "Scheduled",
                                "paymentAmountType": null,
                                "payOnType": null,
                                "dayToPay": null,
                                "createTimestamp": null,
                                "editable": true,
                                "cancelable": true,
                                "type": "BILLPAY",
                                "activityType": "BILLPAY",
                                "checkNumber": "9027",
                                "recurringId": "2AEBE082-956D-AF28-7ABB-7DCDB7D681D1",
                                "toAccountInternal": false,
                                "fromAccountInternal": false,
                                "earliestAvailableDate": null,
                                "latestAvailableDate": null,
                                "automaticPaymentOn": false,
                                "activityClassification": null,
                                "seriesTemplate": false,
                                "normalizedDisplayStatus": "Canceled"
                            }""";
    XferAndPayActivity xferAndPayActivityResponse =
        mockFromFile(
            "xfer_and_pay_BILLPAY_activty_response_success.json", XferAndPayActivity.class);

    when(xferAndPayOrcClient.postTransferAndPayActivity(any()))
        .thenReturn(xferAndPayActivityResponse);
    when(featureFlagService.getFeatureVariant(anyString(), anyString()))
        .thenReturn(FeatureFlagVariants.ENABLED);
    when(featureFlagService.isFeatureEnabled(FeatureFlagNames.ORCHESTRATOR_ENABLED))
        .thenReturn(true);
    this.mockMvc
        .perform(
            post("/activity")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .content(content)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk());
  }

  @Test
  void addActivityInvalidDueDateException() throws Exception {
    var content =
        """
                          {
                                      "id": "52FD8076-2D32-A656-C39C-22DA61D720A6",
                                      "displayId": "YBABJZZF",
                                      "fromAccountId": "abf3228b-5066-4729-8e15-b7d44afc0a8a",
                                      "fromAccountNumber": "X1579",
                                      "fromAccountName": "Fifth Third Momentum Checking",
                                      "toAccountId": "4E82015B-4DF8-ECB1-5FD7-2ECAE0C276E2",
                                      "toAccountNumber": null,
                                      "toAccountType": null,
                                      "toAccountName": "Bill",
                                      "amount": 0.55,
                                      "expressDelivery": false,
                                      "additionalPrincipleAmount": null,
                                      "memo": "Test",
                                      "creationDate": null,
                                      "deliveryDate": null,
                                      "endDate": null,
                                      "dueDate": "2024-03-27",
                                      "frequency": null,
                                      "numberOfActivities": null,
                                      "numberOfRemainingActivities": null,
                                      "status": "SCHEDULED",
                                      "displayStatus": "Scheduled",
                                      "paymentAmountType": null,
                                      "payOnType": null,
                                      "dayToPay": null,
                                      "createTimestamp": null,
                                      "editable": true,
                                      "cancelable": true,
                                      "type": "BILLPAY",
                                      "activityType": "BILLPAY",
                                      "checkNumber": "9027",
                                      "recurringId": "2AEBE082-956D-AF28-7ABB-7DCDB7D681D1",
                                      "toAccountInternal": false,
                                      "fromAccountInternal": false,
                                      "earliestAvailableDate": null,
                                      "latestAvailableDate": null,
                                      "automaticPaymentOn": false,
                                      "activityClassification": null,
                                      "seriesTemplate": false,
                                      "normalizedDisplayStatus": "Canceled",
                                      "scheduleImmediately": true
                                  }""";
    XferAndPayActivity xferAndPayActivityResponse =
        mockFromFile(
            "xfer_and_pay_BILLPAY_activty_response_success.json", XferAndPayActivity.class);

    when(xferAndPayOrcClient.postTransferAndPayActivity(any()))
        .thenReturn(xferAndPayActivityResponse);
    when(featureFlagService.getFeatureVariant(anyString(), anyString()))
        .thenReturn(FeatureFlagVariants.ENABLED);
    when(featureFlagService.isFeatureEnabled(FeatureFlagNames.ORCHESTRATOR_ENABLED))
        .thenReturn(true);

    this.mockMvc
        .perform(
            post("/activity")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .content(content)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andExpect(MockMvcResultMatchers.jsonPath("$.status").value("ERROR"))
        .andExpect(MockMvcResultMatchers.jsonPath("$.statusCode").value("INVALID_DUE_DATE"))
        .andExpect(
            MockMvcResultMatchers.jsonPath("$.statusReason")
                .value(
                    "Due date is more than one day outside of today's date in eastern time for 'scheduleImmediately'"));
  }

  @Test
  void editActivityBillpayHappyPath() throws Exception {
    var content =
        """
                    {
                                "id": "52FD8076-2D32-A656-C39C-22DA61D720A6",
                                "displayId": "YBABJZZF",
                                "fromAccountId": "abf3228b-5066-4729-8e15-b7d44afc0a8a",
                                "fromAccountNumber": "X1579",
                                "fromAccountName": "Fifth Third Momentum Checking",
                                "toAccountId": "4E82015B-4DF8-ECB1-5FD7-2ECAE0C276E2",
                                "toAccountNumber": null,
                                "toAccountType": null,
                                "toAccountName": "Bill",
                                "amount": 0.55,
                                "expressDelivery": false,
                                "additionalPrincipleAmount": null,
                                "memo": "Test",
                                "creationDate": null,
                                "deliveryDate": null,
                                "endDate": null,
                                "dueDate": "2024-03-27",
                                "frequency": null,
                                "numberOfActivities": null,
                                "numberOfRemainingActivities": null,
                                "status": "SCHEDULED",
                                "displayStatus": "Scheduled",
                                "paymentAmountType": null,
                                "payOnType": null,
                                "dayToPay": null,
                                "createTimestamp": null,
                                "editable": true,
                                "cancelable": true,
                                "type": "BILLPAY",
                                "activityType": "BILLPAY",
                                "checkNumber": "9027",
                                "recurringId": "2AEBE082-956D-AF28-7ABB-7DCDB7D681D1",
                                "toAccountInternal": false,
                                "fromAccountInternal": false,
                                "earliestAvailableDate": null,
                                "latestAvailableDate": null,
                                "automaticPaymentOn": false,
                                "activityClassification": null,
                                "seriesTemplate": false,
                                "normalizedDisplayStatus": "Canceled"
                            }""";
    XferAndPayActivity xferAndPayActivityResponse =
        mockFromFile(
            "xfer_and_pay_BILLPAY_activty_response_success.json", XferAndPayActivity.class);

    when(xferAndPayOrcClient.editTransferAndPayActivity(any()))
        .thenReturn(xferAndPayActivityResponse);
    when(featureFlagService.getFeatureVariant(anyString(), anyString()))
        .thenReturn(FeatureFlagVariants.ENABLED);
    when(featureFlagService.isFeatureEnabled(FeatureFlagNames.ORCHESTRATOR_ENABLED))
        .thenReturn(true);
    this.mockMvc
        .perform(
            put("/activity")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .content(content)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk());
  }

  @Test
  void editActivityInvalidDueDateException() throws Exception {
    var content =
        """
                              {
                                          "id": "52FD8076-2D32-A656-C39C-22DA61D720A6",
                                          "displayId": "YBABJZZF",
                                          "fromAccountId": "abf3228b-5066-4729-8e15-b7d44afc0a8a",
                                          "fromAccountNumber": "X1579",
                                          "fromAccountName": "Fifth Third Momentum Checking",
                                          "toAccountId": "4E82015B-4DF8-ECB1-5FD7-2ECAE0C276E2",
                                          "toAccountNumber": null,
                                          "toAccountType": null,
                                          "toAccountName": "Bill",
                                          "amount": 0.55,
                                          "expressDelivery": false,
                                          "additionalPrincipleAmount": null,
                                          "memo": "Test",
                                          "creationDate": null,
                                          "deliveryDate": null,
                                          "endDate": null,
                                          "dueDate": "2024-03-27",
                                          "frequency": null,
                                          "numberOfActivities": null,
                                          "numberOfRemainingActivities": null,
                                          "status": "SCHEDULED",
                                          "displayStatus": "Scheduled",
                                          "paymentAmountType": null,
                                          "payOnType": null,
                                          "dayToPay": null,
                                          "createTimestamp": null,
                                          "editable": true,
                                          "cancelable": true,
                                          "type": "BILLPAY",
                                          "activityType": "BILLPAY",
                                          "checkNumber": "9027",
                                          "recurringId": "2AEBE082-956D-AF28-7ABB-7DCDB7D681D1",
                                          "toAccountInternal": false,
                                          "fromAccountInternal": false,
                                          "earliestAvailableDate": null,
                                          "latestAvailableDate": null,
                                          "automaticPaymentOn": false,
                                          "activityClassification": null,
                                          "seriesTemplate": false,
                                          "normalizedDisplayStatus": "Canceled",
                                          "scheduleImmediately": true
                                      }""";
    XferAndPayActivity xferAndPayActivityResponse =
        mockFromFile(
            "xfer_and_pay_BILLPAY_activty_response_success.json", XferAndPayActivity.class);

    when(xferAndPayOrcClient.editTransferAndPayActivity(any()))
        .thenReturn(xferAndPayActivityResponse);
    when(featureFlagService.getFeatureVariant(anyString(), anyString()))
        .thenReturn(FeatureFlagVariants.ENABLED);
    when(featureFlagService.isFeatureEnabled(FeatureFlagNames.ORCHESTRATOR_ENABLED))
        .thenReturn(true);

    this.mockMvc
        .perform(
            put("/activity")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .content(content)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andExpect(MockMvcResultMatchers.jsonPath("$.status").value("ERROR"))
        .andExpect(MockMvcResultMatchers.jsonPath("$.statusCode").value("INVALID_DUE_DATE"))
        .andExpect(
            MockMvcResultMatchers.jsonPath("$.statusReason")
                .value(
                    "Due date is more than one day outside of today's date in eastern time for 'scheduleImmediately'"));
  }

  @Test
  void cancelActivityReturnsBodyIfSuccessfulV1() throws Exception {
    when(featureFlagService.isFeatureEnabled(ORCHESTRATOR_ENABLED)).thenReturn(false);
    when(cesClient.cancelTransferAndPayActivity(any(String.class))).thenReturn("");
    this.mockMvc
        .perform(
            delete("/activity")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header(
                    "Authorization",
                    "Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6IlFac0xydXlkWEdPQ01qaGk4OXdldGlKa25aVSIsInBpLmF0bSI6Im51eWoifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NsXi0ciImrNR9gsrfQNAO34RKbqMcCPIZjjt8KU3i0xLe86vM_Q4FP8JF8lLdgCJAOUc3lZOTMxD72MPh7TiDJWs7uDZBE7TROIUfSMen4l8mGFRsSF9n4xqThTt93CkdXEoBFh4AuV8SXFYc8vTrDK0cVRWgos918AbpknfYwWxNZZnFzaXsbL_F7wXxx1cQc_GjYb1_ETVmOwKMkIwI_wd-xDbrOC2cOtX0mtxX3Cp3xsYf22324nl3kp8-ObAfcQ4XwzptQ9ReW3XHl3bkbDjPw7ZtA47SNflCgndNXinzEBvL71pwCAlDTdEzcnYHbLUwXSxvaJW-Meri4maSA")
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .queryParam("activityId", "asdf")
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(MockMvcResultMatchers.jsonPath("$.status").value("SUCCESS"));
  }

  @Test
  void cancelActivtyBubblesUpFeignException() throws Exception {
    var ex = new CesFeignException(HttpStatus.NOT_FOUND, "NOT_FOUND");
    ex.setCesResponse(
        new CesBaseResponse("NOT_FOUND", "TRANSFER_NOT_FOUND", "No transfer found with id asdf"));
    when(featureFlagService.isFeatureEnabled(ORCHESTRATOR_ENABLED)).thenReturn(true);
    when(xferAndPayOrcClient.cancelTransferAndPayActivity(any(), any())).thenThrow(ex);

    this.mockMvc
        .perform(
            delete("/activity")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header(
                    "Authorization",
                    "Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6IlFac0xydXlkWEdPQ01qaGk4OXdldGlKa25aVSIsInBpLmF0bSI6Im51eWoifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NsXi0ciImrNR9gsrfQNAO34RKbqMcCPIZjjt8KU3i0xLe86vM_Q4FP8JF8lLdgCJAOUc3lZOTMxD72MPh7TiDJWs7uDZBE7TROIUfSMen4l8mGFRsSF9n4xqThTt93CkdXEoBFh4AuV8SXFYc8vTrDK0cVRWgos918AbpknfYwWxNZZnFzaXsbL_F7wXxx1cQc_GjYb1_ETVmOwKMkIwI_wd-xDbrOC2cOtX0mtxX3Cp3xsYf22324nl3kp8-ObAfcQ4XwzptQ9ReW3XHl3bkbDjPw7ZtA47SNflCgndNXinzEBvL71pwCAlDTdEzcnYHbLUwXSxvaJW-Meri4maSA")
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .queryParam("activityId", "asdf")
                .queryParam("activityType", "INTERNAL_TRANSFER")
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isNotFound())
        .andExpect(MockMvcResultMatchers.jsonPath("$.status").value("NOT_FOUND"))
        .andExpect(MockMvcResultMatchers.jsonPath("$.statusCode").value("TRANSFER_NOT_FOUND"))
        .andExpect(
            MockMvcResultMatchers.jsonPath("$.statusReason")
                .value("No transfer found with id asdf"));
  }

  @Test
  void cancelActivtyBubblesUpFeignExceptionNoOrc() throws Exception {
    var ex = new CesFeignException(HttpStatus.NOT_FOUND, "NOT_FOUND");
    ex.setCesResponse(
        new CesBaseResponse("NOT_FOUND", "TRANSFER_NOT_FOUND", "No transfer found with id asdf"));
    when(featureFlagService.isFeatureEnabled(ORCHESTRATOR_ENABLED)).thenReturn(false);
    when(cesClient.cancelTransferAndPayActivity(any())).thenThrow(ex);

    this.mockMvc
        .perform(
            delete("/activity")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header(
                    "Authorization",
                    "Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6IlFac0xydXlkWEdPQ01qaGk4OXdldGlKa25aVSIsInBpLmF0bSI6Im51eWoifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NsXi0ciImrNR9gsrfQNAO34RKbqMcCPIZjjt8KU3i0xLe86vM_Q4FP8JF8lLdgCJAOUc3lZOTMxD72MPh7TiDJWs7uDZBE7TROIUfSMen4l8mGFRsSF9n4xqThTt93CkdXEoBFh4AuV8SXFYc8vTrDK0cVRWgos918AbpknfYwWxNZZnFzaXsbL_F7wXxx1cQc_GjYb1_ETVmOwKMkIwI_wd-xDbrOC2cOtX0mtxX3Cp3xsYf22324nl3kp8-ObAfcQ4XwzptQ9ReW3XHl3bkbDjPw7ZtA47SNflCgndNXinzEBvL71pwCAlDTdEzcnYHbLUwXSxvaJW-Meri4maSA")
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .queryParam("activityId", "asdf")
                .queryParam("activityType", "INTERNAL_TRANSFER")
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isNotFound())
        .andExpect(MockMvcResultMatchers.jsonPath("$.status").value("NOT_FOUND"))
        .andExpect(MockMvcResultMatchers.jsonPath("$.statusCode").value("TRANSFER_NOT_FOUND"))
        .andExpect(
            MockMvcResultMatchers.jsonPath("$.statusReason")
                .value("No transfer found with id asdf"));
  }

  @Test
  void cancelActivityPropagatesErrorFromDependency() throws Exception {
    when(featureFlagService.isFeatureEnabled(ORCHESTRATOR_ENABLED)).thenReturn(false);
    when(cesClient.cancelTransferAndPayActivity(any(String.class)))
        .thenThrow(new CesFeignException(HttpStatus.NOT_FOUND, "Not Found"));

    this.mockMvc
        .perform(
            delete("/activity")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header(
                    "Authorization",
                    "Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6IlFac0xydXlkWEdPQ01qaGk4OXdldGlKa25aVSIsInBpLmF0bSI6Im51eWoifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NsXi0ciImrNR9gsrfQNAO34RKbqMcCPIZjjt8KU3i0xLe86vM_Q4FP8JF8lLdgCJAOUc3lZOTMxD72MPh7TiDJWs7uDZBE7TROIUfSMen4l8mGFRsSF9n4xqThTt93CkdXEoBFh4AuV8SXFYc8vTrDK0cVRWgos918AbpknfYwWxNZZnFzaXsbL_F7wXxx1cQc_GjYb1_ETVmOwKMkIwI_wd-xDbrOC2cOtX0mtxX3Cp3xsYf22324nl3kp8-ObAfcQ4XwzptQ9ReW3XHl3bkbDjPw7ZtA47SNflCgndNXinzEBvL71pwCAlDTdEzcnYHbLUwXSxvaJW-Meri4maSA")
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .queryParam("activityId", "asdf")
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isNotFound())
        .andExpect(MockMvcResultMatchers.jsonPath("$.status").value("UNKNOWN_ERROR"));
  }

  @Test
  void cancelActivityFormatsErrorWithEmbeddedString() throws Exception {
    when(featureFlagService.isFeatureEnabled(ORCHESTRATOR_ENABLED)).thenReturn(false);
    when(cesClient.cancelTransferAndPayActivity(any(String.class)))
        .thenThrow(
            new CesFeignException(HttpStatus.UNAUTHORIZED, "Should work with \"Not Found\"."));

    this.mockMvc
        .perform(
            delete("/activity")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header(
                    "Authorization",
                    "Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6IlFac0xydXlkWEdPQ01qaGk4OXdldGlKa25aVSIsInBpLmF0bSI6Im51eWoifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NsXi0ciImrNR9gsrfQNAO34RKbqMcCPIZjjt8KU3i0xLe86vM_Q4FP8JF8lLdgCJAOUc3lZOTMxD72MPh7TiDJWs7uDZBE7TROIUfSMen4l8mGFRsSF9n4xqThTt93CkdXEoBFh4AuV8SXFYc8vTrDK0cVRWgos918AbpknfYwWxNZZnFzaXsbL_F7wXxx1cQc_GjYb1_ETVmOwKMkIwI_wd-xDbrOC2cOtX0mtxX3Cp3xsYf22324nl3kp8-ObAfcQ4XwzptQ9ReW3XHl3bkbDjPw7ZtA47SNflCgndNXinzEBvL71pwCAlDTdEzcnYHbLUwXSxvaJW-Meri4maSA")
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .queryParam("activityId", "asdf")
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isUnauthorized())
        .andExpect(MockMvcResultMatchers.jsonPath("$.status").value("UNKNOWN_ERROR"));
  }

  @Test
  void cancelActivityDoesNotRequireActivityTypeForActivityServiceV1() throws Exception {
    when(featureFlagService.isFeatureEnabled(ORCHESTRATOR_ENABLED)).thenReturn(false);
    when(cesClient.cancelTransferAndPayActivity(any())).thenReturn("");

    this.mockMvc
        .perform(
            delete("/activity")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header(
                    "Authorization",
                    "Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6IlFac0xydXlkWEdPQ01qaGk4OXdldGlKa25aVSIsInBpLmF0bSI6Im51eWoifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NsXi0ciImrNR9gsrfQNAO34RKbqMcCPIZjjt8KU3i0xLe86vM_Q4FP8JF8lLdgCJAOUc3lZOTMxD72MPh7TiDJWs7uDZBE7TROIUfSMen4l8mGFRsSF9n4xqThTt93CkdXEoBFh4AuV8SXFYc8vTrDK0cVRWgos918AbpknfYwWxNZZnFzaXsbL_F7wXxx1cQc_GjYb1_ETVmOwKMkIwI_wd-xDbrOC2cOtX0mtxX3Cp3xsYf22324nl3kp8-ObAfcQ4XwzptQ9ReW3XHl3bkbDjPw7ZtA47SNflCgndNXinzEBvL71pwCAlDTdEzcnYHbLUwXSxvaJW-Meri4maSA")
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .queryParam("activityId", "asdf")
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk());
  }

  @Test
  void addActivityReturnsErrorIfMemoIsTooLong() throws Exception {
    var content =
        """
        {
            "requestGuid": "move-money-test-0002",
            "fromAccountId": "e4488ebd-7fed-47ed-81ff-0ad1dd5272b8",
            "toAccountId": "0b7eae00-5317-4f8c-933c-3b5e51d3a1c5",
            "amount": 1.0,
            "dueDate": "2023-04-05",
            "frequency": "ONE_TIME",
            "numberOfTransactions": null,
            "memo": "123456789012345678901234567890123456789012345678901234567890",
            "additionalPrincipleAmount": null,
            "displayId": null
        }""";

    this.mockMvc
        .perform(
            post("/activity")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header(
                    "Authorization",
                    "Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6IlFac0xydXlkWEdPQ01qaGk4OXdldGlKa25aVSIsInBpLmF0bSI6Im51eWoifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NsXi0ciImrNR9gsrfQNAO34RKbqMcCPIZjjt8KU3i0xLe86vM_Q4FP8JF8lLdgCJAOUc3lZOTMxD72MPh7TiDJWs7uDZBE7TROIUfSMen4l8mGFRsSF9n4xqThTt93CkdXEoBFh4AuV8SXFYc8vTrDK0cVRWgos918AbpknfYwWxNZZnFzaXsbL_F7wXxx1cQc_GjYb1_ETVmOwKMkIwI_wd-xDbrOC2cOtX0mtxX3Cp3xsYf22324nl3kp8-ObAfcQ4XwzptQ9ReW3XHl3bkbDjPw7ZtA47SNflCgndNXinzEBvL71pwCAlDTdEzcnYHbLUwXSxvaJW-Meri4maSA")
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .content(content)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andExpect(MockMvcResultMatchers.jsonPath("$.status").exists());
  }
}
