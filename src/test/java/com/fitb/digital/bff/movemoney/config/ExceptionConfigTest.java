/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.config;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.fitb.digital.bff.movemoney.exceptions.CesFeignException;
import com.fitb.digital.bff.movemoney.model.client.CesBaseResponse;
import com.fitb.digital.bff.movemoney.model.client.CesResponse;
import com.fitb.digital.bff.movemoney.service.externaltransfer.ExternalTransferService;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.web.context.request.ServletWebRequest;
import org.springframework.web.context.request.WebRequest;

@SpringBootTest
@AutoConfigureMockMvc
@ActiveProfiles("unit-test")
class ExceptionConfigTest {
  @Autowired private MockMvc mockMvc;

  @MockBean private ExternalTransferService externalTransferService;

  private final String MOCK_HEADER_SUB = "sub";
  private final String MOCK_HEADER_SCOPE = "scope";
  private final String MOCK_HEADER_SCOPE_VALUE = "openid";

  @Test
  void cesFeignException() throws Exception {
    String uri = "/ces-feign-error";
    MvcResult mvcResult = callGet(uri, UUID.randomUUID());
    assertNotEquals(HttpStatus.OK.value(), mvcResult.getResponse().getStatus());
    assertEquals(HttpStatus.GATEWAY_TIMEOUT.value(), mvcResult.getResponse().getStatus());
  }

  @Test
  void cesFeignException_bubblesUpCesResponse() throws Exception {
    CesResponse cesResponse = new CesBaseResponse();
    cesResponse.setStatus("some status");
    cesResponse.setStatusCode("some status code");
    cesResponse.setStatusReason("some status reason");

    CesFeignException cesFeignException =
        new CesFeignException(HttpStatus.BAD_REQUEST, "some reason");
    cesFeignException.setCesResponse(cesResponse);

    when(externalTransferService.getBrokerages()).thenThrow(cesFeignException);

    mockMvc
        .perform(
            MockMvcRequestBuilders.get("/externaltransfer/brokerages")
                .header(MOCK_HEADER_SUB, UUID.randomUUID().toString())
                .header(MOCK_HEADER_SCOPE, MOCK_HEADER_SCOPE_VALUE)
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .accept(MediaType.APPLICATION_JSON_VALUE))
        .andExpect(status().isBadRequest())
        .andExpect(jsonPath("$.status").value("some status"))
        .andExpect(jsonPath("$.statusCode").value("some status code"))
        .andExpect(jsonPath("$.statusReason").value("some status reason"))
        .andReturn();
  }

  @Test
  void responseStatusException() throws Exception {
    String uri = "/response-status-error";
    MvcResult mvcResult = callGet(uri, UUID.randomUUID());
    assertNotEquals(HttpStatus.OK.value(), mvcResult.getResponse().getStatus());
    assertEquals(HttpStatus.BAD_GATEWAY.value(), mvcResult.getResponse().getStatus());
  }

  @Test
  void forbiddenExceptions() throws Exception {
    String uri = "/forbidden-error";
    MvcResult mvcResult = callGet(uri, UUID.randomUUID());
    assertNotEquals(HttpStatus.OK.value(), mvcResult.getResponse().getStatus());
    assertEquals(HttpStatus.FORBIDDEN.value(), mvcResult.getResponse().getStatus());
  }

  @Test
  void handleClientError() throws Exception {
    String uri = "/client-error";
    MvcResult mvcResult = callGet(uri, UUID.randomUUID());
    assertNotEquals(HttpStatus.OK.value(), mvcResult.getResponse().getStatus());
    assertEquals(HttpStatus.BAD_REQUEST.value(), mvcResult.getResponse().getStatus());
  }

  @Test
  void testHandleExceptionInternal() {
    Object body = "Test";
    HttpHeaders headers = HttpHeaders.EMPTY;
    HttpStatus status = HttpStatus.BAD_REQUEST;
    WebRequest request = new ServletWebRequest(new MockHttpServletRequest());
    ExceptionConfig config = new ExceptionConfig();
    assertNotNull(
        config.handleExceptionInternal(new Exception("Test"), body, headers, status, request));
  }

  @Test
  void feignClientException() throws Exception {
    mockMvc
        .perform(
            MockMvcRequestBuilders.get("/feign-client-error")
                .header(MOCK_HEADER_SUB, UUID.randomUUID().toString())
                .header(MOCK_HEADER_SCOPE, MOCK_HEADER_SCOPE_VALUE)
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .accept(MediaType.APPLICATION_JSON_VALUE))
        .andExpect(status().isBadRequest())
        .andExpect(jsonPath("$.status").value(HttpStatus.BAD_REQUEST.name()))
        .andExpect(jsonPath("$.statusCode").value(HttpStatus.BAD_REQUEST.value()))
        .andExpect(jsonPath("$.statusReason").value(HttpStatus.BAD_REQUEST.getReasonPhrase()))
        .andExpect(jsonPath("$.error.code").value(HttpStatus.BAD_REQUEST.name()))
        .andExpect(jsonPath("$.error.message").value(HttpStatus.BAD_REQUEST.getReasonPhrase()))
        .andExpect(jsonPath("$.error.target").value("/feign-client-error"))
        .andReturn();
  }

  @Test
  void feignServerException() throws Exception {
    mockMvc
        .perform(
            MockMvcRequestBuilders.get("/feign-server-error")
                .header(MOCK_HEADER_SUB, UUID.randomUUID().toString())
                .header(MOCK_HEADER_SCOPE, MOCK_HEADER_SCOPE_VALUE)
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .accept(MediaType.APPLICATION_JSON_VALUE))
        .andExpect(status().isInternalServerError())
        .andExpect(jsonPath("$.status").value(HttpStatus.INTERNAL_SERVER_ERROR.name()))
        .andExpect(jsonPath("$.statusCode").value(HttpStatus.INTERNAL_SERVER_ERROR.value()))
        .andExpect(
            jsonPath("$.statusReason").value(HttpStatus.INTERNAL_SERVER_ERROR.getReasonPhrase()))
        .andExpect(jsonPath("$.error.code").value(HttpStatus.INTERNAL_SERVER_ERROR.name()))
        .andExpect(
            jsonPath("$.error.message").value(HttpStatus.INTERNAL_SERVER_ERROR.getReasonPhrase()))
        .andExpect(jsonPath("$.error.target").value("/feign-server-error"))
        .andReturn();
  }

  private MvcResult callGet(final String uri, final UUID userId) throws Exception {
    return mockMvc
        .perform(
            MockMvcRequestBuilders.get(uri)
                .header(MOCK_HEADER_SUB, userId.toString())
                .header(MOCK_HEADER_SCOPE, MOCK_HEADER_SCOPE_VALUE)
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .accept(MediaType.APPLICATION_JSON_VALUE))
        .andReturn();
  }
}
