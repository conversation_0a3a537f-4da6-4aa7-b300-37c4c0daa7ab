/* Copyright 2023 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.config;

/* Copyright 2022 Fifth Third Bankcorp.  All rights reserved. */

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.fasterxml.jackson.core.JsonProcessingException;
import java.nio.charset.StandardCharsets;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springdoc.webmvc.api.OpenApiWebMvcResource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.test.util.ReflectionTestUtils;

public class SwaggerConfigTest {

  @Test
  void testCorsConfiguration() {
    var config = new SwaggerConfig();
    assertNotNull(config.corsConfigurationSource());
  }

  @Test
  void testEndpoint() throws JsonProcessingException {
    OpenApiWebMvcResource mockOpenApiWebMvcResource = Mockito.mock(OpenApiWebMvcResource.class);

    SwaggerConfig config = new SwaggerConfig();
    SwaggerConfig.SwaggerEndpoint endpoint = config.swaggerEndpoint();
    ReflectionTestUtils.setField(endpoint, "openApiWebMvcResource", mockOpenApiWebMvcResource);

    Mockito.when(mockOpenApiWebMvcResource.openapiJson(null, null, LocaleContextHolder.getLocale()))
        .thenReturn("answer".getBytes(StandardCharsets.UTF_8));

    String result = endpoint.loadApiDocs();
    assertEquals("answer", result);
  }
}
