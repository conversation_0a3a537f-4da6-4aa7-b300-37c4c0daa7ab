/* Copyright 2023 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.controller;

import static com.fitb.digital.bff.movemoney.utils.TestUtils.mockFromFile;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.fitb.digital.bff.movemoney.client.CesClient;
import com.fitb.digital.bff.movemoney.client.xferandpayorc.XferAndPayOrcClient;
import com.fitb.digital.bff.movemoney.client.xferandpayorc.model.XferAndPayActivity;
import com.fitb.digital.bff.movemoney.featureflags.FeatureFlagNames;
import com.fitb.digital.bff.movemoney.featureflags.FeatureFlagVariants;
import com.fitb.digital.bff.movemoney.model.bff.BffResponse;
import com.fitb.digital.lib.featureflag.service.FeatureFlagService;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

@SpringBootTest
@AutoConfigureMockMvc
public class InternalTransferSpringBootTest {
  @Autowired private MockMvc mockMvc;
  @MockBean private FeatureFlagService featureFlagService;

  @MockBean XferAndPayOrcClient xferAndPayOrcClient;
  @MockBean CesClient cesClient;

  static final String BEARER_TOKEN =
      "Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6IlFac0xydXlkWEdPQ01qaGk4OXdldGlKa25aVSIsInBpLmF0bSI6Im51eWoifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NsXi0ciImrNR9gsrfQNAO34RKbqMcCPIZjjt8KU3i0xLe86vM_Q4FP8JF8lLdgCJAOUc3lZOTMxD72MPh7TiDJWs7uDZBE7TROIUfSMen4l8mGFRsSF9n4xqThTt93CkdXEoBFh4AuV8SXFYc8vTrDK0cVRWgos918AbpknfYwWxNZZnFzaXsbL_F7wXxx1cQc_GjYb1_ETVmOwKMkIwI_wd-xDbrOC2cOtX0mtxX3Cp3xsYf22324nl3kp8-ObAfcQ4XwzptQ9ReW3XHl3bkbDjPw7ZtA47SNflCgndNXinzEBvL71pwCAlDTdEzcnYHbLUwXSxvaJW-Meri4maSA";

  @Test
  @DisplayName(
      """
                        Given a user tries to create a transfer
                        With a good request.
                        Then the response is OK.
                    """)
  void addActivityInternalTransferHappyPath() throws Exception {
    var content =
        """
                          {
                              "amount": 303.59,
                              "dueDate": "2023-12-19",
                              "frequency": "ONE_TIME",
                              "fromAccountId": "abf3228b-5066-4729-8e15-b7d44afc0a8a",
                              "invoicedRecurringPayment": false,
                              "memo": "qwert",
                              "requestGuid": "267512b1-c64e-4219-ba36-1be9b6a28811",
                              "toAccountId": "8a793e2a-21cc-4d50-af8a-76cab53935a2",
                              "scheduleImmediately": false,
                              "activityType": "INTERNAL_TRANSFER"
                          }
                        """;

    XferAndPayActivity xferAndPayActivityResponse =
        mockFromFile(
            "xfer_and_pay_INTERNAL_TRANSFER_activty_response_success.json",
            XferAndPayActivity.class);

    when(xferAndPayOrcClient.postTransferAndPayActivity(any()))
        .thenReturn(xferAndPayActivityResponse);
    when(featureFlagService.getFeatureVariant(anyString(), anyString()))
        .thenReturn(FeatureFlagVariants.ENABLED);
    when(featureFlagService.isFeatureEnabled(FeatureFlagNames.ORCHESTRATOR_ENABLED))
        .thenReturn(true);
    this.mockMvc
        .perform(
            post("/activity")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .content(content)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk());
  }

  @Test
  @DisplayName(
      """
                    Given a user tries to make a transfer
                    When the payments feature flag is disabled
                    Then the request is denied while passing status, status code, status reason and the newer standard error field.
                    """)
  void addActivityInternalTransfer_paymentsFlagDisabled_throwsBFFStatusPassingException()
      throws Exception {
    var content =
        """
                          {
                              "amount": 303.59,
                              "dueDate": "2023-12-19",
                              "frequency": "ONE_TIME",
                              "fromAccountId": "abf3228b-5066-4729-8e15-b7d44afc0a8a",
                              "invoicedRecurringPayment": false,
                              "memo": "qwert",
                              "requestGuid": "267512b1-c64e-4219-ba36-1be9b6a28811",
                              "toAccountId": "8a793e2a-21cc-4d50-af8a-76cab53935a2",
                              "scheduleImmediately": false,
                              "activityType": "INTERNAL_TRANSFER"
                          }
                        """;

    XferAndPayActivity xferAndPayActivityResponse =
        mockFromFile(
            "xfer_and_pay_INTERNAL_TRANSFER_activty_response_success.json",
            XferAndPayActivity.class);

    when(xferAndPayOrcClient.postTransferAndPayActivity(any()))
        .thenReturn(xferAndPayActivityResponse);
    when(featureFlagService.getFeatureVariant(
            FeatureFlagNames.PAYMENTS_STATUS, FeatureFlagVariants.ENABLED))
        .thenReturn(FeatureFlagVariants.ENABLED);
    when(featureFlagService.getFeatureVariant(
            FeatureFlagNames.TRANSFERS_STATUS, FeatureFlagVariants.ENABLED))
        .thenReturn(FeatureFlagVariants.PLANNED_MAINTENANCE);
    when(featureFlagService.isFeatureEnabled(FeatureFlagNames.ORCHESTRATOR_ENABLED))
        .thenReturn(true);

    this.mockMvc
        .perform(
            post("/activity")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .content(content)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isServiceUnavailable())
        .andExpect(MockMvcResultMatchers.jsonPath("$.status").value("ACTION_UNAVAILABLE"))
        .andExpect(MockMvcResultMatchers.jsonPath("$.statusCode").value("PLANNED_MAINTENANCE"))
        .andExpect(
            MockMvcResultMatchers.jsonPath("$.statusReason")
                .value("Activity is unavailable due to planned maintenance."));
  }

  @Test
  @DisplayName(
      """
                        Given a user tries to make a transfer
                        When the transfer feature flag is disabled
                        Then the request is denied while passing status, status code, status reason and the newer standard error field.
                        """)
  void addActivityInternalTransfer_transferFlagDisabled_throwsBFFStatusPassingException()
      throws Exception {
    var content =
        """
                              {
                                  "amount": 303.59,
                                  "dueDate": "2023-12-19",
                                  "frequency": "ONE_TIME",
                                  "fromAccountId": "abf3228b-5066-4729-8e15-b7d44afc0a8a",
                                  "invoicedRecurringPayment": false,
                                  "memo": "qwert",
                                  "requestGuid": "267512b1-c64e-4219-ba36-1be9b6a28811",
                                  "toAccountId": "8a793e2a-21cc-4d50-af8a-76cab53935a2",
                                  "scheduleImmediately": false,
                                  "activityType": "INTERNAL_TRANSFER"
                              }
                            """;

    XferAndPayActivity xferAndPayActivityResponse =
        mockFromFile(
            "xfer_and_pay_INTERNAL_TRANSFER_activty_response_success.json",
            XferAndPayActivity.class);

    when(xferAndPayOrcClient.postTransferAndPayActivity(any()))
        .thenReturn(xferAndPayActivityResponse);
    when(featureFlagService.getFeatureVariant(
            FeatureFlagNames.PAYMENTS_STATUS, FeatureFlagVariants.ENABLED))
        .thenReturn(FeatureFlagVariants.PLANNED_MAINTENANCE);
    when(featureFlagService.getFeatureVariant(
            FeatureFlagNames.TRANSFERS_STATUS, FeatureFlagVariants.ENABLED))
        .thenReturn(FeatureFlagVariants.ENABLED);
    when(featureFlagService.isFeatureEnabled(FeatureFlagNames.ORCHESTRATOR_ENABLED))
        .thenReturn(true);

    this.mockMvc
        .perform(
            post("/activity")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .content(content)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isServiceUnavailable())
        .andExpect(MockMvcResultMatchers.jsonPath("$.status").value("ACTION_UNAVAILABLE"))
        .andExpect(MockMvcResultMatchers.jsonPath("$.statusCode").value("PLANNED_MAINTENANCE"))
        .andExpect(
            MockMvcResultMatchers.jsonPath("$.statusReason")
                .value("Activity is unavailable due to planned maintenance."));
  }

  @Test
  @DisplayName(
      """
                        Given a user tries to make a payment
                            With an invalid due date two days in future
                            Then the request is denied with Bad Request status code.
                        """)
  void addActivityInternalTransfer_immediate_invalidDate_twoDaysInFuture() throws Exception {

    var invalidDate =
        ZonedDateTime.ofInstant(Instant.now(), ZoneId.of("America/New_York"))
            .toLocalDate()
            .plusDays(2);

    var content =
            """
                              {
                                  "amount": 303.59,
                                  "dueDate": "\
                                  """
            + invalidDate
            +
            """
                                  \",
                                  "frequency": "ONE_TIME",
                                  "fromAccountId": "abf3228b-5066-4729-8e15-b7d44afc0a8a",
                                  "invoicedRecurringPayment": false,
                                  "memo": "qwert",
                                  "requestGuid": "267512b1-c64e-4219-ba36-1be9b6a28811",
                                  "toAccountId": "8a793e2a-21cc-4d50-af8a-76cab53935a2",
                                  "scheduleImmediately": true,
                                  "activityType": "INTERNAL_TRANSFER"
                              }
                            """;

    XferAndPayActivity xferAndPayActivityResponse =
        mockFromFile(
            "xfer_and_pay_INTERNAL_TRANSFER_activty_response_success.json",
            XferAndPayActivity.class);

    when(xferAndPayOrcClient.postTransferAndPayActivity(any()))
        .thenReturn(xferAndPayActivityResponse);
    when(featureFlagService.getFeatureVariant(anyString(), anyString()))
        .thenReturn(FeatureFlagVariants.ENABLED);
    when(featureFlagService.isFeatureEnabled(FeatureFlagNames.ORCHESTRATOR_ENABLED))
        .thenReturn(true);

    this.mockMvc
        .perform(
            post("/activity")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .content(content)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andExpect(MockMvcResultMatchers.jsonPath("$.status").value(BffResponse.BFF_ERROR))
        .andExpect(MockMvcResultMatchers.jsonPath("$.statusCode").value("INVALID_DUE_DATE"))
        .andExpect(
            MockMvcResultMatchers.jsonPath("$.statusReason")
                .value(
                    "Due date is more than one day outside of today's date in eastern time for 'scheduleImmediately'"));
  }

  @Test
  @DisplayName(
      """
                            Given a user tries to make a payment
                            With an invalid due date two days in past
                            Then the request is denied with Bad Request status code.
                            """)
  void addActivityInternalTransfer_immediate_invalidDate_twoDaysInPast() throws Exception {

    var invalidDate =
        ZonedDateTime.ofInstant(Instant.now(), ZoneId.of("America/New_York"))
            .toLocalDate()
            .plusDays(-2);

    var content =
            """
                                  {
                                      "amount": 303.59,
                                      "dueDate": "\
                                      """
            + invalidDate
            +
            """
                                  \",
                                  "frequency": "ONE_TIME",
                                  "fromAccountId": "abf3228b-5066-4729-8e15-b7d44afc0a8a",
                                  "invoicedRecurringPayment": false,
                                  "memo": "qwert",
                                  "requestGuid": "267512b1-c64e-4219-ba36-1be9b6a28811",
                                  "toAccountId": "8a793e2a-21cc-4d50-af8a-76cab53935a2",
                                  "scheduleImmediately": true,
                                  "activityType": "INTERNAL_TRANSFER"
                              }
                            """;

    XferAndPayActivity xferAndPayActivityResponse =
        mockFromFile(
            "xfer_and_pay_INTERNAL_TRANSFER_activty_response_success.json",
            XferAndPayActivity.class);

    when(xferAndPayOrcClient.postTransferAndPayActivity(any()))
        .thenReturn(xferAndPayActivityResponse);
    when(featureFlagService.getFeatureVariant(anyString(), anyString()))
        .thenReturn(FeatureFlagVariants.ENABLED);
    when(featureFlagService.isFeatureEnabled(FeatureFlagNames.ORCHESTRATOR_ENABLED))
        .thenReturn(true);

    this.mockMvc
        .perform(
            post("/activity")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .content(content)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andExpect(MockMvcResultMatchers.jsonPath("$.status").value(BffResponse.BFF_ERROR))
        .andExpect(MockMvcResultMatchers.jsonPath("$.statusCode").value("INVALID_DUE_DATE"))
        .andExpect(
            MockMvcResultMatchers.jsonPath("$.statusReason")
                .value(
                    "Due date is more than one day outside of today's date in eastern time for 'scheduleImmediately'"));
  }

  @Test
  @DisplayName(
      """
                        Given a user tries to update a transfer
                        With a good request.
                        Then the response is OK.
                        """)
  void updateActivityInternalTransferHappyPath() throws Exception {
    var content =
        """
                    {
                        "id": "********",
                        "fromAccountId": "abf3228b-5066-4729-8e15-b7d44afc0a8a",
                        "toAccountId": "8a793e2a-21cc-4d50-af8a-76cab53935a2",
                        "activityType": "INTERNAL_TRANSFER",
                        "amount": 22.39,
                        "frequency": "ONE_TIME",
                        "dueDate": "2023-12-29",
                        "numberOfTransactions": 1,
                        "additionalPrincipleAmount": null
                    }

                                                """;

    XferAndPayActivity xferAndPayActivityResponse =
        mockFromFile(
            "xfer_and_pay_INTERNAL_TRANSFER_update_activty_response_success.json",
            XferAndPayActivity.class);

    when(xferAndPayOrcClient.editTransferAndPayActivity(any()))
        .thenReturn(xferAndPayActivityResponse);
    when(featureFlagService.getFeatureVariant(anyString(), anyString()))
        .thenReturn(FeatureFlagVariants.ENABLED);
    when(featureFlagService.isFeatureEnabled(FeatureFlagNames.ORCHESTRATOR_ENABLED))
        .thenReturn(true);
    this.mockMvc
        .perform(
            put("/activity")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .content(content)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk());
  }

  @Test
  @DisplayName(
      """
                            Given a user tries to update a transfer
                            With an invalid due date two days in the future.
                            Then the response is Bad Request.
                            """)
  void updateActivityInternalTransfer_InvalidDueDate_twoDaysFuture() throws Exception {
    var invalidDate =
        ZonedDateTime.ofInstant(Instant.now(), ZoneId.of("America/New_York"))
            .toLocalDate()
            .plusDays(2);
    var content =
        String.format(
            """
                        {
                            "id": "********",
                            "fromAccountId": "abf3228b-5066-4729-8e15-b7d44afc0a8a",
                            "toAccountId": "8a793e2a-21cc-4d50-af8a-76cab53935a2",
                            "activityType": "INTERNAL_TRANSFER",
                            "amount": 22.39,
                            "frequency": "ONE_TIME",
                            "dueDate": "%s",
                            "numberOfTransactions": 1,
                            "scheduleImmediately": true,
                            "additionalPrincipleAmount": null
                        }

                                                    """,
            invalidDate);

    XferAndPayActivity xferAndPayActivityResponse =
        mockFromFile(
            "xfer_and_pay_INTERNAL_TRANSFER_update_activty_response_success.json",
            XferAndPayActivity.class);

    when(xferAndPayOrcClient.editTransferAndPayActivity(any()))
        .thenReturn(xferAndPayActivityResponse);
    when(featureFlagService.getFeatureVariant(anyString(), anyString()))
        .thenReturn(FeatureFlagVariants.ENABLED);
    when(featureFlagService.isFeatureEnabled(FeatureFlagNames.ORCHESTRATOR_ENABLED))
        .thenReturn(true);
    this.mockMvc
        .perform(
            put("/activity")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .content(content)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andExpect(MockMvcResultMatchers.jsonPath("$.status").value(BffResponse.BFF_ERROR))
        .andExpect(MockMvcResultMatchers.jsonPath("$.statusCode").value("INVALID_DUE_DATE"))
        .andExpect(
            MockMvcResultMatchers.jsonPath("$.statusReason")
                .value(
                    "Due date is more than one day outside of today's date in eastern time for 'scheduleImmediately'"));
  }

  @Test
  @DisplayName(
      """
                                Given a user tries to update a transfer
                                With an invalid due date two days in the past.
                                Then the response is Bad Request.
                                """)
  void updateActivityInternalTransfer_InvalidDueDate_twoDaysPast() throws Exception {
    var invalidDate =
        ZonedDateTime.ofInstant(Instant.now(), ZoneId.of("America/New_York"))
            .toLocalDate()
            .plusDays(-2);
    var content =
        String.format(
            """
                                {
                                    "id": "********",
                                    "fromAccountId": "abf3228b-5066-4729-8e15-b7d44afc0a8a",
                                    "toAccountId": "8a793e2a-21cc-4d50-af8a-76cab53935a2",
                                    "activityType": "INTERNAL_TRANSFER",
                                    "amount": 22.39,
                                    "frequency": "ONE_TIME",
                                    "dueDate": "%s",
                                    "numberOfTransactions": 1,
                                    "scheduleImmediately": true,
                                    "additionalPrincipleAmount": null
                                }

                                                            """,
            invalidDate);

    XferAndPayActivity xferAndPayActivityResponse =
        mockFromFile(
            "xfer_and_pay_INTERNAL_TRANSFER_update_activty_response_success.json",
            XferAndPayActivity.class);

    when(xferAndPayOrcClient.editTransferAndPayActivity(any()))
        .thenReturn(xferAndPayActivityResponse);
    when(featureFlagService.getFeatureVariant(anyString(), anyString()))
        .thenReturn(FeatureFlagVariants.ENABLED);
    when(featureFlagService.isFeatureEnabled(FeatureFlagNames.ORCHESTRATOR_ENABLED))
        .thenReturn(true);
    this.mockMvc
        .perform(
            put("/activity")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .content(content)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andExpect(MockMvcResultMatchers.jsonPath("$.status").value(BffResponse.BFF_ERROR))
        .andExpect(MockMvcResultMatchers.jsonPath("$.statusCode").value("INVALID_DUE_DATE"))
        .andExpect(
            MockMvcResultMatchers.jsonPath("$.statusReason")
                .value(
                    "Due date is more than one day outside of today's date in eastern time for 'scheduleImmediately'"));
  }
}
