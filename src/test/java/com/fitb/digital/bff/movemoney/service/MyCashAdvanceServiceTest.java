/* Copyright 2022 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.service;

import static com.fitb.digital.bff.movemoney.utils.TestUtils.mockFromFile;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

import com.fitb.digital.bff.movemoney.client.CesClient;
import com.fitb.digital.bff.movemoney.model.bff.myadvance.requests.BffMyAdvanceRequest;
import com.fitb.digital.bff.movemoney.model.client.cashadvance.requests.TransferRequest;
import com.fitb.digital.bff.movemoney.model.client.cashadvance.responses.CesCashAdvanceResponse;
import java.math.BigDecimal;
import java.time.*;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

@Slf4j
@ExtendWith(MockitoExtension.class)
public class MyCashAdvanceServiceTest {
  @Spy @InjectMocks private MyAdvanceService service;

  @Mock private CesClient cesClient;

  @Test
  void verifyAdvanceHappyPath() {
    var re = mockFromFile("service/add_advance_response.json", CesCashAdvanceResponse.class);
    when(cesClient.postCashAdvanceActivity(any())).thenReturn(re);
    var transferRequest =
        mockFromFile("service/add_advance_request.json", BffMyAdvanceRequest.class);
    var transferResponse = service.performCashAdvance(transferRequest);
    assertNotNull(transferResponse);
    assertEquals(new BigDecimal("5.00"), transferResponse.getAmount());
    assertEquals("13941114", transferResponse.getDisplayId());
  }

  @Test
  void verifyPaymentHappyPath() {
    var re = mockFromFile("service/pay_advance_response.json", CesCashAdvanceResponse.class);
    when(cesClient.postCashAdvanceActivity(any())).thenReturn(re);
    var transferRequest =
        mockFromFile("service/pay_advance_request.json", BffMyAdvanceRequest.class);
    var transferResponse = service.performCashAdvance(transferRequest);
    assertNotNull(transferResponse);
    assertEquals(new BigDecimal("5.15"), transferResponse.getAmount());
    assertEquals("13941114", transferResponse.getDisplayId());
  }

  @Test
  void cashAdvanceUsesEasternTimeZone() {
    var re = mockFromFile("service/pay_advance_response.json", CesCashAdvanceResponse.class);
    when(cesClient.postCashAdvanceActivity(any())).thenReturn(re);

    Instant instant = Instant.parse("2022-09-07T02:00:00Z");
    ZonedDateTime mockZDt = instant.atZone(ZoneId.of("America/New_York"));
    when(service.getCurrentEasternZonedDateTime()).thenReturn(mockZDt);

    ArgumentCaptor<TransferRequest> argumentCaptor = ArgumentCaptor.forClass(TransferRequest.class);

    service.performCashAdvance(new BffMyAdvanceRequest());

    verify(cesClient).postCashAdvanceActivity(argumentCaptor.capture());

    var arg = argumentCaptor.getValue();

    assertEquals(LocalDate.of(2022, 9, 6), arg.getDueDate());
  }
}
