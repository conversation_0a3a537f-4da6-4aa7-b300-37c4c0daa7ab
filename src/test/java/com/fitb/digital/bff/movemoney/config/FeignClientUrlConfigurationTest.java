/* Copyright 2024 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.config;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

import com.fitb.digital.lib.featureflag.service.FeatureFlagService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.core.env.Environment;

class FeignClientUrlConfigurationTest {

  @Mock private FeatureFlagService featureFlagService;

  @Mock private Environment environment;

  private FeignClientUrlConfiguration feignClientUrlConfiguration =
      new FeignClientUrlConfiguration(featureFlagService, environment);

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
    feignClientUrlConfiguration = new FeignClientUrlConfiguration(featureFlagService, environment);
  }

  @Test
  void afterPropertiesSet_ShouldAddFlagChangeListener() {
    // Given
    when(environment.getProperty(any(), anyString())).thenReturn("aMockUrl");
    when(environment.getProperty(any())).thenReturn("aMockUrl");
    when(featureFlagService.getFeatureVariant(anyString(), anyString())).thenReturn("aFlagMockUrl");

    // When
    feignClientUrlConfiguration.afterPropertiesSet();

    // Then
    verify(featureFlagService, times(1)).addFlagChangeListener(any());
  }

  @Test
  void destroy_ShouldRemoveFlagChangeListener() {
    // When
    feignClientUrlConfiguration.destroy();

    // Then
    verify(featureFlagService, times(1)).removeFlagChangeListener(any());
  }

  @Test
  void onFlagChange_ShouldUpdateUrlFromFlag() {
    // Given
    String flag = feignClientUrlConfiguration.flagToUrlPropMap.keySet().stream().findFirst().get();
    String urlProp = feignClientUrlConfiguration.flagToUrlPropMap.get(flag);
    String legacyUrl = "https://default.com";
    String scfUrl = "https://example.com";

    when(environment.getProperty(urlProp + FeignClientUrlConfiguration.PROPERTY_SUFFIX_DEFAULT, ""))
        .thenReturn(legacyUrl);
    when(environment.getProperty(urlProp + FeignClientUrlConfiguration.PROPERTY_SUFFIX_LEGACY))
        .thenReturn(legacyUrl);
    when(environment.getProperty(urlProp + FeignClientUrlConfiguration.PROPERTY_SUFFIX_SCF))
        .thenReturn(scfUrl);
    when(featureFlagService.isFeatureEnabled(flag, false)).thenReturn(true);

    // When
    feignClientUrlConfiguration.onFlagChange(flag);

    // Then
    assertEquals(scfUrl, feignClientUrlConfiguration.urlReplacementMap.get(legacyUrl));
  }
}
