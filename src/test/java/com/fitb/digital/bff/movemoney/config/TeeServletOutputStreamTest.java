/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.config;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import java.io.IOException;
import java.io.OutputStream;
import org.bouncycastle.util.io.TeeOutputStream;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class TeeServletOutputStreamTest {
  @Mock TeeOutputStream targetStream;
  @Mock OutputStream one;
  @Mock OutputStream two;
  TeeServletOutputStream teeServletOutputStream;

  @BeforeEach
  public void onSetUp() {
    MockitoAnnotations.initMocks(this);
    teeServletOutputStream = new TeeServletOutputStream(one, two);
  }

  @Test
  public void testWrite() throws IOException {
    teeServletOutputStream.write(100);
    verify(targetStream, times(0)).write(anyInt());
  }

  @Test
  public void testFlush() throws IOException {
    teeServletOutputStream.flush();
    verify(targetStream, times(0)).flush();
  }

  @Test
  public void testClose() throws IOException {
    teeServletOutputStream.close();
    verify(targetStream, times(0)).close();
  }

  @Test
  public void testIsReady() {
    assertFalse(teeServletOutputStream.isReady());
  }
}
