/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.controller.util;

import com.fitb.digital.bff.movemoney.exceptions.CesFeignException;
import com.fitb.digital.bff.movemoney.exceptions.FeignClientException;
import com.fitb.digital.bff.movemoney.exceptions.FeignServerException;
import com.fitb.digital.lib.response.Error;
import org.springframework.context.annotation.Profile;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.server.ResponseStatusException;

@RestController(value = "/test/exceptions")
@Profile({"unit-test"})
public class MockController {
  @GetMapping(value = "/client-error")
  public void throwClientErrorException() {
    throw new IllegalArgumentException("Testing IllegalArgumentException handling.");
  }

  @GetMapping(value = "/forbidden-error")
  public void throwForbiddenError() {
    throw new AccessDeniedException("Testing EntityNotFoundException handling.");
  }

  @GetMapping(value = "/response-status-error")
  public void throwResponseStatusError() {
    throw new ResponseStatusException(HttpStatus.BAD_GATEWAY);
  }

  @GetMapping(value = "/generic-error")
  public void throwGeneralError() throws Exception {
    throw new Exception("Testing generic exception handling.");
  }

  @GetMapping(value = "/ces-feign-error")
  public void throwCesFeignError() throws Exception {
    throw new CesFeignException(HttpStatus.GATEWAY_TIMEOUT, "Gateway timed out.");
  }

  @GetMapping(value = "/feign-client-error")
  public void throwFeignClientError() throws Exception {
    throw FeignClientException.builder()
        .httpStatus(HttpStatus.BAD_REQUEST)
        .error(
            Error.builder()
                .code(HttpStatus.BAD_REQUEST.name())
                .message(HttpStatus.BAD_REQUEST.getReasonPhrase())
                .target("/feign-client-error")
                .build())
        .build();
  }

  @GetMapping(value = "/feign-server-error")
  public void throwFeignServerError() throws Exception {
    throw FeignServerException.builder()
        .httpStatus(HttpStatus.INTERNAL_SERVER_ERROR)
        .error(
            Error.builder()
                .code(HttpStatus.INTERNAL_SERVER_ERROR.name())
                .message(HttpStatus.INTERNAL_SERVER_ERROR.getReasonPhrase())
                .target("/feign-server-error")
                .build())
        .build();
  }
}
