/* Copyright 2023 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.service.activity;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.fitb.digital.bff.movemoney.client.xferandpayorc.XferAndPayOrcClient;
import com.fitb.digital.bff.movemoney.client.xferandpayorc.model.XferAndPayOrcBaseResponse;
import com.fitb.digital.bff.movemoney.model.ActivityType;
import com.fitb.digital.bff.movemoney.model.client.CesResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@Slf4j
@ExtendWith(MockitoExtension.class)
public class ActivtyServiceCancelTest {
  @InjectMocks private ActivityService activityService;
  @Mock private XferAndPayOrcClient xferAndPayOrcClient;

  @Test
  void cancelActivityReturnsBodyIfSuccessful() {
    when(xferAndPayOrcClient.cancelTransferAndPayActivity(any(String.class), any()))
        .thenReturn(new XferAndPayOrcBaseResponse());
    var activityResponse = activityService.cancelActivity("12345", ActivityType.INTERNAL_TRANSFER);
    assertEquals(CesResponse.SUCCESS, activityResponse.getStatus());
  }
}
