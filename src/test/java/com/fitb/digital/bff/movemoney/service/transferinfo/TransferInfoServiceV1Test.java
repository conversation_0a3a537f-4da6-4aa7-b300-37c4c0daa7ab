/* Copyright 2022 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.service.transferinfo;

import static com.fitb.digital.bff.movemoney.utils.TestUtils.*;
import static java.util.stream.Collectors.toSet;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;

import com.fitb.digital.bff.movemoney.client.CesClient;
import com.fitb.digital.bff.movemoney.client.CesClientShortTimeout;
import com.fitb.digital.bff.movemoney.constants.ExceptionStatus;
import com.fitb.digital.bff.movemoney.exceptions.BffException;
import com.fitb.digital.bff.movemoney.exceptions.CesFeignException;
import com.fitb.digital.bff.movemoney.featureflags.FeatureFlagNames;
import com.fitb.digital.bff.movemoney.model.ActivityBase;
import com.fitb.digital.bff.movemoney.model.RetrievalErrors;
import com.fitb.digital.bff.movemoney.model.bff.BffResponse;
import com.fitb.digital.bff.movemoney.model.bff.account.responses.Account;
import com.fitb.digital.bff.movemoney.model.bff.account.responses.Recipient;
import com.fitb.digital.bff.movemoney.model.bff.account.responses.Source;
import com.fitb.digital.bff.movemoney.model.client.CesResponse;
import com.fitb.digital.bff.movemoney.model.client.account.responses.CesGoal;
import com.fitb.digital.bff.movemoney.model.client.account.responses.ListResponse;
import com.fitb.digital.bff.movemoney.model.client.external.transfer.responses.CesGetUserAccountsResponse;
import com.fitb.digital.bff.movemoney.model.client.profile.responses.ProfileResponse;
import com.fitb.digital.bff.movemoney.model.service.account.ServiceGetAccountResponse;
import com.fitb.digital.bff.movemoney.service.BlackoutDateService;
import com.fitb.digital.bff.movemoney.service.account.AccountService;
import com.fitb.digital.bff.movemoney.service.account.AccountServiceAsync;
import com.fitb.digital.bff.movemoney.util.ExceptionUtil;
import com.fitb.digital.bff.movemoney.utils.TestUtils;
import com.fitb.digital.lib.featureflag.service.FeatureFlagService;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.web.server.ResponseStatusException;

@Slf4j
@ExtendWith(MockitoExtension.class)
public class TransferInfoServiceV1Test {

  @Mock private CesClient cesClient;
  @Mock private CesClientShortTimeout cesClientShortTimeout;
  @Mock private FeatureFlagService mockFeatureFlagService;

  private AccountService accountService;
  private AccountServiceAsync accountServiceAsync;

  @Mock(strictness = Mock.Strictness.LENIENT)
  private BlackoutDateService blackoutDateService;

  private TransferInfoServiceV1 transferInfoServiceV1;

  @BeforeEach
  void setUp() {
    accountService = new AccountService(cesClient);
    accountServiceAsync = new AccountServiceAsync(cesClient);
    when(blackoutDateService.getBlackoutDates(any(), any())).thenReturn(new ArrayList<>());
    transferInfoServiceV1 =
        new TransferInfoServiceV1(
            accountService, accountServiceAsync, blackoutDateService, mockFeatureFlagService, 5);
  }

  @Test
  void verifyFeignException() {
    Exception e =
        new RuntimeException(
            new CesFeignException(HttpStatus.I_AM_A_TEAPOT, "Honey, I'm exceptional!"));
    assertThrows(CesFeignException.class, () -> ExceptionUtil.handleAsyncExceptions(e, "", ""));
  }

  @Test
  void combineResults() {
    ServiceGetAccountResponse response =
        transferInfoServiceV1.combineResults(
            mockCESFullProfileResponse(), mockCESFullAccountList());
    assertNotNull(response);
  }

  @Test
  void verifyOtherExceptions() {
    final Exception e = new RuntimeException(new RuntimeException("A nested exception"));
    final BffException expected =
        BffException.serviceUnavailable(ExceptionStatus.SERVICE_UNAVAILABLE);
    final BffException actual =
        assertThrows(BffException.class, () -> ExceptionUtil.handleAsyncExceptions(e, "", ""));
    assertEquals(expected, actual);
  }

  @Test
  void getAccountsReturnsErrorIfGetTransferAndPayProfileIsNull() {
    when(cesClient.getTransferAndPayProfile(any())).thenReturn(null);
    when(cesClient.getAccountsList(any())).thenReturn(null);
    final BffException expected = BffException.internalServerError("Missing client results.");
    final BffException actual =
        assertThrows(
            BffException.class, () -> transferInfoServiceV1.getTransferAccounts(true, true));
    assertEquals(expected, actual);
  }

  @Test
  void getAccountsHandlesProfileException() {
    when(cesClient.getTransferAndPayProfile(any()))
        .thenThrow(new CesFeignException(HttpStatus.INTERNAL_SERVER_ERROR, "some reason"));
    final BffException expected =
        BffException.serviceUnavailable(ExceptionStatus.SERVICE_UNAVAILABLE);
    final BffException actual =
        assertThrows(
            BffException.class, () -> transferInfoServiceV1.getTransferAccounts(true, true));
    assertEquals(expected, actual);
  }

  @Test
  void getAccountsHandlesListException() {
    when(cesClient.getAccountsList(true))
        .thenThrow(new ResponseStatusException(HttpStatus.SERVICE_UNAVAILABLE, "I'm here!"));
    final BffException expected =
        BffException.serviceUnavailable(ExceptionStatus.SERVICE_UNAVAILABLE);
    final BffException actual =
        assertThrows(
            BffException.class, () -> transferInfoServiceV1.getTransferAccounts(true, true));
    assertEquals(expected, actual);
  }

  @Test
  void getAccountsReturnsErrorIfGetAccountsListFails() {
    when(cesClient.getTransferAndPayProfile(any())).thenReturn(mockProfileResponse());
    when(cesClient.getAccountsList(any())).thenReturn(mockCESFullAccountList());
    var response = transferInfoServiceV1.getTransferAccounts(true, true);
    assertNotEquals(CesResponse.SUCCESS, response.getStatus());
  }

  @Test
  void getAccountsReturnsErrorIfGetAccountsListError() {
    ListResponse listResponse = new ListResponse();
    listResponse.setStatus(CesResponse.ERROR);
    listResponse.setStatusCode("SERVICE_DOWN");
    listResponse.setStatusReason("Because I said so.");
    when(cesClient.getTransferAndPayProfile(any())).thenReturn(mockProfileResponse());
    when(cesClient.getAccountsList(any())).thenReturn(listResponse);
    var response = transferInfoServiceV1.getTransferAccounts(true, true);
    assertNotEquals(BffResponse.SUCCESS, response.getStatus());
  }

  @Test
  void getTransferAccountsReturnsOnlyNotBillpayTargets() {
    when(cesClient.getTransferAndPayProfile(any())).thenReturn(TestUtils.mockCESProfileResponse());
    when(cesClient.getAccountsList(any())).thenReturn(mockCESListResponse());

    var accountResponse = transferInfoServiceV1.getTransferAccounts(true, true);
    assertNotNull(accountResponse.getAccounts());

    for (var account : accountResponse.getAccounts()) {
      assertTrue(
          account.getRecipients().stream()
              .noneMatch(recipient -> recipient.getActivityType().equals("BILLPAY")));
    }
  }

  @Test
  void getTransferAccountsReturnsOnlyBillpaySourcesForPayeeAccounts() {
    when(cesClient.getTransferAndPayProfile(any())).thenReturn(TestUtils.mockCESProfileResponse());
    when(cesClient.getAccountsList(any())).thenReturn(mockCESListResponse());

    var accountResponse = transferInfoServiceV1.getTransferAccounts(true, true);
    assertNotNull(accountResponse.getAccounts());

    for (var account : accountResponse.getAccounts()) {
      if (account.getAccountType().equals("PERSONAL_PAYEE")
          || account.getAccountType().equals("BUSINESS_PAYEE")) {
        assertTrue(
            account.getSources().stream()
                .allMatch(source -> source.getActivityType().equals("BILLPAY")));
      }
    }
  }

  @Test
  void getAccountsReceivesProfileRetrievalError() {
    when(cesClient.getTransferAndPayProfile(any())).thenReturn(mockCESProfileResponse());
    when(cesClient.getAccountsList(any())).thenReturn(mockCESListResponse());

    var accountResponse = transferInfoServiceV1.getTransferAccounts(true, true);
    assertNotNull(accountResponse);
    assertNotNull(accountResponse.getStatus());
    assertTrue(
        accountResponse
            .getRetrievalErrors()
            .contains(RetrievalErrors.RETRIEVAL_ERROR_EXTERNAL_TRANSFER));
    assertNotNull(accountResponse.getAccounts().get(0).getAvailableBalance());
  }

  @Test
  void getAccountsSyncPopulatesExpectedFields() {
    when(cesClient.getTransferAndPayProfile(any())).thenReturn(mockCESProfileResponse());
    when(cesClient.getAccountsList(any())).thenReturn(mockCESListResponse());
    var accountResponse = transferInfoServiceV1.getTransferAccounts(true, true);
    assertEquals(
        new BigDecimal("15937.9"), accountResponse.getAccounts().get(0).getAvailableBalance());
    assertEquals(
        new BigDecimal("10000.9"), accountResponse.getAccounts().get(0).getLedgerBalance());
    assertFalse(accountResponse.getAccounts().get(0).getCashAdvanceEligible());
    assertTrue(accountResponse.getAccounts().get(4).getCashAdvanceEligible());
    assertFalse(accountResponse.getAccounts().get(0).isVerificationRequired());
    assertEquals(5, accountResponse.getMaxExternalTransferAccounts());
  }

  @Test
  void getAccountsSetsCurrentExternalTransferAccounts() {
    when(cesClient.getTransferAndPayProfile(any())).thenReturn(mockCESProfileResponse());
    when(cesClient.getAccountsList(any())).thenReturn(mockCESListResponse());
    var accountResponse = transferInfoServiceV1.getTransferAccounts(true, true);
    assertEquals(1, accountResponse.getCurrentExternalTransferAccounts());
  }

  @Test
  void getAccountsSetsGoalsToImmediateTransferOnly() {
    when(cesClient.getTransferAndPayProfile(any())).thenReturn(mockProfileGoalsResponse());
    when(cesClient.getAccountsList(any())).thenReturn(mockAccountListGoalsResponse());
    var accountResponse = transferInfoServiceV1.getTransferAccounts(true, true);

    accountResponse
        .getAccounts()
        .forEach(
            account -> {
              account
                  .getRecipients()
                  .forEach(
                      recipient -> {
                        if (recipient.getActivityType().equalsIgnoreCase(ActivityBase.GOALS)) {
                          assertEquals(
                              recipient.getEarliestAvailableDate(),
                              recipient.getLatestAvailableDate());
                        }
                      });

              account
                  .getSources()
                  .forEach(
                      source -> {
                        if (source.getActivityType().equalsIgnoreCase(ActivityBase.GOALS)) {
                          assertEquals(
                              source.getEarliestAvailableDate(), source.getLatestAvailableDate());
                        }
                      });
            });
  }

  @Test
  public void paymentAccountHasSources() {
    when(cesClient.getTransferAndPayProfile(any())).thenReturn(mockCESFullProfileResponse());
    when(cesClient.getAccountsList(any())).thenReturn(TestUtils.mockCESFullAccountList());

    var accountResponse = transferInfoServiceV1.getTransferAccounts(true, true);
    assertNotNull(accountResponse);
    assertNotNull(accountResponse.getStatus());

    var accounts = accountResponse.getAccounts();
    var payments = accounts.stream().filter(account -> account.isPaymentTarget());

    payments
        .filter(account -> !account.getSources().isEmpty())
        .forEach(
            account -> {
              assertTrue(account.getSources().size() > 0);
            });
  }

  @Test
  @DisplayName(
      """
      Given the getTransfersAccount is called
      When an earliestAvailableDateRecurring is sent from CES
      Then earliestAvailableDateRecurring is passed to the caller.
      """)
  public void whenEarliestAvailableDateRecurringIsMapped() {
    when(cesClient.getTransferAndPayProfile(any())).thenReturn(mockCESFullProfileResponse());
    when(cesClient.getAccountsList(any())).thenReturn(TestUtils.mockCESFullAccountList());

    var accountResponse = transferInfoServiceV1.getTransferAccounts(true, true);
    assertNotNull(accountResponse);
    assertNotNull(accountResponse.getStatus());

    var accounts = accountResponse.getAccounts();
    var responseExternalAccount =
        accounts.stream()
            .filter(
                account -> account.getId().equalsIgnoreCase("507F7855-0FBB-C79F-5BE7-0636CF2EC3A6"))
            .findFirst()
            .get();
    var cesExternalAccount =
        mockCESFullProfileResponse().getActors().stream()
            .filter(actor -> actor.getId().equalsIgnoreCase("507F7855-0FBB-C79F-5BE7-0636CF2EC3A6"))
            .findFirst()
            .get();

    var responseInternalAccount = accounts.get(0);
    var cesInternalAccount = mockCESFullProfileResponse().getActors().get(0);

    var cesExternalRecipient =
        cesInternalAccount.getAvailableRecipients().stream()
            .filter(actor -> actor.getId().equalsIgnoreCase("507F7855-0FBB-C79F-5BE7-0636CF2EC3A6"))
            .findFirst()
            .get();
    var responseExternalRecipient =
        responseInternalAccount.getRecipients().stream()
            .filter(actor -> actor.getId().equalsIgnoreCase("507F7855-0FBB-C79F-5BE7-0636CF2EC3A6"))
            .findFirst()
            .get();

    var cesExternalSource =
        cesInternalAccount.getAvailableSources().stream()
            .filter(actor -> actor.getId().equalsIgnoreCase("507F7855-0FBB-C79F-5BE7-0636CF2EC3A6"))
            .findFirst()
            .get();
    var responseExternalSource =
        responseInternalAccount.getSources().stream()
            .filter(actor -> actor.getId().equalsIgnoreCase("507F7855-0FBB-C79F-5BE7-0636CF2EC3A6"))
            .findFirst()
            .get();

    assertEquals(
        cesExternalRecipient.getEarliestAvailableDateRecurring(),
        responseExternalRecipient.getEarliestAvailableDateRecurring());

    assertEquals(
        cesExternalSource.getEarliestAvailableDateRecurring(),
        responseExternalSource.getEarliestAvailableDateRecurring());

    assertEquals(
        cesExternalAccount.getAvailableSources().get(0).getEarliestAvailableDateRecurring(),
        responseExternalAccount.getSources().get(0).getEarliestAvailableDateRecurring());

    assertEquals(
        cesExternalAccount.getAvailableRecipients().get(0).getEarliestAvailableDateRecurring(),
        responseExternalAccount.getRecipients().get(0).getEarliestAvailableDateRecurring());
  }

  @Test
  void goalsGetMappedAndNoGeneralSavings() {
    var mockCesAccountListGoalsResponse = mockAccountListGoalsResponse();

    when(cesClient.getTransferAndPayProfile(any())).thenReturn(mockProfileGoalsResponse());
    when(cesClient.getAccountsList(any())).thenReturn(mockCesAccountListGoalsResponse);

    var accountResponse = transferInfoServiceV1.getTransferAccounts(true, true);

    var goalAccounts =
        accountResponse.getAccounts().stream()
            .filter(account -> account.getAccountType().equals("SAV_GOAL"))
            .toList();

    var generalSavingsAccountsInServiceResponse =
        goalAccounts.stream()
            .filter(account -> account.getDisplayName().equals("General Savings"))
            .toList();

    var generalSavingsAccountsInCesResponse =
        mockCesAccountListGoalsResponse.getGoals().stream()
            .filter(account -> account.getName().equals("General Savings"))
            .toList();

    // Assert the general savings are removed from the recipients and sources
    accountResponse
        .getAccounts()
        .forEach(
            account -> {
              assertFalse(
                  account.getRecipients().stream()
                      .map(Recipient::getId)
                      .anyMatch(
                          generalSavingsAccountsInCesResponse.stream()
                                  .map(CesGoal::getId)
                                  .collect(toSet())
                              ::contains));

              assertFalse(
                  account.getSources().stream()
                      .map(Source::getId)
                      .anyMatch(
                          generalSavingsAccountsInCesResponse.stream()
                                  .map(CesGoal::getId)
                                  .collect(toSet())
                              ::contains));
            });

    assertEquals(2, goalAccounts.size());
    assertEquals(0, generalSavingsAccountsInServiceResponse.size());
  }

  @Test
  void transferinfo_setsGoalAmountNotAllocatedOnMomentumSavingsAccount() {
    // The 'Amount Not Assigned to Goals' value for a Momentum Savings Account is listed as a field
    // on the Momentum Savings Account
    var mockCesAccountListGoalsResponse = mockAccountListGoalsResponse();

    when(cesClient.getTransferAndPayProfile(any())).thenReturn(mockProfileGoalsResponse());
    when(cesClient.getAccountsList(any())).thenReturn(mockCesAccountListGoalsResponse);

    var accountResponse = transferInfoServiceV1.getTransferAccounts(true, true);

    var momentumSavingsAcct =
        accountResponse.getAccounts().stream()
            .filter(acct -> acct.getId().equals("28311b71-acd0-46f7-a537-fe4d5d7acb16"))
            .findFirst()
            .get();

    var generalSavingsGoal =
        mockCesAccountListGoalsResponse.getGoals().stream()
            .filter(goal -> goal.getGeneralSavings())
            .findFirst()
            .get();

    // assert the goalAmountNotAllocated is set from the general savings goal
    assertEquals(
        generalSavingsGoal.getProgressAmount(), momentumSavingsAcct.getGoalAmountNotAllocated());
  }

  @Test
  void
      transferinfo_setsGoalAmountNotAllocatedOnMomentumSavingsAccount_whenMultipleMomentumSavings() {
    // The 'Amount Not Assigned to Goals' value for a Momentum Savings Account is listed as a field
    // on the Momentum Savings Account
    var mockCesAccountListGoalsResponse = mockAccountListMultiMomSavGoalsResponse();

    when(cesClient.getTransferAndPayProfile(any()))
        .thenReturn(mockProfileMultiMomSavGoalsResponse());
    when(cesClient.getAccountsList(any())).thenReturn(mockCesAccountListGoalsResponse);

    var accountResponse = transferInfoServiceV1.getTransferAccounts(true, true);

    var momSavId1 = "28311b71-acd0-46f7-a537-fe4d5d7acb16";
    var momentumSavingsAcct1 =
        accountResponse.getAccounts().stream()
            .filter(acct -> acct.getId().equals(momSavId1))
            .findFirst()
            .get();

    var momSavId2 = "8a3e6e82-9b80-49cd-94ec-01c6dbfe8d91";
    var momentumSavingsAcct2 =
        accountResponse.getAccounts().stream()
            .filter(acct -> acct.getId().equals(momSavId2))
            .findFirst()
            .get();

    var generalSavingsGoal1 =
        mockCesAccountListGoalsResponse.getGoals().stream()
            .filter(goal -> goal.getAccountId().equals(momSavId1) && goal.getGeneralSavings())
            .findFirst()
            .get();

    var generalSavingsGoal2 =
        mockCesAccountListGoalsResponse.getGoals().stream()
            .filter(goal -> goal.getAccountId().equals(momSavId2) && goal.getGeneralSavings())
            .findFirst()
            .get();

    // assert the goalAmountNotAllocated is set from the general savings goal
    assertEquals(
        generalSavingsGoal1.getProgressAmount(), momentumSavingsAcct1.getGoalAmountNotAllocated());

    assertEquals(
        generalSavingsGoal2.getProgressAmount(), momentumSavingsAcct2.getGoalAmountNotAllocated());
  }

  @Test
  void transferinfo_setsNestedGoalSortOrder() {
    // The 'Amount Not Assigned to Goals' value for a Momentum Savings Account is listed as a field
    // on the Momentum Savings Account
    var mockCesAccountListGoalsResponse = mockAccountListGoalsResponse();

    when(cesClient.getTransferAndPayProfile(any())).thenReturn(mockProfileGoalsResponse());
    when(cesClient.getAccountsList(any())).thenReturn(mockCesAccountListGoalsResponse);

    var accountResponse = transferInfoServiceV1.getTransferAccounts(true, true);

    var momentumSavingsAcct =
        accountResponse.getAccounts().stream()
            .filter(acct -> acct.getId().equals("28311b71-acd0-46f7-a537-fe4d5d7acb16"))
            .findFirst()
            .get();

    var goalIds =
        mockCesAccountListGoalsResponse.getGoals().stream()
            .filter(goal -> !goal.getGeneralSavings())
            .sorted(Comparator.comparing(CesGoal::getTargetDate))
            .map(CesGoal::getId)
            .toList();

    for (Account account : accountResponse.getAccounts()) {
      if (goalIds.contains(account.getId())) {
        BigDecimal expectedSortOrder = getExpectedSortOrder(momentumSavingsAcct, goalIds, account);
        assertEquals(expectedSortOrder, account.getDisplaySortOrder());
      }
    }
  }

  @Test
  void transferinfo_throwsBaseExceptionWhenGoalIsMissingFromAccountList() {
    // The 'Amount Not Assigned to Goals' value for a Momentum Savings Account is listed as a field
    // on the Momentum Savings Account
    var mockCesAccountListGoalsResponse = mockAccountListGoalsRemovedResponse();

    when(cesClient.getTransferAndPayProfile(true)).thenReturn(mockProfileGoalsResponse());
    when(cesClient.getAccountsList(true)).thenReturn(mockCesAccountListGoalsResponse);

    final BffException expected =
        BffException.serviceUnavailable(ExceptionStatus.SERVICE_UNAVAILABLE);
    final BffException actual =
        assertThrows(
            BffException.class, () -> transferInfoServiceV1.getTransferAccounts(true, true));
    assertEquals(expected, actual);
  }

  @Test
  void transferinfo_throwsBaseExceptionWhenGoalIsMissingFromProfileResponse() {
    // The 'Amount Not Assigned to Goals' value for a Momentum Savings Account is listed as a field
    // on the Momentum Savings Account
    var mockCesAccountListGoalsResponse = mockAccountListGoalsResponse();

    when(cesClient.getTransferAndPayProfile(true)).thenReturn(mockProfileGoalsRemovedResponse());
    when(cesClient.getAccountsList(true)).thenReturn(mockCesAccountListGoalsResponse);

    final BffException expected =
        BffException.serviceUnavailable(ExceptionStatus.SERVICE_UNAVAILABLE);
    final BffException actual =
        assertThrows(
            BffException.class, () -> transferInfoServiceV1.getTransferAccounts(true, true));
    assertEquals(expected, actual);
  }

  @Test
  void transferinfo_setsNestedGoalSortOrder_whenMultipleMomentumSavings() {
    // The 'Amount Not Assigned to Goals' value for a Momentum Savings Account is listed as a field
    // on the Momentum Savings Account
    var mockCesAccountListGoalsResponse = mockAccountListMultiMomSavGoalsResponse();

    when(cesClient.getTransferAndPayProfile(any()))
        .thenReturn(mockProfileMultiMomSavGoalsResponse());
    when(cesClient.getAccountsList(any())).thenReturn(mockCesAccountListGoalsResponse);

    var accountResponse = transferInfoServiceV1.getTransferAccounts(true, true);

    var momSavId1 = "28311b71-acd0-46f7-a537-fe4d5d7acb16";
    var momentumSavingsAcct1 =
        accountResponse.getAccounts().stream()
            .filter(acct -> acct.getId().equals(momSavId1))
            .findFirst()
            .get();

    var momSavId2 = "8a3e6e82-9b80-49cd-94ec-01c6dbfe8d91";
    var momentumSavingsAcct2 =
        accountResponse.getAccounts().stream()
            .filter(acct -> acct.getId().equals(momSavId2))
            .findFirst()
            .get();

    var goalIds1 =
        mockCesAccountListGoalsResponse.getGoals().stream()
            .filter(goal -> !goal.getGeneralSavings() && goal.getAccountId().equals(momSavId1))
            .sorted(Comparator.comparing(CesGoal::getTargetDate))
            .map(CesGoal::getId)
            .toList();

    var goalIds2 =
        mockCesAccountListGoalsResponse.getGoals().stream()
            .filter(goal -> !goal.getGeneralSavings() && goal.getAccountId().equals(momSavId2))
            .sorted(Comparator.comparing(CesGoal::getTargetDate))
            .map(CesGoal::getId)
            .toList();

    for (Account account : accountResponse.getAccounts()) {
      if (goalIds1.contains(account.getId())) {
        BigDecimal expectedSortOrder =
            getExpectedSortOrder(momentumSavingsAcct1, goalIds1, account);
        assertEquals(expectedSortOrder, account.getDisplaySortOrder());
      } else if (goalIds2.contains(account.getId())) {
        BigDecimal expectedSortOrder =
            getExpectedSortOrder(momentumSavingsAcct2, goalIds2, account);
        assertEquals(expectedSortOrder, account.getDisplaySortOrder());
      }
    }
  }

  @Test
  void getTransferInfo_removesAccess360_whenAccess360Disabled() {
    when(mockFeatureFlagService.isFeatureEnabled(FeatureFlagNames.ENABLE_ACCESS_360))
        .thenReturn(false);
    var mockCesAccountListAccess360Response = mockAccountListAccess360Response();

    when(cesClient.getTransferAndPayProfile(any())).thenReturn(mockProfileAccess360Response());
    when(cesClient.getAccountsList(any())).thenReturn(mockCesAccountListAccess360Response);

    var accountResponse = transferInfoServiceV1.getTransferAccounts(true, true);

    var access360AccountIds =
        accountResponse.getAccounts().stream()
            .filter(account -> account.getAccountType().equals("GPR"))
            .map(Account::getId)
            .toList();

    // Assert 360 accounts are removed from account list
    assertFalse(
        accountResponse.getAccounts().stream()
            .map(Account::getId)
            .anyMatch(access360AccountIds::contains));

    // Assert the Access 360 are removed from the recipients and sources
    accountResponse
        .getAccounts()
        .forEach(
            account -> {
              assertFalse(
                  account.getRecipients().stream()
                      .map(Recipient::getId)
                      .anyMatch(access360AccountIds::contains));

              assertFalse(
                  account.getSources().stream()
                      .map(Source::getId)
                      .anyMatch(access360AccountIds::contains));
            });
  }

  @Test
  void getTransferInfo_containsAccess360_whenAccess360Enabled() {
    when(mockFeatureFlagService.isFeatureEnabled(FeatureFlagNames.ENABLE_ACCESS_360))
        .thenReturn(true);
    var mockCesAccountListAccess360Response = mockAccountListAccess360Response();

    when(cesClient.getTransferAndPayProfile(any())).thenReturn(mockProfileAccess360Response());
    when(cesClient.getAccountsList(any())).thenReturn(mockCesAccountListAccess360Response);

    var accountResponse = transferInfoServiceV1.getTransferAccounts(true, true);

    var access360AccountIds =
        accountResponse.getAccounts().stream()
            .filter(account -> account.getAccountType().equals("GPR"))
            .map(Account::getId)
            .toList();

    // Assert 360 accounts are in the from account list
    assertTrue(
        accountResponse.getAccounts().stream()
            .map(Account::getId)
            .anyMatch(access360AccountIds::contains));

    // Assert the Access 360 are not in sources
    accountResponse
        .getAccounts()
        .forEach(
            account -> {
              assertFalse(
                  account.getSources().stream()
                      .map(Source::getId)
                      .anyMatch(access360AccountIds::contains));
            });

    assertTrue(
        accountResponse.getAccounts().get(0).getRecipients().stream()
            .map(Recipient::getId)
            .anyMatch(access360AccountIds::contains));
  }

  @Test
  void
      getTransferInfo_throwsException_whenOneGoalNotPresentInøAccountListButPresentInProfileResponse() {
    when(cesClient.getTransferAndPayProfile(any())).thenReturn(mockProfileGoalsResponse());
    when(cesClient.getAccountsList(any())).thenReturn(mockAccountListOneGoalRemovedResponse());

    final BffException expected =
        BffException.internalServerError(
            "Account list does not contain goals for momentum savings account.");
    final BffException actual =
        assertThrows(
            BffException.class, () -> transferInfoServiceV1.getTransferAccounts(false, false));
    assertEquals(expected, actual);
  }

  @Test
  void
      getTransferInfo_throwsException_whenGoalsListEmptyInAccountListButPresentInProfileResponse() {
    when(cesClient.getTransferAndPayProfile(any())).thenReturn(mockProfileGoalsResponse());
    when(cesClient.getAccountsList(any())).thenReturn(mockAccountListGoalsRemovedResponse());

    final BffException expected =
        BffException.internalServerError(
            "Account list does not contain goals for momentum savings account.");
    final BffException actual =
        assertThrows(
            BffException.class, () -> transferInfoServiceV1.getTransferAccounts(false, false));
    assertEquals(expected, actual);
  }

  @NotNull
  private BigDecimal getExpectedSortOrder(
      Account momentumSavingsAcct, List<String> goalIds, Account account) {
    var rawIndex = new BigDecimal(goalIds.indexOf(account.getId()) + 1);
    var maxGoals = new BigDecimal("10000");
    var computedIndex = rawIndex.divide(maxGoals, 4, RoundingMode.UNNECESSARY);

    return new BigDecimal(String.valueOf(momentumSavingsAcct.getDisplaySortOrder()))
        .add(computedIndex);
  }

  private ProfileResponse mockProfileResponse() {
    return new ProfileResponse();
  }

  private ProfileResponse mockProfileGoalsResponse() {
    return mockFromFile("service/transferinfo/profile_response_goals.json", ProfileResponse.class);
  }

  private ProfileResponse mockProfileAccess360Response() {
    return mockFromFile("service/transferinfo/profile_access_360.json", ProfileResponse.class);
  }

  private ProfileResponse mockProfileGoalsRemovedResponse() {
    return mockFromFile(
        "service/transferinfo/profile_response_goals_removed.json", ProfileResponse.class);
  }

  private ProfileResponse mockProfileMultiMomSavGoalsResponse() {
    return mockFromFile(
        "service/transferinfo/profile_response_multi_goals.json", ProfileResponse.class);
  }

  private ListResponse mockAccountListGoalsResponse() {
    return mockFromFile(
        "service/transferinfo/account_list_response_goals.json", ListResponse.class);
  }

  private ListResponse mockAccountListAccess360Response() {
    return mockFromFile("service/transferinfo/account_list_access_360.json", ListResponse.class);
  }

  private ListResponse mockAccountListGoalsRemovedResponse() {
    return mockFromFile(
        "service/transferinfo/account_list_response_goals_removed.json", ListResponse.class);
  }

  private ListResponse mockAccountListOneGoalRemovedResponse() {
    return mockFromFile(
        "service/transferinfo/account_list_response_goals_one_goal_removed.json",
        ListResponse.class);
  }

  private ListResponse mockAccountListMultiMomSavGoalsResponse() {
    return mockFromFile(
        "service/transferinfo/account_list_response_multi_goals.json", ListResponse.class);
  }

  private ProfileResponse mockProfileMixedExternal() {
    return mockFromFile(
        "service/transferinfo/profile_external_mixed_approved_denied.json", ProfileResponse.class);
  }

  private ProfileResponse mockProfileMixedExternalInvoiceDetails() {
    return mockFromFile(
        "service/transferinfo/profile_external_mixed_approved_denied_invoicedetails.json",
        ProfileResponse.class);
  }

  private ListResponse mockAccountListMixedExternal() {
    return mockFromFile(
        "service/transferinfo/account_list_external_mixed_approved_denied.json",
        ListResponse.class);
  }

  private CesGetUserAccountsResponse mockExternalTransferAccountsMixed() {
    return mockFromFile(
        "service/externaltransfer/external_user_accounts_mixed_approved_denied.json",
        CesGetUserAccountsResponse.class);
  }
}
