/* Copyright 2022 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.controller;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.fitb.digital.bff.movemoney.exceptions.CesFeignException;
import com.fitb.digital.bff.movemoney.model.bff.myadvance.requests.BffMyAdvanceRequest;
import com.fitb.digital.bff.movemoney.service.MyAdvanceService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;

@ExtendWith(MockitoExtension.class)
public class MmMyAdvanceControllerTest {
  @Mock private MyAdvanceService service;

  @InjectMocks private MmMyAdvanceController controller;

  @Test
  void feignExceptionIsHandled() {
    when(service.performCashAdvance(any()))
        .thenThrow(new CesFeignException(HttpStatus.NOT_FOUND, "Out of here."));
    assertThrows(
        RuntimeException.class, () -> controller.initiateAdvance(new BffMyAdvanceRequest()));
  }
}
