/* Copyright 2024 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.service.activity;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.fitb.digital.bff.movemoney.client.CesClient;
import com.fitb.digital.bff.movemoney.model.client.activity.responses.ClientActivityResponse;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class ActivityServiceAsyncTest {

  @Mock private CesClient cesClient;

  private ActivityServiceAsync service;

  @BeforeEach
  void setUp() {
    service = new ActivityServiceAsync(cesClient);
  }

  @Test
  void getActivityAsync_ShouldReturnClientActivityResponse()
      throws ExecutionException, InterruptedException {
    // Given
    ClientActivityResponse expectedResponse = new ClientActivityResponse();
    when(cesClient.getTransferAndPayActivity()).thenReturn(expectedResponse);

    // When
    CompletableFuture<ClientActivityResponse> future = service.getActivityAsync();
    ClientActivityResponse result = future.get();

    // Then
    assertNotNull(result);
    verify(cesClient, times(1)).getTransferAndPayActivity();
  }

  @Test
  void getActivityAsync_ShouldReturnCompletedFuture() {
    // Given
    ClientActivityResponse expectedResponse = new ClientActivityResponse();
    when(cesClient.getTransferAndPayActivity()).thenReturn(expectedResponse);

    // When
    CompletableFuture<ClientActivityResponse> future = service.getActivityAsync();

    // Then
    assertNotNull(future);
    assertTrue(future.isDone());
    assertFalse(future.isCompletedExceptionally());
  }

  @Test
  void getActivityAsync_WhenClientReturnsNull_ShouldReturnNull()
      throws ExecutionException, InterruptedException {
    // Given
    when(cesClient.getTransferAndPayActivity()).thenReturn(null);

    // When
    CompletableFuture<ClientActivityResponse> future = service.getActivityAsync();
    ClientActivityResponse result = future.get();

    // Then
    assertNull(result);
    verify(cesClient, times(1)).getTransferAndPayActivity();
  }

  @Test
  void getActivityAsync_WhenClientThrowsException_ShouldPropagateException() {
    // Given
    when(cesClient.getTransferAndPayActivity()).thenThrow(new RuntimeException("Service error"));

    // When & Then
    // The exception should be propagated and the test should expect it
    assertThrows(
        RuntimeException.class,
        () -> {
          CompletableFuture<ClientActivityResponse> future = service.getActivityAsync();
          try {
            future.get(); // This will throw the exception
          } catch (ExecutionException e) {
            throw (RuntimeException) e.getCause();
          } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException(e);
          }
        });
    verify(cesClient, times(1)).getTransferAndPayActivity();
  }
}
