/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.controller;

import static com.fitb.digital.bff.movemoney.utils.TestUtils.mockBffCreditCardDetails;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.fitb.digital.bff.movemoney.model.bff.account.responses.BffAccountDetail;
import com.fitb.digital.bff.movemoney.model.bff.account.responses.BffAccountDetailResponse;
import com.fitb.digital.bff.movemoney.service.account.AccountService;
import java.math.BigDecimal;
import java.time.LocalDate;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class MmAccountControllerTest {
  @Mock private AccountService accountService;

  @InjectMocks private MmAccountController controller;

  @Test
  void getAccountDetailReturnsDetail() {
    BffAccountDetailResponse clientDetail = getBffAccountDetailResponse();

    when(accountService.getAccountDetails(any())).thenReturn(clientDetail);

    BffAccountDetailResponse bffResponse = controller.getAccountDetail("yea buddy");

    BffAccountDetail response = bffResponse.getAccountDetail();
    assertEquals(
        clientDetail.getAccountDetail().getAccountDescription(), response.getAccountDescription());
    assertEquals(
        clientDetail.getAccountDetail().getDisplayAccountNumber(),
        response.getDisplayAccountNumber());
    assertEquals(clientDetail.getAccountDetail().getNickname(), response.getNickname());
    assertEquals(clientDetail.getAccountDetail().getAccountType(), response.getAccountType());
    assertEquals(clientDetail.getAccountDetail().getAffiliate(), response.getAffiliate());
    assertEquals(
        clientDetail.getAccountDetail().getAvailableBalance(), response.getAvailableBalance());
    assertEquals(clientDetail.getAccountDetail().getLedgerBalance(), response.getLedgerBalance());
    assertEquals(clientDetail.getAccountDetail().getPayoffAmount(), response.getPayoffAmount());
    assertEquals(
        clientDetail.getAccountDetail().getNextPaymentAmount(), response.getNextPaymentAmount());
    assertEquals(
        clientDetail.getAccountDetail().getNextPaymentDate(), response.getNextPaymentDate());
  }

  @NotNull
  private BffAccountDetailResponse getBffAccountDetailResponse() {
    BffAccountDetailResponse clientDetail = new BffAccountDetailResponse();
    clientDetail.setAccountDetail(new BffAccountDetail());
    clientDetail.getAccountDetail().setAccountDescription("descrip");
    clientDetail.getAccountDetail().setDisplayAccountNumber("number");
    clientDetail.getAccountDetail().setNickname("nick");
    clientDetail.getAccountDetail().setAccountType("MLS");
    clientDetail.getAccountDetail().setAffiliate("affili");
    clientDetail.getAccountDetail().setAvailableBalance(new BigDecimal("100.00"));
    clientDetail.getAccountDetail().setLedgerBalance(BigDecimal.ZERO);
    clientDetail.getAccountDetail().setNextPaymentAmount(new BigDecimal("110.00"));
    clientDetail.getAccountDetail().setNextPaymentDate(LocalDate.now());
    clientDetail.getAccountDetail().setPayoffAmount(new BigDecimal("1000.00"));
    return clientDetail;
  }

  @Test
  public void creditCardDetailsAreMapped() {
    when(accountService.getAccountDetails(anyString())).thenReturn((mockBffCreditCardDetails()));
    BffAccountDetailResponse bffResponse = controller.getAccountDetail("master-card");
    BffAccountDetail response = bffResponse.getAccountDetail();
    assertNotNull(response.getCardLimitAmount());
    assertNotNull(response.getCashLimitAmount());
    assertNotNull(response.getCashAdvanceRate());
    assertNotNull(response.getPurchaseRate());
    assertNotNull(response.getAvailableBalance());
    assertNotNull(response.getMaximumCardLimitAmount());
    assertNotNull(response.getMaximumCardLimitAmount());
    assertNotNull(response.getMinimumPaymentAmount());
  }
}
