/* Copyright 2022 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.service.transferinfo;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fitb.digital.bff.movemoney.model.bff.account.responses.Account;
import com.fitb.digital.bff.movemoney.model.client.account.responses.InternalAccount;
import com.fitb.digital.bff.movemoney.model.client.profile.responses.Actor;
import com.fitb.digital.bff.movemoney.service.BlackoutDateService;
import com.fitb.digital.bff.movemoney.service.account.AccountService;
import com.fitb.digital.bff.movemoney.service.account.AccountServiceAsync;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@Slf4j
@ExtendWith(MockitoExtension.class)
class ProfileMapperTest {
  @InjectMocks TransferInfoServiceV1 infoService;

  @Mock AccountService accountService;
  @Mock AccountServiceAsync accountServiceAsync;
  @Mock BlackoutDateService blackoutDateService;

  @Test
  void verifiyClientToBffMapping() throws JsonProcessingException {
    ObjectMapper mapper = new ObjectMapper();
    mapper.registerModule(new JavaTimeModule());
    mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    Actor actor = mapper.readValue(exampleProfileEntry, Actor.class);
    InternalAccount internalAccount =
        mapper.readValue(exampleInternalAccount, InternalAccount.class);
    var combiner = new AccountCombiner(accountService, true);
    Account account = combiner.accountFromActor(actor, Optional.of(internalAccount));

    assertEquals(actor.getId(), account.getId());
    assertEquals(actor.getType(), account.getAccountType());
    assertEquals(actor.getDisplayAccountNumber(), account.getDisplayAccountNumber());
    assertEquals(actor.isVerificationRequired(), account.isVerificationRequired());
    assertEquals(actor.isTransferSource(), account.isTransferSource());
    assertEquals(actor.isPaymentRecipient(), account.isPaymentTarget());
    assertEquals(actor.getLastPaymentAmount(), account.getLastPaymentAmount());
    assertEquals(actor.getLastPaymentDate(), account.getLastPaymentDate());
    assertEquals(actor.isTransferSource(), account.isTransferSource());
    assertEquals(actor.isElectronicBillingEnabled(), account.isElectronicBillingEnabled());
    assertEquals(actor.isInvoiceEnabled(), account.isInvoiceEnabled());

    assertEquals(internalAccount.getAvailableBalance(), account.getAvailableBalance());
    assertEquals(internalAccount.getLedgerBalance(), account.getLedgerBalance());
    assertEquals(internalAccount.getGoalCategory(), account.getGoalCategory());
    assertEquals(internalAccount.getGoalTargetAmount(), account.getGoalTargetAmount());
    assertEquals(internalAccount.getGoalTargetDate(), account.getGoalTargetDate());
    assertEquals(
        internalAccount.isExternalTransferEligible(), account.isExternalTransferEligible());
    assertEquals("5/3 Enhanced Checking", account.getDisplayName());
    assertEquals("**********", actor.getInvoiceDetails().getInvoiceId());
  }

  private final String exampleInternalAccount =
      "{\n"
          + "\t\t\"goodFunds\" : true,\n"
          + "\t\t\"authUserMultiCardAccount\" : false,\n"
          + "\t\t\"cashAdvanceAccount\" : false,\n"
          + "\t\t\"id\" : \"********-A64A-3BCD-8E1A-88785C9CDA12\",\n"
          + "\t\t\"sweepAccount\" : false,\n"
          + "\t\t\"accountType\" : \"DDA\",\n"
          + "\t\t\"interestOnLastStatement\" : 0.19,\n"
          + "\t\t\"remoteAccountDetailsTemplate\" : false,\n"
          + "\t\t\"accountDescription\" : \"5\\/3 ENHANCED CHECKING\",\n"
          + "\t\t\"brokerageAccount\" : false,\n"
          + "\t\t\"displayFullAccountAndRoutingOk\" : true,\n"
          + "\t\t\"accountDetailsExternal\" : false,\n"
          + "\t\t\"reloadablePrepaidCard\" : false,\n"
          + "\t\t\"linkedPca\" : false,\n"
          + "\t\t\"hasParentAccountNumber\" : false,\n"
          + "\t\t\"activitySettingManageAlertsOk\" : true,\n"
          + "\t\t\"displayCardManagementOk\": true,\n"
          + "\t\t\"closed\" : false,\n"
          + "\t\t\"checksAccount\" : true,\n"
          + "\t\t\"interestYtd\" : 0.55,\n"
          + "\t\t\"bankEmployeeAccount\" : false,\n"
          + "\t\t\"business\" : false,\n"
          + "\t\t\"revolvingLineOfCreditAccount\" : false,\n"
          + "\t\t\"expressChecking\" : false,\n"
          + "\t\t\"basicChecking\" : false,\n"
          + "\t\t\"affiliate\" : \"NPAL\",\n"
          + "\t\t\"searchableOk\" : true,\n"
          + "\t\t\"openDate\" : \"2006-07-20\",\n"
          + "\t\t\"statementEligible\" : true,\n"
          + "\t\t\"ledgerBalance\" : 10000.9,\n"
          + "\t\t\"availableBalance\" : 15937.9,\n"
          + "\t\t\"interestSinceLastStatement\" : 0.08,\n"
          + "\t\t\"customerDataVendorManaged\" : false,\n"
          + "\t\t\"activitySettingActivateCardOk\" : true,\n"
          + "\t\t\"checksReorder\" : true,\n"
          + "\t\t\"transferToOk\" : true,\n"
          + "\t\t\"displayTransactionOk\" : true,\n"
          + "\t\t\"interestLastYear\" : 1.62,\n"
          + "\t\t\"activitySettingChangePinOk\" : true,\n"
          + "\t\t\"billPayOk\" : true,\n"
          + "\t\t\"blockedForCredit\" : false,\n"
          + "\t\t\"supportsStatementCycles\" : true,\n"
          + "\t\t\"displayName\" : \"5\\/3 ENHANCED CHECKING\",\n"
          + "\t\t\"hidden\" : false,\n"
          + "\t\t\"displayAccountDetailsOk\" : true,\n"
          + "\t\t\"cashAdvanceEligible\" : false,\n"
          + "\t\t\"shortName\" : \"CK1\",\n"
          + "\t\t\"sortOrder\" : 0,\n"
          + "\t\t\"liability\" : false,\n"
          + "\t\t\"noBalanceDisplay\" : false,\n"
          + "\t\t\"depositAccount\" : true,\n"
          + "\t\t\"displayAccountNumber\" : \"X5534\",\n"
          + "\t\t\"ableChecking\" : false,\n"
          + "\t\t\"fromTransferOk\" : true,\n"
          + "\t\t\"changeAddressEligible\" : true,\n"
          + "\t\t\"creditCardAccount\" : false,\n"
          + "\t\t\"cardReplacementCode\" : \"CARD_ACTIVE\",\n"
          + "\t\t\"trustAccount\" : false,\n"
          + "\t\t\"transactionExportEligible\" : true,\n"
          + "\t\t\"externalTransferEligible\" : true\n"
          + "\t  }";
  private final String exampleProfileEntry =
      "      {\n"
          + "        \"id\" : \"********-A64A-3BCD-8E1A-88785C9CDA12\",\n"
          + "        \"canAddPayAccount\" : false,\n"
          + "        \"displayAccountNumber\" : \"X5534\",\n"
          + "        \"availableSources\" : [\n"
          + "          {\n"
          + "            \"minimumAmount\" : 0.01,\n"
          + "            \"maximumAmount\" : 5000000,\n"
          + "            \"id\" : \"E0C5748F-60DF-885A-53E9-616DCD656054\",\n"
          + "            \"latestAvailableDate\" : \"2019-06-14\",\n"
          + "            \"additionalAmountFields\" : [\n"
          + "  \n"
          + "            ],\n"
          + "            \"activityType\" : \"INTERNAL_TRANSFER\",\n"
          + "            \"recurringSupported\" : true,\n"
          + "            \"earliestAvailableDate\" : \"2018-05-14\",\n"
          + "            \"memoSupported\" : false\n"
          + "          },\n"
          + "          {\n"
          + "            \"minimumAmount\" : 0.01,\n"
          + "            \"maximumAmount\" : 5000000,\n"
          + "            \"id\" : \"********-2007-93AD-7CF0-E1D7394985AA\",\n"
          + "            \"latestAvailableDate\" : \"2019-06-14\",\n"
          + "            \"additionalAmountFields\" : [\n"
          + "  \n"
          + "            ],\n"
          + "            \"activityType\" : \"INTERNAL_TRANSFER\",\n"
          + "            \"recurringSupported\" : true,\n"
          + "            \"earliestAvailableDate\" : \"2018-05-14\",\n"
          + "            \"memoSupported\" : false\n"
          + "          },\n"
          + "          {\n"
          + "            \"minimumAmount\" : 0.01,\n"
          + "            \"maximumAmount\" : 5000000,\n"
          + "            \"id\" : \"A78F7A75-B787-986C-2113-1AAAF2A6852F\",\n"
          + "            \"latestAvailableDate\" : \"2019-06-14\",\n"
          + "            \"additionalAmountFields\" : [\n"
          + "  \n"
          + "            ],\n"
          + "            \"activityType\" : \"INTERNAL_TRANSFER\",\n"
          + "            \"recurringSupported\" : true,\n"
          + "            \"earliestAvailableDate\" : \"2018-05-14\",\n"
          + "            \"memoSupported\" : false\n"
          + "          },\n"
          + "          {\n"
          + "            \"minimumAmount\" : 0.01,\n"
          + "            \"maximumAmount\" : 5000000,\n"
          + "            \"id\" : \"77A62961-61E4-D0A2-7530-7C47477090AD\",\n"
          + "            \"latestAvailableDate\" : \"2019-06-14\",\n"
          + "            \"additionalAmountFields\" : [\n"
          + "  \n"
          + "            ],\n"
          + "            \"activityType\" : \"INTERNAL_TRANSFER\",\n"
          + "            \"recurringSupported\" : true,\n"
          + "            \"earliestAvailableDate\" : \"2018-05-14\",\n"
          + "            \"memoSupported\" : false\n"
          + "          },\n"
          + "          {\n"
          + "            \"minimumAmount\" : 0.01,\n"
          + "            \"maximumAmount\" : 5000000,\n"
          + "            \"id\" : \"72395E36-3AE4-BB93-4CA3-4540BC171DCA\",\n"
          + "            \"latestAvailableDate\" : \"2019-06-14\",\n"
          + "            \"additionalAmountFields\" : [\n"
          + "  \n"
          + "            ],\n"
          + "            \"activityType\" : \"INTERNAL_TRANSFER\",\n"
          + "            \"recurringSupported\" : true,\n"
          + "            \"earliestAvailableDate\" : \"2018-05-14\",\n"
          + "            \"memoSupported\" : false\n"
          + "          },\n"
          + "          {\n"
          + "            \"minimumAmount\" : 0.01,\n"
          + "            \"maximumAmount\" : 5000000,\n"
          + "            \"id\" : \"539EAD6F-20ED-C6B5-3611-1F056295014A\",\n"
          + "            \"latestAvailableDate\" : \"2019-06-14\",\n"
          + "            \"additionalAmountFields\" : [\n"
          + "  \n"
          + "            ],\n"
          + "            \"activityType\" : \"INTERNAL_TRANSFER\",\n"
          + "            \"recurringSupported\" : true,\n"
          + "            \"earliestAvailableDate\" : \"2018-05-14\",\n"
          + "            \"memoSupported\" : false\n"
          + "          }\n"
          + "        ],\n"
          + "        \"active\" : true,\n"
          + "        \"availableRecipients\" : [\n"
          + "          {\n"
          + "            \"minimumAmount\" : 0.01,\n"
          + "            \"maximumAmount\" : 5000000,\n"
          + "            \"id\" : \"E0C5748F-60DF-885A-53E9-616DCD656054\",\n"
          + "            \"latestAvailableDate\" : \"2019-06-14\",\n"
          + "            \"additionalAmountFields\" : [\n"
          + "  \n"
          + "            ],\n"
          + "            \"activityType\" : \"INTERNAL_TRANSFER\",\n"
          + "            \"recurringSupported\" : true,\n"
          + "            \"earliestAvailableDate\" : \"2018-05-14\",\n"
          + "            \"memoSupported\" : false\n"
          + "          },\n"
          + "          {\n"
          + "            \"minimumAmount\" : 0.01,\n"
          + "            \"maximumAmount\" : 5000000,\n"
          + "            \"id\" : \"********-2007-93AD-7CF0-E1D7394985AA\",\n"
          + "            \"latestAvailableDate\" : \"2019-06-14\",\n"
          + "            \"additionalAmountFields\" : [\n"
          + "  \n"
          + "            ],\n"
          + "            \"activityType\" : \"INTERNAL_TRANSFER\",\n"
          + "            \"recurringSupported\" : true,\n"
          + "            \"earliestAvailableDate\" : \"2018-05-14\",\n"
          + "            \"memoSupported\" : false\n"
          + "          },\n"
          + "          {\n"
          + "            \"minimumAmount\" : 0.01,\n"
          + "            \"maximumAmount\" : 5000000,\n"
          + "            \"id\" : \"A78F7A75-B787-986C-2113-1AAAF2A6852F\",\n"
          + "            \"latestAvailableDate\" : \"2019-06-14\",\n"
          + "            \"additionalAmountFields\" : [\n"
          + "  \n"
          + "            ],\n"
          + "            \"activityType\" : \"INTERNAL_TRANSFER\",\n"
          + "            \"recurringSupported\" : true,\n"
          + "            \"earliestAvailableDate\" : \"2018-05-14\",\n"
          + "            \"memoSupported\" : false\n"
          + "          },\n"
          + "          {\n"
          + "            \"minimumAmount\" : 0.01,\n"
          + "            \"maximumAmount\" : 5000000,\n"
          + "            \"id\" : \"77A62961-61E4-D0A2-7530-7C47477090AD\",\n"
          + "            \"latestAvailableDate\" : \"2019-06-14\",\n"
          + "            \"additionalAmountFields\" : [\n"
          + "  \n"
          + "            ],\n"
          + "            \"activityType\" : \"INTERNAL_TRANSFER\",\n"
          + "            \"recurringSupported\" : true,\n"
          + "            \"earliestAvailableDate\" : \"2018-05-14\",\n"
          + "            \"memoSupported\" : false\n"
          + "          },\n"
          + "          {\n"
          + "            \"minimumAmount\" : 0.01,\n"
          + "            \"maximumAmount\" : 5000000,\n"
          + "            \"id\" : \"72395E36-3AE4-BB93-4CA3-4540BC171DCA\",\n"
          + "            \"latestAvailableDate\" : \"2019-06-14\",\n"
          + "            \"additionalAmountFields\" : [\n"
          + "  \n"
          + "            ],\n"
          + "            \"activityType\" : \"INTERNAL_TRANSFER\",\n"
          + "            \"recurringSupported\" : true,\n"
          + "            \"earliestAvailableDate\" : \"2018-05-14\",\n"
          + "            \"memoSupported\" : false\n"
          + "          },\n"
          + "          {\n"
          + "            \"minimumAmount\" : 0.01,\n"
          + "            \"maximumAmount\" : 5000000,\n"
          + "            \"id\" : \"539EAD6F-20ED-C6B5-3611-1F056295014A\",\n"
          + "            \"latestAvailableDate\" : \"2019-06-14\",\n"
          + "            \"additionalAmountFields\" : [\n"
          + "              \"additionalPrincipleAmount\"\n"
          + "            ],\n"
          + "            \"activityType\" : \"INTERNAL_TRANSFER\",\n"
          + "            \"recurringSupported\" : true,\n"
          + "            \"earliestAvailableDate\" : \"2018-05-14\",\n"
          + "            \"memoSupported\" : false\n"
          + "          },\n"
          + "          {\n"
          + "            \"minimumAmount\" : 0.01,\n"
          + "            \"maximumAmount\" : 5000000,\n"
          + "            \"id\" : \"4423B530-EEF6-4B52-B6A7-3C20F6A1F28F\",\n"
          + "            \"latestAvailableDate\" : \"2019-06-14\",\n"
          + "            \"additionalAmountFields\" : [\n"
          + "              \"additionalPrincipleAmount\"\n"
          + "            ],\n"
          + "            \"activityType\" : \"INTERNAL_TRANSFER\",\n"
          + "            \"recurringSupported\" : true,\n"
          + "            \"earliestAvailableDate\" : \"2018-05-14\",\n"
          + "            \"memoSupported\" : false\n"
          + "          },\n"
          + "          {\n"
          + "            \"minimumAmount\" : 0.01,\n"
          + "            \"maximumAmount\" : 50000,\n"
          + "            \"id\" : \"A718C70A-D4E8-DD27-AB64-E1FE8C7E9B9C\",\n"
          + "            \"latestAvailableDate\" : \"2019-05-13\",\n"
          + "            \"additionalAmountFields\" : [\n"
          + "  \n"
          + "            ],\n"
          + "            \"activityType\" : \"BILLPAY\",\n"
          + "            \"recurringSupported\" : true,\n"
          + "            \"earliestAvailableDate\" : \"2018-05-15\",\n"
          + "            \"memoSupported\" : true\n"
          + "          },\n"
          + "          {\n"
          + "            \"minimumAmount\" : 0.01,\n"
          + "            \"maximumAmount\" : 50000,\n"
          + "            \"id\" : \"51840AB2-6607-A207-74A3-ACA2533D7E65\",\n"
          + "            \"latestAvailableDate\" : \"2019-05-13\",\n"
          + "            \"additionalAmountFields\" : [\n"
          + "  \n"
          + "            ],\n"
          + "            \"activityType\" : \"BILLPAY\",\n"
          + "            \"recurringSupported\" : true,\n"
          + "            \"earliestAvailableDate\" : \"2018-05-18\",\n"
          + "            \"memoSupported\" : true\n"
          + "          },\n"
          + "          {\n"
          + "            \"minimumAmount\" : 0.01,\n"
          + "            \"maximumAmount\" : 50000,\n"
          + "            \"id\" : \"08385F8A-555F-DEC6-39D6-BFD6F1606BF3\",\n"
          + "            \"latestAvailableDate\" : \"2019-05-13\",\n"
          + "            \"additionalAmountFields\" : [\n"
          + "  \n"
          + "            ],\n"
          + "            \"activityType\" : \"BILLPAY\",\n"
          + "            \"recurringSupported\" : true,\n"
          + "            \"earliestAvailableDate\" : \"2018-05-18\",\n"
          + "            \"memoSupported\" : true\n"
          + "          },\n"
          + "          {\n"
          + "            \"minimumAmount\" : 0.01,\n"
          + "            \"maximumAmount\" : 50000,\n"
          + "            \"id\" : \"EE409A94-6EF6-7700-2889-FE8935F1FCCB\",\n"
          + "            \"latestAvailableDate\" : \"2019-05-13\",\n"
          + "            \"additionalAmountFields\" : [\n"
          + "  \n"
          + "            ],\n"
          + "            \"activityType\" : \"BILLPAY\",\n"
          + "            \"recurringSupported\" : true,\n"
          + "            \"earliestAvailableDate\" : \"2018-05-18\",\n"
          + "            \"memoSupported\" : true\n"
          + "          },\n"
          + "          {\n"
          + "            \"minimumAmount\" : 0.01,\n"
          + "            \"maximumAmount\" : 50000,\n"
          + "            \"id\" : \"DF68B239-0A11-A01C-EAA1-A4A70F44BAFE\",\n"
          + "            \"latestAvailableDate\" : \"2019-05-13\",\n"
          + "            \"additionalAmountFields\" : [\n"
          + "  \n"
          + "            ],\n"
          + "            \"activityType\" : \"BILLPAY\",\n"
          + "            \"recurringSupported\" : true,\n"
          + "            \"earliestAvailableDate\" : \"2018-05-18\",\n"
          + "            \"memoSupported\" : true\n"
          + "          }\n"
          + "        ],\n"
          + "        \"paymentRecipient\" : false,\n"
          + "        \"canAddPayee\" : true,\n"
          + "        \"canAddRecurring\" : true,\n"
          + "        \"type\" : \"DDA\",\n"
          + "        \"verificationRequired\" : false,\n"
          + "        \"transferSource\" : true,\n"
          + "        \"electronicBillingEnabled\" : true,\n"
          + "        \"invoiceEnabled\" : true,\n"
          + "        \"canAddTransferAccount\" : true,\n"
          + "        \"displayName\" : \"5\\/3 ENHANCED CHECKING\",\n"
          + "        \"invoiceDetails\":  {\n"
          + "            \"totalAccountBalancePayment\": 0.0,\n "
          + "              \"minimumPaymentAmount\": 0.0,\n"
          + "              \"amountDuePayment\": 0.0,\n"
          + "              \"otherAmountPayment\": 0.0,\n"
          + "              \"invoiceId\": \"**********\"\n"
          + "           }\n"
          + "      },\n";
}
