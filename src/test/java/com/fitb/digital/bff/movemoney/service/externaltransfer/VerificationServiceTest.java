/* Copyright 2022 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.service.externaltransfer;

import static com.fitb.digital.bff.movemoney.utils.TestUtils.mockFromFile;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import com.fitb.digital.bff.movemoney.client.CesClient;
import com.fitb.digital.bff.movemoney.exceptions.CesFeignException;
import com.fitb.digital.bff.movemoney.model.bff.external.transfer.requests.BffRealTimeVerificationRequest;
import com.fitb.digital.bff.movemoney.model.bff.external.transfer.requests.BffVerifyTrialDepositsRequest;
import com.fitb.digital.bff.movemoney.model.bff.external.transfer.requests.VerificationParameter;
import com.fitb.digital.bff.movemoney.model.client.CesBaseResponse;
import com.fitb.digital.bff.movemoney.model.client.CesResponse;
import com.fitb.digital.bff.movemoney.model.client.external.transfer.requests.CesAddExternalAccountRequest;
import com.fitb.digital.bff.movemoney.model.client.external.transfer.responses.*;
import com.fitb.digital.lib.tokenization.tokenizer.Tokenizer;
import java.util.Arrays;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentMatcher;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;

@ExtendWith(MockitoExtension.class)
class VerificationServiceTest {
  @InjectMocks private VerificationService verificationService;
  @Mock private CesClient cesClient;
  @Mock private ExternalTransferService externalTransferService;
  @Mock private Tokenizer tokenizer;

  @Test
  void addExternalAccountWithRealTime_bffRequestMappingStep1() {
    when(cesClient.addExternalAccountByRealTime(any()))
        .thenReturn(
            mockFromFile(
                "service/add_extern_acct_real_time_rs1.json",
                CesAddExternalAccountRealTimeResponse.class));
    var bffRealTimeRequest = new BffRealTimeVerificationRequest();
    bffRealTimeRequest.setAccountId("my-bologna-has-a-first-name");
    VerificationParameter[] parameters = {
      new VerificationParameter("username", "Enter your username."),
      new VerificationParameter("password", "Enter your password.")
    };
    bffRealTimeRequest.setVerificationParameters(Arrays.asList(parameters));
    var realTimeResponse = verificationService.verifyExternalAccountRealTime(bffRealTimeRequest);
    assertEquals("SUCCESS", realTimeResponse.getStatus());
    assertEquals("5092", realTimeResponse.getVerificationStatus().getStatusCode());
    assertEquals(
        "Two FA Online Account Verification",
        realTimeResponse.getVerificationStatus().getStatusDesc());
    assertEquals("X0000", realTimeResponse.getVerificationStatus().getAccountNumber());
    assertEquals("online", realTimeResponse.getVerificationStatus().getVerificationMode());
    assertEquals(
        "Requires Approval", realTimeResponse.getVerificationStatus().getVerificationStatus());
    assertEquals("303", realTimeResponse.getVerificationStatus().getFailureReasonCode());
    assertEquals("303", realTimeResponse.getVerificationStatus().getFailureReasonDesc());
    assertEquals(1, realTimeResponse.getParameters().size());
  }

  @Test
  void verifyTrialDeposits_mapsCesResponseOnSuccess() {
    var cesResponse = new CesBaseResponse();
    cesResponse.setStatus("SUCCESS");
    when(cesClient.verifyTrialDepositAmounts(any())).thenReturn(cesResponse);

    var result = verificationService.verifyTrialDeposits(new BffVerifyTrialDepositsRequest());

    assertEquals("SUCCESS", result.getStatus());
  }

  public static class CesAddExternalAccountRequestMatcher
      implements ArgumentMatcher<CesAddExternalAccountRequest> {
    @Override
    public boolean matches(CesAddExternalAccountRequest request) {
      return true;
    }
  }

  @Test
  void initiateTrialDeposits_mapsCesResponse() {
    var cesResponse = new CesAddExternalAccountResponse();
    var cesExternalAccountVerificationStatus = new CesExternalAccountVerificationStatus();
    cesExternalAccountVerificationStatus.setStatusCode("0");
    cesResponse.setCesExternalAccountVerificationStatus(cesExternalAccountVerificationStatus);

    when(cesClient.addExternalAccountByTrialDeposit(any())).thenReturn(cesResponse);

    var result = verificationService.initiateTrialDeposits("some id", "", "");

    assertEquals("0", result.getExternalAccountVerificationStatus().getStatusCode());
    verify(tokenizer).tokenize(anyString(), argThat(new CesAddExternalAccountRequestMatcher()));
  }

  @Test
  void initiateTrialDepoitsWithBrokerage_handlesFISeedDataError_byMakingRequestToCes() {
    CesResponse cesResponse = new CesBaseResponse();
    cesResponse.setStatus("NOT_FOUND");
    cesResponse.setStatusCode("FIID_NOT_FOUND");
    cesResponse.setStatusReason("FIID missing. Did you forget to fetch FISeedData");

    CesFeignException cesFeignException =
        new CesFeignException(HttpStatus.NOT_FOUND, "some reason");
    cesFeignException.setCesResponse(cesResponse);

    var cesAddExternalAccountResponse = new CesAddExternalAccountResponse();
    cesAddExternalAccountResponse.setCesExternalAccountVerificationStatus(
        new CesExternalAccountVerificationStatus());

    when(cesClient.addExternalAccountByTrialDeposit(any()))
        .thenThrow(cesFeignException)
        .thenReturn(cesAddExternalAccountResponse);

    var result = verificationService.initiateTrialDeposits("some id", "brokerage", null);

    verify(externalTransferService, times(1))
        .getFinancialInstitutionInfoForBrokerageName("brokerage");
  }

  @Test
  void initiateTrialDepoitsWithRoutingNumber_handlesFISeedDataError_byMakingRequestToCes() {
    CesResponse cesResponse = new CesBaseResponse();
    cesResponse.setStatus("NOT_FOUND");
    cesResponse.setStatusCode("FIID_NOT_FOUND");
    cesResponse.setStatusReason("FIID missing. Did you forget to fetch FISeedData");

    CesFeignException cesFeignException =
        new CesFeignException(HttpStatus.NOT_FOUND, "some reason");
    cesFeignException.setCesResponse(cesResponse);

    var cesAddExternalAccountResponse = new CesAddExternalAccountResponse();
    cesAddExternalAccountResponse.setCesExternalAccountVerificationStatus(
        new CesExternalAccountVerificationStatus());

    when(cesClient.addExternalAccountByTrialDeposit(any()))
        .thenThrow(cesFeignException)
        .thenReturn(cesAddExternalAccountResponse);

    var result = verificationService.initiateTrialDeposits("some id", null, "01234");

    verify(externalTransferService, times(1)).getFinancialInstitutionInfoForRoutingNumber("01234");
  }

  @Test
  void initiateTrialDepoitsWithRoutingNumber_bubblesError_whenCesFailsTwice() {
    CesResponse cesResponse = new CesBaseResponse();
    cesResponse.setStatus("NOT_FOUND");
    cesResponse.setStatusCode("FIID_NOT_FOUND");
    cesResponse.setStatusReason("FIID missing. Did you forget to fetch FISeedData");

    CesFeignException cesFeignException =
        new CesFeignException(HttpStatus.NOT_FOUND, "some reason");
    cesFeignException.setCesResponse(cesResponse);

    var cesAddExternalAccountResponse = new CesAddExternalAccountResponse();
    cesAddExternalAccountResponse.setCesExternalAccountVerificationStatus(
        new CesExternalAccountVerificationStatus());

    when(cesClient.addExternalAccountByTrialDeposit(any())).thenThrow(cesFeignException);

    assertThrows(
        CesFeignException.class,
        () -> verificationService.initiateTrialDeposits("some id", null, "01234"));
  }

  @Test
  void initiateTrialDepoitsWithRoutingNumber_rethrowsError_whenCesFailsWithNonSeedDataError() {
    CesResponse cesResponse = new CesBaseResponse();
    cesResponse.setStatus("NOT_FOUND");
    cesResponse.setStatusCode("FIID_NOT_FOUND");
    cesResponse.setStatusReason("FIID missing. Did you forget to fetch FISeedData");

    CesFeignException cesFeignException =
        new CesFeignException(HttpStatus.INTERNAL_SERVER_ERROR, "some reason");
    cesFeignException.setCesResponse(cesResponse);

    var cesAddExternalAccountResponse = new CesAddExternalAccountResponse();
    cesAddExternalAccountResponse.setCesExternalAccountVerificationStatus(
        new CesExternalAccountVerificationStatus());

    when(cesClient.addExternalAccountByTrialDeposit(any())).thenThrow(cesFeignException);

    assertThrows(
        CesFeignException.class,
        () -> verificationService.initiateTrialDeposits("some id", null, "01234"));
  }
}
