/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.service;

import static com.fitb.digital.bff.movemoney.util.FeignWrapper.mapFeignClientException;
import static org.junit.jupiter.api.Assertions.*;

import com.fitb.digital.bff.movemoney.exceptions.CesFeignException;
import com.fitb.digital.bff.movemoney.model.bff.BffResponse;
import com.fitb.digital.bff.movemoney.model.bff.BffSimpleResponse;
import com.fitb.digital.bff.movemoney.model.client.CesBaseResponse;
import com.fitb.digital.bff.movemoney.model.client.CesResponse;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

@ExtendWith(MockitoExtension.class)
public class CesClientServiceTest {
  @InjectMocks private CesClientService cesClientService;

  private CesResponse returnBaseResponse() {
    return new CesBaseResponse(CesResponse.SUCCESS, "", "");
  }

  private String returnStringResponse() {
    return "{}";
  }

  private String stringException() {
    throw new RuntimeException();
  }

  @Test
  void clientWrapper() {
    CesResponse response = cesClientService.callClient(() -> this.returnBaseResponse());
    assertNotNull(response);
    assertEquals(CesResponse.SUCCESS, response.getStatus());
  }

  @Test
  void mapFeignWrapperClientException() {
    CesFeignException fce = new CesFeignException(HttpStatus.BAD_REQUEST, "Example.");
    fce.setCesResponse(
        new CesBaseResponse(CesResponse.CLIENT_EXCEPTION, "BAD_REQUEST", "Read the doc."));
    ResponseEntity<BffResponse> responseEntity =
        new ResponseEntity<>(
            mapFeignClientException(BffSimpleResponse::new, fce), fce.getStatusCode());
    assertNotEquals("Example.", responseEntity.getBody().getStatusReason());
  }

  @Test
  void mapFeignWrapperClientExceptionWithoutCesResponse() {
    CesFeignException fce = new CesFeignException(HttpStatus.BAD_REQUEST, "Example.");
    ResponseEntity<BffResponse> responseEntity =
        new ResponseEntity<>(
            mapFeignClientException(BffSimpleResponse::new, fce), fce.getStatusCode());
    assertEquals("Example.", responseEntity.getBody().getStatusReason());
  }
}
