/* Copyright 2022 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.config;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertSame;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutionException;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.task.TaskExecutor;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

@SpringBootTest
@Slf4j
class AsyncConfigTest {
  @Autowired AsyncTestService asyncTestService;
  @Autowired TaskExecutor executor;

  @Test
  void testAsyncConfiged() throws ExecutionException, InterruptedException {
    Authentication mockAuthentication = Mockito.mock(Authentication.class);
    SecurityContextHolder.getContext().setAuthentication(mockAuthentication);
    CompletableFuture<Authentication> future = asyncTestService.getAuthentication();
    Authentication authentication = future.get();
    assertSame(mockAuthentication, authentication);
  }

  /**
   * The class is private so i cant do an instance of, this may break with spring security updates,
   * if so, just make sure the intent is still captured.
   */
  @Test
  void testSecurityContextMode() {
    assertEquals(
        "org.springframework.security.core.context.InheritableThreadLocalSecurityContextHolderStrategy",
        SecurityContextHolder.getContextHolderStrategy().getClass().getCanonicalName());
  }

  void testAuth() throws ExecutionException, InterruptedException {
    Authentication mockAuthentication = Mockito.mock(Authentication.class);
    SecurityContextHolder.getContext().setAuthentication(mockAuthentication);
    RequestContextHolder.setRequestAttributes(Mockito.mock(RequestAttributes.class));

    CompletableFuture<Authentication> future = asyncTestService.getAuthentication();
    Authentication authentication = future.get();
    assertSame(mockAuthentication, authentication);
  }

  @Test
  void testSecurityContextContamination() throws InterruptedException {
    List<Throwable> failures = new ArrayList<>();

    int numberOfThreads = 1000;
    CountDownLatch latch = new CountDownLatch(numberOfThreads);
    SecurityContextHolder.clearContext();

    final ThreadPoolTaskExecutor testExecutor = new ThreadPoolTaskExecutor();
    testExecutor.setMaxPoolSize(100);
    testExecutor.initialize();

    for (int i = 0; i < numberOfThreads; i++) {
      testExecutor.submit(
          () -> {
            try {
              testAuth();
            } catch (Throwable ex) {
              failures.add(ex);
            } finally {
              latch.countDown();
            }
          });
    }
    latch.await();
    for (Throwable ex : failures) {
      // log.error("ERROR:", ex);
    }
    assertEquals(0, failures.size());
  }
}
