/* Copyright 2024 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.client.configuration;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fitb.digital.bff.movemoney.exceptions.FeignClientException;
import com.fitb.digital.bff.movemoney.exceptions.FeignServerException;
import com.fitb.digital.lib.response.Error;
import com.fitb.digital.lib.response.InnerError;
import feign.Request;
import feign.Response;
import feign.codec.ErrorDecoder;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Stream;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.springframework.http.HttpStatus;

public class DefaultErrorDecoderTest {
  private ErrorDecoder errorDecoder;

  @BeforeEach
  void setUp() {
    errorDecoder = new DefaultErrorDecoder(new ObjectMapper());
  }

  @Test
  void decode_failToParse() {
    final int statusCode = 400;
    final Response response =
        Response.builder()
            .request(getRequest())
            .status(statusCode)
            .body(
                """
              {
                "not_a_valid_error": {
                  "a": 1,
                  "b": "test
                }
              }
              """,
                StandardCharsets.UTF_8)
            .build();
    final Exception expected =
        FeignClientException.builder().httpStatus(HttpStatus.valueOf(statusCode)).build();
    final Exception actual = errorDecoder.decode("", response);
    assertEquals(expected, actual);
  }

  @ParameterizedTest
  @MethodSource("client_status_codes")
  void decode_is4xx(final int statusCode) {
    final Exception expected =
        FeignClientException.builder()
            .httpStatus(HttpStatus.valueOf(statusCode))
            .error(getError())
            .build();
    verifyException(statusCode, expected);
  }

  private static Stream<Arguments> client_status_codes() {
    return Arrays.stream(HttpStatus.values())
        .filter(HttpStatus::is4xxClientError)
        .map(httpStatus -> Arguments.of(httpStatus.value()));
  }

  @ParameterizedTest
  @MethodSource("server_status_codes")
  void decode_is5xx(final int statusCode) {
    final Exception expected =
        FeignServerException.builder()
            .httpStatus(HttpStatus.valueOf(statusCode))
            .error(getError())
            .build();
    verifyException(statusCode, expected);
  }

  private static Stream<Arguments> server_status_codes() {
    return Arrays.stream(HttpStatus.values())
        .filter(HttpStatus::is5xxServerError)
        .map(httpStatus -> Arguments.of(httpStatus.value()));
  }

  private void verifyException(final int statusCode, final Exception expected) {
    final Response response = getResponse(statusCode);
    final Exception actual = errorDecoder.decode("", response);
    assertEquals(expected, actual);
  }

  private Request getRequest() {
    return Request.create(
        Request.HttpMethod.GET,
        "https://donotcare",
        Collections.emptyMap(),
        Request.Body.empty(),
        null);
  }

  private Response getResponse(final int statusCode) {
    return Response.builder()
        .request(getRequest())
        .status(statusCode)
        .body(
            """
          {
            "error": {
              "code": "error code",
              "message": "error message",
              "target": "error target",
              "details": [
                {
                  "code": "detail code",
                  "message": "detail message",
                  "target": "detail target"
                }
              ],
              "innerError": {
                "code": "inner code"
              }
            }
          }
          """,
            StandardCharsets.UTF_8)
        .build();
  }

  private Error getError() {
    return Error.builder()
        .code("error code")
        .message("error message")
        .target("error target")
        .details(
            List.of(
                Error.builder()
                    .code("detail code")
                    .message("detail message")
                    .target("detail target")
                    .build()))
        .innerError(InnerError.builder().code("inner code").build())
        .build();
  }
}
