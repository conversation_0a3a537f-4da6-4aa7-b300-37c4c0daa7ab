/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.utils;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fitb.digital.bff.movemoney.client.xferandpayorc.model.XferAndPayProfile;
import com.fitb.digital.bff.movemoney.model.bff.account.responses.BffAccountDetailResponse;
import com.fitb.digital.bff.movemoney.model.bff.external.transfer.responses.BffExternalAccount;
import com.fitb.digital.bff.movemoney.model.client.account.responses.ClientAccountDetailResponse;
import com.fitb.digital.bff.movemoney.model.client.account.responses.ListResponse;
import com.fitb.digital.bff.movemoney.model.client.activity.responses.ClientActivityResponse;
import com.fitb.digital.bff.movemoney.model.client.external.transfer.responses.CesExternalAccount;
import com.fitb.digital.bff.movemoney.model.client.profile.responses.ProfileResponse;
import java.io.File;
import java.io.FileNotFoundException;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;
import org.modelmapper.Converter;
import org.modelmapper.ModelMapper;
import org.modelmapper.spi.MappingContext;

public class TestUtils {

  public static ProfileResponse mockCESProfileResponse() {
    return mockFromFile("mock_ces_profile_response.json", ProfileResponse.class);
  }

  public static XferAndPayProfile mockXferAndPayOrcProfileResponse() {
    return mockFromFile("mock_xfer_and_pay_orc_profile_response.json", XferAndPayProfile.class);
  }

  public static ProfileResponse mockCESFullProfileResponse() {
    return mockFromFile("mock_ces_full_profile.json", ProfileResponse.class);
  }

  public static XferAndPayProfile mockXferAndPayOrcFullProfileResponse() {
    return mockFromFile("mock_xfer_and_pay_orc_full_profile.json", XferAndPayProfile.class);
  }

  public static ClientActivityResponse mockCESActivityResponse() {
    return mockFromFile("mock_ces_get_activity_response.json", ClientActivityResponse.class);
  }

  public static ListResponse mockCESListResponse() {
    return mockFromFile("mock_ces_list_response.json", ListResponse.class);
  }

  public static ListResponse mockCESFullAccountList() {
    return mockFromFile("mock_ces_full_account_list.json", ListResponse.class);
  }

  public static ClientAccountDetailResponse mockAccountDetails() {
    return mockFromFile("details.json", ClientAccountDetailResponse.class);
  }

  public static ClientAccountDetailResponse mockCreditCardDetails() {
    return mockFromFile("credit_card_details.json", ClientAccountDetailResponse.class);
  }

  public static BffAccountDetailResponse mockBffCreditCardDetails() {
    return mockFromFile("credit_card_details.json", BffAccountDetailResponse.class);
  }

  public static ClientAccountDetailResponse mockInstallmentLoanDetails() {
    return mockFromFile("installment_loan_details.json", ClientAccountDetailResponse.class);
  }

  public static List<BffExternalAccount> cesExternalAccountsToBffExternalAccounts(
      List<CesExternalAccount> accountDataList) {
    var mapper = new ModelMapper();
    mapper.addConverter(getStringToBigDecimalConverter());
    return accountDataList.stream()
        .filter(
            acct ->
                !acct.getIsHostAccount()
                    .equals("1")) // Host accounts are internal to 5/3, do not display to user
        .map(
            account -> {
              // TODO replace with simple map once other verification methods are supported.
              var bff = mapper.map(account, BffExternalAccount.class);
              bff.patchTrialDepositStatus();
              return bff;
            })
        .collect(Collectors.toList());
  }

  public static <T> T mockFromFile(String fileName, Class<T> clazz) {
    T response = null;
    try {
      ObjectMapper mapper = new ObjectMapper();
      mapper.registerModule(new JavaTimeModule());
      mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
      response =
          mapper.readValue(new File(String.format("src/test/resources/%s", fileName)), clazz);
    } catch (FileNotFoundException ex) {
      org.slf4j.LoggerFactory.getLogger(TestUtils.class)
          .error(
              String.format("%s not found. Did you add it to the resource directory?", fileName));
    } catch (Exception ex) {
      org.slf4j.LoggerFactory.getLogger(TestUtils.class)
          .error(String.format("Error reading %s", fileName), ex);
    }
    return response;
  }

  private static Converter<String, BigDecimal> getStringToBigDecimalConverter() {
    return new Converter<>() {
      public BigDecimal convert(MappingContext<String, BigDecimal> context) {
        return new BigDecimal(context.getSource().replace(",", ""));
      }
    };
  }
}
