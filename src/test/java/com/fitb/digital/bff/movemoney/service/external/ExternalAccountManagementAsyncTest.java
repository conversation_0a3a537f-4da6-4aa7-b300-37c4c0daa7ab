/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.service.external;

import static com.fitb.digital.bff.movemoney.utils.TestUtils.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.fitb.digital.bff.movemoney.client.CesClient;
import com.fitb.digital.bff.movemoney.client.CesClientShortTimeout;
import com.fitb.digital.bff.movemoney.model.bff.external.transfer.responses.BffExternalAccount;
import com.fitb.digital.bff.movemoney.model.client.external.transfer.responses.CesGetUserAccountsResponse;
import com.fitb.digital.bff.movemoney.model.client.invoicedpayment.responses.CesInvoicedPaymentAccountResponse;
import com.fitb.digital.bff.movemoney.model.client.webpayment.responses.CesWebpaymentProfileResponse;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@Slf4j
@ExtendWith(MockitoExtension.class)
public class ExternalAccountManagementAsyncTest {
  @InjectMocks private ExternalAccountManagementAsync accountManagementAsync;
  @Mock private CesClient cesClient;
  @Mock private CesClientShortTimeout cesClientShortTimeout;

  @Test
  void getExternalTransferAccounts() throws ExecutionException, InterruptedException {
    when(cesClientShortTimeout.getExternalTransferUserAccounts())
        .thenReturn(new CesGetUserAccountsResponse());
    CompletableFuture<List<BffExternalAccount>> userAccountsFork =
        accountManagementAsync.getExternalTransferAccounts();
    List<BffExternalAccount> accountList = userAccountsFork.get();
    assertEquals(0, accountList.size());
  }

  @Test
  void getWebPaymentProfile() throws ExecutionException, InterruptedException {
    when(cesClient.getWebPaymentProfile()).thenReturn(new CesWebpaymentProfileResponse());
    CompletableFuture<CesWebpaymentProfileResponse> webPaymentsFork =
        accountManagementAsync.getWebPaymentProfile();
    CesWebpaymentProfileResponse response = webPaymentsFork.get();
    assertNotNull(response);
  }

  @Test
  void getInvoicedPaymentProfile() throws ExecutionException, InterruptedException {
    when(cesClient.getFundingAccount()).thenReturn(new CesInvoicedPaymentAccountResponse());
    CompletableFuture<CesInvoicedPaymentAccountResponse> webPaymentsFork =
        accountManagementAsync.getInvoicedPaymentProfile();
    CesInvoicedPaymentAccountResponse response = webPaymentsFork.get();
    assertNotNull(response);
  }
}
