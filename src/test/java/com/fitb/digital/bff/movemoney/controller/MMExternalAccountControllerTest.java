/* Copyright 2022 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.when;

import com.fitb.digital.bff.movemoney.model.bff.external.account.responses.BffExternalUserAccountsResponse;
import com.fitb.digital.bff.movemoney.service.external.ExternalAccountManagementService;
import java.util.ArrayList;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class MMExternalAccountControllerTest {
  @Mock private ExternalAccountManagementService service;

  @InjectMocks private MmExternalAccountController controller;

  @Test
  void getExternalAccounts() {
    var externalAccounts = new BffExternalUserAccountsResponse();
    externalAccounts.setTransferAccounts(new ArrayList<>());
    externalAccounts.setPaymentAccounts(new ArrayList<>());

    when(service.getAccounts()).thenReturn(externalAccounts);
    var response = controller.getAccounts();
    assertNotNull(response);
    assertEquals(0, response.getTransferAccounts().size());
    assertEquals(0, response.getPaymentAccounts().size());
  }
}
