/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.client.configuration;

import static org.junit.jupiter.api.Assertions.*;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fitb.digital.bff.movemoney.exceptions.CesFeignException;
import com.fitb.digital.bff.movemoney.model.client.CesBaseResponse;
import com.fitb.digital.bff.movemoney.model.client.CesResponse;
import feign.Request;
import feign.Response;
import feign.RetryableException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class FeignErrorDecoderTest {

  @Test
  public void nonRetryable504Error() {
    var decoder = new CesErrorDecoder();
    Request request =
        Request.create(
            Request.HttpMethod.GET,
            "https://donotcare",
            new HashMap<>(),
            Request.Body.empty(),
            null);
    Response response = Response.builder().status(504).request(request).build();
    Exception result = decoder.decode("SomeClient", response);
    assertEquals(CesFeignException.class, result.getClass());
  }

  @Test
  public void nonRetryable503Error() throws JsonProcessingException {
    var decoder = new CesErrorDecoder();
    Request request =
        Request.create(
            Request.HttpMethod.GET,
            "https://donotcare",
            new HashMap<>(),
            Request.Body.empty(),
            null);
    ObjectMapper mapper = new ObjectMapper();
    Response response =
        Response.builder()
            .status(503)
            .body(
                mapper.writeValueAsString(
                    new CesBaseResponse(CesResponse.ERROR, "", "Server down.")),
                StandardCharsets.UTF_8)
            .request(request)
            .build();
    Exception result = decoder.decode("SomeClient", response);
    assertEquals(CesFeignException.class, result.getClass());
  }

  @Test
  public void notRetryableError() throws JsonProcessingException {
    var decoder = new CesErrorDecoder();
    Request request =
        Request.create(
            Request.HttpMethod.GET,
            "https://donotcare",
            new HashMap<>(),
            Request.Body.empty(),
            null);
    ObjectMapper mapper = new ObjectMapper();
    Response response =
        Response.builder()
            .status(404)
            .body(
                mapper.writeValueAsString(new CesBaseResponse(CesResponse.SUCCESS, "", "")),
                StandardCharsets.UTF_8)
            .request(request)
            .build();
    Exception result = decoder.decode("SomeClient", response);
    assertNotEquals(RetryableException.class, result.getClass());
  }

  @Test
  public void unprocessableEntityResponse_whenAddingBillPayee_bubblesAppropriately() {
    var decoder = new CesErrorDecoder();
    Request request =
        Request.create(
            Request.HttpMethod.GET,
            "https://donotcare",
            new HashMap<>(),
            Request.Body.empty(),
            null);
    ObjectMapper mapper = new ObjectMapper();
    String cesResponse =
        "{\n"
            + "    \"status\": \"VALIDATION_ERROR\",\n"
            + "    \"statusCode\": \"ADDRESS_CHANGED\",\n"
            + "    \"cesPayee\": {\n"
            + "        \"cesAddress\": {\n"
            + "            \"@type\": \"CesAddress\",\n"
            + "            \"streetLine1\": \"1234 fun LN\",\n"
            + "            \"streetLine2\": \" \",\n"
            + "            \"city\": \"cincinnati\",\n"
            + "            \"stateOrProvince\": \"OH\",\n"
            + "            \"postalCode\": \"13210\",\n"
            + "            \"badAddressIndicator\": false\n"
            + "        }\n"
            + "    }\n"
            + "}";
    Response response =
        Response.builder()
            .status(422)
            .body(cesResponse, StandardCharsets.UTF_8)
            .request(request)
            .build();
    CesFeignException exception =
        (CesFeignException) decoder.decode("CesClient#addPayee(CesPayeeRequest)", response);
    assertEquals(cesResponse, exception.getCesResponse().getUnmappableBody());
  }

  @Test
  public void nonJsonMapsToUnmappableBody() {
    var decoder = new CesErrorDecoder();
    Request request =
        Request.create(
            Request.HttpMethod.GET,
            "https://donotcare",
            new HashMap<>(),
            Request.Body.empty(),
            null);
    ObjectMapper mapper = new ObjectMapper();
    String cesResponse = "{some garbage}";
    Response response =
        Response.builder()
            .status(422)
            .body(cesResponse, StandardCharsets.UTF_8)
            .request(request)
            .build();
    CesFeignException exception = (CesFeignException) decoder.decode("WhoCares", response);
    assertEquals(cesResponse, exception.getCesResponse().getUnmappableBody());
  }
}
