/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.service;

import static com.fitb.digital.bff.movemoney.utils.TestUtils.mockFromFile;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fitb.digital.bff.movemoney.client.CesClient;
import com.fitb.digital.bff.movemoney.constants.TokenizationProfiles;
import com.fitb.digital.bff.movemoney.exceptions.CesFeignException;
import com.fitb.digital.bff.movemoney.model.CesPayeeType;
import com.fitb.digital.bff.movemoney.model.bff.billpay.BffPayee;
import com.fitb.digital.bff.movemoney.model.bff.billpay.requests.BffAddPayeeRequest;
import com.fitb.digital.bff.movemoney.model.client.CesBaseResponse;
import com.fitb.digital.bff.movemoney.model.client.CesResponse;
import com.fitb.digital.bff.movemoney.model.client.billpay.CesPayee;
import com.fitb.digital.bff.movemoney.model.client.billpay.requests.CesPayeeRequest;
import com.fitb.digital.bff.movemoney.model.client.billpay.responses.*;
import com.fitb.digital.lib.tokenization.tokenizer.Tokenizer;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;

@Slf4j
@ExtendWith(MockitoExtension.class)
public class BillpayServiceTest {
  private BillpayService billpayService;
  @Mock private CesClient cesClient;

  @Mock private Tokenizer tokenizer;

  @BeforeEach
  void init() {
    billpayService = new BillpayService(tokenizer, cesClient);
  }

  @Test
  public void getGlobalPayeesHappyPath() {
    ClientGlobalPayeeResponse globalPayeeList =
        mockFromFile("globalPayees.json", ClientGlobalPayeeResponse.class);
    when(cesClient.getGlobalPayees()).thenReturn(globalPayeeList);
    var payeeList = billpayService.getGlobalPayees();
    assertNotNull(payeeList);
    assertFalse(payeeList.getGlobalPayees().isEmpty());
  }

  @Test
  public void getGlobalPayeesWithError() {
    when(cesClient.getGlobalPayees()).thenReturn(new ClientGlobalPayeeResponse(CesResponse.ERROR));
    var payeeList = billpayService.getGlobalPayees();
    assertNotNull(payeeList);
    assertTrue(payeeList.getGlobalPayees().isEmpty());
  }

  @Test
  public void addPayeeSuccess() {
    when(cesClient.addPayee(any()))
        .thenReturn(mockFromFile("service/add_payee_response.json", CesPayeeResponse.class));
    BffAddPayeeRequest addPayeeRequest = new BffAddPayeeRequest();
    addPayeeRequest.setName("Cincinnati Bell");
    addPayeeRequest.setAccountNumber("************");

    var payee = billpayService.createPayee(addPayeeRequest);
    assertNotNull(payee);
    assertNotNull(payee.getBffPayee());
  }

  @Test
  public void addPayeeWithNullNickname_DefaultsNicknameToName() {
    when(cesClient.addPayee(any()))
        .thenReturn(mockFromFile("service/add_payee_response.json", CesPayeeResponse.class));
    BffAddPayeeRequest addPayeeRequest = new BffAddPayeeRequest();
    addPayeeRequest.setName("Cincinnati Bell");
    addPayeeRequest.setAccountNumber("************");

    CesPayee cesPayee2 = new CesPayee();
    cesPayee2.setName("Cincinnati Bell");
    cesPayee2.setAccountNumber("************");
    cesPayee2.setCesPayeeType(CesPayeeType.TWOFACTOR);
    cesPayee2.setNickname(cesPayee2.getName());
    CesPayeeRequest addPayeeRequest2 = new CesPayeeRequest();
    addPayeeRequest2.setCesPayee(cesPayee2);

    when(tokenizer.tokenize(anyString(), any(CesPayeeRequest.class)))
        .thenReturn(new CesPayeeRequest());

    billpayService.createPayee(addPayeeRequest);

    verify(tokenizer).tokenize(TokenizationProfiles.CES_PROFILE, addPayeeRequest2);
  }

  @Test
  public void addPayeeWithEmptyNickname_DefaultsNicknameToName() {
    when(cesClient.addPayee(any()))
        .thenReturn(mockFromFile("service/add_payee_response.json", CesPayeeResponse.class));
    var bffAddPayeeRequest = new BffAddPayeeRequest();
    bffAddPayeeRequest.setName("Cincinnati Bell");
    bffAddPayeeRequest.setAccountNumber("************");
    bffAddPayeeRequest.setNickname("");

    CesPayee cesPayee2 = new CesPayee();
    cesPayee2.setName("Cincinnati Bell");
    cesPayee2.setAccountNumber("************");
    cesPayee2.setCesPayeeType(CesPayeeType.TWOFACTOR);
    cesPayee2.setNickname(cesPayee2.getName());
    CesPayeeRequest addPayeeRequest2 = new CesPayeeRequest();
    addPayeeRequest2.setCesPayee(cesPayee2);

    when(tokenizer.tokenize(anyString(), (CesPayeeRequest) any()))
        .thenReturn(new CesPayeeRequest());

    billpayService.createPayee(bffAddPayeeRequest);

    verify(tokenizer).tokenize(TokenizationProfiles.CES_PROFILE, addPayeeRequest2);
  }

  @Test
  public void addPayeeWithWhiteSpaceNickname_DefaultsNicknameToName() {
    when(cesClient.addPayee(any()))
        .thenReturn(mockFromFile("service/add_payee_response.json", CesPayeeResponse.class));
    var addPayeeRequest = new BffAddPayeeRequest();
    addPayeeRequest.setName("Cincinnati Bell");
    addPayeeRequest.setAccountNumber("************");
    addPayeeRequest.setNickname("     ");

    CesPayee cesPayee2 = new CesPayee();
    cesPayee2.setName("Cincinnati Bell");
    cesPayee2.setAccountNumber("************");
    cesPayee2.setCesPayeeType(CesPayeeType.TWOFACTOR);
    cesPayee2.setNickname(cesPayee2.getName());
    CesPayeeRequest addPayeeRequest2 = new CesPayeeRequest();
    addPayeeRequest2.setCesPayee(cesPayee2);

    when(tokenizer.tokenize(anyString(), (CesPayeeRequest) any()))
        .thenReturn(new CesPayeeRequest());

    billpayService.createPayee(addPayeeRequest);

    verify(tokenizer).tokenize(TokenizationProfiles.CES_PROFILE, addPayeeRequest2);
  }

  @Test
  public void updatePayeeSuccess() {
    var mockPayeeSuccess = mockFromFile("service/edit_payee_response.json", CesPayeeResponse.class);
    when(cesClient.updatePayee(any(CesPayeeRequest.class))).thenReturn(mockPayeeSuccess);

    when(tokenizer.tokenize(anyString(), any(CesPayeeRequest.class)))
        .thenReturn(new CesPayeeRequest());

    var payeeRequest = mockFromFile("service/edit_payee_request.json", BffPayee.class);
    var payee = billpayService.updatePayee(payeeRequest);
    assertNotNull(payee);
    assertEquals(HttpStatus.OK, payee.getCesHttpStatus());
  }

  @Test
  public void deletePayeeSuccess() {
    when(cesClient.deletePayee(anyString()))
        .thenReturn(
            mockFromFile("service/base_response_success.json", CesDeletePayeeResponse.class));
    var response = billpayService.deletePayee("some-payee-id");
    assertNotNull(response);
    assertNotNull(response.getStatus());
  }

  @Test
  public void deletePayeeSuccessHandlesNullBodyFromCes() { // CES returns a null body on a success
    when(cesClient.deletePayee(anyString())).thenReturn(null);
    var response = billpayService.deletePayee("some-payee-id");
    assertNotNull(response);
    assertNotNull(response.getStatus());
  }

  @Test
  public void getBillpayProfile() {
    when(cesClient.getProfile())
        .thenReturn(mockFromFile("service/billpay_profile.json", CesProfileResponse.class));
    var profile = billpayService.getProfile();
    assertNotNull(profile);
    assertFalse(profile.getPayees().isEmpty());
  }

  @Test
  public void getPayeeAccountHappyPath() {
    var payeeAccountResponse =
        mockFromFile("service/billpay/tokenizedPayeeAccount.json", CesPayeeAccountResponse.class);
    when(cesClient.getTokenizedPayeeAccount(anyString())).thenReturn(payeeAccountResponse);

    var mockPayeeAccount = new CesPayeeAccountResponse();
    mockPayeeAccount.setAccountNumber(payeeAccountResponse.getAccountNumber());
    when(tokenizer.detokenize(any(CesPayeeAccountResponse.class))).thenReturn(mockPayeeAccount);

    var payeeAccount = billpayService.getTokenizedPayeeAccount("payeeId");

    assertNotNull(payeeAccount);
    assertEquals(payeeAccountResponse.getAccountNumber(), payeeAccount.getAccountNumber());
  }

  @Test
  public void getPayeeAccountWithError() {
    CesPayeeAccountResponse errorResponse = new CesPayeeAccountResponse();
    errorResponse.setStatus(CesResponse.ERROR);

    when(cesClient.getTokenizedPayeeAccount(anyString())).thenReturn(errorResponse);

    var detokenizedPayeeAccountResponse = new CesPayeeAccountResponse();
    when(tokenizer.detokenize(any(CesPayeeAccountResponse.class)))
        .thenReturn(detokenizedPayeeAccountResponse);

    var accountResponse = billpayService.getTokenizedPayeeAccount("payeeId");
    assertNotNull(accountResponse);
    assertNull(accountResponse.getAccountNumber());
  }

  @Test
  void addPayeeBetterAddressResponse() throws JsonProcessingException {
    var bffAddPayeeRequest =
        mockFromFile("add_payee_request_6factor.json", BffAddPayeeRequest.class);
    var cesPayee =
        mockFromFile(
            "service/billpay/add_payee_response_address_fixed.json", CesPayeeResponse.class);
    CesFeignException cesFeignException =
        new CesFeignException(HttpStatus.UNPROCESSABLE_ENTITY, "status");
    CesBaseResponse cesBaseResponse = new CesBaseResponse();

    ObjectMapper objectMapper = new ObjectMapper();
    String asString = objectMapper.writeValueAsString(cesPayee);

    cesBaseResponse.setUnmappableBody(asString);
    cesFeignException.setCesResponse(cesBaseResponse);
    when(cesClient.addPayee(any())).thenThrow(cesFeignException);
    var bffPayee = billpayService.createPayee(bffAddPayeeRequest);
    assertEquals(HttpStatus.UNPROCESSABLE_ENTITY, bffPayee.getCesHttpStatus());
    assertEquals("ADDRESS_CHANGED", bffPayee.getStatusCode());
    assertNotNull(bffPayee.getBffPayee().getAddress());
  }

  @Test
  void addPayeeAddressDoesNotExist_BubblesCESResponse() throws JsonProcessingException {
    var bffAddPayeeRequest =
        mockFromFile("add_payee_invalid_address.json", BffAddPayeeRequest.class);
    CesFeignException cesFeignException =
        new CesFeignException(HttpStatus.UNPROCESSABLE_ENTITY, "status");
    CesBaseResponse cesBaseResponse = new CesBaseResponse();
    String asString = "{\"status\":\"VALIDATION_ERROR\",\"statusCode\":\"ADDRESS_NOT_FOUND\"}";

    cesBaseResponse.setUnmappableBody(asString);
    cesFeignException.setCesResponse(cesBaseResponse);
    when(cesClient.addPayee(any())).thenThrow(cesFeignException);
    var bffPayee = billpayService.createPayee(bffAddPayeeRequest);
    assertEquals(HttpStatus.UNPROCESSABLE_ENTITY, bffPayee.getCesHttpStatus());
    assertEquals("ADDRESS_NOT_FOUND", bffPayee.getStatusCode());
    assertEquals("VALIDATION_ERROR", bffPayee.getStatus());
  }

  @Test
  void addPayeeMinimumResponse() {
    var bffAddPayeeRequest =
        mockFromFile("add_payee_request_6factor.json", BffAddPayeeRequest.class);
    var cesPayee = mockFromFile("service/add_payee_428_minimum.json", CesBaseResponse.class);
    CesFeignException cesFeignException =
        new CesFeignException(HttpStatus.UNPROCESSABLE_ENTITY, "status");

    cesFeignException.setCesResponse(cesPayee);
    when(cesClient.addPayee(any())).thenThrow(cesFeignException);
    var bffPayee = billpayService.createPayee(bffAddPayeeRequest);
    assertEquals(HttpStatus.UNPROCESSABLE_ENTITY, bffPayee.getCesHttpStatus());
    assertEquals("MER021", bffPayee.getStatusCode());
  }
}
