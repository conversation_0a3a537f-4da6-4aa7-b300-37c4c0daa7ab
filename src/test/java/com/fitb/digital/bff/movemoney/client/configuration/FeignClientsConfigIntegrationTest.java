/* Copyright 2024 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.client.configuration;

import static com.github.tomakehurst.wiremock.client.WireMock.*;
import static com.github.tomakehurst.wiremock.core.WireMockConfiguration.wireMockConfig;
import static org.junit.jupiter.api.Assertions.assertThrows;

import com.fitb.digital.bff.movemoney.client.CesClient;
import com.fitb.digital.bff.movemoney.client.CesClientShortTimeout;
import com.fitb.digital.bff.movemoney.client.xferandpayorc.XferAndPayOrcClient;
import com.fitb.digital.bff.movemoney.exceptions.CesFeignException;
import com.fitb.digital.bff.movemoney.exceptions.FeignClientException;
import com.fitb.digital.bff.movemoney.exceptions.FeignServerException;
import com.github.tomakehurst.wiremock.WireMockServer;
import com.github.tomakehurst.wiremock.client.ResponseDefinitionBuilder;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.cloud.contract.wiremock.AutoConfigureWireMock;
import org.springframework.http.HttpStatus;

@AutoConfigureMockMvc
@SpringBootTest(
    properties = {
      "feign.client.config.xfer-and-pay-orc.url=http://localhost:${wiremock.server.port}",
      "feign.client.config.ces-proxy.url=http://localhost:${wiremock.server.port}",
      "feign.client.config.transfer-cd.url=http://localhost:${wiremock.server.port}"
    })
@AutoConfigureWireMock(port = 0)
public class FeignClientsConfigIntegrationTest {
  private ResponseDefinitionBuilder clientResponse;

  @Autowired private CesClient cesClient;
  @Autowired private CesClientShortTimeout cesClientShortTimeout;
  @Autowired private XferAndPayOrcClient xferAndPayOrcClient;

  WireMockServer wireMockServer;

  @BeforeEach
  void setUp() {
    wireMockServer = new WireMockServer(wireMockConfig().port(3001));
    wireMockServer.start();
  }

  @AfterEach
  void tearDown() {
    wireMockServer.stop();
  }

  @Test
  void cesClient_shouldThrowException() {
    final HttpStatus httpStatus = HttpStatus.BAD_REQUEST;
    clientResponse =
        aResponse().withStatus(httpStatus.value()).withHeader("content-type", "application/json");
    wireMockServer.stubFor(get("/services/externaltransfer/brokerages").willReturn(clientResponse));
    assertThrows(CesFeignException.class, () -> cesClient.getBrokerages());
  }

  @Test
  void cesClientShortTimeout_shouldThrowException() {
    final HttpStatus httpStatus = HttpStatus.BAD_REQUEST;
    clientResponse =
        aResponse().withStatus(httpStatus.value()).withHeader("content-type", "application/json");
    wireMockServer.stubFor(
        get("/services/externaltransfer/userAccounts").willReturn(clientResponse));
    assertThrows(
        CesFeignException.class, () -> cesClientShortTimeout.getExternalTransferUserAccounts());
  }

  @Test
  void xferAndPayOrc_shouldThrowClientException() {
    final HttpStatus httpStatus = HttpStatus.BAD_REQUEST;
    clientResponse =
        aResponse().withStatus(httpStatus.value()).withHeader("content-type", "application/json");
    wireMockServer.stubFor(get("/profile").willReturn(clientResponse));
    assertThrows(FeignClientException.class, () -> xferAndPayOrcClient.getTransferAndPayProfile());
  }

  @Test
  void xferAndPayOrc_shouldThrowServerException() {
    final HttpStatus httpStatus = HttpStatus.INTERNAL_SERVER_ERROR;
    clientResponse =
        aResponse().withStatus(httpStatus.value()).withHeader("content-type", "application/json");
    wireMockServer.stubFor(get("/profile").willReturn(clientResponse));
    assertThrows(FeignServerException.class, () -> xferAndPayOrcClient.getTransferAndPayProfile());
  }
}
