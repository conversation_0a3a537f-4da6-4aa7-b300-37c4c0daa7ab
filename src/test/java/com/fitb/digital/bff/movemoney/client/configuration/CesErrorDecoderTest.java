/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.client.configuration;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.fitb.digital.bff.movemoney.exceptions.CesFeignException;
import feign.Request;
import feign.Response;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.server.ResponseStatusException;

@Slf4j
@ExtendWith(MockitoExtension.class)
public class CesErrorDecoderTest {

  @InjectMocks CesErrorDecoder errorDecoder;

  @Test
  public void testDefaultExceptionHandling() {
    Request request =
        Request.create(
            Request.HttpMethod.GET,
            "https://donotcare",
            new HashMap<>(),
            Request.Body.empty(),
            null);
    Response response = Response.builder().request(request).status(404).build();
    Exception e = errorDecoder.decode("", response);

    assertEquals(CesFeignException.class, e.getClass());
    assertEquals(404, ((ResponseStatusException) e).getStatusCode().value());
  }

  @Test
  public void testBillPaymentErrorHandling() {
    Request request =
        Request.create(
            Request.HttpMethod.GET,
            "https://donotcare",
            new HashMap<>(),
            Request.Body.empty(),
            null);
    Response response = Response.builder().request(request).status(401).build();
    Exception e = errorDecoder.decode(CesErrorDecoder.CES_CLIENT_GET_PROFILE, response);

    assertEquals(CesFeignException.class, e.getClass());
    if (e instanceof CesFeignException fe) {
      assertEquals(200, fe.getStatusCode().value());
      assertNotNull(fe.getCesResponse());
    }
  }

  @Test
  public void testDuplicateNicknameErrorHandling() {
    Request request =
        Request.create(
            Request.HttpMethod.GET,
            "https://donotcare",
            new HashMap<>(),
            Request.Body.empty(),
            null);
    Response response =
        Response.builder()
            .request(request)
            .status(400)
            .body(
                """
        {
            "status": "CASHEDGE_ERROR",
            "statusCode": "UNKNOWN_ERROR_CODE",
            "statusReason": "The nickname you entered already has been added to your online profile.",
            "unmappableBody": null
        }
        """,
                StandardCharsets.UTF_8)
            .build();
    Exception e = errorDecoder.decode(CesErrorDecoder.CES_CLIENT_UPDATE_EXTERN_NICKNAME, response);

    assertEquals(CesFeignException.class, e.getClass());
    assertInstanceOf(CesFeignException.class, e);
    CesFeignException fce = (CesFeignException) e;
    assertEquals(400, fce.getStatusCode().value());
    assertEquals("DUPLICATE_NICKNAME", fce.getCesResponse().getStatusCode());
  }

  @Test
  public void addPayeeAddressValidationError_shouldMapAddressToUnmappableBody() {
    Request request =
        Request.create(
            Request.HttpMethod.POST,
            "https://donotcare",
            new HashMap<>(),
            Request.Body.empty(),
            null);
    Response response =
        Response.builder()
            .request(request)
            .status(422)
            .body(
                """
                      {
                           "status": "VALIDATION_ERROR",
                           "statusCode": "ADDRESS_CHANGED",
                           "cesPayee": {
                               "cesAddress": {
                                   "@type": "CesAddress",
                                   "streetLine1": "3267 BRAMPTON ST",
                                   "streetLine2": " ",
                                   "city": "DUBLIN",
                                   "stateOrProvince": "OH",
                                   "postalCode": "43017",
                                   "badAddressIndicator": false,
                                   "country": "US"
                               }
                           }
                       }
                                    """,
                StandardCharsets.UTF_8)
            .build();
    Exception e = errorDecoder.decode(CesErrorDecoder.CES_CLIENT_ADD_PAYEE, response);

    assertEquals(CesFeignException.class, e.getClass());
    assertInstanceOf(CesFeignException.class, e);
    CesFeignException fce = (CesFeignException) e;
    assertEquals(422, fce.getStatusCode().value());
    assertEquals(
        """
            {
                 "status": "VALIDATION_ERROR",
                 "statusCode": "ADDRESS_CHANGED",
                 "cesPayee": {
                     "cesAddress": {
                         "@type": "CesAddress",
                         "streetLine1": "3267 BRAMPTON ST",
                         "streetLine2": " ",
                         "city": "DUBLIN",
                         "stateOrProvince": "OH",
                         "postalCode": "43017",
                         "badAddressIndicator": false,
                         "country": "US"
                     }
                 }
             }
            """,
        fce.getCesResponse().getUnmappableBody());
  }

  @Test
  public void updatePayeeAddressValidationError_shouldMapAddressToUnmappableBody() {
    Request request =
        Request.create(
            Request.HttpMethod.PUT,
            "https://donotcare",
            new HashMap<>(),
            Request.Body.empty(),
            null);
    Response response =
        Response.builder()
            .request(request)
            .status(422)
            .body(
                """
                                  {
                                       "status": "VALIDATION_ERROR",
                                       "statusCode": "ADDRESS_CHANGED",
                                       "cesPayee": {
                                           "cesAddress": {
                                               "@type": "CesAddress",
                                               "streetLine1": "3267 BRAMPTON ST",
                                               "streetLine2": " ",
                                               "city": "DUBLIN",
                                               "stateOrProvince": "OH",
                                               "postalCode": "43017",
                                               "badAddressIndicator": false,
                                               "country": "US"
                                           }
                                       }
                                   }
                                                """,
                StandardCharsets.UTF_8)
            .build();
    Exception e = errorDecoder.decode(CesErrorDecoder.CES_CLIENT_UPDATE_PAYEE, response);

    assertEquals(CesFeignException.class, e.getClass());
    assertInstanceOf(CesFeignException.class, e);
    CesFeignException fce = (CesFeignException) e;
    assertEquals(422, fce.getStatusCode().value());
    assertEquals(
        """
                {
                     "status": "VALIDATION_ERROR",
                     "statusCode": "ADDRESS_CHANGED",
                     "cesPayee": {
                         "cesAddress": {
                             "@type": "CesAddress",
                             "streetLine1": "3267 BRAMPTON ST",
                             "streetLine2": " ",
                             "city": "DUBLIN",
                             "stateOrProvince": "OH",
                             "postalCode": "43017",
                             "badAddressIndicator": false,
                             "country": "US"
                         }
                     }
                 }
                """,
        fce.getCesResponse().getUnmappableBody());
  }
}
