/* Copyright 2022 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.service.transferinfo;

import static com.fitb.digital.bff.movemoney.utils.TestUtils.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;

import com.fitb.digital.bff.movemoney.client.xferandpayorc.XferAndPayOrcClient;
import com.fitb.digital.bff.movemoney.client.xferandpayorc.model.XferAndPayProfile;
import com.fitb.digital.bff.movemoney.constants.ExceptionStatus;
import com.fitb.digital.bff.movemoney.exceptions.BffException;
import com.fitb.digital.bff.movemoney.exceptions.CesFeignException;
import com.fitb.digital.bff.movemoney.model.RetrievalErrors;
import com.fitb.digital.bff.movemoney.model.bff.account.responses.Account;
import com.fitb.digital.bff.movemoney.model.client.account.responses.ListResponse;
import com.fitb.digital.bff.movemoney.model.client.external.transfer.responses.CesGetUserAccountsResponse;
import com.fitb.digital.bff.movemoney.model.client.profile.responses.ProfileResponse;
import com.fitb.digital.bff.movemoney.service.BlackoutDateService;
import com.fitb.digital.bff.movemoney.util.ExceptionUtil;
import com.fitb.digital.bff.movemoney.utils.TestUtils;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;

@Slf4j
@ExtendWith(MockitoExtension.class)
public class TransferInfoServiceTest {

  @Mock(strictness = Mock.Strictness.LENIENT)
  private BlackoutDateService blackoutDateService;

  @Mock private XferAndPayOrcClient xferAndPayOrcClient;
  @InjectMocks private TransferInfoService transferInfoService;

  @BeforeEach
  void setUp() {
    when(blackoutDateService.getBlackoutDates(any(), any())).thenReturn(new ArrayList<>());
  }

  @Test
  void verifyFeignException() {
    Exception e =
        new RuntimeException(
            new CesFeignException(HttpStatus.I_AM_A_TEAPOT, "Honey, I'm exceptional!"));
    assertThrows(CesFeignException.class, () -> ExceptionUtil.handleAsyncExceptions(e, "", ""));
  }

  @Test
  void verifyOtherExceptions() {
    final Exception e = new RuntimeException(new RuntimeException("A nested exception"));
    final BffException expected =
        BffException.serviceUnavailable(ExceptionStatus.SERVICE_UNAVAILABLE);
    final BffException actual =
        assertThrows(BffException.class, () -> ExceptionUtil.handleAsyncExceptions(e, "", ""));
    assertEquals(expected, actual);
  }

  @Test
  void getTransferAccountsReturnsOnlyNotBillpayTargets() {
    when(xferAndPayOrcClient.getTransferAndPayProfile())
        .thenReturn(TestUtils.mockXferAndPayOrcProfileResponse());

    var accountResponse = transferInfoService.getTransferAccounts();
    assertNotNull(accountResponse.getAccounts());

    for (var account : accountResponse.getAccounts()) {
      assertTrue(
          account.getRecipients().stream()
              .noneMatch(recipient -> recipient.getActivityType().equals("BILLPAY")));
    }
  }

  @Test
  void getTransferAccountsReturnsOnlyBillpaySourcesForPayeeAccounts() {
    when(xferAndPayOrcClient.getTransferAndPayProfile())
        .thenReturn(TestUtils.mockXferAndPayOrcProfileResponse());

    var accountResponse = transferInfoService.getTransferAccounts();

    for (var account : accountResponse.getAccounts()) {
      if (account.getAccountType().equals("PERSONAL_PAYEE")
          || account.getAccountType().equals("BUSINESS_PAYEE")) {
        assertTrue(
            account.getSources().stream()
                .allMatch(source -> source.getActivityType().equals("BILLPAY")));
      }
    }
  }

  @Test
  void getAccountsReceivesProfileRetrievalError() {
    when(xferAndPayOrcClient.getTransferAndPayProfile())
        .thenReturn(mockXferAndPayOrcProfileResponse());

    var accountResponse = transferInfoService.getTransferAccounts();
    assertNotNull(accountResponse);
    assertNotNull(accountResponse.getStatus());
    assertTrue(
        accountResponse
            .getRetrievalErrors()
            .contains(RetrievalErrors.RETRIEVAL_ERROR_EXTERNAL_TRANSFER));
    assertNotNull(accountResponse.getAccounts().get(0).getAvailableBalance());
  }

  @Test
  void getXferAndPayOrcProfilePopulatesExpectedFields() {
    when(xferAndPayOrcClient.getTransferAndPayProfile())
        .thenReturn(mockXferAndPayOrcProfileResponse());
    var accountResponse = transferInfoService.getTransferAccounts();
    assertEquals(
        new BigDecimal("15937.9"), accountResponse.getAccounts().get(0).getAvailableBalance());
    assertEquals(
        new BigDecimal("10000.9"), accountResponse.getAccounts().get(0).getLedgerBalance());
    assertFalse(accountResponse.getAccounts().get(0).getCashAdvanceEligible());
    assertTrue(accountResponse.getAccounts().get(6).getCashAdvanceEligible());
    assertFalse(accountResponse.getAccounts().get(0).isVerificationRequired());
    assertEquals(5, accountResponse.getMaxExternalTransferAccounts());
    assertTrue(accountResponse.getAccounts().get(0).isExternalTransferEligible());
    assertFalse(accountResponse.getAccounts().get(6).isExternalTransferEligible());

    // Find MMC to verify lastPaymentDate
    final Account account =
        accountResponse.getAccounts().stream()
            .filter(a -> a.getId().equals("A78F7A75-B787-986C-2113-1AAAF2A6852F"))
            .findFirst()
            .get();
    assertEquals(LocalDate.of(2011, 10, 28), account.getLastPaymentDate());
  }

  @Test
  void getAccountsSetsCurrentExternalTransferAccounts() {
    when(xferAndPayOrcClient.getTransferAndPayProfile())
        .thenReturn(mockXferAndPayOrcProfileResponse());
    var accountResponse = transferInfoService.getTransferAccounts();
    assertEquals(1, accountResponse.getCurrentExternalTransferAccounts());
  }

  @Test
  public void paymentAccountHasSources() { // TODO:
    //    when(cesClient.getTransferAndPayProfile(any()))
    //        .thenReturn(new ResponseEntity<>(mockCESFullProfileResponse(), HttpStatus.OK));
    //    when(cesClient.getAccountsList(any()))
    //        .thenReturn(new ResponseEntity<>(TestUtils.mockCESFullAccountList(), HttpStatus.OK));
    //
    //    var accountResponse = transferInfoServiceV1.getTransferAccounts(true, true);
    //    assertNotNull(accountResponse);
    //    assertNotNull(accountResponse.getStatus());
    //
    //    var accounts = accountResponse.getAccounts();
    //    var payments = accounts.stream().filter(account -> account.isPaymentTarget());
    //
    //    payments
    //        .filter(account -> !account.getSources().isEmpty())
    //        .forEach(
    //            account -> {
    //              assertTrue(account.getSources().size() > 0);
    //            });
  }

  @Test
  @DisplayName(
      """
        Given the getTransfersAccount is called
        When an earliestAvailableDateRecurring is sent from CES
        Then earliestAvailableDateRecurring is passed to the caller.
        """)
  public void whenEarliestAvailableDateRecurringIsMapped() {
    when(xferAndPayOrcClient.getTransferAndPayProfile())
        .thenReturn(mockXferAndPayOrcFullProfileResponse());

    var accountResponse = transferInfoService.getTransferAccounts();

    var accounts = accountResponse.getAccounts();
    var responseExternalAccount =
        accounts.stream()
            .filter(
                account -> account.getId().equalsIgnoreCase("507F7855-0FBB-C79F-5BE7-0636CF2EC3A6"))
            .findFirst()
            .get();
    var externalAccountActor =
        mockXferAndPayOrcFullProfileResponse().getActors().stream()
            .filter(actor -> actor.getId().equalsIgnoreCase("507F7855-0FBB-C79F-5BE7-0636CF2EC3A6"))
            .findFirst()
            .get();

    var responseInternalAccount = accounts.get(0);
    var internalActor = mockXferAndPayOrcFullProfileResponse().getActors().get(0);

    var externalRecipient =
        internalActor.getAvailableRecipients().stream()
            .filter(actor -> actor.getId().equalsIgnoreCase("507F7855-0FBB-C79F-5BE7-0636CF2EC3A6"))
            .findFirst()
            .get();
    var responseExternalRecipient =
        responseInternalAccount.getRecipients().stream()
            .filter(actor -> actor.getId().equalsIgnoreCase("507F7855-0FBB-C79F-5BE7-0636CF2EC3A6"))
            .findFirst()
            .get();

    var externalSource =
        internalActor.getAvailableSources().stream()
            .filter(actor -> actor.getId().equalsIgnoreCase("507F7855-0FBB-C79F-5BE7-0636CF2EC3A6"))
            .findFirst()
            .get();
    var responseExternalSource =
        responseInternalAccount.getSources().stream()
            .filter(actor -> actor.getId().equalsIgnoreCase("507F7855-0FBB-C79F-5BE7-0636CF2EC3A6"))
            .findFirst()
            .get();

    assertEquals(
        externalRecipient.getEarliestAvailableDateRecurring(),
        responseExternalRecipient.getEarliestAvailableDateRecurring());

    assertEquals(
        externalSource.getEarliestAvailableDateRecurring(),
        responseExternalSource.getEarliestAvailableDateRecurring());

    assertEquals(
        externalAccountActor.getAvailableSources().get(0).getEarliestAvailableDateRecurring(),
        responseExternalAccount.getSources().get(0).getEarliestAvailableDateRecurring());

    assertEquals(
        externalAccountActor.getAvailableRecipients().get(0).getEarliestAvailableDateRecurring(),
        responseExternalAccount.getRecipients().get(0).getEarliestAvailableDateRecurring());
  }

  @Test
  public void mapsDisplaySortOrderFromTPO() {
    when(xferAndPayOrcClient.getTransferAndPayProfile()).thenReturn(mockProfileGoalsResponse());
    var accountResponse = transferInfoService.getTransferAccounts();
    BigDecimal expectedSortOrder = BigDecimal.ONE, expectedGoalSortOrder = BigDecimal.valueOf(7.1);
    for (Account account : accountResponse.getAccounts()) {
      if (account.isGoal()) {
        assertEquals(expectedGoalSortOrder, account.getDisplaySortOrder());
        expectedGoalSortOrder = expectedGoalSortOrder.add(BigDecimal.valueOf(0.1));
      } else {
        assertEquals(expectedSortOrder, account.getDisplaySortOrder());
        expectedSortOrder = expectedSortOrder.add(BigDecimal.ONE);
      }
    }
  }

  @NotNull
  private BigDecimal getExpectedSortOrder(
      Account momentumSavingsAcct, List<String> goalIds, Account account) {
    var rawIndex = new BigDecimal(goalIds.indexOf(account.getId()) + 1);
    var maxGoals = new BigDecimal("10000");
    var computedIndex = rawIndex.divide(maxGoals, 4, RoundingMode.UNNECESSARY);

    return new BigDecimal(String.valueOf(momentumSavingsAcct.getDisplaySortOrder()))
        .add(computedIndex);
  }

  private ProfileResponse mockProfileResponse() {
    return new ProfileResponse();
  }

  private XferAndPayProfile mockProfileGoalsResponse() {
    return mockFromFile(
        "service/transferinfo/xfer_and_pay_orc_profile_response_goals.json",
        XferAndPayProfile.class);
  }

  private ProfileResponse mockProfileGoalsRemovedResponse() {
    return mockFromFile(
        "service/transferinfo/profile_response_goals_removed.json", ProfileResponse.class);
  }

  private XferAndPayProfile mockProfileMultiMomSavGoalsResponse() {
    return mockFromFile(
        "service/transferinfo/xfer_and_pay_orc_profile_response_multi_goals.json",
        XferAndPayProfile.class);
  }

  private ListResponse mockAccountListGoalsResponse() {
    return mockFromFile(
        "service/transferinfo/account_list_response_goals.json", ListResponse.class);
  }

  private ListResponse mockAccountListAccess360Response() {
    return mockFromFile("service/transferinfo/account_list_access_360.json", ListResponse.class);
  }

  private ListResponse mockAccountListGoalsRemovedResponse() {
    return mockFromFile(
        "service/transferinfo/account_list_response_goals_removed.json", ListResponse.class);
  }

  private ListResponse mockAccountListOneGoalRemovedResponse() {
    return mockFromFile(
        "service/transferinfo/account_list_response_goals_one_goal_removed.json",
        ListResponse.class);
  }

  private ListResponse mockAccountListMultiMomSavGoalsResponse() {
    return mockFromFile(
        "service/transferinfo/account_list_response_multi_goals.json", ListResponse.class);
  }

  private ProfileResponse mockProfileMixedExternal() {
    return mockFromFile(
        "service/transferinfo/profile_external_mixed_approved_denied.json", ProfileResponse.class);
  }

  private ProfileResponse mockProfileMixedExternalInvoiceDetails() {
    return mockFromFile(
        "service/transferinfo/profile_external_mixed_approved_denied_invoicedetails.json",
        ProfileResponse.class);
  }

  private ListResponse mockAccountListMixedExternal() {
    return mockFromFile(
        "service/transferinfo/account_list_external_mixed_approved_denied.json",
        ListResponse.class);
  }

  private CesGetUserAccountsResponse mockExternalTransferAccountsMixed() {
    return mockFromFile(
        "service/externaltransfer/external_user_accounts_mixed_approved_denied.json",
        CesGetUserAccountsResponse.class);
  }

  private ListResponse mockCESListResponse() {
    return mockFromFile("mock_ces_list_response.json", ListResponse.class);
  }
}
