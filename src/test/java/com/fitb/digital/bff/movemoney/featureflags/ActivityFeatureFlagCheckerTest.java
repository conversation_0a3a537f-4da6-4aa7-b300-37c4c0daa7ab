/* Copyright 2024 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.featureflags;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.fitb.digital.bff.movemoney.constants.ExceptionStatus;
import com.fitb.digital.bff.movemoney.exceptions.BffException;
import com.fitb.digital.bff.movemoney.model.ActivityType;
import com.fitb.digital.lib.featureflag.service.FeatureFlagService;
import java.util.stream.Stream;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;

@ExtendWith(MockitoExtension.class)
public class ActivityFeatureFlagCheckerTest {
  @Mock private FeatureFlagService featureFlagService;

  @InjectMocks private ActivityFeatureFlagChecker checker;

  @ParameterizedTest
  @ValueSource(
      strings = {
        ActivityType.WEB_PAYMENT,
        ActivityType.BILLPAY,
        ActivityType.INVOICED_PAYMENT,
        ActivityType.INTERNAL_TRANSFER,
        ActivityType.EXTERNAL_TRANSFER,
        ActivityType.GOALS
      })
  public void checkFeatureEnabled_AllEnabled_ShouldNotThrow(final String activityType) {
    when(featureFlagService.getFeatureVariant(
            FeatureFlagNames.PAYMENTS_STATUS, FeatureFlagVariants.ENABLED))
        .thenReturn(FeatureFlagVariants.ENABLED);

    when(featureFlagService.getFeatureVariant(
            FeatureFlagNames.TRANSFERS_STATUS, FeatureFlagVariants.ENABLED))
        .thenReturn(FeatureFlagVariants.ENABLED);

    assertDoesNotThrow(() -> checker.checkFeatureEnabled(activityType));

    verify(featureFlagService)
        .getFeatureVariant(FeatureFlagNames.PAYMENTS_STATUS, FeatureFlagVariants.ENABLED);
  }

  @ParameterizedTest
  @ValueSource(strings = {ActivityType.GOALS})
  public void checkFeatureEnabled_AllDisabled_ShouldNotThrow(final String activityType) {
    when(featureFlagService.getFeatureVariant(
            FeatureFlagNames.PAYMENTS_STATUS, FeatureFlagVariants.ENABLED))
        .thenReturn(FeatureFlagVariants.OLB_REDIRECT);

    when(featureFlagService.getFeatureVariant(
            FeatureFlagNames.TRANSFERS_STATUS, FeatureFlagVariants.ENABLED))
        .thenReturn(FeatureFlagVariants.PLANNED_MAINTENANCE);

    assertDoesNotThrow(() -> checker.checkFeatureEnabled(activityType));

    verify(featureFlagService)
        .getFeatureVariant(FeatureFlagNames.PAYMENTS_STATUS, FeatureFlagVariants.ENABLED);
  }

  @ParameterizedTest
  @MethodSource("checkFeatureEnabled_Payments_ShouldThrow_Args")
  public void checkFeatureEnabled_Payments_ShouldThrow(
      final String activityType, final String flagVariant, final ExceptionStatus exceptionStatus) {
    when(featureFlagService.getFeatureVariant(
            FeatureFlagNames.PAYMENTS_STATUS, FeatureFlagVariants.ENABLED))
        .thenReturn(flagVariant);

    when(featureFlagService.getFeatureVariant(
            FeatureFlagNames.TRANSFERS_STATUS, FeatureFlagVariants.ENABLED))
        .thenReturn(FeatureFlagVariants.ENABLED);

    final BffException actual =
        assertThrows(BffException.class, () -> checker.checkFeatureEnabled(activityType));
    assertEquals(HttpStatus.SERVICE_UNAVAILABLE, actual.getHttpStatus());
    assertEquals(exceptionStatus.getStatus(), actual.getStatus());
    assertEquals(exceptionStatus.getCode(), actual.getStatusCode());
    assertEquals(exceptionStatus.getReason(), actual.getStatusReason());

    verify(featureFlagService)
        .getFeatureVariant(FeatureFlagNames.PAYMENTS_STATUS, FeatureFlagVariants.ENABLED);
  }

  private static Stream<Arguments> checkFeatureEnabled_Payments_ShouldThrow_Args() {
    return Stream.of(
        Arguments.of(
            ActivityType.WEB_PAYMENT,
            FeatureFlagVariants.PLANNED_MAINTENANCE,
            ExceptionStatus.FEATURE_PLANNED_MAINTENANCE),
        Arguments.of(
            ActivityType.WEB_PAYMENT,
            FeatureFlagVariants.OLB_REDIRECT,
            ExceptionStatus.FEATURE_OLB_REDIRECT),
        Arguments.of(
            ActivityType.WEB_PAYMENT,
            FeatureFlagVariants.HELP_CENTER_REDIRECT,
            ExceptionStatus.FEATURE_HELP_CENTER_REDIRECT),
        Arguments.of(
            ActivityType.BILLPAY,
            FeatureFlagVariants.PLANNED_MAINTENANCE,
            ExceptionStatus.FEATURE_PLANNED_MAINTENANCE),
        Arguments.of(
            ActivityType.BILLPAY,
            FeatureFlagVariants.OLB_REDIRECT,
            ExceptionStatus.FEATURE_OLB_REDIRECT),
        Arguments.of(
            ActivityType.BILLPAY,
            FeatureFlagVariants.HELP_CENTER_REDIRECT,
            ExceptionStatus.FEATURE_HELP_CENTER_REDIRECT),
        Arguments.of(
            ActivityType.INVOICED_PAYMENT,
            FeatureFlagVariants.PLANNED_MAINTENANCE,
            ExceptionStatus.FEATURE_PLANNED_MAINTENANCE),
        Arguments.of(
            ActivityType.INVOICED_PAYMENT,
            FeatureFlagVariants.OLB_REDIRECT,
            ExceptionStatus.FEATURE_OLB_REDIRECT),
        Arguments.of(
            ActivityType.INVOICED_PAYMENT,
            FeatureFlagVariants.HELP_CENTER_REDIRECT,
            ExceptionStatus.FEATURE_HELP_CENTER_REDIRECT),
        Arguments.of(
            ActivityType.INTERNAL_TRANSFER,
            FeatureFlagVariants.PLANNED_MAINTENANCE,
            ExceptionStatus.FEATURE_PLANNED_MAINTENANCE),
        Arguments.of(
            ActivityType.INTERNAL_TRANSFER,
            FeatureFlagVariants.OLB_REDIRECT,
            ExceptionStatus.FEATURE_OLB_REDIRECT),
        Arguments.of(
            ActivityType.INTERNAL_TRANSFER,
            FeatureFlagVariants.HELP_CENTER_REDIRECT,
            ExceptionStatus.FEATURE_HELP_CENTER_REDIRECT));
  }

  @ParameterizedTest
  @MethodSource("checkFeatureEnabled_Transfers_ShouldThrow_Args")
  public void checkFeatureEnabled_Transfers_ShouldThrow(
      final String activityType, final String flagVariant, final ExceptionStatus exceptionStatus) {
    when(featureFlagService.getFeatureVariant(
            FeatureFlagNames.PAYMENTS_STATUS, FeatureFlagVariants.ENABLED))
        .thenReturn(FeatureFlagVariants.ENABLED);

    when(featureFlagService.getFeatureVariant(
            FeatureFlagNames.TRANSFERS_STATUS, FeatureFlagVariants.ENABLED))
        .thenReturn(flagVariant);

    final BffException actual =
        assertThrows(BffException.class, () -> checker.checkFeatureEnabled(activityType));
    assertEquals(HttpStatus.SERVICE_UNAVAILABLE, actual.getHttpStatus());
    assertEquals(exceptionStatus.getStatus(), actual.getStatus());
    assertEquals(exceptionStatus.getCode(), actual.getStatusCode());
    assertEquals(exceptionStatus.getReason(), actual.getStatusReason());

    verify(featureFlagService)
        .getFeatureVariant(FeatureFlagNames.PAYMENTS_STATUS, FeatureFlagVariants.ENABLED);
  }

  private static Stream<Arguments> checkFeatureEnabled_Transfers_ShouldThrow_Args() {
    return Stream.of(
        Arguments.of(
            ActivityType.INTERNAL_TRANSFER,
            FeatureFlagVariants.PLANNED_MAINTENANCE,
            ExceptionStatus.FEATURE_PLANNED_MAINTENANCE),
        Arguments.of(
            ActivityType.INTERNAL_TRANSFER,
            FeatureFlagVariants.OLB_REDIRECT,
            ExceptionStatus.FEATURE_OLB_REDIRECT),
        Arguments.of(
            ActivityType.INTERNAL_TRANSFER,
            FeatureFlagVariants.HELP_CENTER_REDIRECT,
            ExceptionStatus.FEATURE_HELP_CENTER_REDIRECT),
        Arguments.of(
            ActivityType.EXTERNAL_TRANSFER,
            FeatureFlagVariants.PLANNED_MAINTENANCE,
            ExceptionStatus.FEATURE_PLANNED_MAINTENANCE),
        Arguments.of(
            ActivityType.EXTERNAL_TRANSFER,
            FeatureFlagVariants.OLB_REDIRECT,
            ExceptionStatus.FEATURE_OLB_REDIRECT),
        Arguments.of(
            ActivityType.EXTERNAL_TRANSFER,
            FeatureFlagVariants.HELP_CENTER_REDIRECT,
            ExceptionStatus.FEATURE_HELP_CENTER_REDIRECT),
        Arguments.of(
            ActivityType.INVOICED_PAYMENT,
            FeatureFlagVariants.PLANNED_MAINTENANCE,
            ExceptionStatus.FEATURE_PLANNED_MAINTENANCE),
        Arguments.of(
            ActivityType.INVOICED_PAYMENT,
            FeatureFlagVariants.OLB_REDIRECT,
            ExceptionStatus.FEATURE_OLB_REDIRECT),
        Arguments.of(
            ActivityType.INVOICED_PAYMENT,
            FeatureFlagVariants.HELP_CENTER_REDIRECT,
            ExceptionStatus.FEATURE_HELP_CENTER_REDIRECT));
  }
}
