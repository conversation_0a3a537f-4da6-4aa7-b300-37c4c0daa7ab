/* Copyright 2024 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.model.bff.cd;

import static org.assertj.core.api.Assertions.assertThat;

import com.fitb.digital.bff.movemoney.model.bff.cd.requests.CDFundTransferRequest;
import java.math.BigDecimal;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class CDFundTransferRequestTest {

  private CDFundTransferRequest request;

  @BeforeEach
  public void setup() {
    request =
        new CDFundTransferRequest(
            UUID.randomUUID().toString(),
            UUID.randomUUID().toString(),
            BigDecimal.TEN,
            true,
            "internal");
  }

  @Test
  public void testAllFieldsAreNotNullByDefault() {
    assertThat(request.getFromAccountId()).isNotNull().isNotEmpty();
    assertThat(request.getToAccountId()).isNotNull().isNotEmpty();
    assertThat(request.getAmount()).isNotNull().isGreaterThan(BigDecimal.ZERO);
    assertThat(request.getCutoff()).isNotNull().isTrue();
    assertThat(request.getTransferType()).isNotNull().isEqualTo("internal");
  }

  @Test
  public void testDefaultConstructorInitializesFields() {
    assertThat(request.getFromAccountId()).isNotNull().isNotEmpty();
    assertThat(request.getToAccountId()).isNotNull().isNotEmpty();
    assertThat(request.getAmount()).isNotNull().isGreaterThan(BigDecimal.ZERO);
    assertThat(request.getCutoff()).isNotNull().isTrue();
    assertThat(request.getTransferType()).isNotNull().isEqualTo("internal");
  }

  @Test
  public void testAllFieldsAreNotNullWhenManualConstructorIsUsed() {
    CDFundTransferRequest request =
        new CDFundTransferRequest(
            "manual-from-account", "manual-to-account", BigDecimal.TEN, true, "internal");
    assertThat(request.getFromAccountId()).isNotNull().isEqualTo("manual-from-account");
    assertThat(request.getToAccountId()).isNotNull().isEqualTo("manual-to-account");
    assertThat(request.getAmount()).isNotNull().isEqualTo(BigDecimal.TEN);
    assertThat(request.getCutoff()).isNotNull().isTrue();
    assertThat(request.getTransferType()).isNotNull().isEqualTo("internal");
  }
}
