/* Copyright 2023 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.service.activity;

import static com.fitb.digital.bff.movemoney.util.CesBffActivityTypes.*;
import static com.fitb.digital.bff.movemoney.util.CesBffActivityTypes.STATUS_COMPLETE;
import static com.fitb.digital.bff.movemoney.utils.TestUtils.mockFromFile;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

import com.fitb.digital.bff.movemoney.client.xferandpayorc.XferAndPayOrcClient;
import com.fitb.digital.bff.movemoney.client.xferandpayorc.model.XferAndPayActivityResponse;
import com.fitb.digital.bff.movemoney.model.ActivityType;
import com.fitb.digital.bff.movemoney.model.bff.activity.BffActivity;
import com.fitb.digital.bff.movemoney.model.bff.activity.responses.BffGetActivityResponse;
import com.fitb.digital.bff.movemoney.model.client.activity.responses.ClientActivity;
import com.fitb.digital.bff.movemoney.model.client.activity.responses.ClientActivityResponse;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.text.WordUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@Slf4j
@ExtendWith(MockitoExtension.class)
class ActivityServiceTest {
  private ActivityService activityService;

  @Mock private XferAndPayOrcClient activityClient;

  @BeforeEach
  public void setup() {
    when(activityClient.getTransferAndPayActivity())
        .thenReturn(
            mockFromFile(
                "transfer_and_pay_orc_activity_response.json", XferAndPayActivityResponse.class));
    activityService = new ActivityService(activityClient);
    activityService.postConstructor();
  }

  @Test
  void getActivityWhenProfileResponseIsValidAndEditableActivityExists() {
    var response = activityService.getActivity(50, 50);
    assertNotNull(response);
    BffActivity firstActivity = response.getUpcomingActivities().get(0);
    assertEquals(ActivityType.INTERNAL_TRANSFER, firstActivity.getActivityType());
    assertTrue(firstActivity.isEditable());
    assertTrue(firstActivity.isCancelable());
  }

  @Test
  void getActivity_recentListContainsCorrectStatuses() {
    Set<String> expected =
        new HashSet<>(
            Arrays.asList(
                WordUtils.capitalizeFully(STATUS_PROCESSED),
                WordUtils.capitalizeFully(STATUS_CANCELLED),
                WordUtils.capitalizeFully(STATUS_UNSUCCESSFUL),
                WordUtils.capitalizeFully(STATUS_COMPLETE),
                "Decapitalized"));

    var response = activityService.getActivity(50, 50);
    assertNotNull(response);
    assertTrue(
        response.getRecentActivities().stream()
            .allMatch(e -> expected.contains(e.getDisplayStatus())));
  }

  @Test
  void getActivityReturnsLimitedActivityIfBothLimitsSet() {
    BffGetActivityResponse result = activityService.getActivity(2, 2);
    assertEquals(2, result.getRecentActivities().size());
    assertEquals(2, result.getUpcomingActivities().size());

    assertTrue(result.getRecentTruncated());
    assertTrue(result.getUpcomingTruncated());
  }

  @Test
  void getActivityCapitalizesDisplayStatus() {
    BffGetActivityResponse result = activityService.getActivity(null, null);

    assertEquals(
        "Scheduled",
        result
            .getRecurringActivities()
            .get(result.getRecurringActivities().size() - 1)
            .getDisplayStatus());
  }

  @Test
  void getActivityDoesNotReturnNullDueDates() {
    BffGetActivityResponse result = activityService.getActivity(null, null);

    assertTrue(
        result.getRecentActivities().stream()
            .filter(activity -> activity.getDueDate() == null)
            .toList()
            .isEmpty());
    assertTrue(
        result.getRecurringActivities().stream()
            .filter(activity -> activity.getDueDate() == null)
            .toList()
            .isEmpty());
  }

  @Test
  void getActivityTitleCasesAccountNames() {
    BffGetActivityResponse result = activityService.getActivity(null, null);

    assertEquals(
        "ING Bank Savings 6789",
        result
            .getRecurringActivities()
            .get(result.getRecurringActivities().size() - 1)
            .getFromAccountName());
    assertEquals(
        "Fifth Third Bank 6410",
        result
            .getRecurringActivities()
            .get(result.getRecurringActivities().size() - 1)
            .getToAccountName());
  }

  @Test
  void getActivityReturnsAllActivityIfNoLimitSet() {
    BffGetActivityResponse result = activityService.getActivity(null, null);

    assertEquals(10, result.getRecentActivities().size());
    assertEquals(4, result.getUpcomingActivities().size());
    assertEquals(1, result.getRecurringActivities().size());
  }

  @Test
  void getActivityReturnsRecurringActivity_WithIsSeriesTemplateSetToTrue() {
    BffGetActivityResponse result = activityService.getActivity(null, null);

    result.getRecentActivities().forEach(a -> assertFalse(a.isSeriesTemplate()));
    result.getUpcomingActivities().forEach(a -> assertFalse(a.isSeriesTemplate()));
    result.getRecurringActivities().forEach(a -> assertTrue(a.isSeriesTemplate()));
  }

  @Test
  void getActivityReturnsLimitedActivityIfOnlyRecentLimitSet() {
    BffGetActivityResponse result = activityService.getActivity(5, null);

    assertEquals(5, result.getRecentActivities().size());
    assertEquals(4, result.getUpcomingActivities().size());
    assertTrue(result.getRecentTruncated());
    assertFalse(result.getUpcomingTruncated());
  }

  @Test
  void getActivityReturnsLimitedActivityIfUpcomingLimitSet() {
    BffGetActivityResponse result = activityService.getActivity(null, 2);

    assertEquals(10, result.getRecentActivities().size());
    assertEquals(2, result.getUpcomingActivities().size());
    assertFalse(result.getRecentTruncated());
    assertTrue(result.getUpcomingTruncated());
  }

  @Test
  void processedActivitiesAreConvertedToCompleted() {
    var unknownClientTypeResponse = new ClientActivityResponse();
    var clientActivity = new ClientActivity();
    clientActivity.setAmount(1.00);
    clientActivity.setFromAccountId("1");
    clientActivity.setToAccountId("2");
    clientActivity.setType("INTERNAL_TRANSFER");
    clientActivity.setDisplayStatus("Processed");
    clientActivity.setDueDate(LocalDate.now());
    unknownClientTypeResponse.getActivities().add(clientActivity);
    BffGetActivityResponse result = activityService.getActivity(null, 5);

    assertEquals("Completed", result.getRecentActivities().get(0).getDisplayStatus());
  }
}
