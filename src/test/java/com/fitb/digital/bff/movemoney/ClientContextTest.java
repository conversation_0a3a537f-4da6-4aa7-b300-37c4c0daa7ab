/* Copyright 2021 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

import com.fitb.digital.bff.movemoney.model.ClientContext;
import com.fitb.digital.bff.movemoney.model.ClientContextHolder;
import java.util.UUID;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.context.ApplicationContext;

class ClientContextTest {

  @Mock ApplicationContext appContext;

  private ClientContext fixture;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.initMocks(this);
    fixture = ClientContextHolder.getContext(true);
    fixture.setApplicationContext(appContext);
    assertNotNull(fixture.getApplicationContext(), "app context cannot be null");
  }

  @AfterEach
  void tearDown() {
    ClientContextHolder.clearContext();
    fixture = null;
  }

  @Test
  void getBeanByNameAndType() {
    String name = "my-spring-bean1";
    String expected = "value";
    when(appContext.getBean(eq(name), eq(String.class))).thenReturn(expected);
    String actualResult = fixture.getBean(name, String.class);
    assertEquals(expected, actualResult, "must match");
  }

  @Test
  void getBeanByNameAndTypeNullAppContext() {
    fixture.setApplicationContext(null);
    String name = "my-spring-bean1";
    String actualResult = fixture.getBean(name, String.class);
    assertNull(actualResult, "must be null");
  }

  @Test
  void testGetBeanByType() {
    String expected = "value";
    when(appContext.getBean(eq(String.class))).thenReturn(expected);
    String actualResult = fixture.getBean(String.class);
    assertEquals(expected, actualResult, "must match");
  }

  @Test
  void testGetBeanByTypeNullAppContext() {
    fixture.setApplicationContext(null);
    String actualResult = fixture.getBean(String.class);
    assertNull(actualResult, "must be null");
  }

  @Test
  void testUserId() {
    UUID userId = UUID.randomUUID();
    fixture.setUsername(userId.toString());
    assertEquals(userId.toString(), fixture.getUsername(), "must match");
  }

  @Test
  void testProcessingException() {
    RuntimeException ex = new RuntimeException("bblah");
    fixture.setProcessingException(ex);
    assertEquals(ex, fixture.getProcessingException(), "must match");
  }
}
