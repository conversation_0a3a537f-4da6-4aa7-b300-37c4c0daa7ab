This repository contains the implmentation of the Move Money Backend For Frontend (move-money-bff).

If you run it locally, it will start with a single controller at the index (http://localhost:8080/hello), 
will provide Swagger UI (http://localhost:8080/swagger-ui/index.html or http://localhost:8080/move-money-bff/swagger-ui/index.html if you are running the spring `local` profile) and will expose 
Spring Actuator endpoints (http://localhost:8080/actuator).  The project defines a simple API for creating, destroying, 
and listing account transfers and payements.

Visit `HELP.md` in this project's root directory for additional documentation.

# Contents
This repo contains the basic structure of a Spring Boot Java application generated by the 
[Spring Initializr](https://start.spring.io/).  It also contains a few extras
- The Gradle wrapper (latest as of when this repo was updated).  You can 
[update to the latest version](https://docs.gradle.org/current/userguide/gradle_wrapper.html#sec:upgrading_wrapper) 
easily, and probably should.  This includes a jar, which is .gitignored, so updates to the wrapper must be force added 
(git add -f gradle/wrapper/gradle-wrapper.jar). Grade has been configured to work with Fifth Third's JFrog Artifactory repository.
- A basic gradle build with some standard spring dependencies like actuator, cloud-config-client, security, mvc.  See 
[HELP.md](HELP.md) for more information
- The pre-commit hook to format code according to google-java-format standards (.githooks/pre-commit). The hook will not 
run unless you configure git to look for it.  `git config --global core.hooksPath .githooks` will look for `.githooks` 
in any repo and run the hooks there.  Removing `--global` will only apply the config to a single repo.  Note that 
updates to this file must be force added (git add -f .githooks/pre-commit) to avoid unintentional changes.
- The CODEOWNERS file, which will enforce PR review rules.  See 
[GitHub documentation](https://help.github.com/en/articles/about-code-owners) for details.  You can change this if you 
need different ownerships.
 - [Swagger](http://swagger.io) automatically generates API documentation.  You should make sure that only public APIs are being exposed (see the SwaggerConfig class).
 - An example controller (ServiceController)
 - [Lombok](https://projectlombok.org/) is included as a dependency.  For this to work in your IDE there are instructions on the lombok site.  For instance, IntelliJ requires a plugin and a configuration change.
- Postman Collections to run against this BFF. Postman can be installed from Fifth-Third Self Service. To open the collections and environments, open Postman, go to File>Import>Upload File and navigate to `"this project root directory">postman-collections>Backup.postman_dump.json`. 

## CES Interaction
We have documented our experience with bill payments using the [CES proxy](ces-billpay/billpay.md).

## Architecture

![Module Diagram](./documentation/logicalModuleDiagram.png)

## OAuth Configuration
This BFF assumes that ISSUER_B2C has been configured in the environment SSM. For the time being we will also look for
ISSUER_B2C_OLD to support the transition to a new OAuth provider.

## Swagger UI
At the top of the dialog is an authorize button. This allows you to enter the client id and secret. These can be found in 
the Hashi Vault. Use the /move-money-bff/swagger-ui/index.html endpoint.

## Wiremock

For details on adding local test users see [Local Test Data](./documentation/wiremock/localTestData.md)
To start Wiremock, run this in the root directory:
./runMock.sh
~ OR ~
docker-compose -f ./src/test/newman/wiremock/docker-compose.yml up

The JSON used by Wiremock can be found @ <root_directory>/src/test/resources/stubs/__files/mmbff/ces/services/