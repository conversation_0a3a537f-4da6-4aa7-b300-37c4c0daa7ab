jenkins:
  pipeline:
    version: master
  libs:
    - "config.groovy"
    - "test.groovy"
    - "deploy.groovy"
  agent: LINUX
  env:
    MAJOR_VERSION: "{{context.lib.config.majorVersion}}"
    MINOR_VERSION: "{{context.lib.config.minorVersion}}"
    APP_NAME: "{{context.lib.config.appName}}"
    TEAM_DISTRIBUTION: "{{context.lib.config.teamDistribution}}"
    VERSION_PREFIX: "{{context.lib.config.versionPrefix}}"
    DNS_SAFE_VERSION_PREFIX: "{{context.lib.config.versionPrefixAlphaOnly}}"
    BUILD_VERSION: "{{context.lib.config.buildVersion}}"
    DOCKER_TAG_VERSION: "{{context.lib.config.dockerTagVersion}}"
    SERVICE_KEY: "{{context.lib.config.serviceKey}}"
  tools:
    java: "1.21.0"

build:
  #
  # Enable each command (or group of commands) to run in parallel
  #
  parallel: true
  #
  # Each command runs in it's own folder.
  #
  separateFolders: true
  #
  # Enable automatic unstashing of workspace files
  #
  autoUnstash: true
  commands:

    # Build the spring boot jar
    - type: gradlew
      name: "Clean and Build - DEV"
      commandGroup: "build-container"
      command: "clean build test jacocoTestReport --rerun-tasks"
      file: "build.gradle"

      setup-commands:
        - type: clean

      post-commands:
        #
        # Make sure the built file is included in the
        # files that are unstashed for the dockerbuild
        #
        - type: stash
          name: "workspace-files"
          includes: "**"

        # Process the Junit files generated by the build
        #
        - type: "junit"
          options:
            junitxmlfiles: "build/test-results/test/**/*.xml"
            allowEmptyResults: false


    # Build the app docker container
    - command: "dockerbuild"
      name: "build-image"
      commandGroup: "build-container"
      useMyDockerFile: true
      dockerFilePath: "Dockerfile"
      tagName: "{{SERVICE_KEY}}"
      tagVersion: "{{DOCKER_TAG_VERSION}}"
      failOnViolation: false
      buildArgs:
        - "JAR_FILE=./build/libs/{{APP_NAME}}-{{BUILD_VERSION}}.jar"

#    # Build the wiremock docker container
#    - command: "dockerbuild"
#      name: "build-wiremock"
#      commandGroup: "build-container"
#      useMyDockerFile: true
#      dockerFilePath: "src/test/newman/wiremock/Dockerfile"
#      buildContext: "src/test/newman/wiremock/"
#      tagName: "{{SERVICE_KEY}}-wiremock-ces"
#      tagVersion: "{{DOCKER_TAG_VERSION}}"
#      failOnViolation: false
#      prismaScan: false
#
#      # Zip up the deployment files
#    - type: zip
#      commandGroup: "build-ta-deploy-zip"
#      options:
#        name: "{{APP_NAME}}-ta-deploy"
#        zipExt: ".zip"
#        directory: "deploy/ta"
#        includeBuildNumber: true
#        includes: "**"
#        archive: true
#      artifacts:
#        groupId: "com.fitb.${env.TEAM_NAME}.{{APP_NAME}}"
#        artifactId: "{{APP_NAME}}-ta-deploy"

test:
  #
  # Eanble each command (or group of commands) to run in parallel
  #
  parallel: true
  #
  # Each command runs in it's own folder.
  #
  separateFolders: true
  #
  # Enable automatic unstashing of workspace files
  #
  autoUnstash: true
  commands:
    - command: "sonarqube"
      name: "Sonarqube"
      commandGroup: "quality-test"
      failOnViolation: true
      sourceCode: "src/main"
      email: "{{TEAM_DISTRIBUTION}}"
      options:
        baselineBranch: "develop"
        sonarProperties: "-Dsonar.sources=src/main/java -Dsonar.tests=src/test/java -Dsonar.coverage.jacoco.xmlReportPaths=build/reports/jacoco/test/jacocoTestReport.xml -Dsonar.java.source=1.17 -Dsonar.java.libraries=build/libs/*.jar -Dsonar.java.binaries=build/classes/java/main -Dsonar.junit.reportPaths=build/test-results/test -Dsonar.verbose=false -Dsonar.language=java -Dsonar.sourceEncoding=UTF-8 -Dsonar.coverage.exclusions=**/model/**/*,**/config/LocalOAuth2ResourceServer.java"
        qualityGateStatusAttempts:
          time: 20
          unit: SECONDS
          attempts: 10
      setup-commands:
        #
        # Make sure we start with a clean workspace
        #
        - type: clean
        - type: unstash
          name: "workspace-files"

#    - type: testautomationstart
#      name: "regression-test-create"
#      commandGroup: "regression-test"
#      appName: "{{APP_NAME}}-{{DNS_SAFE_VERSION_PREFIX}}"
#      deployVerifyRetryDelay: '10'
#      deployVerifyRetryCount: '30'
#      artifacts:
#        groupId: "com.fitb.${env.TEAM_NAME}.{{APP_NAME}}"
#        artifactId: "{{APP_NAME}}-ta-deploy"
#        tagName: "{{SERVICE_KEY}}"
#
#    - type: librarymethod
#      name: "regression-test-run"
#      commandGroup: "regression-test"
#      libraryClass: "test"
#      method: "runRegressionTests"
#      env:
#        TEST_SERVLET_CONTEXT: "/{{TEAM_NAME}}-{{APP_NAME}}-{{DNS_SAFE_VERSION_PREFIX}}"
#        TEST_APP_CONTEXT: "/{{APP_NAME}}"
#        TEST_HOST: "jenkins.info53.com"
#        TEST_PORT: "443"
#        TEST_PROTOCOL: "https"
#        TESTS_LOCATION: "src/test/newman"
#      post-commands:
#        # Process Newman results
#        - type: "junit"
#          options:
#            junitxmlfiles: "build/test-results/newman/**/*.xml"
#            allowEmptyResults: false
post:
  # This is handled via the check-auto-deploy command
  #autoTriggerDeploy: true
  commands:
#    - type: testautomationstop
#      name: "regression-test-teardown"
#      when: always
#      appName: "{{APP_NAME}}-{{DNS_SAFE_VERSION_PREFIX}}"
    - command: "blackduck"
      when: success
      failOnViolation: false
    - command: "checkmarx"
      when: success
      failOnViolation: false
      setup-commands:
        - type: unstash
          name: "workspace-files"
    - type: librarymethod
      name: "check-auto-deploy"
      #when: "success"
      libraryClass: "deploy"
      method: "checkAutoDeploy"