jenkins:
  pipeline:
    version: v3.1.14
  skipPromptTargetEnvironment: true
  approveEachCommand: false
  libs:
    - "config.groovy"
    - "test.groovy"
    - "deploy.groovy"
  agent: LINUX
  env:
    APP_NAME: "{{context.lib.config.appName}}"
    TEAM_DISTRIBUTION: "{{context.lib.config.teamDistribution}}"
    TFE_ORG: "{{context.lib.config.tfeOrg}}"
    TARGET_ENVIRONMENT: "{{context.lib.config.targetEnvironmentDev}}"
    AWS_DEFAULT_REGION: "{{context.lib.config.awsDefaultRegion}}"
    CLOUD_CONFIG_PROFILE: "{{context.lib.deploy.cloudConfigProfile}}"
    USE_RDS: "{{context.lib.config.useRds}}"
    ADDITIONAL_PROFILES: "{{context.lib.deploy.additionalProfiles}}"
    SERVICE_KEY: "{{context.lib.config.serviceKey}}"
    WORKSPACE_PREFIX: "{{context.lib.config.workspacePrefix}}"
    WORKSPACE_SUFFIX: "{{context.lib.deploy.workspaceSuffix}}"
    DOCKER_TAG_VERSION: "{{context.lib.config.dockerTagVersion}}"
    TERRAFORM_VERSION: "{{context.lib.deploy.terraformVersion}}"
deploy:
  commands:
    - type: "terraform"
      name: "Terraform Apply Service - DEV"
      terraformOrg: "{{TFE_ORG}}"
      deployType: "terraform"
      appName: "{{SERVICE_KEY}}"
      tagName: "{{SERVICE_KEY}}"
      tagVersion: "{{DOCKER_TAG_VERSION}}"
      awsSTSEnabled: true
      #existingAwsAccount: true
      TF_WORKSPACE: "{{WORKSPACE_SUFFIX}}"
      options:
        init: true
        initOptions: "-upgrade -input=false -no-color"
        # No plan due to -auto-approve on apply
        plan: false
        planOptions: "-input=false"
        apply: true
        applyOptions: "-auto-approve -input=false -no-color"
        dirForTerraform: "terraform"
      config:
        targetEnvironment: "dev"
        applicationName: "{{APP_NAME}}"
      vars:
        - key: "AWS_DEFAULT_REGION"
          description: "AWS region"
          value: "{{AWS_DEFAULT_REGION}}"
          category: "env"
        - key: "spring_profiles_active"
          value: "AWS,DEV{{ADDITIONAL_PROFILES}}"
          category: "terraform"
        - key: "app_name"
          value: "{{APP_NAME}}"
          category: "terraform"
        - key: "docker_image_tag"
          value: "{{DOCKER_TAG_VERSION}}"
        - key: "use_rds"
          value: "{{USE_RDS}}"
          category: "terraform"
        - key: "service_key"
          value: "{{SERVICE_KEY}}"
          category: "terraform"
        - key: cloud_config_profile
          value: "{{CLOUD_CONFIG_PROFILE}}"
          category: "terraform"
        - key: "base_environment"
          value: "{{TARGET_ENVIRONMENT.toUpperCase()}}"
          category: "terraform"
    - type: librarymethod
      name: "Validate-Service-Deploy-DEV"
      libraryClass: "deploy"
      method: "validateServiceDeployment"
      config:
        targetEnvironment: "dev"
        applicationName: "{{APP_NAME}}"
      env:
        SERVICE_URL: "{{context.lib.deploy.serviceUrl}}"
        AWS_CA_BUNDLE: "/usr/local/share/ca-certificates/ca-bundle.crt"
        TF_WORKSPACE: "{{WORKSPACE_SUFFIX}}"