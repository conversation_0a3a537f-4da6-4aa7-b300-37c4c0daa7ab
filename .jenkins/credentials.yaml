development:
  deploy-pipeline:
    hashivault:
      - config:
          vaultURL: 'https://vault-m1.ise-secdev-prod.nube.53.com/'
          id: 'a341a367-3e83-41f8-ac07-4fac44a9fbcb'
          engineVersion: 2
      - secrets:
          - path: "kv/mo3607/pipeline/aws_secrets"
            env: dev
            engineVersion:
            secretValues:
              - envVar: "TFE_TOKEN"
                vaultKey: "tfe_token"
              - envVar: "TFE_TOKEN_STS"
                vaultKey: "tfe_token_sts"
      - secrets:
          - path: "kv/mo3607/pipeline/aws_sts_secrets_dev_scf"
            engineVersion: 2
            awsStsCred: true
            env: dev
release:
  deploy-pipeline:
    hashivault:
      - config:
          vaultURL: 'https://vault-m1.ise-secdev-prod.nube.53.com/'
          id: 'a341a367-3e83-41f8-ac07-4fac44a9fbcb'
          engineVersion: 2
      - secrets:
          - path: "kv/mo3607/pipeline/aws_secrets"
            env: dev
            awsStsCred : false
            engineVersion: 2
            secretValues:
              - envVar: "TFE_TOKEN"
                vaultKey: "tfe_token"
              - envVar: "TFE_TOKEN_STS"
                vaultKey: "tfe_token_sts"
      - secrets:
          - path: "kv/mo3607/pipeline/aws_sts_secrets_dev_scf"
            engineVersion: 2
            awsStsCred: true
            env: dev
      - secrets:
          - path: "kv/mo3607/pipeline/aws_sts_secrets_tribe_dev_scf"
            env: tribe_dev
            awsStsCred: true
            engineVersion: 2
      - secrets:
          - path: "kv/mo3607/pipeline/aws_sts_secrets_sit_scf"
            env: sit
            awsStsCred: true
            engineVersion: 2
      - secrets:
          - path: "kv/mo3607/pipeline/aws_sts_secrets_uat_scf"
            env: uat
            awsStsCred: true
            engineVersion: 2
      - secrets:
          - path: "kv/mo3607/pipeline/aws_sts_secrets_prod_scf"
            engineVersion: 2
            awsStsCred: true
            env: prod