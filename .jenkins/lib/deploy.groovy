#!groovy
import groovy.transform.Field

@Field
def buildScript

@Field
def context

def checkAutoDeploy(){
    /*
     * disable auto deploy for nodeploy branches
     */
    if ( context.branch.contains("${context.lib.config.noDeployBranch}") ){
        echo "disabling automatic deployment triggering"
        context.jobConfig.isAutoDeploy = false
    } else {
        echo "enabling automatic deployment triggering"
        context.jobConfig.isAutoDeploy = true
    }
}

def getWorkspaceSuffix(){
    if(!context.isRelease && !context.isPr){
        return "${context.lib.config.targetEnvironmentDev.toUpperCase()}-${context.lib.config.getVersionPrefix()}"
    }
    return ""
}

def getCloudConfigProfile(){
    if(!context.isRelease && !context.isPr){
        return "_${context.lib.config.getVersionPrefix()}"
    }
    return ""
}

def getAdditionalProfiles(){
    if(!context.isRelease && !context.isPr){
        return ",${context.lib.config.getVersionPrefix()}"
    }
    return ""
}

def getTerraformVersion(){
    return context.lib.config.tfeVersion
}

def getServiceUrl(){
    return context.terraform.getOutput("service_url")
}


def validateServiceDeployment(){
    echo "Inside: .jenkins/libs/deploy.groovy:validateServiceDeployment(): no post-deploy validation implementation added."
}

return this