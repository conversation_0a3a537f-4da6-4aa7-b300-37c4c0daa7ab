#!groovy
import groovy.transform.Field

@Field
def buildScript

@Field
def context

def runRegressionTests(){

    /**
     * Generate the jenkins-specific env json file
     */
    generateNewmanEnvJsonFile()

    /**
     * Make sure all the actuators are reporting as healthy
     */
    verifyActuatorsHealthy()

    /*
     * run the newman tests
     */
    executeNewmanTests()

}

def generateNewmanEnvJsonFile(){
    /**
     * Generate the jenkins-specific env json file
     */
    def newmanContainerEnvVars = [
        id: "ce1480b2-0aa2-4c63-8f72-d62deed746c6",
        name: "Container Test",
        values: [[
                key: "protocol",
                value: "${TEST_PROTOCOL}",
                enabled: true
            ],[
                key: "host",
                value: "${TEST_HOST}",
                enabled: true
            ],[
                key: "port",
                value: "${TEST_PORT}",
                enabled: true
            ],[
                key: "servletcontext",
                value: "${TEST_SERVLET_CONTEXT}",
                enabled: true
            ],[
                key: "context",
                value: "${TEST_APP_CONTEXT}",
                enabled: true
            ]
        ],
        _postman_variable_scope: "environment",
        _postman_exported_using: "Jenkins"
    ]
    buildScript.dir("${TESTS_LOCATION}"){
        buildScript.writeFile(file:"container_test.postman_environment.json",text: context.jsonUtils.toJson(newmanContainerEnvVars))
    }
}

/** 
* Runs curl commands in parallel against the actuator endpoints to verify they're all running
* 
* Note that this is required even though the startupProbe entries on the deploy/ta/*.yml.j2 files
* are checking these same actuator endpoints before reporting the pod as started. I think these
* are required because the gateway (nginx-ingress-controller in kubernetes) takes a bit to startup. 
* so even though the startupProbe's are verifying the actuator/health endpoints this check is really 
* to verify that the gateway is up and running.
*
*/
def verifyActuatorsHealthy(){
    def actuators = context.lib.config.getTestAutomationServicesForReadinessChecks()
    //Build the parallel execution steps
    def parallelExecutors = [:]
    parallelExecutors.failFast = true
    actuators.each() { actuator ->
        parallelExecutors["${actuator}"] = {
            def context = actuator
            waitForService("${TEST_PROTOCOL}://${TEST_HOST}:${TEST_PORT}${TEST_SERVLET_CONTEXT}${actuator}/actuator/health", 30, 30)
        }
    }
    /**
     * Run the waitForService methods in parallel
     */
    buildScript.parallel parallelExecutors
}

def waitForService(def serviceUrl, def retries = 10, def retryWait = 10){
    echo "Checking: curl -X GET ${serviceUrl}"
    def params = [
        url: "${serviceUrl}",
        contentType: "application/json",
        method: "GET",
        throwError: true
    ]
    def tryNumber = 1
    while(tryNumber<=retries){
        try{
            def response = context.jsonServices.sendRequest(params)
            echo "response: ${response}"
            if(!response){
                error("invalid response from API: ${serviceUrl}")
            }
            break
        } catch (e){
            /**
             * If this is the final try, fail the whole thing.
             */
            if(tryNumber == retries){
                throw e
            }
            buildScript.echo("Error getting ${serviceUrl}: try: ${tryNumber}")
            buildScript.echo("${e.message}")
        } finally{
            tryNumber++
        }
        echo "Try number ${tryNumber} failed, waiting for ${retryWait} seconds"
        buildScript.sleep(retryWait)
    }
}

def executeNewmanTests(){
    buildScript.sh("""
        npm config set registry http://${JFROG_HOST}/artifactory/api/npm/npm-virtual/
        npm install --global newman newman-reporter-junitfull newman-reporter-htmlextra

        newman run -k ${TESTS_LOCATION}/app.postman_collection.json -g ${TESTS_LOCATION}/postman_globals.json -e ${TESTS_LOCATION}/container_test.postman_environment.json --reporters='json,junit,html,cli' --reporter-json-export='test-results/newman-results.json' --reporter-junit-export='test-results/newman-results.xml' --reporter-html-export='reports/newman-results.html'
    """)
}

return this
