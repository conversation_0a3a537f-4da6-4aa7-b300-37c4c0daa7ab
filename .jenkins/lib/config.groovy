#!groovy
import groovy.transform.Field

/*
 * Standard Pipeline Configuration
 */
@Field
def buildScript
@Field
def context


/**
 * Static Configuration
 */
@Field
def majorVersion = "0"
@Field
def minorVersion = "0"
@Field
def appName = "move-money-bff"
@Field
def targetEnvironmentDev = "dev"
@Field
def targetEnvironmentTbdev = "tribe_dev"
@Field
def targetEnvironmentSit = "sit"
@Field
def targetEnvironmentUat = "uat"
@Field
def targetEnvironmentProd = "prod"
@Field
def teamDistribution = "<EMAIL>"
@Field
def tfeOrg = "consumer_mobile"
@Field
def tfeVersion = "1.1.9"
@Field
def awsDefaultRegion = "us-east-2"
@Field
def useRds = false
@Field
def noDeployBranch = "nodeploy"


/**
 * To be able to access env vars, these fields, while static,
 * need to be wrapped in a function to delay the 
 * resolution of the env variable until after this 
 * class is converted to a groovy class.
 *
 * Essentially these are one-line property getters
 */
def getWorkspacePrefix(){ return "${env.REPO_NAME}-" }

def getTestAutomationServicesForReadinessChecks() {
    return [
            "${TEST_APP_CONTEXT}",
            "/consumer-mock-ces"
    ]
}

/**
 * Dynamic Configuration
 */

// This is so we don't keep recalculating the prefix.
@Field
def prefix

def getVersionPrefix(){
    if(prefix){
        return prefix
    }
    if(context.isPr){
        prefix = "PR-${context.prData.sourceBranch}"
    } else {
        if(context.isRelease){
            prefix = majorVersion + "." + minorVersion
        } else {
            prefix = "${context.branch}"
        }
    }
    prefix = prefix.minus("feature/").minus("release/")
    //Remove non-alphanumeric characters from the branch name
    if(!context.isRelease){
        prefix = prefix.replaceAll(/[^a-zA-Z0-9\-]/, "")
    }
    return prefix
}

def getVersionPrefixAlphaOnly(){
    return getVersionPrefix().replaceAll(/[^a-zA-Z0-9\-]/, "")
}

def getBuildNumber(){
    return context.envVars.currentBuildNumber()
}

def getBuildVersion(){
    return "${getVersionPrefix()}.${getBuildNumber()}"
}

def getServiceKey(){
    if(context.isRelease){
        return "${appName}"
    }
    return "${appName}-${getVersionPrefix().toLowerCase()}"
}

def getDockerTagVersion(){
    return "${getVersionPrefix().toLowerCase()}.${getBuildNumber()}"
}


/** 
 * Required by Groovy dynamic classloading
 * so the class that loads this class has a reference
 * to the "this" object that is generated when this class
 * is compiled into a Java class
 */
return this
