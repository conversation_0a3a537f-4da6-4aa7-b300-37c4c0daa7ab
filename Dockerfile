FROM jfrog.info53.com/fitb-release-docker-local/xqz-openjdk-image/21-jdk-slim-wget:1.1.10

VOLUME /tmp
ARG JAR_FILE=./build/libs/move-money-bff.jar
COPY ${JAR_FILE} app.jar
USER root
RUN wget https://onlinebanking.53.com/apps/ib/mbl/json/blackoutDates.json

#$USER Setup and configured in xqz-openjdk-image parent image.
USER $USER

ENTRYPOINT ["java", "--add-opens", "java.base/java.lang=ALL-UNNAMED", "--add-opens", "java.base/java.lang.reflect=ALL-UNNAMED", "--add-opens", "java.base/java.util=ALL-UNNAMED", "-jar", "/app.jar"]
