version: "3.3"

services:
  move-money-bff:
    build: ..
    hostname: move-money-bff
    #    user: $CURRENT_UID
    security_opt:
      - label:disable
    environment:
      - SPRING_PROFILES_ACTIVE=local
      - kafka-brokers=kafka:19092
    #    logging:
    #      driver: none
    networks:
      kafka:
        aliases:
          - move-money-bff
    depends_on:
      - kafka

  gateway:
    image: jfrog.info53.com/docker-virtual/nginx:latest
    #    user: $CURRENT_UID #Cant bind to 80 as non root user.
    hostname: gateway #https://github.com/docker/compose/issues/2925
    security_opt:
      - label:disable
    volumes:
      - "./nginx.conf:/etc/nginx/nginx.conf"
    networks:
      kafka:
        aliases:
          - gateway
    depends_on:
      - "move-money-bff"