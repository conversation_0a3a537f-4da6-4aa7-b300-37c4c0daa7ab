#!/usr/bin/env bash

set -e

port="$1"
shift
path="$1"
shift
cmd="$@"

TRY_COUNT=0

while [[ `curl -s -o /dev/null -w '%{http_code}' localhost:$port$path` != "200" ]]; do
  >&2 echo "Service on port $port at $path is not running yet - sleeping"
  TRY_COUNT=$((TRY_COUNT + 5));
  if (( TRY_COUNT > 60 )); then
    echo "Service did not come up in required time." 1>&2
    exit 64
  fi
  sleep 5
done

>&2 echo "Service is up - executing command"
exec $cmd