version: "3.3"

services:
  jar-build:
    image: jfrog.info53.com/fitb-release-docker-local/xqz-openjdk-image/21-jdk-slim-wget:1.1.10
    user: $CURRENT_UID
    security_opt:
      - label:disable
    working_dir: /opt/build
    volumes:
      - "../:/opt/build/"
    command: "./gradlew clean build test jacocoTestReport --rerun-tasks"

  nginx-config:
    image: jfrog.info53.com/fitb-release-docker-local/xqz-nginx-autoconfigure:0.0.3
    security_opt:
      - label:disable
    volumes:
      - "./docker-compose.run.ports.yaml:/opt/nginxConf/docker-compose.run.ports.yaml" #The ports i want mapped
      - "./:/opt/nginxConf/output/" #So i can receive the output conf