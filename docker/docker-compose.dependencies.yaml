version: "3.3"

services:
  zookeeper:
    image: jfrog.info53.com/docker-virtual/confluentinc/cp-zookeeper
    hostname: zookeeper
    security_opt:
      - label:disable
    environment:
      - ZOOKEEPER_CLIENT_PORT=2181
      #      - ZOOKEEPER_SERVER_ID=0
      #      - Z<PERSON><PERSON>EEPER_SERVERS=zookeeper:2181
      - KAFKA_LOG4J_LOGGERS="kafka.controller=WARN"
      - KAFKA_LOG4J_ROOT_LOGLEVEL=WARN
      - KAFKA_TOOLS_LOG4J_LOGLEVEL=ERROR
    #    volumes: #Uncomment to persist config between runs
    #      - ./localdb/zookeeper/data:/data
    #      - ./localdb/zookeeper/datalog:/datalog
    #    logging:
    #      driver: none
    networks:
      kafka:
        aliases:
          - zookeeper

  kafka:
    image: jfrog.info53.com/docker-virtual/confluentinc/cp-kafka
    hostname: kafka
    security_opt:
      - label:disable
    environment:
      KAFKA_ADVERTISED_LISTENERS: LISTENER_DOCKER_INTERNAL://kafka:19092,LISTENER_DOCKER_EXTERNAL://${DOCKER_HOST_IP:-127.0.0.1}:9092
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: LISTENER_DOCKER_INTERNAL:PLAINTEXT,LISTENER_DOCKER_EXTERNAL:PLAINTEXT
      KAFKA_INTER_BROKER_LISTENER_NAME: LISTENER_DOCKER_INTERNAL
      KAFKA_ZOOKEEPER_CONNECT: "zookeeper:2181"
      KAFKA_LOG4J_LOGGERS: "kafka.controller=WARN,kafka.producer.async.DefaultEventHandler=WARN,state.change.logger=WARN"
      KAFKA_LOG4J_ROOT_LOGLEVEL: WARN
      KAFKA_TOOLS_LOG4J_LOGLEVEL: ERROR
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      CONFLUENT_SUPPORT_METRICS_ENABLE: 0
    #    volumes: #Uncomment to persist topics between runs
    #      - ./localdb/kafka/data:/var/lib/kafka/data
    #    logging:
    #      driver: none
    networks:
      kafka:
        aliases:
          - kafka
    depends_on:
      - zookeeper

networks:
  kafka:
    attachable: true