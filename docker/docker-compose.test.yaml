version: "3.3"

services:
  newman:
    image: jfrog.info53.com/docker-virtual/postman/newman:ubuntu
    security_opt:
      - label:disable
    command:
      run app.postman_collection.json
      -g postman_globals.json
      -e container_test.postman_environment.json
      --reporters='json,junit,html,cli'
      --reporter-json-export='/tmp/test-results/newman-results.json'
      --reporter-junit-export='/tmp/test-results/newman-results.xml'
      --reporter-html-export='/tmp/reports/newman-results.html'
    tty: true
    volumes:
      - ../src/test/newman:/etc/newman
      - ../build/reports/newman:/tmp/reports
      - ../build/test-results/newman:/tmp/test-results
    networks:
      - kafka

networks:
  kafka:
    attachable: true