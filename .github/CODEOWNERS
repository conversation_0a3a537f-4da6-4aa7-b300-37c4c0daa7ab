# Lines starting with '#' are comments.
# Each line is a file pattern followed by one or more owners.

# These approvers will be the default approvers for everything in the repo.
* @fitb-mobile-tribe/digital-software-engineering-approvers

# Order is important. The last matching pattern has the most precedence.
# So if a pull request only touches javascript files, only these owners
# will be requested to review.
# *.js    @octocat @github/js

# make sure that consumer devops approves
# changes to .jenkins/release-* files
.jenkins/release-* @fitb-mobile-tribe/pipeline-engineering-approvers

# You can also use email addresses if you prefer.

#Only let the maintainers team modify the CODEOWNERS file
.github/CODEOWNERS @fitb-mobile-tribe/digital-software-engineering-codeowner-approvers
