kind: Ingress
apiVersion: extensions/v1beta1
metadata:
  name: "{{ teamName }}-{{app.name}}-gateway-wiremock"
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/ssl-passthrough: "true"
    nginx.ingress.kubernetes.io/rewrite-target: /$2
spec:
  tls:
    - hosts:
      - "{{jenkins_host}}"
      secretName: jenkins-cert
  rules:
    - host: "{{jenkins_host}}"
      http:
        paths:
          - path: "/{{teamName}}-{{app.name}}/consumer-wiremock-ces(/|$)(.*)"
            backend:
              serviceName: "consumer-wiremock-ces"
              servicePort: {{jenkins_port}}
