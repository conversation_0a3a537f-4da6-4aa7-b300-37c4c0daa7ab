---

kind: Ingress
apiVersion: extensions/v1beta1
metadata:
  name: "{{ teamName }}-{{app.name}}-gateway"
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/ssl-passthrough: "true"
    nginx.ingress.kubernetes.io/backend-protocol: "HTTPS"
spec:
  tls:
    - hosts:
      - "{{jenkins_host}}"
      secretName: jenkins-cert
  rules:
    - host: "{{jenkins_host}}"
      http:
        paths:
          - path: "/{{teamName}}-{{app.name}}/move-money-bff"
            backend:
              serviceName: "{{teamName}}-{{app.name}}"
              servicePort: {{jenkins_port}}
          - path: "/{{teamName}}-{{app.name}}/oauth"
            backend:
              serviceName: "oauth-proxy"
              servicePort: {{jenkins_port}}