---
apiVersion: v1
kind: ConfigMap
metadata:
  name: nginx-config
data:
  nginx.conf: |
    events {}
    http {
      server {
          listen       80;
          server_name  _;
          location / {
            return 200 '{"sub":"happyhippo","full_name":"Test User","last_name":"User","first_name":"Test","username":"happyhippo"}';
            add_header Content-Type application/json;
          }
      }
    }


---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: "oauth-proxy"
  labels:
    app: "oauth-proxy"
    type: "deployment"
spec:
  strategy:
    type: "Recreate"
  replicas: 1
  selector:
    matchLabels:
      app: "oauth-proxy"
      type: "deployment"
  template:
    metadata:
      labels:
        app: "oauth-proxy"
        type: "deployment"
    spec:
      terminationGracePeriodSeconds: 10
      serviceAccountName: "{{teamName}}-{{app.name}}-sa"
      nodeSelector:
        team: "{{nodeSelector}}"
      containers:
      - name: "oauth-proxy"
        image: "{{jfrog_host}}/docker-virtual/nginx"
        ports:
          - containerPort: 80
        resources:
          requests:
            cpu: "100m"
            memory: "128Mi"
        volumeMounts:
          - name: nginx-config
            mountPath: /etc/nginx/nginx.conf
            subPath: nginx.conf
      volumes:
        - name: nginx-config
          configMap:
            name: nginx-config

---

apiVersion: v1
kind: Service
metadata:
  name: "oauth-proxy"
  labels:
    app: "oauth-proxy"
    type: "service"
spec:
  type: ClusterIP
  ports:
    - name: "http"
      port: 80
      targetPort: 80
    - name: "https"
      port: 443
      targetPort: 80
  selector:
    app: "oauth-proxy"
    type: "deployment"