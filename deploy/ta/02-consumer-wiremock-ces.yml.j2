---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: "consumer-wiremock-ces"
  labels:
    app: "consumer-wiremock-ces"
    type: "deployment"
spec:
  strategy:
    type: "Recreate"
  replicas: 1
  selector:
    matchLabels:
      app: "consumer-wiremock-ces"
      type: "deployment"
  template:
    metadata:
      labels:
        app: "consumer-wiremock-ces"
        type: "deployment"
    spec:
      terminationGracePeriodSeconds: 10
      serviceAccountName: "{{teamName}}-{{app.name}}-sa"
      nodeSelector:
        team: "{{nodeSelector}}"
      containers:
        - name: "consumer-wiremock-ces"
          image: "{{jfrog_host}}/docker-virtual/{{image_name}}-wiremock-ces:{{image_version}}"
          ports:
            - containerPort: 8080
              name: http
          readinessProbe:
            httpGet:
              path: "/__admin"
              port: http
            failureThreshold: 10
            periodSeconds: 10
          resources:
            requests:
              cpu: "100m"
              memory: "128Mi"
          volumeMounts:
            - name: data
              mountPath: /data
      volumes:
        - name: data
          emptyDir: {}

---

apiVersion: v1
kind: Service
metadata:
  name: "consumer-wiremock-ces"
  labels:
    app: "consumer-wiremock-ces"
    type: "service"
spec:
  type: ClusterIP
  ports:
    - name: "http"
      port: 80
      targetPort: 8080
    - name: "https"
      port: 443
      targetPort: 8080
  selector:
    app: "consumer-wiremock-ces"