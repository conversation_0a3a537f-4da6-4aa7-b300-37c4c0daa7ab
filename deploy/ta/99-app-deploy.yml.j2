---

apiVersion: v1
kind: ConfigMap
metadata:
  name: "{{teamName}}-{{app.name}}-config"
  labels:
    app: "{{teamName}}-{{app.name}}"
    type: "config"
data:
  # The values in double brackets are deploy-time replacements 
  # that are swapped out by the pipeline based on the
  # target deployment environment
  APP_NAME: "{{app.name}}"
  TEAM_NAME: "{{teamName}}"
  SPRING_PROFILES_ACTIVE: "newman-test,local"
  VAULT_ADDR: "{{vault_addr}}"
  # required for the test automation deploy to work properly
  SERVER_SERVLET_CONTEXT_PATH: "/{{teamName}}-{{app.name}}/move-money-bff"
  ces_proxy_uri: "https://{{jenkins_host}}/{{teamName}}-{{app.name}}/consumer-wiremock-ces"

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: "{{teamName}}-{{app.name}}"
  labels:
    app: "{{teamName}}-{{app.name}}"
    type: "deployment"
spec:
  strategy:
    type: "Recreate"
  replicas: 1
  selector:
    matchLabels:
      app: "{{teamName}}-{{app.name}}"
      type: "deployment"
  template:
    metadata:
      labels:
        app: "{{teamName}}-{{app.name}}"
        type: "deployment"
    spec:
      terminationGracePeriodSeconds: 10
      serviceAccountName: "{{teamName}}-{{app.name}}-sa"
      nodeSelector:
        team: "{{nodeSelector}}"
      containers:
      - name: "{{app.name}}"
        image: "{{jfrog_host}}/docker-virtual/{{image_name}}:{{image_version}}"
        ports:
          - containerPort: 8443
            name: https
        envFrom:
          - configMapRef:
              name: "{{teamName}}-{{app.name}}-config"
        readinessProbe:
          httpGet:
            path: "/{{teamName}}-{{app.name}}/move-money-bff/actuator/health"
            port: https
            scheme: HTTPS
          failureThreshold: 30
          periodSeconds: 20
        volumeMounts:
        - name: data
          mountPath: /data
        #
        # Requesting 1 cpu and 1gb
        #
        resources:
          requests:
            cpu: "1000m"
            memory: "1Gi"
      volumes:
      - name: data
        emptyDir: {}

---

apiVersion: v1
kind: Service
metadata:
  name: "{{teamName}}-{{app.name}}"
  labels:
    app: "{{teamName}}-{{app.name}}"
    type: "service"
spec:
  type: ClusterIP
  ports:
    - name: "http"
      port: 80
      targetPort: 8080
    - name: "https"
      port: 443
      targetPort: 8443
  selector:
    app: "{{teamName}}-{{app.name}}"