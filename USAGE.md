# Move Money Backend for Frontend
Provides application specific views of transfer and payment data obtained from CES.

Implementation [notes](https://github.info53.com/dennis-caldwell/notes) contain examples of CES
interactions and exceptions found during development.

# Endpoints

## /transfer/info

| Verb | Description |
| ---- | ----------- |
| GET | Retrieve a list of accounts available for transfer operations. |

## /account/detail

| Verb | Description |
| ---- | ----------- |
| GET | Retrieve account balances and payment information for a given account.|

## /activity

| Verb | Description |
| ---- | ----------- |
| GET | Obtains activity for all accounts. |
| POST | Create a new transfer, single or recurring. |
| PUT | Update an existing transfer. |
| DELETE | Remove an existing transfer. |

## /activity/transferlimits

| Verb | Description |
| ---- | ----------- |
| GET | Determine the transfer limits for a pair of accounts. | 