apply plugin: 'maven-publish'

buildscript{
    project.ext{
        // configure jfrog variables
        jfrogUsername = System.getenv("JFROG_USER") ?: System.getProperty("user.name").toLowerCase()
        jfrogApiKey = System.getenv("JFROG_API_KEY")

//        if(!jfrogApiKey?.trim()){
//            throw new IllegalArgumentException("JFROG_API_KEY must be set")
//        }else if(jfrogApiKey.length() <= 64){
//            throw new IllegalArgumentException("JFROG_API_KEY is too short to be a key")
//        }

        if(!project.hasProperty("artifactoryHost") || artifactoryHost == null){
            artifactoryHost = "jfrog.info53.com"
        }

        artifactoryBase = "https://" + artifactoryHost + "/artifactory"
        fitbRepoName = project.hasProperty('fitbRepoName') ? project.getProperty('fitbRepoName') : 'fitb-development-virtual'
        mavenRepo = artifactoryBase + '/' + fitbRepoName
    }
}
def jfrogRepositories =  {
    maven {
        url mavenRepo
//        credentials {               // auth credentials (leave out to use anonymous resolution)
//            username = jfrogUsername
//            password = jfrogApiKey
//        }

    }
}

project.buildscript.repositories(jfrogRepositories)

repositories(jfrogRepositories)