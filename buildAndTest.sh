#!/usr/bin/env bash

{ #try
export CURRENT_UID=$(id -u):$(id -g)

docker-compose -f ./docker/docker-compose.build.yaml run nginx-config
docker-compose -f ./docker/docker-compose.build.yaml run jar-build

docker-compose -f ./docker/docker-compose.dependencies.yaml -f ./docker/docker-compose.run.yaml -f ./docker/docker-compose.test.yaml pull
docker-compose -f ./docker/docker-compose.dependencies.yaml -f ./docker/docker-compose.run.yaml -f ./docker/docker-compose.test.ports.yaml up -d --remove-orphans --build
gateway_instance_id=`docker-compose -f ./docker/docker-compose.dependencies.yaml -f ./docker/docker-compose.run.yaml -f ./docker/docker-compose.test.ports.yaml ps -q gateway`
port=`docker inspect -f '{{ (index (index .NetworkSettings.Ports "80/tcp") 0).HostPort }}' $gateway_instance_id`
docker/wait-for-service.sh $port /move-money-bff/actuator/health
docker-compose -f ./docker/docker-compose.test.yaml up
} || { #catch
    echo "An error occured"
    failed=1
}
#finally
docker run --security-opt label=disable -v `pwd`/build:/tmp/reports jfrog.info53.com/docker-virtual/ubuntu bash -c "chown -R $CURRENT_UID /tmp/reports/* && chmod -R 766 /tmp/reports/*"
docker-compose -f ./docker/docker-compose.dependencies.yaml -f ./docker/docker-compose.run.yaml down
docker-compose -f ./docker/docker-compose.test.yaml down
if [[ ! -z $failed ]]; then
    exit 64
fi