# Move Money BFF API Specification

## Overview
This document provides a comprehensive API specification for the Move Money Backend for Frontend (BFF) service. The service provides endpoints for managing transfers, bill payments, external accounts, and CD operations.

## Base URL
- Development: `https://developer-stg.info53.com`
- Production: `https://developer.info53.com`

## Authentication
All endpoints require proper authentication. Unauthorized requests will receive a 401 response.

## Common Response Codes
- `200` - Success
- `401` - Unauthorized
- `403` - Forbidden
- `422` - Additional data required
- `500` - Server error

---

## 1. Activity Controller (`MmActivityController`)

### 1.1 Create Activity
**POST** `/activity`

Creates a new transfer or bill pay activity.

**Request Body:**
```json
{
  "activityType": "string",
  "amount": "number",
  "fromAccountId": "string",
  "toAccountId": "string",
  "memo": "string",
  "scheduledDate": "string (ISO date)"
}
```

**Response:**
```json
{
  "status": "string",
  "statusCode": "string",
  "statusReason": "string",
  "retrievalErrors": ["string"],
  "activityId": "string"
}
```

### 1.2 Update Activity
**PUT** `/activity`

Updates an existing transfer or bill pay activity.

**Request Body:**
```json
{
  "activityId": "string",
  "amount": "number",
  "scheduledDate": "string (ISO date)",
  "memo": "string"
}
```

**Response:**
```json
{
  "status": "string",
  "statusCode": "string",
  "statusReason": "string",
  "retrievalErrors": ["string"]
}
```

### 1.3 Get Activity (V2)
**GET** `/v2/activity`

Returns transfer and bill pay activity for the current user.

**Query Parameters:**
- `recentLimit` (optional): Integer - Limit for recent activities
- `upcomingLimit` (optional): Integer - Limit for upcoming activities

**Response:**
```json
{
  "status": "string",
  "statusCode": "string",
  "statusReason": "string",
  "retrievalErrors": ["string"],
  "recentActivities": [
    {
      "activityId": "string",
      "activityType": "string",
      "amount": "number",
      "fromAccount": {
        "accountId": "string",
        "displayAccountNumber": "string",
        "nickname": "string"
      },
      "toAccount": {
        "accountId": "string",
        "displayAccountNumber": "string",
        "nickname": "string"
      },
      "scheduledDate": "string (ISO date)",
      "status": "string",
      "memo": "string"
    }
  ],
  "upcomingActivities": [
    {
      "activityId": "string",
      "activityType": "string",
      "amount": "number",
      "scheduledDate": "string (ISO date)",
      "status": "string"
    }
  ],
  "recurringActivities": [
    {
      "activityId": "string",
      "frequency": "string",
      "nextDate": "string (ISO date)"
    }
  ],
  "recentTruncated": "boolean",
  "upcomingTruncated": "boolean"
}
```

### 1.4 Get Transfer Limits
**GET** `/activity/transferlimits`

Returns transfer limits for account pairs.

**Query Parameters:**
- `fromAccountId` (required): String
- `toAccountId` (required): String

**Response:**
```json
{
  "status": "string",
  "statusCode": "string",
  "statusReason": "string",
  "retrievalErrors": ["string"],
  "limits": [
    {
      "limitType": "string",
      "amount": "number",
      "frequency": "string"
    }
  ]
}
```

---

## 2. External Transfer Controller (`MmExternalTransferController`)

### 2.1 Get Account Verification Info
**GET** `/externaltransfer/accountVerificationInfo`

Returns verification information for an external account.

**Query Parameters:**
- `accountId` (required): String

**Response:**
```json
{
  "status": "string",
  "statusCode": "string",
  "accountId": "string",
  "verificationStatus": "string",
  "verificationMethods": ["string"],
  "trialDepositVerification": "boolean",
  "realTimeVerification": "boolean"
}
```

### 2.2 Get Bank Information
**GET** `/externaltransfer/bankInfo`

Returns financial institution information.

**Query Parameters:**
- `routingNumber` (optional): String
- `brokerageName` (optional): String

**Response:**
```json
{
  "status": "string",
  "statusCode": "string",
  "institutionName": "string",
  "isHostInstitution": "boolean",
  "trialDepositVerification": "boolean",
  "realTimeVerification": "boolean",
  "finInsLoginInfoList": [
    {
      "loginField": "string",
      "displayName": "string"
    }
  ],
  "instantVerificationEnabledAcctTypes": ["string"]
}
```

### 2.3 Add External Account
**POST** `/externaltransfer/addAccount`

Adds a new external account for transfers.

**Request Body:**
```json
{
  "accountNickName": "string",
  "accountNumber": "string",
  "accountTypeCode": "CHECKING|SAVINGS",
  "creditAccountNumber": "string",
  "creditRoutingNumber": "string",
  "routingNumber": "string"
}
```

**Response:**
```json
{
  "status": "string",
  "statusCode": "string",
  "statusReason": "string",
  "externalAccountVerificationStatus": "PENDING|VERIFIED|FAILED"
}
```

### 2.4 Delete External Account
**DELETE** `/externaltransfer/deleteAccount`

Removes an external account.

**Query Parameters:**
- `accountId` (required): String

**Response:**
```json
{
  "status": "string",
  "statusCode": "string",
  "statusReason": "string"
}
```

### 2.5 Verify Account Real Time
**POST** `/externaltransfer/verifyAccountRealTime`

Performs real-time verification of an external account.

**Request Body:**
```json
{
  "accountId": "string",
  "verificationParameters": [
    {
      "name": "string",
      "value": "string"
    }
  ]
}
```

**Response:**
```json
{
  "status": "string",
  "statusCode": "string",
  "verificationStatus": "SUCCESS|FAILED|PENDING",
  "parameters": [
    {
      "name": "string",
      "displayName": "string",
      "required": "boolean"
    }
  ]
}
```

### 2.6 Verify Trial Deposits
**POST** `/externaltransfer/verifyTrialDeposits`

Verifies trial deposits for account verification.

**Request Body:**
```json
{
  "accountId": "string",
  "deposit1": "number",
  "deposit2": "number"
}
```

**Response:**
```json
{
  "status": "string",
  "statusCode": "string",
  "statusReason": "string"
}
```

### 2.7 Update Account Nickname
**PUT** `/externaltransfer/updateAccountNickname`

Updates the nickname of an external account.

**Request Body:**
```json
{
  "accountId": "string",
  "nickname": "string"
}
```

**Response:**
```json
{
  "status": "string",
  "statusCode": "string",
  "statusReason": "string"
}
```

---

## 3. External Account Controller (`MmExternalAccountController`)

### 3.1 Get External Accounts
**GET** `/external/accounts`

Returns all external accounts for the user.

**Response:**
```json
{
  "status": "string",
  "statusCode": "string",
  "statusReason": "string",
  "transferAccounts": [
    {
      "accountId": "string",
      "accountNickName": "string",
      "accountNumber": "string",
      "accountTypeCode": "string",
      "routingNumber": "string",
      "institutionName": "string",
      "verificationStatus": "string"
    }
  ],
  "paymentAccounts": [
    {
      "accountId": "string",
      "nickname": "string",
      "accountType": "string"
    }
  ],
  "invoicedPaymentAccounts": [
    {
      "accountId": "string",
      "accountType": "string"
    }
  ],
  "maxExternalTransferAccounts": "integer"
}
```

---

## 4. Bill Pay Controller (`MmBillpayController`)

### 4.1 Get Global Payees
**GET** `/billpay/globalpayees`

Returns a list of all global payees with their default input model.

**Response:**
```json
{
  "status": "string",
  "statusCode": "string",
  "statusReason": "string",
  "globalPayees": [
    {
      "name": "string",
      "type": "GLOBAL|CUSTOM"
    }
  ]
}
```

### 4.2 Get Payee Account Number
**GET** `/billpay/payee/account`

Returns the complete payee account number for the given personal payee.

**Query Parameters:**
- `payeeId` (required): String

**Response:**
```json
{
  "status": "string",
  "statusCode": "string",
  "statusReason": "string",
  "accountNumber": "string"
}
```

### 4.3 Add Payee
**POST** `/billpay/addpayee`

Adds a new global or custom payee to personal list.

**Request Body:**
```json
{
  "name": "string",
  "nickname": "string",
  "accountNumber": "string",
  "address": {
    "streetLine1": "string",
    "streetLine2": "string",
    "city": "string",
    "state": "string",
    "zipCode": "string"
  },
  "personalPayee": "boolean"
}
```

**Response:**
```json
{
  "status": "string",
  "statusCode": "string",
  "statusReason": "string",
  "bffPayee": {
    "id": "string",
    "name": "string",
    "nickname": "string",
    "accountNumber": "string",
    "address": {
      "streetLine1": "string",
      "city": "string",
      "state": "string",
      "zipCode": "string"
    },
    "cesAccountType": "string",
    "phoneNumber": "string",
    "paymentCutoffTime": "string",
    "nearestNewPaymentDate": "string",
    "categoryTypeId": "string",
    "electronicBillingEnabled": "boolean",
    "fisPayeeType": "string",
    "cesPayeeType": "string",
    "lastPaymentAmount": "number",
    "lastPaymentDate": "string"
  }
}
```

### 4.4 Edit Payee
**PUT** `/billpay/editpayee`

Updates a global or custom payee in the payee list.

**Request Body:**
```json
{
  "payeeId": "string",
  "name": "string",
  "nickname": "string",
  "accountNumber": "string",
  "address": {
    "streetLine1": "string",
    "streetLine2": "string",
    "city": "string",
    "state": "string",
    "zipCode": "string"
  }
}
```

**Response:**
```json
{
  "status": "string",
  "statusCode": "string",
  "statusReason": "string",
  "bffPayee": {
    "id": "string",
    "name": "string",
    "nickname": "string",
    "accountNumber": "string"
  }
}
```

### 4.5 Get Bill Pay Profile
**GET** `/billpay/profile`

Returns a list of accounts that are eligible to participate in bill payment.

**Response:**
```json
{
  "status": "string",
  "statusCode": "string",
  "statusReason": "string",
  "payees": [
    {
      "id": "string",
      "name": "string",
      "nickname": "string",
      "accountNumber": "string",
      "cesAccountType": "string",
      "phoneNumber": "string",
      "address": {
        "streetLine1": "string",
        "city": "string",
        "state": "string",
        "zipCode": "string"
      },
      "paymentCutoffTime": "string",
      "nearestNewPaymentDate": "string",
      "categoryTypeId": "string",
      "electronicBillingEnabled": "boolean",
      "fisPayeeType": "string",
      "cesPayeeType": "string",
      "lastPaymentAmount": "number",
      "lastPaymentDate": "string"
    }
  ],
  "fundingAccounts": [
    {
      "accountId": "string",
      "displayAccountNumber": "string",
      "nickname": "string",
      "accountType": "string",
      "availableBalance": "number"
    }
  ]
}
```

---

## 5. Transfer Info Controller (`MmTransferInfoController`)

### 5.1 Get Transfer Accounts
**GET** `/transfer/info`

Returns all accounts that can be used in transfer operations.

**Query Parameters:**
- `useAccountCache` (optional): Boolean - Default: false
- `useProfileCache` (optional): Boolean - Default: false

**Response:**
```json
{
  "status": "string",
  "statusCode": "string",
  "statusReason": "string",
  "maxExternalTransferAccounts": "integer",
  "currentExternalTransferAccounts": "integer",
  "memoFieldMaximumLength": "integer",
  "retrievalErrors": ["string"],
  "accounts": [
    {
      "accountId": "string",
      "displayAccountNumber": "string",
      "nickname": "string",
      "accountType": "string",
      "affiliate": "string",
      "availableBalance": "number",
      "ledgerBalance": "number",
      "accountDescription": "string"
    }
  ],
  "blackoutDates": ["string (ISO date)"]
}
```

---

## 6. CD Controllers

### 6.1 CD Funds Transfer Controller (`MmCDFundsTransferController`)

#### 6.1.1 Redeem CD
**POST** `/cd/redeem`

Redeems a Certificate of Deposit.

**Request Body:**
```json
{
  "fromAccountId": "string",
  "toAccountId": "string",
  "amount": "number",
  "cutoff": "boolean",
  "transferType": "string"
}
```

**Response:**
```json
{
  "transactionReferenceNumber": "string",
  "cutoff": "boolean"
}
```

### 6.2 CD Funding Account Controller (`MmCDFundingAccountController`)

#### 6.2.1 Get CD Funding Accounts
**GET** `/cd/funding-accounts`

Returns eligible funding accounts for CD operations.

**Response:**
```json
{
  "status": "string",
  "statusCode": "string",
  "fundingAccounts": [
    {
      "accountId": "string",
      "displayAccountNumber": "string",
      "nickname": "string",
      "accountType": "string",
      "availableBalance": "number",
      "accountDescription": "string"
    }
  ]
}
```

---

## Error Handling

### Standard Error Response
All endpoints return errors in the following format:

```json
{
  "status": "ERROR",
  "statusCode": "string",
  "statusReason": "string",
  "retrievalErrors": ["string"],
  "timestamp": "string (ISO datetime)",
  "path": "string"
}
```

### Common Error Codes
- `400` - Bad Request: Invalid input parameters
- `401` - Unauthorized: Authentication required
- `403` - Forbidden: Access denied
- `404` - Not Found: Resource not found
- `422` - Unprocessable Entity: Additional data required
- `500` - Internal Server Error: Server-side error

---

## Data Models

### Account Object
```json
{
  "accountId": "string",
  "displayAccountNumber": "string",
  "nickname": "string",
  "accountType": "string",
  "affiliate": "string",
  "availableBalance": "number",
  "ledgerBalance": "number",
  "accountDescription": "string",
  "nextPaymentAmount": "number",
  "nextPaymentDate": "string",
  "minimumPaymentAmount": "number",
  "lastStatementBalance": "number",
  "lastStatementDate": "string",
  "initialPrincipal": "number",
  "lastPaymentAmount": "number",
  "lastPaymentDate": "string",
  "cashAdvanceRate": "number",
  "purchaseRate": "number",
  "cashLimitAmount": "number"
}
```

### Activity Object
```json
{
  "activityId": "string",
  "activityType": "string",
  "amount": "number",
  "fromAccount": {
    "accountId": "string",
    "displayAccountNumber": "string",
    "nickname": "string"
  },
  "toAccount": {
    "accountId": "string",
    "displayAccountNumber": "string",
    "nickname": "string"
  },
  "scheduledDate": "string (ISO date)",
  "status": "string",
  "memo": "string",
  "frequency": "string",
  "nextDate": "string (ISO date)"
}
```

### Address Object
```json
{
  "streetLine1": "string",
  "streetLine2": "string",
  "city": "string",
  "state": "string",
  "zipCode": "string"
}
```

---

## Notes

1. All monetary amounts are represented as numbers with decimal precision.
2. Dates are in ISO 8601 format (YYYY-MM-DD).
3. All endpoints support CORS for the specified origins.
4. Risk scoring is applied to certain endpoints for fraud prevention.
5. Feature flags control the routing between different service implementations.
6. Some endpoints have caching capabilities controlled by query parameters.
