buildscript{
    apply from: 'jfrog.gradle' // use jFrog repositories for dependency resolution
}

plugins {
    id 'org.springframework.boot' version "${spring_boot_version}"
    id 'io.spring.dependency-management' version "${dependency_management_version}"
    id 'java'
    id "com.diffplug.spotless" version "${spotless_version}"
    id 'jacoco'
    id 'application'
}

application {
    applicationDefaultJvmArgs = ['-XX:+UseZGC', '-verbose:gc', '-XX:InitialRAMPercentage=75.0', '-XX:MaxRAMPercentage=75.0', '--add-opens=java.base/java.lang=ALL-UNNAMED','--add-opens=java.base/java.lang.reflect=ALL-UNNAMED', '--add-opens=java.base/java.util=ALL-UNNAMED']
    mainClass = 'com.fitb.digital.bff.movemoney.MoveMoneyService'
}

group = 'com.fitb.digital.service'
sourceCompatibility = JavaVersion.VERSION_21
targetCompatibility = JavaVersion.VERSION_21

configurations {
    developmentOnly
    runtimeClasspath {
        extendsFrom developmentOnly
    }
    compileOnly {
        extendsFrom annotationProcessor
    }
    //test {
    //    //needed for OpenPojo support with Java 17
    //    jvmArgs '--add-opens=java.base/java.time=ALL-UNNAMED'
    //    useJUnitPlatform()
    //}
}

configurations.configureEach {
        // Can add global excludes here
        exclude group: 'commons-logging', module: 'commons-logging'
}

jacocoTestReport{
    reports {
        //xml.enabled true
        //html.enabled(true)
        xml.required=true
    }
}

spotless {
    java {
        googleJavaFormat("${google_java_format_version}")
        licenseHeader '/* Copyright $YEAR Fifth Third Bankcorp.  All rights reserved. */'
    }
    compileJava.dependsOn 'spotlessApply'
}

dependencyManagement {
    imports {
        mavenBom "com.fitb.mobile-tribe:fitb-mobile-bom:${mobile_bom_version}"
    }
    dependencies {
        // Override Tomcat version to address CVE-2025-31651, CVE-2025-31650, CVE-2025-48988, CVE-2025-48976, CVE-2025-49125
        dependency "org.apache.tomcat.embed:tomcat-embed-core:${tomcat_version}"
        dependency "org.apache.tomcat.embed:tomcat-embed-websocket:${tomcat_version}"
        dependency "org.apache.tomcat.embed:tomcat-embed-el:${tomcat_version}"

        // Override Spring Framework version to address CVE-2025-22233, CVE-2025-41234
        dependency "org.springframework:spring-core:${spring_framework_version}"
        dependency "org.springframework:spring-context:${spring_framework_version}"
        dependency "org.springframework:spring-web:${spring_framework_version}"
        dependency "org.springframework:spring-webmvc:${spring_framework_version}"
        dependency "org.springframework:spring-beans:${spring_framework_version}"
        dependency "org.springframework:spring-expression:${spring_framework_version}"

        // Override Spring Security version to address CVE-2025-41232
        dependency "org.springframework.security:spring-security-core:${spring_security_version}"
        dependency "org.springframework.security:spring-security-web:${spring_security_version}"
        dependency "org.springframework.security:spring-security-config:${spring_security_version}"
        dependency "org.springframework.security:spring-security-oauth2-resource-server:${spring_security_version}"
        dependency "org.springframework.security:spring-security-oauth2-jose:${spring_security_version}"

        // Override Spring Boot version to address CVE-2025-22235
        dependency "org.springframework.boot:spring-boot:${spring_boot_version}"
        dependency "org.springframework.boot:spring-boot-autoconfigure:${spring_boot_version}"
        dependency "org.springframework.boot:spring-boot-actuator:${spring_boot_version}"
        dependency "org.springframework.boot:spring-boot-actuator-autoconfigure:${spring_boot_version}"
    }
}


dependencies {
    //Required core dependencies
    implementation 'org.springframework.boot:spring-boot-starter'
    implementation "org.jetbrains:annotations:${jetbrains_annotations_version}"
    implementation 'org.projectlombok:lombok'
    runtimeOnly 'org.springframework.plugin:spring-plugin-core'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    runtimeOnly "com.fasterxml.jackson.module:jackson-module-kotlin:${jackson_kotlin_version}"

    implementation "org.springdoc:springdoc-openapi-starter-webmvc-ui"
    implementation 'org.springframework:spring-orm'

    implementation 'io.micrometer:micrometer-registry-prometheus'
    runtimeOnly 'ch.qos.logback.contrib:logback-json-classic'
    runtimeOnly 'ch.qos.logback.contrib:logback-jackson'

    compileOnly 'org.projectlombok:lombok'
    developmentOnly 'org.springframework.boot:spring-boot-devtools'
    annotationProcessor 'org.springframework.boot:spring-boot-configuration-processor'
    annotationProcessor 'org.projectlombok:lombok'

    //Spring boot admin (@see application.yml spring.boot.admin.client.url
    implementation 'de.codecentric:spring-boot-admin-starter-client'

    //Spring Security OAUTH
    implementation 'org.springframework.boot:spring-boot-starter-oauth2-resource-server'
    implementation "org.apache.commons:commons-text:${commons_text_version}"
    implementation 'commons-io:commons-io'

    implementation 'org.springframework.boot:spring-boot-starter-validation'

    implementation "org.modelmapper:modelmapper:${modelmapper_version}"

    //User for any Controller needs
    implementation 'org.springframework.boot:spring-boot-starter-web'

    // Support simple client calls.
    implementation 'org.springframework.cloud:spring-cloud-starter-openfeign'

    implementation('com.fitb.digital.lib:lib-tokenization')
    implementation('com.fitb.digital.lib:fitb-lib-spring-oauth-resource-server')
    implementation 'com.fitb.mobile-tribe:xqz-spring-service-logging-lib'
    implementation "com.fitb.enterprise-software:rest-error-api-boot-starter:${rest_error_api_version}"

    //Enable SSL
    implementation 'com.fitb.digital.lib:tls-spring-boot-configuration'

    //Spring Cloud Vault - Hashicorp Vault integration
    runtimeOnly 'org.springframework.cloud:spring-cloud-starter-vault-config'
    implementation 'com.amazonaws:aws-java-sdk-core'

    //AWS configuration
    implementation "io.awspring.cloud:spring-cloud-aws-starter-parameter-store"
    implementation("io.awspring.cloud:spring-cloud-aws-starter-secrets-manager"){
        exclude group: 'io.awspring.cloud', module: 'spring-cloud-aws-autoconfigure'
    }

    implementation("org.apache.httpcomponents.client5:httpclient5:${httpclient5_version}")

    // risk scoring
    implementation("com.fitb.digital.lib:lib-risk-score:master.45")

    //launch darkly
    implementation("com.fitb.digital.lib:feature-flag-spring-boot-configuration:${feature_flag_version}"){
        exclude group: 'com.squareup.okio', module: 'okio'
    }
    implementation "com.squareup.okio:okio:${okio_version}"
    implementation "com.openpojo:openpojo:${openpojo_version}"

    //Test dependencies
    testRuntimeOnly('org.junit.jupiter:junit-jupiter-engine')
    testImplementation('org.junit.jupiter:junit-jupiter-api')
    testImplementation('org.springframework.boot:spring-boot-starter-test') {
        exclude group: 'org.junit.vintage', module: 'junit-vintage-engine'
    }
    testImplementation 'org.springframework.security:spring-security-test'
    testImplementation "io.jsonwebtoken:jjwt-api:${jjwt_version}"
    testRuntimeOnly "io.jsonwebtoken:jjwt-impl:${jjwt_version}"
    testRuntimeOnly "io.jsonwebtoken:jjwt-jackson:${jjwt_version}"
    testImplementation 'org.springframework.cloud:spring-cloud-contract-wiremock'

}
configurations {
    testCompile.extendsFrom compileOnly
}

test {
    useJUnitPlatform()
    test {
        useJUnitPlatform()
        jvmArgs '--add-opens','java.base/java.lang=ALL-UNNAMED','--add-opens','java.base/java.lang.reflect=ALL-UNNAMED','--add-opens','java.base/java.util=ALL-UNNAMED', '--add-opens','java.base/java.time=ALL-UNNAMED'
        testLogging {
            events "failed"
            exceptionFormat "full"
            showExceptions true
            showCauses true
            showStackTraces true
        }
    }
}
