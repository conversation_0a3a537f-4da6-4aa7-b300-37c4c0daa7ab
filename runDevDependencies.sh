#!/usr/bin/env bash
export CURRENT_UID=$(id -u):$(id -g)

#Just checking for any variable, otherwise assuming up
if [[ -z $1 ]]; then
    docker-compose -f ./docker/docker-compose.dependencies.yaml pull
    docker-compose -f ./docker/docker-compose.dependencies.yaml -f ./docker/docker-compose.dependencies.ports.yaml up -d --remove-orphans --build
else
    docker-compose -f ./docker/docker-compose.dependencies.yaml down
fi
